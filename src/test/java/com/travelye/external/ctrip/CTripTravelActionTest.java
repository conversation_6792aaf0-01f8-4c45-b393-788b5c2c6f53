package com.travelye.external.ctrip;

import com.travelye.base.tools.JSONUtils;
import com.travelye.base.tools.RandomUtil;
import com.travelye.sjis.engine.HttpUtil;
import lombok.Data;
import org.junit.jupiter.api.Test;

import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

class CTripTravelActionTest {

    final static String PREMIUM_JSON = """
            {
              "9118,LYASIA": {
                "1-7": 120,
                "8-10": 200,
                "11-14": 310,
                "15-17": 439,
                "18-21": 567,
                "22-24": 728,
                "25-30": 904
              },
              "9118,CYASIA": {
                "1-7": 150,
                "8-10": 240,
                "11-14": 372,
                "15-17": 518,
                "18-21": 663,
                "22-24": 841,
                "25-30": 1035
              },
              "9118,LYEURO": {
                "1-7": 135,
                "8-10": 231,
                "11-14": 374,
                "15-17": 492,
                "18-21": 590,
                "22-24": 712,
                "25-30": 822
              },
              "9118,LYGLB": {
                "1-7": 145,
                "8-10": 243,
                "11-14": 401,
                "15-17": 552,
                "18-21": 692,
                "22-24": 875,
                "25-30": 1048
              },
              "9118,CYGLB": {
                "1-7": 235,
                "8-10": 312,
                "11-14": 520,
                "15-17": 709,
                "18-21": 882,
                "22-24": 1107,
                "25-30": 1314
              }
            }
            """;

    final static String BODY = """
            <?xml version="1.0" encoding="UTF-8"?>
            <ApplyRequest>
              <TransData>
                <TransID>2747392</TransID>
                <MerchantNo>188</MerchantNo>
                <TransTime>20250208111434</TransTime>
                <OrderID>365220312311</OrderID>
                <ProductCode>9118,LYASIA</ProductCode>
                <EffectTime>20250605000000</EffectTime>
                <ExpiryTime>20250605235959</ExpiryTime>
                <PolicyCount>1</PolicyCount>
                <Premium>120.00</Premium>
                <Destination>东京</Destination>
                <ProposerName>TY IT TEST</ProposerName>
                <ProposerCardType>99</ProposerCardType>
                <ProposerCardNo>TEST2025020801</ProposerCardNo>
                <ProposerBirthDate>19770701</ProposerBirthDate>
                <ProposerGender>M</ProposerGender>
                <ProposerMobile/>
                <ProposerNeedSMS>F</ProposerNeedSMS>
                <ProposerEmail></ProposerEmail>
                <ProposerRelation>05</ProposerRelation>
                <Extend1/>
                <FlightInfo/>
                <InsuredInfo>
                  <InsuredName>TY IT TEST</InsuredName>
                  <InsuredCardType>99</InsuredCardType>
                  <InsuredCardNo>TEST2025020801</InsuredCardNo>
                  <InsuredBirthDate>19770701</InsuredBirthDate>
                  <InsuredGender>M</InsuredGender>
                  <InsuredMobile/>
                  <InsuredNeedSMS>F</InsuredNeedSMS>
                  <InsuredEmail></InsuredEmail>
                  <Beneficiarys>
                    <Beneficiary>
                      <SN>1</SN>
                      <BeneficiaryName/>
                      <BeneficiaryCardType/>
                      <BeneficiaryCardNo/>
                      <BeneficiaryBirthDate/>
                      <BeneficiaryGender/>
                      <BeneficiaryMobile/>
                      <BeneficiaryNeedSMS/>
                      <BeneficiaryEmail/>
                      <BeneficiaryRelation/>
                      <BeneficiaryRate/>
                    </Beneficiary>
                  </Beneficiarys>
                </InsuredInfo>
              </TransData>
              <TransSignature>ctrip20120705</TransSignature>
            </ApplyRequest>
            """;

    final static List<String> PLAN_LIST = Arrays.asList("9118,LYASIA", "9118,CYASIA", "9118,LYEURO", "9118,LYGLB", "9118,CYGLB");
    final static LocalDateTime EXPIRY = LocalDateTime.of(2025, 6, 5, 23, 59, 59);

    final static String URL = "https://uat-travelye.aig.com.cn/external/ctrip.action";


    @Test
    void insure() {
        List<InsureRecord> recordList = new ArrayList<>();
        Map<String, Object> price = JSONUtils.readJson2Map(PREMIUM_JSON);
        for (String plan : PLAN_LIST) {
            Map<String, Integer> planPrice = (Map<String, Integer>) price.get(plan);
            for (int i = 0; i < 30; i++) {
                Integer premium = 0;
                if (i < 7) {
                    premium = planPrice.get("1-7");
                } else if (i < 10) {
                    premium = planPrice.get("8-10");
                } else if (i < 14) {
                    premium = planPrice.get("11-14");
                } else if (i < 17) {
                    premium = planPrice.get("15-17");
                } else if (i < 21) {
                    premium = planPrice.get("18-21");
                } else if (i < 24) {
                    premium = planPrice.get("22-24");
                } else if (i < 30) {
                    premium = planPrice.get("25-30");
                }
                LocalDateTime expiryDate = EXPIRY.plusDays(i);
                InsureRecord insureRecord = new InsureRecord();
                insureRecord.setPremium(premium);
                insureRecord.setPlanCode(plan.replace("9118,", ""));

                recordList.add(insureRecord);

                String transId = UUID.randomUUID().toString();
                String idNo = RandomUtil.randomAlphabetString(2) + RandomUtil.randomNumberString(7);
                String message = BODY.replaceAll("(<TransID>)(.*?)(</TransID>)", "$1" + transId + "$3")
                        .replaceAll("(<ExpiryTime>)(.*?)(</ExpiryTime>)", "$1" + expiryDate.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "$3")
                        .replaceAll("(<InsuredCardNo>)(.*?)(</InsuredCardNo>)", "$1" + idNo + "$3")
                        .replaceAll("(<ProductCode>)(.*?)(</ProductCode>)", "$1" + plan + "$3")
                        .replaceAll("(<Premium>)(.*?)(</Premium>)", "$1" + premium + "$3");

                insureRecord.setEffectiveDate("20250601000000");
                insureRecord.setExpiryDate(expiryDate.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

                try {
                    HttpResponse<String> response = HttpUtil.post(URL, message, HttpUtil.BODY_TYPE_TEXT, null, String.class);
                    if (response.statusCode() == 200) {

                        Pattern pattern = Pattern.compile("<ResultMsg>(.*?)</ResultMsg>");
                        Matcher matcher = pattern.matcher(response.body());
                        if (matcher.find()) {
                            String resultMsg = matcher.group(1);
                            insureRecord.setComment(resultMsg != null ? resultMsg : "");
                            continue;
                        }

                        // 正则表达式匹配 <PolicyNo>标签中的内容
                        pattern = Pattern.compile("<PolicyNo>(.*?)</PolicyNo>");
                        matcher = pattern.matcher(response.body());
                        if (matcher.find()) {
                            String policyNo = matcher.group(1);
                            System.out.println("PolicyNo 值: " + policyNo);
                            insureRecord.setPolicyNo(policyNo);
                        } else {
                            System.out.println("未找到 PolicyNo");
                        }

                        pattern = Pattern.compile("<EPolicyInfo>(.*?)</EPolicyInfo>");
                        matcher = pattern.matcher(response.body());
                        if (matcher.find()) {
                            String pdfUrl = matcher.group(1);
                            insureRecord.setPdfUrl(pdfUrl);
                        } else {
                            System.out.println("未找到 pdfUrl");
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    insureRecord.setComment("系统异常");
                }
            }
        }

        recordList.forEach(record -> {
            System.out.println(record.toString());
        });
    }

    @Data
    static class InsureRecord {
        private String policyNo;
        private String effectiveDate;
        private String expiryDate;
        private String comment;
        private Integer premium;
        private String pdfUrl;
        private String planCode;

        @Override
        public String toString() {
            return policyNo + "," + planCode + "," + effectiveDate + "," + expiryDate + "," + premium + "," + pdfUrl + "," + comment;
        }
    }
}

package com.travelye.external.wesure.dto.insure.request;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ExtraInfoTest {

    @Test
    void destinationsTest() {
        InsureTransaction transaction = new InsureTransaction();
        ExtraInfo extraInfo = new ExtraInfo();
        extraInfo.setDestinations("AS_MYS_马来西亚|AS_SGP_新加坡|AS_THA_泰国");
        transaction.setExtraInfo(extraInfo);

        System.out.println(transaction.alphabetDestinations());
    }
}

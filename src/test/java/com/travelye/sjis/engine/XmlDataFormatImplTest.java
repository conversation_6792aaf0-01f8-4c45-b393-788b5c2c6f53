package com.travelye.sjis.engine;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.travelye.api.down.liberty.DTO.LibertyResponse;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.base.MessageException;
import com.travelye.base.tools.RandomUtil;
import com.travelye.external.LibertyErrorCode;
import com.travelye.vo.Plan;
import com.travelye.vo.Policy;
import com.travelye.vo.Policyuser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.*;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@SpringBootTest(classes = XmlDataFormatImpl.class)
class XmlDataFormatImplTest {

    private static final String url = "https://services.uat.travelguard.cn/WAATS/servlet/WaatsEngine.WTEngine";

    @Autowired
    private XmlDataFormatImpl xmlDataFormatImpl;

    private String getResultXml(HttpResponse<String> httpResponse) {
        // 网络异常或返回body为空
        if (httpResponse.statusCode() != 200 || StringUtils.isBlank(httpResponse.body())) {
            throw new MessageException("网络异常，请稍后再试。");
        }

        try {
            String[] lines = httpResponse.body().split("\n");
            int stateCode = Integer.parseInt(lines[1].trim());
            System.out.println("status code:  " + stateCode);
            if (0 == stateCode) {

                String xml = httpResponse.body().substring(httpResponse.body().indexOf("<TINS_XML_DATA>"),
                        httpResponse.body().indexOf("</TINS_XML_DATA>") + 16
                ).trim();

                if (StringUtils.isBlank(xml)) {
                    throw new MessageException("网络异常，请稍后重试。");
                }
                return xml;
            } else {
                throw new MessageException("系统异常，请稍后重试。");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    @Test
    void cancelXml() throws DocumentException {

    }

    @Test
    void insureXml() {
        List<String> list = new ArrayList<>();

        try {
            String source = FileUtils.readFileToString(new File("/Users/<USER>/Desktop/product.json"), StandardCharsets.UTF_8);
            Map<String, Object> map = JsonMapperUtils.jsonToObject(source, Map.class);
            for (String key : map.keySet()) {
                try {

                    Map<String, Object> value = (Map<String, Object>) map.get(key);
                    List<String> planList = (List<String>) value.get("plan");
                    List<String> benefitCdList = (List<String>) value.get("benefitCd");
                    Agency agency = Agency.builder()
                            .agencyPcc(((Map<String, String>) value.get("agency")).get("agencyPcc"))
                            .gdsCode(((Map<String, String>) value.get("agency")).get("gdsCode"))
                            .iataCntryCd(((Map<String, String>) value.get("agency")).get("iataCntryCd"))
                            .subCode(((Map<String, String>) value.get("agency")).get("subCode"))
                            .build();

                    Result adultResultDto = Result.builder()
                            .productNum(key)
                            .agency(agency.toString())
                            .build();

                    Result childResultDto = Result.builder()
                            .productNum(key)
                            .agency(agency.toString())
                            .build();


                    for (String planCode : planList) {
                        adultResultDto.setPlanCode(planCode);
                        childResultDto.setPlanCode(planCode);

                        for (String benefitCd : benefitCdList) {
                            adultResultDto.setBenefitCd(benefitCd);
                            childResultDto.setBenefitCd(benefitCd);
                            finalResult(key, planCode, benefitCd, agency, true, childResultDto);
                            list.add(childResultDto.toString());
                            finalResult(key, planCode, benefitCd, agency, false, adultResultDto);
                            list.add(adultResultDto.toString());
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (JsonProcessingException e) {
            System.out.println("source map 解析失败");
            e.printStackTrace();
        } catch (IOException e) {
            System.out.println("读取产品数据json文件错误");
            e.printStackTrace();
        }

        list.forEach(System.out::println);
    }

    private void finalResult(String key, String planCode, String benefitCd, Agency agency, boolean isChild, Result resultDto) {
        try {
            HttpResponse<String> httpResponse = HttpUtil.postForm(url, Map.of("MessageText", getXml(key, planCode, benefitCd, agency, isChild, resultDto), "MessageType", ChartisBJParameters.AIGmessageType));

            LibertyResponse libertyResponse = getResult(getResultXml(httpResponse));

            if (0 == libertyResponse.getErrorCode()) {
                resultDto.setPolicyNumber(libertyResponse.getPolicyNumber());
            } else {
                resultDto.setError(libertyResponse.getErrorMessage());
            }


        } catch (Exception e) {
            e.printStackTrace();
            String error = "系统异常";
            if (e instanceof DocumentException) {
                error = "解析报文异常";
            }
            if (e instanceof MessageException) {
                error = e.getMessage();
            }
            resultDto.setError(error);
        }

    }

    private LibertyResponse getResult(String responseXml) throws DocumentException {
        LibertyResponse libertyResponseDto = new LibertyResponse();
        Document document = DocumentHelper.parseText(responseXml);
        Element root = document.getRootElement();
        Node segment = root.selectSingleNode("Segment");

        Node header = root.selectSingleNode("Header");
        libertyResponseDto.setTransactionId(header.valueOf("MessageId"));

        int errorCode = segment.numberValueOf("ErrorCode").intValue();
        libertyResponseDto.setErrorCode(errorCode);
        if (errorCode != 0) {

            LibertyErrorCode libertyErrorCodeEnum = LibertyErrorCode.valueOfCode(errorCode);
            if (libertyErrorCodeEnum != null) {
                libertyResponseDto.setErrorMessage(libertyErrorCodeEnum.getDescription());
                return libertyResponseDto;
            }

            libertyResponseDto.setErrorMessage(segment.valueOf("ErrorMessage"));
            return libertyResponseDto;
        }
        String policyNumber = segment.valueOf("PolicyOut/PolicyNumber");
        String premium = segment.valueOf("PolicyOut/TotalPremium");
        if (StringUtils.isBlank(policyNumber)) {
            throw new MessageException("网络异常，请稍后重试。核心系统返回保单号为空。");
        }

        libertyResponseDto.setPolicyNumber(policyNumber);
        libertyResponseDto.setPremium(premium);
        return libertyResponseDto;

    }

    private String getXml(String productNum, String planCode, String benefitCd, Agency agency, boolean isChild, Result result) {
        Plan plan = new Plan();
        plan.setIsExtendPlan(0);

        if ("1".equals(benefitCd)) {
            plan.setIsExtendPlan(0);
        } else {
            plan.setIsExtendPlan(1);
        }
        plan.setExtendPlanCode(benefitCd);

        Policy policy = new Policy();
        policy.setPolicyID(Long.parseLong(RandomUtil.randomNumberString(6)));
        policy.setGDSCode(agency.getGdsCode());
        policy.setSourceId(agency.getGdsCode());
        policy.setAgencyCode(agency.getSubCode());
        policy.setAgencyPCC(agency.getAgencyPcc());
        policy.setAttn("");
        policy.setIATACntryCd(agency.getIataCntryCd());
        policy.setProductNum(productNum);
        policy.setProductCode(productNum);
        policy.setPlanCode(planCode);
        policy.setPlan(plan);
        policy.setEffectiveDate(LocalDateTime.of(2024, 12, 10, 0, 0, 0));
        policy.setExpiryDate(LocalDateTime.of(2024, 12, 10, 23, 59, 59));
        policy.setLasttime(LocalDateTime.now());

        List<String> annualPlanList = List.of(
                "1200419 AAP",
                "1200419 ADP",
                "1200419 AGP",
                "1200419 ASP",
                "13301217 AAP",
                "13301217 ABP",
                "ANNADV",
                "ANNSTD"
        );

        if (annualPlanList.contains(planCode)) {
            policy.setEffectiveDate(LocalDateTime.of(2024, 12, 10, 0, 0, 0));
            policy.setExpiryDate(LocalDateTime.of(2025, 12, 9, 23, 59, 59));
        }


        List<Policyuser> insuredList = new ArrayList<>();
        Policyuser applicant = Policyuser.builder()
                .insuredname("投保人" + RandomUtil.randomAlphabetString(4))
                .birthday(LocalDate.of(1991, 11, 11))
                .insuredIdNoType("其他")
                .insuredIdNo("E" + RandomUtil.randomNumberString(8))
                .deathBeneficiary("法定Legal Heir")
                .isInsuredFlag(0)
                .relationship("Self")
                .build();

        Policyuser insured = Policyuser.builder()
                .insuredname((isChild ? "未成年人" : "成年人"))
                .birthday(isChild ? LocalDate.of(2022, 5, 2) : LocalDate.of(2000, 1, 1))
                .insuredIdNoType("其他")
                .insuredIdNo("E" + RandomUtil.randomNumberString(8))
                .deathBeneficiary("法定Legal Heir")
                .isInsuredFlag(2)
                .relationship(isChild ? "子女 Child" : "配偶 Spouse")
                .build();

        result.setApplicant(applicant.getInsuredFullName() + " " + applicant.getInsuredIdNo());
        result.setInsured(insured.getInsuredFullName() + " " + insured.getInsuredIdNo());

        insuredList.add(applicant);
        insuredList.add(insured);

        return xmlDataFormatImpl.constructApplyPolicy(policy, insuredList, ChartisBJParameters.NSell);
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Agency {
        private String gdsCode;
        private String agencyPcc;
        private String subCode;
        private String iataCntryCd;

        public String toString() {
            return gdsCode + "-" + agencyPcc + "-" + subCode;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class Result {
        private String productNum;
        private String planCode;
        private String benefitCd;
        private String insured;
        private String applicant;
        private String policyNumber;
        private String error;
        private String agency;

        public String toString() {
            return productNum + ","
                    + planCode + ","
                    + benefitCd + ","
                    + policyNumber + ","
                    + insured + ","
                    + applicant + ","
                    + agency + ","
                    + error;
        }
    }

}

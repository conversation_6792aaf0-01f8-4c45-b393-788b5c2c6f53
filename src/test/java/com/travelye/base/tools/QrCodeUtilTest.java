package com.travelye.base.tools;

import org.junit.jupiter.api.Test;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QrCodeUtilTest {

    @Test
    void qrCode() {
        List<Integer> productIdList = List.of(133,
                138,
                221,
                223,
                226,
                227,
                228,
                229,
                230,
                231,
                232,
                233,
                234,
                235,
                236,
                237,
                238,
                239,
                240,
                243,
                244,
                245,
                246,
                247,
                248,
                249,
                251,
                252,
                254,
                255,
                256,
                257,
                258,
                260
        );

        String url = "https://travelye.aig.com.cn/policyCenter/b2c/termShow?productId=";

        productIdList.forEach(p -> {
                    try {
                        byte[] qrCode = QrCodeUtil.QREncode(url + p, 40, 40);

                        BufferedImage origin = ImageIO.read(new ByteArrayInputStream(qrCode));

                        BufferedImage result = new BufferedImage(140, 140, BufferedImage.TYPE_INT_RGB);
                        Graphics graphics = result.getGraphics();
                        graphics.drawImage(origin, 0, 0, 140, 140, null);
                        graphics.dispose();

                        ImageIO.write(result, "png", new File("/Users/<USER>/Desktop/qrcode/" + p + ".png"));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
        );
    }
}

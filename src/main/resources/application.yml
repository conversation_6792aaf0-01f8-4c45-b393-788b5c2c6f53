server:
  tomcat:
    basedir: src/main/webapp
  port: ${env.port}
  max-http-request-header-size: 10MB
spring:
  config:
    additional-location: classpath:travelye-${profile.active}.properties
  main:
    allow-circular-references: true
  profiles:
    active: ${profiles.active}
  session:
    timeout: 900
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    hikari:
      connection-timeout: 6000
  freemarker:
    suffix: .ftl
    template-loader-path: classpath:/templates/template
    charset: UTF-8
    expose-request-attributes: true
    expose-session-attributes: true
    allow-request-override: true
    settings:
      number_format: 0.##
    cache: true
  security:
    saml2:
      relyingparty:
        registration:
          okta:
            entity-id: https://uat.chartis-travel.com/auth/okta/assertion.actio
            #            signing:
            #              credentials:
            #                - private-key-location:
            #                  certificate-location:
            acs:
              location: https://uat.chartis-travel.com/login/saml2/sso/okta
            assertingparty:
              #              metadata-uri: https://dev-75068840.okta.com/app/exklnja53wdOWJBuD5d7/sso/saml/metadata
              verification:
                credentials:
                - certificate-location: classpath:saml/okta-uat.cert
              metadata-uri: classpath:saml/metadata-uat
              singlesignon:
                #                url: https://dev-75068840.okta.com/app/dev-75068840_localsaml2devservice_1/exklnja53wdOWJBuD5d7/sso/saml
                url: https://uataigtech.oktapreview.com/app/uataigtech_cnpatravelyeuat_1/exk13hw8yqe7uQHbP0h8/sso/saml
  web:
    resources:
      chain:
        strategy:
          content:
            enabled: true
            paths: /**
        cache: true
      cache:
        cachecontrol:
          cache-public: true
      static-locations: classpath:/static/

cxf:
  path: /webservice

logging:
  level:
    org.springframework.boot.autoconfigure: ERROR
    org.apache.hc: INFO
  config: classpath:logback-spring.xml

profiles:
  active: dev
env:
  port: 8080
database:
  url: *******************************
  username: root
  password:
saml2:
  entity-id: http://localhost:8080/saml/doLogin
  identityprovider: http://www.okta.com/exk3uxm60xXBqM2LM5d7
  certificate-location: "classpath:saml/okta-localhost.cert"
  sso-url: https://dev-75068840.okta.com/app/dev-75068840_localhost_1/exk3uxm60xXBqM2LM5d7/sso/saml
  metadata-location: classpath:saml/metadata-localhost
  acs-location: http://localhost:8080/login/saml2/sso/okta

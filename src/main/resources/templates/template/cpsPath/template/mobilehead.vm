<link rel="stylesheet" href="$bPath/css/mobileinsure.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
<script type="text/javascript">

</script>
<style type="text/css">
    .tipsinfo{position:absolute;color:#999;margin-left:0.5em;}
    .loginForm{width:100%;position:absolute;background-color:#ffffff;margin:auto;padding:20px 0px;box-shadow:0px 3px 5px #ccc;display:none;}
    .user_edit_ul{background-color:#fff;margin:auto;box-shadow:0px 3px 5px #ccc;display:none;padding:0;}
    .user_edit_ul li{list-style-type:none;border-bottom:1px solid #ccc;margin:0;padding:0;color:#333;}
    .user_edit_ul .logout{border-bottom:none;}
</style>
<div style="display:none;" id="chromestyle">
    <script type="text/javascript">
        if(window.chrome){
            var st = "<style type='text/css'>"+
                    "input[type='checkbox']{vertical-align:-2px;}"+
                    "input[type='text'],input[type='number']{padding-left:4px;}"+
                    "input[type='date']{-webkit-appearance:none;}"+
                    "input,select{font-size:1em;}"+
                    "</style>";
            $("#chromestyle").html(st);
        }
    </script>
</div>
<div class="policyHead">
	#if("$!{headParam.backData}" != "")
	<span class="editHead" cdata="$!{headParam.backData}"><i class="fa fa-arrow-left"></i></span>
	#end
	<span>$!{headParam.pageTitle}</span>
	<div class="loginForm" style=""></div>
	<ul class="user_edit_ul"></ul>
</div>
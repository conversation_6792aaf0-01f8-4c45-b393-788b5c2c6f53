<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Pragma" content="no-cache"/>   
<meta http-equiv="Cache-Control" content="no-cache"/>   
<meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js"></script>
<script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
<script type="text/javascript">
function doSubmit() 
{
   	document.formname.action="/china/systemManager/saveAction";
	
   	document.formname.submit();
}	
</script>
</head>

<body>
<#include "../common/top.ftl">
    <div class="content">
		<div id="contentbar" class="contentbar">
            <span>${(action.actionID?? || action.actionID! == 0)?string('系统管理 > 权限管理 > 增加权限','系统管理 > 权限管理 > 修改权限')}</span>
        </div>
		<#if message??>${message!}<a href="/china/systemManager/action">继续添加</a></#if>
 		<form name="formname" method="post" action="/china/systemManager/saveAction">
			<input id="actionid" name="actionid" type="hidden" value="${action.actionID!}" />
			<div class="tbxx upinfo">
                <table>
                    <tr>
                        <td width="60" align="right">权限分类：</td>
                        <td><input name="actiontype" size="50" type="text" value="${action.actionType!}"/></td>
                    </tr>
                    <tr>
                        <td align="right">权限名称：</td>
                        <td><input name="actionname" size="50" type="text" value="${action.actionName!}"/></td>
                    </tr>
					<tr>
						<td align="right">Action：</td>
                        <td><input name="actionfield" size="50" type="text" value="${action.action!}"/></td>
                    </tr>
                </table>
            </div>
		</form>
		<div class="btn_tj"><input type="button" value="保 存" onclick="doSubmit();" style="margin-right:10px;"/><input type="button" value="取 消" onclick="{location.href='/china/systemManager/listAction'}"/></div>
	</div>
<div id="msg" class="tb_box">
	<iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
       		<tr>
            	<td class="btm"></td>
                <td class="box_ct" valign="top">
                	<div id="msg_ban" class="b_til">
                    	<span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                    	<div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr> 
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end--> 
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/insure.js?v=04"></script>
</html>

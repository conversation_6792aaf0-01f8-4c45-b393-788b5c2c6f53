<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
    <script language="javascript" src="/js/jquery.js"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
    <!-- <script type="text/javascript" src="/fckeditor/fckeditor.js"></script> -->
    <script type="text/javascript" src="/ckeditor/ckeditor.js"></script>
    <script type="text/javascript">
        function doSubmit()
        {
            if(document.formname.productname.value.replace(/\s/g,"")==""){
                alert("请您输入正确的名称。");
                document.formname.productname.focus();
                return;
            }
            if(document.formname.productcode.value.replace(/\s/g,"")==""){
                alert("请您输入正确的代号。");
                document.formname.productcode.focus();
                return;
            }

            if($("#isWaats1").attr("checked")=="checked"){
                if(document.formname.productNum.value.replace(/\s/g,"")==""){
                    alert("请您输入正确的产品代号。");
                    document.formname.productNum.focus();
                    return;
                }
            }else{
                if(document.formname.productNum.value.replace(/\s/g,"")==""){
                    document.formname.productNum.value = document.formname.productcode.value;
                }
            }

            var arr = [];
            var isRepeat=false;
            $("#trbs tr").each(function (i) {
                var count=i+1;
                if ($(this).find("input[name='jhzt_"+count+"']").val()== "1" ){     //计划有效
                    if (jQuery.inArray($(this).find("input[name='jhsubcode_"+count+"']").val(),arr) > 0){
                        isRepeat=true;
                        return;
                    }else {
                        arr.push($(this).val());
                    }
                }
            })
            if (isRepeat){
                alert("此次欲保存的渠道对接代码有重复，请修改。");
                return;
            }

            document.formname.action="saveProduct";
            document.formname.submit();
        }
    </script>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar">
        <span>${((product.productid)?? || ((product.productid)!0) == 0)?string('系统管理->产品管理->增加产品','系统管理->产品管理->修改产品')}
</span>
    </div>
    <#if message??>
        ${message!}
    </#if>
    <form id="jsonform" name="jsondata" method="post" action="exportJson" target="_self">
        <input type="hidden" name="json" id="jsondata" />
    </form>
    <!--[if IE 6]>
    <script type="text/javascript">
        document.getElementById("jsonform").target="_blank";
        //兼容IE6在本页面不能导出文件的bug
    </script>
    <![endif]-->
    <form name="formname" method="post" enctype="multipart/form-data" action="saveProduct">
        <input name="productid" type="hidden" value="${(product.productid)!}" />
        <input name="branch" type="hidden" value="${(product.branch)!}" />
        <div class="tbxx upinfo">
            <table>
                <tr>
                    <td align="right">产品操作：</td>
                    <td><a class="exportall" href="javascript:void(0)" style="margin-left:0px;">导出产品</a><a class="importall" href="javascript:void(0)">导入产品</a></td>
                </tr>
                <tr class="productNum" style="display: none;">
                    <td align="right">产品代号：</td>
                    <td><input name="productNum" type="text" value="${(product.productNum)!}" /></td>
                </tr>
                <tr>
                    <td align="right">渠道对接代码：</td>
                    <td><input name="productcode" type="text" value="${(product.productcode)!}" /></td>
                </tr>
                <tr>
                    <td align="right">产品名称：</td>
                    <td><input name="productname" size="50" type="text" value="${(product.productname)!}"/></td>
                </tr>
                <tr>
                    <td align="right">产品简称：</td>
                    <td><input name="abbname" size="50" type="text" value="${(product.abbname)!}"/></td>
                </tr>
                <tr>
                    <td align="right">英文全称：</td>
                    <td><input name="englishname" size="50" type="text" value="${(product.englishname)!}"/></td>
                </tr>
                <tr>
                    <td align="right">是否标准产品：</td>
                    <td><input id="isStandard" name="isStandard" type="radio" value="1"
                                <#if (((product.isStandard)!0) == 1)> checked </#if>>是
                        <input id="isStandard" name="isStandard" type="radio" value="0"
                                <#if (((product.isStandard)!0) == 0)> checked </#if>>否
                    </td>
                </tr>
                <tr>
                    <td align="right">是否Waats产品：</td>
                    <td><input id="isWaats1" name="isWaats" type="radio" value="1"
                                <#if (((product.isWaats)!0) == 1)> checked </#if>>是
                        <input id="isWaats2" name="isWaats" type="radio" value="0"
                                <#if (((product.isWaats)!0) == 0)> checked </#if>>否
                    </td>
                </tr>
                <tr>
                    <td align="right">是否团险产品：</td>
                    <td><input id="isGroup1" name="isGroup" type="radio" value="1"
                                <#if (((product.isGroup)!0) == 1)> checked </#if>>是
                        <input id="isGroup2" name="isGroup" type="radio" value="0"
                                <#if (((product.isGroup)!0) == 0)> checked </#if>>否
                    </td>
                </tr>
                <tr>
                    <td align="right">状态：</td>
                    <td><input id="status1" name="status" type="radio" value="1"
                                <#if (((product.status)!0) == 1)> checked </#if>>有效
                        <input id="status2" name="status" type="radio" value="0"
                                <#if (((product.status)!0) == 0)> checked </#if>>无效
                    </td>
                </tr>


            </table>
        </div>
        <div class="fltil">
            <span>产品计划</span>
            <a hideFocus id="showbox" class="add" href="javascript:void(0)"></a>
        </div>
        <div style="overflow-x: auto;">
            <table class="tbli" >
                <tr class="tbhead">
                    <th class="bh" width="45">编号</th>
                    <th width="90">计划代号</th>
                    <th width="90">渠道对接代码</th>
                    <th>计划名称</th>
                    <th>英文名称</th>
                    <th>计划状态</th>
                    <th>投保年龄范围</th>
                    <th>投保时间范围</th>
                    <th>保障项目</th>
                    <th>计划费率</th>
                    <th>费率公式</th>
                    <th>备注</th>
                    <th>操作</th>
                </tr>
                <tbody id="trbs">
                <#list planlist! as item>
                    <tr>
                        <td class='tbh'>${item?counter}<input name='ExtendPlanCode_${item?counter}' type='hidden' value='${(item.ExtendPlanCode)!}' /><input name='isExtendPlan_${item?counter}' type='hidden' value='${(item.isExtendPlan)!}' /></td>
                        <td><span>${(item.plancode)!}</span><input name='jhid_${item?counter}' type='hidden' value='${(item.plancode)!}' /><input name='planid_${item?counter}' type='hidden' value='${(item.planid)!}' /></td>
                        <td><span>${(item.subplancode)!}</span><input name='jhsubcode_${item?counter}' type='hidden' value='${(item.subplancode)!}' /></td>
                        <td><span>${(item.planname)!}</span><input name='jhnm_${item?counter}' type='hidden' value='${(item.planname)!}' /></td>
                        <td><span>${(item.englishName)!}</span><input name='jhenm_${item?counter}' type='hidden' value='${(item.englishName)!}' /></td>
                        <td><span>${(item.status)!}</span><input name='jhzt_${item?counter}' type='hidden' value='${(item.status)!}' /></td>
                        <td><span>${(item.minage)!}-${(item.maxage)!}</span><input name='sage_${item?counter}' type='hidden' value='${(item.minage)!}' /><input name='mage_${item?counter}' type='hidden' value='${(item.maxage)!}' /></td>
                        <td><span>${(item.minday)!}-${(item.maxday)!}</span><input name='sday_${item?counter}' type='hidden' value='${(item.minday)!}' /><input name='mday_${item?counter}' type='hidden' value='${(item.maxday)!}' /></td>
                        <td><a id="showproj" href="javascript:void(0)">查看</a><input name="bzxm_${item?counter}" type="hidden" value="${(item.insureproject)!}" /></td>
                        <td><a id='showrate' href="javascript:void(0)">查看</a><input name='jhrate_${item?counter}' type='hidden' value='${(item.pricerule)!}' /><input name='jhindex_${item?counter}' type='hidden' value='${(item.orders)!}' /></td>
                        <td><a id="showratejs" href="javascript:void(0)">查看</a><input name="jsFormula_${item?counter}" type="hidden" value="${(item.jsFormula)!}" /></td>
                        <td><a id="showbz" href="javascript:void(0)">查看</a><input name="bzinfo_${item?counter}" type="hidden" value="${(item.planmemo)!}" /></td>
                        <td><a id='showbox' href='javascript:void(0)'>修改</a><a class='deletePlan'  href='javascript:void(0)' onclick='deletePlan("china",this)'>删除</a><a class='export' href='javascript:void(0)'>导出</a></td>
                    </tr>
                    </#list>
                </tbody>
            </table>
        </div>
        <div id="xToolbar"></div>
        <div id="tableContainer">
            <table style="margin-top:0px;margin-left:0px;width:100%;">
                <div class="fltil">
                    <span>投保声明\重要提示\主要责任免除\特别提醒：</span>
                </div>
                <tr>
                    <textarea id="statement" name="statement">${(product.statement)!}</textarea>
                    <script type="text/javascript">
                        try{
                            var editor=CKEDITOR.replace("statement" ,
                                    {	skin:"moono",
                                        height:300,
                                        resizeType:0
                                    });
                        }catch(e){
                        }
                    </script>
                    </td>
                </tr>
                <tr>
                    <!--td align="right">适用条款：</td-->
                    <td id="upload">适用条款：<br/><input name="upload" type="file" />&nbsp;&nbsp;&nbsp;只允许上传pdf的文件</td>
                </tr>
                <tr style="display:none;">
                    <!--td></td--><td><a id="addtk" class="infoadd" href="javascript:void(0)">增加条款</a></td>
                </tr>
                <tr>
                    <td id="uploadimg">产品封面图片：<br/><input name="uploadimg" type="file" />&nbsp;&nbsp;&nbsp;只允许上传jpg,png的文件</td>
                </tr>
            </table>
            <div class="btn_tj"><input type="button" value="保 存" onclick="doSubmit();" style="margin-right:10px;"><input type="button" value="取 消" ></div>
        </div>
    </form>

<!--/div-->
<!--box -->
<div id="cp_box" class="cp_box">
    <iframe id="ifh" frameborder="0" ></iframe>
    <div class="box_cp">
        <div class="dbox_t"><div class="dlb"></div><div class="dmb"></div><div class="drb"></div></div>
        <table width="100%" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="b_til" class="b_til cp_til">
                        <span>添加保险计划</span>
                        <a id="clos_btn" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <form id="resinfo" name="resinfo" action="" method="get">
                        <input type="hidden" id="planid" value="" />
                        <div class="tip">
                            <div class="info"></div>
                            <div class="info_t">添加成功，您可以 继续添加 或 <a name="clos_btn" id="clos_btn" href="javascript:void(0)">离开</a>!</div>
                        </div>
                        <table class="cp_fk">
                            <tr>
                                <td align="right" width="110">计划操作：</td>
                                <td><a class="import" href="javascript:void(0)">导入计划</a></td>
                            </tr>
                            <tr>
                                <td align="right">计划编号：</td>
                                <td><input id="jhid" type="text" /></td>
                            </tr>
                            <tr>
                                <td align="right" width="110">渠道对接代码：</td>
                                <td><input id="jhsubcode" type="text" /></td>
                            </tr>
                            <tr>
                                <td align="right">计划名称：</td>
                                <td><input id="jhnm" size="35" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td align="right">计划英文名称：</td>
                                <td><input id="jhenm" size="35" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td align="right">状态：</td>
                                <td><select id="jhzt" name=""><option value=1>有效</option><option value=0>无效</option></select></td>
                            </tr>
                            <tr>
                                <td align="right">顺序：</td>
                                <td><input id="jhindex" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td >允许投保最小年龄：</td>
                                <td><input id="sage" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td >允许投保最大年龄：</td>
                                <td><input id="mage" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td >允许投保最小天数：</td>
                                <td><input id="sday" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td >允许投保最大天数：</td>
                                <td><input id="mday" name="" type="text" /></td>
                            </tr>
                            <tr>
                                <td align="right">是否包含扩展代码${(item.isExtendPlan)!}：</td>
                                <td><input id="isExtendPlan1" name="isExtendPlan" type="radio" val="1" value="1">是
                                    <input id="isExtendPlan2" name="isExtendPlan" type="radio" val="0" value="0">否
                                </td>
                            </tr>
                            <tr id="extendPlanNo" style="display: none">
                                <td style="text-align: right">扩展代码：</td>
                                <td><input id="ExtendPlanCode" name="" type="text" value="" /><span style="color: red">&nbsp;*&nbsp;扩展代码为在原计划编号和名称后所添加后缀，例如：xxx option，“option”即为拓展代码，注意区分大小写</span></td>
                            </tr>
                            <tr>
                                <td valign="top" align="right"><span class="fl_dq">保障项目：</span></td>
                                <td valign="top">
                                    <table class="bx_proj" cellspacing="1">
                                        <thead>
                                        <tr>
                                            <th rowspan="2" width="19">#</th>
                                            <th rowspan="2">险种编号</th>
                                            <th rowspan="2" width="355">保障项目</th>
                                            <th colspan="2" >保险金额(单位：元)</th>
                                            <th rowspan="2" width="55"><div style="width:60px;"><a id="delproall" href="javascript:void(0)">删除全部</a></div></th>
                                        </tr>
                                        <tr>
                                            <th>成人</th>
                                            <th>儿童</th>
                                        </tr>
                                        </thead>
                                        <tbody id="proj"></tbody>
                                        <tr class="addproj">
                                            <td align="center">#</td>
                                            <td><input id="pronumber" class="" type="text" /></td>
                                            <td width="355"><input id="proname" class="xmnm" type="text" /></td>
                                            <td><input id="pro_adu" class="xmfy" type="text" /></td>
                                            <td><input id="pro_chi" class="xmfy" type="text" onClick="this.value = '';" onBlur="if (this.value == '') this.value = pro_adu.value;" /></td>
                                            <td style="padding-left:10px;"><img id="addproj" src="/img/add2.gif"/></td>
                                            <script language="javascript">
                                                var o_5 = document.getElementById("pro_adu");
                                                var o_6 = document.getElementById("pro_chi");
                                                o_5.onkeyup = function(){o_6.value = o_5.value}
                                            </script>
                                        </tr>
                                    </table>

                                </td>
                            </tr>
                            <tr>
                                <td valign="top" align="right"><span class="fl_dq">计划费率：</span></td>
                                <td valign="top">
                                    <table class="bx_rate" cellspacing="1">
                                        <thead>
                                        <tr>
                                            <th rowspan="2" width="9">#</th>
                                            <th rowspan="2">保险期间 (单位：天)</th>
                                            <th colspan="2">保险费率 (单位：元)</th>
                                            <th rowspan="2"><a id="delrate" href="javascript:void(0)">删除全部</a></th>
                                        </tr>
                                        <tr>
                                            <th>成人</th>
                                            <th>儿童</th>
                                        </tr>
                                        </thead>
                                        <tbody id="rate"></tbody>
                                        <tr class="add_rate">
                                            <td>#</td>
                                            <td><input id="ksday" type="text" /> - <input id="jsday" type="text" /></td>
                                            <td><input id="ratey" type="text" /> / <input id="ratet" type="text" /> 天</td>
                                            <td><input id="ratey2" type="text" onClick="this.value = '';" /> / <input id="ratet2" type="text" onClick="this.value = '';" onBlur="if (this.value == '') this.value = ratet.value;"/> 天</td>
                                            <td><img id="addrate" src="/img/add2.gif"/></td>
                                            <script language="javascript">
                                                var fee1 = document.getElementById("ratey");
                                                var day1 = document.getElementById("ratet");
                                                var fee2 = document.getElementById("ratey2");
                                                var day2 = document.getElementById("ratet2");

                                                fee1.onblur = function(){
                                                    if (fee1.value.split(".")[0]!="" && fee1.value.split(".")[1]!="00"){
                                                        fee1.value = fee1.value += ".00";
                                                    }
                                                    else{
                                                        fee1.value = fee1.value;
                                                    }fee2.value = fee1.value;}

                                                fee2.onblur = function(){
                                                    if (fee2.value == ""){
                                                        fee2.value = fee1.value;
                                                    }else if(fee2.value!= "" && fee2.value.split(".")[1]!="00"){
                                                        fee2.value = fee2.value += ".00";
                                                    }
                                                    else{
                                                        fee2.value = fee2.value;
                                                    }}

                                                day1.onkeyup = function(){day2.value = day1.value;}

                                            </script>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td valign="top" align="right"><span class="fl_dq">费率公式：</span></td>
                                <td valign="top"><textarea id="jsFormula" style="width: 584px; height: 129px;resize:none;"></textarea></td>
                            </tr>
                            <tbody id="addbz">
                            <tr>
                                <td valign="top" align="right"><span class="fl_dq">备注一：</span></td>
                                <td valign="top">
                                    <div class="bx_inp_w">
                                        <div class="bx_inp">
                                            <span>中文</span>
                                            <div><textarea id="bxzw_1" ></textarea></div>
                                            <input type="checkbox" name="checkboxz_1"/>加粗</br><select id="selectz_1"><option value="6">6号字</option><option value="7">7号字</option><option value="8">8号字</option></select>
                                        </div>
                                        <div class="bx_inp">
                                            <span>英文</span>
                                            <div><textarea id="bxyw_1" ></textarea></div>
                                            <input type="checkbox" name="checkboxy_1"/>加粗</br><select id="selecty_1"><option value="6">6号字</option><option value="7">7号字</option><option value="8">8号字</option></select>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                            <tr>
                                <td></td>
                                <td><a id="lovadd" href="javascript:void(0)" hidefocus="true" class="infoadd">添加备注</a></td>
                            </tr>
                        </table>
                        <div class="tb_btn"><a id="add_btn" href="javascript:void(0)"><img src="/img/add.gif"/></a><a id="clos_btn" href="javascript:void(0)"><img src="/img/clo.gif"/></a></div>
                    </form>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="dbox_bom"><div class="dbl"></div><div class="dmb"></div><div class="dbr"></div></div>
    </div>
</div>

<!--box end-->
<!--jhrate -->
<div id="jhrate" class="jhrate" onmousedown=MDown(jhrate)>
    <div class="tip_til">
        <span id="jh_name"></span>
        <a id="clos_rat" href="javascript:void(0)"><b>×</b></a>
    </div>
    <div id="rateinfo" class="wrat">你还没有为该计划添加费率！</div>
    <table id="fkrate" class="bx_rate bx_rate2">
        <thead>
        <tr>
            <th rowspan="2">保险期间 (单位：天)</th>
            <th colspan="2">保险费率 (单位：元)</th>
        </tr>
        <tr>
            <th>成人</th>
            <th>儿童</th>
        </tr>
        <thead>
        <tbody id="rate2">
        </tbody>
    </table>
</div>
<!--jhrate end-->

<!--jhrate -->
<div id="projct" class="jhrate" onmousedown=MDown(projct)>
    <div class="tip_til">
        <span id="pro_name"></span>
        <a id="clos_pro" href="javascript:void(0)"><b>×</b></a>
    </div>
    <div id="proinfo" class="wrat">你还没有为该计划添加保障项目！</div>
    <table id="fkpro" class="bx_rate bx_rate2" cellspacing="1">
        <tr>
            <th rowspan="2" width="5%">序号</th>
            <th rowspan="2" width="10%">险种编号</th>
            <th rowspan="2" width="55%">保障项目</th>
            <th colspan="2" width="20%">保险费率 (单位：元)</th>
        </tr>
        <tr>
            <th>成人</th>
            <th>儿童</th>
        </tr>
        <tbody id="projadd">
        </tbody>
    </table>
</div>
<!--jhrate end-->

<!--jhrate -->
<div id="probz" class="jhrate" onmousedown=MDown(probz)>
    <div class="tip_til">
        <span id="bz_name"></span>
        <a id="clos_bz" href="javascript:void(0)"><b>×</b></a>
    </div>
    <div id="bzinfo" class="wrat">你还没有为该计划添加备注！</div>
    <div id="bz_detail" class="bz_detail">
    </div>
</div>
<!--jhrate end-->

<!--jsrate -->
<div id="jsrate" class="jhrate" onmousedown=MDown(jsrate)>
    <div class="tip_til">
        <span id="jsrate_name"></span>
        <a id="clos_jsrate" href="javascript:void(0)"><b>×</b></a>
    </div>
    <div id="jsinfo" class="wrat">你还没有为该计划添加费率公式！</div>
    <div id="js_detail" class="js_detail">
    </div>
</div>
<!--jsrate end-->

<!-- tipbox -->
<div id="msg" class="tb_box">
    <iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="msg_ban" class="b_til">
                        <span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                        <div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<iframe id="excelIframe" style="display:none;" src="/china/json.html" name="excelIframe"></iframe>
<!-- tipbox end-->
<#include "../common/foot.html">
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/insure_cp.js?v=02"></script>
</html>

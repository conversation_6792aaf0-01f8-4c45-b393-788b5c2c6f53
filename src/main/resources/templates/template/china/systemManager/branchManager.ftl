<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Pragma" content="no-cache"/>   
<meta http-equiv="Cache-Control" content="no-cache"/>   
<meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js"></script>
<script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
<script type="text/javascript">
function pageaction(i){
	document.conditionform.action = "/china/systemManager/listBranch?pagestart="+i;
	document.conditionform.submit();
}
  //跳转
  function goPage(){
      var num=document.all["goNumber"].value.replace(/\s/g,"");
      var lastpage="${(page.lastPage)!}";
      if(num==""){
         alert('请输入要跳转的页数');
         return;
      }
      if(parseInt(num)>parseInt(lastpage)){
         alert('超出了最大页数，最大页数为'+lastpage);
         return;
      }
      pageaction(num);
  }	
</script>
</head>

<body>
<#include "../common/top.ftl">
    <div class="content">
		<div id="contentbar" class="contentbar"><span>分公司管理</span><input type="button" value="添加" class="dc_data" onclick="{location.href='/china/systemManager/add.action'}"/></div>
		<form name="conditionform" action="" method="post"  ></form>
		<div id="tableContainer" class="tableContainer"> 
			<table class="tbli" >
                <tr class="tbhead">
                    <th class="bh" width="45">编号</th>
                    <th width="90">分公司名称</th>
                    <th>分公司代码</th>
                    <th>分公司地址</th>
                    <th>联系方式</th>
                    <th>联系人</th>
                    <th>操作</th>
                 </tr>
                    <tbody id="trbs">
                    <#list list! as item>
                   <tr>
                    	<td class='tbh'>${item?counter}</td>
                       	<td>${(item.agencyName)!}</td>
                       	<td>${(item.IATACntryCd)!}</td>
                        <td>${(item.address)!}</td>
                        <td>${(item.telphone)!}</td>
                        <td>${(item.person)!}</td>
                        <td><a id='showbox' href='/china/systemManager/edit?partnerid=${(item.agencyID)!}'>修改</a></td>
                    </tr>
					</#list>
                    </tbody>
            </table>
	    </div>
        <div class="fy_box">
                    <div class="page_fy">
                           <#if ((page.pageStart)! > 1)>
                         	 <a class="page_pre" onClick="pageaction('${(page.priPage)!}')">上一页</a>
						   </#if>
                           <#if ((page.pageStart)! < (page.lastPage)!)>
                            <a class="page_nex" onClick="pageaction('${(page.nextPage)!}')">下一页</a>
                           </#if>
                           <script>                               
                                  var k=0;  //临时记录数
                                  var largetcount=10;    //每页显示多少个      10
                                  var cp=${(page.pageStart)!}; //当前页         98
                                  var pagecount=${(page.lastPage)!}; //页面总数 100
                                  var selfcnt=Math.floor(largetcount/2);     //5
                                  var lcnt=Math.floor(pagecount/2);          //50
                                  var startp=cp-selfcnt;                     //93
                                  if(pagecount-cp<selfcnt){ 
                                      startp=startp-(selfcnt-(pagecount-cp));
                                  }
                                  for(var i=startp;i<=cp;i++){
                                     if(i<1||i>cp||i>pagecount)
                                       continue;
                                     k=k+1;
                                     if(i==cp) 
                                        document.write("<a class='page_dq' href='#'>"+i+"</a>");              
                                     else
                                        document.write("<a onClick=pageaction('"+i+"')>"+i+"</a>")                                    
                                  }
                                  if(cp<pagecount && k<largetcount){
                                     for(var i=cp+1;i<=pagecount;i++){
                                        if(k>largetcount)
                                           break;
                                        k=k+1;
                                        document.write("<a onClick=pageaction('"+i+"')>"+i+"</a>");
                                      }
                                  }
                               
                           </script>    
                        <div>
                            第 <input name="goNumber" class="tex" type="text" /> 页 <input type="submit" class="submit" value="跳转" onClick="goPage()">
                        </div>
                    </div>
        </div>
    </div>

<div id="msg" class="tb_box">
	<iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
       		<tr>
            	<td class="btm"></td>
                <td class="box_ct" valign="top">
                	<div id="msg_ban" class="b_til">
                    	<span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                    	<div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr> 
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end--> 
<#include "../common/foot.html">
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/mag_cp.js?v=01"></script>
</html>

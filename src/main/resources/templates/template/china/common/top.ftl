<style>
    .changeUser {
        display: inline-block;
    }

    .changeUser:hover .changeUserList {
        display: block;
    }

    .changeUserList {
        display: none;
        position: absolute;
        background-color: #f5f5f5;
        right: 0;
        padding: 10px 20px;
        border: 1px #d2d2d2 solid;
        line-height: 24px;
        list-style: none
    }

    .changeUserList li:hover {
        color: #e99411;
    }
</style>
<div class="pageHeader">
    <img class="l" src="/img/zurich_logo.png" style="height: 50px;"/>
</div>
<div class="dl_info">欢迎您
    <span class="user">
        <span>${securityUser.agencyName!} | ${securityUser.loginName!}</span>
    </span>
    <#list securityUser.relatedUsersList!>
        <span>|</span>
        <div class="changeUser">
            <a href="javascript:void(0)">切换分公司</a>
            <ul class="changeUserList">
                <#items as item>
                    <li><a href="/admin/switchBranchAgency?uid=${item.uid!}">${item.loginname!}</a></li>
                </#items>
            </ul>
        </div>
    </#list>
    <span class="fgx">|</span><a href="/logout">安全退出</a><span class="zwf"></span>
</div>
<div id="leftmenu">
    <ul>
        <#if securityUser.uid! == 1>
            <li>
                <a href="#">系统管理-></a>
                <ul>
                    <li><a href="/china/systemManager/listProduct">产品管理</a></li>
                    <li><a href="/china/systemManager/listAgency">Agency Manager</a></li>
                    <li><a href="/china/systemManager/listPartner">Partner Manager</a></li>
                    <li><a href="/china/client/index">Branch Manager</a></li>
                    <li><a href="/china/department/index">部门管理</a></li>
                    <li><a href="/china/query/policyStats">保单统计</a></li>
                    <li><a href="/china/sendurl/list">对接管理</a></li>
                    <li><a href="/china/blacklistconfig/editConfig">黑名单配置项</a></li>
                    <li><a href="/china/paymentConfig/paymentQuery">支付方式配置项</a></li>
                    <li><a href="/china/systemManager/exportvm">保监数据导出</a></li>
                </ul>
            </li>
        <#else>
            <#list securityUser.mainMenulist as mainmenu>
                <#if mainmenu.menuName! == "查询服务">
                    <li>
                        <a href="#">查询服务-></a>
                        <ul>
                            <li><a href="/china/query/query400">保单查询</a></li>
                        </ul>
                    </li>
                </#if>
            </#list>
        </#if>
        <li>
            <a href="editpass">修改密码</a>
        </li>
        <div class="clear"></div>
    </ul>
</div>

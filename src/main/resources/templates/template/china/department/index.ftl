<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Pragma" content="no-cache"/>
<meta http-equiv="Cache-Control" content="no-cache"/>
<meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<link href="/css/department/list_department.css?v=${JS_AND_CSS_VER}" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/china_department/live_excel.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/china_department/list_splice.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/china_department/list_department.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript">
var id = [];
var name = [];
</script>
</head>
<body>
<div id="img_closed">
<img src="/img/department/sending.gif">
</div>
<#include "../common/top.ftl">
<div class="content">
<ul id="hide_power">
<#list actionlist! as item>
<li><span class="all_id">${item.actionID!}</span><span class="all_name">${item.actionName!}</span></li>
</#list>
</ul>
<table style="width:50%;height:50%;" border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td align="left" valign="top" id="left_scroll_main" width="155">
     <div id="left_scroll_top">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="department_edit" type="button" value="部 门 编 辑" style="width:100px; height:30px;" /></div>
     <div class="department_serch"><input type="" value="部门查找" style="width:145px;border:1px solid #ccc;height:20px;background-color:#E0E8FC;line-height:20px;text-align:left"/></div>
     <div id="left_scroll">
      <ul id="list_companey">
      </ul>
     </div>
    </td>
    <td id="splice" style="width:10px;cursor:e-resize;background-color:#EFEFEF;"   align="center"   valign="middle"
            onmousedown="Resize2_mousedown(event,this);"   onmouseup="Resize2_mouseup(event,this);"   onmousemove="Resize2_mousemove(event,this);"><font style="size:3px;background-color:#eeeeee;cursor:pointer;"   onmousedown="Resize2_setDefault(event,this);"><</font></td>
    <td id="right" valign="top" align="left">
         <div class="tab">
            <div class="tab_menu">
              <table>
                <tr>
                  <ul>
                    <td width="80">
                      <li class="selected">部 门 用 户</li>
                    </td>
                    <td width="80">
                      <li>部 门 权 限</li>
                    </td>
                    <td widt="80">
                      <li>部 门 信 息</li>
                    </td>
                  </ul>
                </tr>
              </table>
            </div>
            <div class="tab_box">
               <div>
                 <div id="user" current_id_see="">
                   <table cellpadding="0" cellspacing="0" border="0" width="780" style="table-layout:auto;">
                     <tr id="power" align="center">
                       <td width="30" align="left">序号</td><td width="80" align="left">用户名</td><td width="80" align="left">姓名</td><td width="100" align="left">联系方式</td><td width="90" align="left">工号</td><td width="50" align="left">权限</td><td width="50">绑定</td><td width="100">绑定MAC地址</td><td width="100">subCode</td><td width="100">是否管理员</td><td width="100">是否启用</td>
                     </tr>
                     <tbody id="user_content">

					 </tbody>
                    <!--  <tr>
                       <td colspan="9" valign="top">
                         <div id="user_content">
                         <table cellpadding="0" cellspacing="0" width="100%">
                         </table>
                         </div>
                       </td>
                     </tr> -->
                   </table>
                 </div>
               </div>
               <div class="hide">
                 <table width="780"><tr><td id="select_bottom">部门功能权限&nbsp;&nbsp;&nbsp;&nbsp;<span id="select_power"></span></td></tr>
                 <tr height="360"><td valign="top">
                 <table id="main_power"></table>
                 </td></tr>
                 <tr><td id="power_bottom">
                 </td></tr></table>
               </div>
               <div class="hide">
                 <table cellpadding="0" cellspacing="0" border="0" id="department_info">
                   <tr><td width="120" height="50" align="right"><label>部门编号：</label></td><td width="420"><input id="c_name" type="text" name="c_name" readonly="readonly" /></td></tr>
                   <tr><td width="120" height="50" align="right"><label>部门名称：</label></td><td><input id="c_add" type="text" name="c_add" readonly="readonly" /></td></tr>
                   <tr><td width="120" height="50" align="right"><label>部门简称：</label></td><td><input id="c_phone" type="text" name="c_phone" readonly="readonly" /></td></tr>
                   <tr><td width="120" height="50" align="right"><label>销售网点：</label></td><td><input id="c_sell" type="text" name="c_sell" readonly="readonly" /></td></tr>
                 </table>
               </div>
            </div>
         </div>
    </td>
  </tr>
</table>

<div id="user_opacity"></div>
<div id="user_opacity_edit"></div>
<div id="user_alert">
<div id="user_alert_title">消息框<span style="display:none;"></span></div>
<div id="user_alert_body">
<table width="290">
  <tr><td colspan="2" align="center" height="30" id="message"></td></tr>
  <tr><td align="center" valign="bottom"><input type="button" id="user_sure" value="确定" /></td><td align="center" valign="bottom"><input id="user_cancel" type="button" value="取消" /></td></tr>
</table>
</div>
</div>
<div id="user_power">
<div id="user_power_title"><table><tr><td width="570">用户权限信息</td><td width="20"><img id="close_power_brows" src="/img/department/close.gif"></img></td></tr></table></div>
<div id="user_power_body">
<div id="user_power_1"><table cellpadding="0" cellspacing="0"><tr valign="bottom"><td width ="500" height="25">&nbsp;<span id="current_user"></span><span id="current_name"></span></td><td aling="rigth"><span id="current_user_select"></span></td></tr></table></div>
<div id="user_power_2">
<table id="user_power_list" border="0"></table>
</div>
<div id="user_power_center"></div>
<div id="user_power_4">
<table id="user_power_data_list" border="0"></table>
</div>
<div id="user_power_3"><input type="button" style="width:60px;" id="user_power_cancel" value="关&nbsp;&nbsp;闭" /></span></div>
</div>
</div>

<div id="user_power_edit">
<div id="user_power_title_edit"><table><tr><td width="570">用户权限设置</td><td width="20"><img id="close_power_edit" src="/img/department/close.gif"></img></td></tr></table></div>
<div id="user_power_body_edit">
<div id="user_power_1_edit"><table cellpadding="0" cellspacing="0"><tr valign="bottom"><td width ="360" height="25">&nbsp;<span id="current_user_edit"></span>&nbsp;&nbsp;<span id="current_name_edit"></span></td><td width="100"><span id="current_user_select_all">全选</span>&nbsp;&nbsp;<span id="current_user_select_cancel">取消全部</span></td><td><span id="current_user_select_edit"></span></td></tr></table></div>
<div id="user_power_2_edit">
<table id="user_power_list_edit" border="0"></table>
</div>
    <div style="height: 20px;line-height: 20px;padding-right: 35px;_padding-right: 15px;border-bottom: 1px solid #ccc;">
        <span style="margin-left:5px">拥有以下部门的权限</span>
        <span id="edit_power_center" style="border-bottom: none ;margin-left: 320px;padding-right:0px;" ></span>
    </div>
<div id="user_power_4_edit">
<table id="user_power_data_list_edit" border="0"></table>
</div>
<div id="user_power_3_edit"><input type="button" style="width:60px;" id="user_power_sure_edit" value="确&nbsp;&nbsp;定" />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="button" style="width:60px;" id="user_power_cancel_edit" value="取&nbsp;&nbsp;消" /></span></div>
</div>
</div>

<div id="department_edit_box">
  <div id="department_edit_top"><table><tr><td width="970"><span style="margin-left:20px;">部 门 编 辑 器</span></td><td width="20"><img id="close_power" src="/img/department/close.gif"></img></td></tr></table></div>
  <div id="department_edit_body">
     <table style="width:100%;height:100%;"  border="0"  cellspacing="0" cellpadding="0">
        <tr>
           <td style="width:200px;" align="left" valign="top" id="department_edit_left_scroll_main">
              <div style="margin-top:5px;" id="function_button">&nbsp;<input type="button" value="新部门" id="add_new_department" style="width:60px;" />&nbsp;<input type="button" value="子部门" id="add_sub_department" style="width:60px;" />&nbsp;<input type="button" id="delete_new_department" value="删除" style="width:60px;" /></div>
              <div id="department_serch_edit"><input type="text" style="width:185px; height:20px;" value="部门查找" /></div>
              <div id="department_edit_body_left" class="department_edit_body_left"></div>
            </td>
            <td id="department_edit_splice" style="width:10px;cursor:e-resize;background-color:#D7E2F9;"   align="center"   valign="middle" onmousedown="Resize2_mousedown(event,this);"   onmouseup="Resize2_mouseup(event,this);"   onmousemove="Resize2_mousemove(event,this);"><font style="size:3px;background-color:#D7E2F9;cursor:pointer;"   onmousedown="Resize2_setDefault(event,this);"><</font></td>
            <td id="right" valign="top" align="left">
            <div id="scroll_right">
               <div id="department_label"><span class="department_top_select" id="department_top_select1">部 门 信 息</span><span class="checkBasicInfo" id="department_top_select2">用 户 管 理</span><span class="checkBasicInfo" id="department_top_select3">权 限 设 置</span></div>
               <div id="department_edit_body_right">
                  <table cellpadding="0" cellspacing="0">
                     <tr>
                        <td width="790">
                           <div id="department_input">
                               <table cellpadding="0" cellspacing="0" border="0" style="margin-left:140px;">
                                 <tr><td align="center" height="40" width="90">部门编号</td><td width="400"><input id="department_number" disabled="disabled" type="text" style="width:390px; height:25px;" /></td></tr>
                                 <tr><td align="center" height="40" width="90">部门名称</td><td width="400"><input id="department_id" disabled="disabled" currentid="" type="text" style="width:390px; height:25px;" /></td></tr>
                                 <tr><td align="center" height="40" width="90">部门简称</td><td width="400"><input id="department_sname" disabled="disabled" type="text" style="width:390px; height:25px;" /></td></tr>
                                 <tr><td align="center" height="40" width="90">销售网点</td><td width="400"><input id="department_sell" disabled="disabled" type="text" style="width:390px; height:25px;" /></td></tr>
                              </table>
                           </div>
                        </td>
                     </tr>
                  </table>
                  </div>
                  <div id="right_main_user" style="display:none;">
                     <table cellpadding="0" cellspacing="0" border="0" id="user_scroll_edit"><thead style="font-size:12px; color:#069;" align="center"><th width="50" height="20">ID</th><th width="100" height="20">用户名</th><th width="50">密码</th><th width="100">真实姓名</th><th width="100">联系方式</th><th width="60">工号</th><th width="50">权限</th><th width="70">是否绑定</th><th width="100">绑定地址</th><th width="200">subCode</th><th width="60">是否管理员</th><th width="60">是否启用</th><th width="50"></th></thead>
                     <tfoot><tr><td colspan="12" align="center" height="50" valign="bottom" id="add_user_button"><input type="button" disabled="disabled" value="选择用户"/></td></tr></tfoot>
                     <tbody id="right_main_user_sub"></tbody></table>
                  </div>
                  <div id="right_main_power" style="display:none">
                     <div>
                        <table><tr><td width="100"><b>功能权限设置</b></td><td width="60"><a id="edit_all_select">全选</a></td><td width="100"><a id="edit_cancel_select">取消全部</a></td><td>&nbsp;&nbsp;&nbsp;<b id="power_name" style="color:red;"></b></td><td width="200"><span id="all_select_count"></span></td><td width="200"><span id="current_select_count"></span></td></tr></table>
                        <div id="power_container">
                        <fieldset>
                          <table cellpadding="0" cellspacing="0" id="manager_power"></table>
                        </fieldset>
                        </div>
                     </div>
                  </div>
                </div>
            </td>
         </tr>
      </table>
</div>
<div id="department_right_bottom"><span><input type="button" value="保 存" style="width:100px; height:25px;" id="department_button_save" /></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span><input type="button" value="取 消" style="width:100px; height:25px;" id="department_button_cancel" /></span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span><input type="button" disabled="true" value="应 用" style="width:100px; height:25px;" id="department_button_accept" /></span>
</div>
</div>
</div>
<div id = "my_alert">
<div>
<table><tr><td>
您确定取消所有操作？
</td><tr><td width="200" height="50" valing="center">
<input id="alert_yes" type="button" value="确定"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input id="alert_no" type="button" value="取消"/>
</td></tr></table>
</div>
</div>
<img class="loadImg" src="/img/department/loading.gif"/>
<div id = "del_power_select"></div>
<div id="add_user_window">
<div id="add_user_window_top"><span id="add_user_cound"></span><input type="text" id="add_user_search" value="用户查找"/></div>
<div id="add_user_window_content">
  <table>
    <thead><th width="80">选择</th><th width="80">用户ID</th><th width="100">用户名</th><th width="100">真实姓名</th><th width="120">联系方式</th><th width="100">工号</th><th width="70">是否管理员</th><th width="70">是否启用</th></thead>
       <tbody id="add_user_window_table"></tbody>
  </table>
</div>
<div id="add_user_window_foot"><table><tr><td width="790" align="center"><input type="button" id="add_user_sure" value="确定"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="button" id="add_user_close" value="取消"/></td></tr></table></div>
</div>
<div id="alert_opacity"></div>
<#include "../common/foot.html">
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
</html>

<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
    <script language="javascript" src="/js/jquery.js"></script>
    <style type="text/css">
        .tbxx{
            padding: 0px;
        }
        .tbxx tr td{
            text-align: left;
            /*padding-left: 30px;*/
        }
        .tbxx tr td:first-child{
            padding-left: 10px;
        }
        .tbxx tr td:second-child{
            padding-left: 60px;
        }
        .tbxx tr:nth-child(2n+1){
            background: rgb(241, 244, 249);
        }
        .tbxx table tr:hover{
            background-color: cornsilk;
        }
        .json{
            overflow: hidden;
            width: 300px;
            cursor: pointer;
            white-space:nowrap;
            -ms-text-overflow: ellipsis;
            text-overflow: ellipsis;
        }
        .hidden_td{
            display: none;
        }
        .tbxx .col td{
            text-align: center;
        }
        .gif_close{
            width: 14px;
            height: 14px;
            background-image: url(/img/close.gif);
            background-position: center;
            background-repeat: no-repeat;
        }
        .info_edit_body input{
            width: 236px;
            height: 24px;
        }
    </style>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar">
        <span>系统管理 > 支付方式管理</span>
        <input type="button" value="添加" class="dc_data" />
    </div>
    <div id="ct_main" class="ct_main">
        <div id="main_r" class="main_r">
            <form name="formname" id="config_form" method="post" action="agencypayment.action">
                <div class="tbxx">
                    <table style="width: 100%;table-layout: fixed">
                        <tr class="tbhead" style="text-align: left">
                            <th style="text-align: left">支付名称</th>
                            <th style="text-align: left">支付CODE</th>
                            <th>JSON模板</th>
                            <th style="text-align: center">最后维护时间</th>
                            <th>最后维护人</th>
                            <th>操作</th>
                        </tr>
                        <#list paymentTypes! as item>
                            <tr class="col">
                                <td class="hidden_td">
                                    <input  value='${item.jsontemplate!}' class="content_json" />
                                </td>
                                <td style="text-align: left;text-indent: 10px;">${item.name!}</td>
                                <td style="text-align: left;text-indent: 10px;">${item.code!}</td>
                                <td class="json" style="text-align: left" title="点击查看全部内容">${item.jsontemplate!}</td>
                                <td>${item.editTime!}</td>
                                <td>${item.operator!}</td>
                                <td>
                                    <input class="editpaymet" type="hidden"  value='${item.id!}|${item.name!}|${item.code!}|${item.jsontemplate!}' />
                                    <span class="edit" style="cursor: pointer;" onclick="editpaymet(this)">修改</span>
                                    <span class="delete" style="cursor: pointer;" onclick="delpaymet(${item.id!})">删除</span>
                                </td>
                            </tr>
                        </#list>
                    </table>
                </div class="tbxx">
            </form>
        </div>
    </div>
</div>
<div class="info_edit_box" id="info_edit_box"  style="width: 550px">
    <div class="info_edit_head"><span class="info_edit_title">修改支付方式</span>
        <div class="gif_close info_edit_close" id="uInfo_close"></div>
    </div>
    <div class="info_edit_body">
        <form class="uInfoForm" id="uInfoForm" action="" method="post" autocomplete="off">
            <input type="hidden" id="payment_id" name="id" />
            <table>
                <tr>
                    <td style="text-align:right;width:165px">支付方式：</td>
                    <td>
                        <input class="payment_name" id="payment_name" name="name" />
                    </td>
                </tr>
                <tr>
                    <td align="right">code：</td>
                    <td><input type="text" id="payment_code" name="code" />
                    </td>
                </tr>
                <tr>
                    <td align="right">JSON模板：</td>
                    <td>
                        <textarea id="payment_JSON" name="jsontemplate" style="margin: 0px; width: 238px; height: 101px;"></textarea>
                    </td>
                </tr>
            </table>
        </form>
    </div>
    <div class="info_edit_foot">
        <input type="button" value="保 存" style="width:100px; height:25px;" id="uInfo_save"/>
        <input type="button" value="取 消" style="width:100px; height:25px;" id="uInfo_cancel"/>
    </div>
</div>
<div class="info_edit_box user_edit_3" style=""><!--jsonTemplate内容-->
    <div class="info_edit_head">
        <span class="info_edit_title">jsonTemplate内容</span><div class="gif_close info_edit_close"></div>
        <input type="hidden" class="edithidden" />
    </div>
    <div class="info_edit_body" style="word-wrap:break-word; overflow:hidden;">
    </div>
    <div class="info_edit_foot">
        <input type="button" style="margin-top:10px;" value="关闭" class="user_edit_cancel">
    </div>
</div>
<#include "../common/foot.html">
<!-- tipbox end-->
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
<script type="text/javascript">
    function editpaymet(param){
        var payment=$(param).closest("td").find(".editpaymet").val().split("|");
        $("#payment_id").val(payment[0]);
        $("#payment_name").val(payment[1]);
        $("#payment_code").val(payment[2]);
        var result = JSON.stringify(JSON.parse(payment[3]), null, 4);
        $("#payment_JSON").val(result);
        scren_point($("#info_edit_box"));
    }

    function delpaymet(id){
        jQuery.ajax({
            url:"delPaymentType",
            type:"post",
            dataType:"json",
            data:"id="+id,
            success:function(data){
                if(data.result == 1){
                    window.location.reload();
                }else{
                    alert("删除失败："+data.message);
                }
            }
        })
    }

    $("#info_edit_box").on("click","#uInfo_cancel,#uInfo_close",function(){
        $("#info_edit_box .info_edit_title").text("修改支付方式");
        hidePopup($("#info_edit_box,#opa"));
        $("#payment_id,#payment_name,#payment_code,#payment_JSON").val("");
    })


    $(".col").on("click",".json",function(){
        var json = $(this).closest("tr").find(".content_json").val();
        var result = JSON.stringify(JSON.parse(json), null, 4);
        $(".user_edit_3 .info_edit_body").html(result);
        scren_point($(".user_edit_3"));
    })

    $(".user_edit_3 .user_edit_cancel,.user_edit_3 .info_edit_close").on("click",function(){
        hidePopup($(".user_edit_3,#opa"));
        $(".user_edit_3 .info_edit_body").html("");
    })

    $("#uInfo_save").on("click",function(){
        jQuery.ajax({
            url:"savePaymentType",
            type:"post",
            dataType:"json",
            data:$("#uInfoForm").serialize(),
            success:function(data){
                if(data.result == 1){
                    alert("保存成功！");
                    window.location.reload();
                }else{
                    alert("保存失败："+data.message);
                }
            }
        })
    })

    $(".dc_data").on("click",function(){
        $("#info_edit_box .info_edit_title").text("新增支付方式");
        scren_point($("#info_edit_box"));
    })
</script>
</html>

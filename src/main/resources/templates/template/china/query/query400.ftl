<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Pragma" content="no-cache"/>   
<meta http-equiv="Cache-Control" content="no-cache"/>   
<meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js"></script>
<script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
<script type="text/javascript">
function pageaction(i){
	document.conditionform.action = "/china/query/query400?pagestart="+i;
	document.conditionform.submit();
}
  //跳转
  function goPage(){
      var num=document.all["goNumber"].value.replace(/\s/g,"");
      var lastpage="${(page.lastPage)!}";
      if(num==""){
         alert('请输入要跳转的页数');
         return;
      }
      if(parseInt(num)>parseInt(lastpage)){
         alert('超出了最大页数，最大页数为'+lastpage);
         return;
      }
      pageaction(num);
  }	
	function doSubmit() 
	{
   		document.conditionform.action="/china/query/query400";
   		document.conditionform.submit();
	}	
	function doExport() 
	{
   		document.conditionform.action="exportTongji";
   		document.conditionform.submit();
	}	

	function selectAgency() {
  		document.conditionform.action="query400";
   		document.conditionform.submit();
	}
</script>
</head>

<body>
<#include "../common/top.ftl">
    <div class="content">
		<div id="contentbar" class="contentbar"><span>投保查询 > 按保单查询</span></div>
		<form name="conditionform" action="" method="post"  >	
		<input name="paginalcount" type="hidden" value="20" />
        <div id="tbxx" class="tbxx">
			<table>
                 <tr>
                     <td class="tbxxright">签单日期：</td>
                     <td width="70"><input name="issueBeginDate" id="issueBeginDate" value="${condition.issueBeginDate!}" readonly="readonly" onclick="showcalendar(event,this,false,true);" onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''" type="text" /></td>
                     <td class="tbxxright">-------到：</td>
                     <td><input name="issueEndDate" id="issueEndDate" value="${condition.issueEndDate!}" readonly="readonly" onclick="showcalendar(event,this,false,true);" onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''" type="text" /></td>
                    <td class="tbxxright">线路/团号：</td>
                     <td><input name="lineno" type="text" value="${condition.lineno!}" /></td>
                     
				 </tr>
				<tr>
					<td class="tbxxright">生效日期：</td>
                    <td width="70"><input name="effectiveBeginDate" id="effectiveBeginDate" value="${condition.effectiveBeginDate!}" readonly="readonly" onclick="showcalendar(event,this,false,true);" onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''" type="text" /></td>
                    <td class="tbxxright">-------到：</td>
                    <td><input name="effectiveEndDate" id="effectiveEndDate" value="${condition.effectiveEndDate!}" readonly="readonly" onclick="showcalendar(event,this,false,true);" onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''" type="text" /></td>
                <td class="tbxxright">凭证号码：</td>
                     <td><input name="policyID" type="text" value="${condition.policyID!}" /></td>
				</tr>
				<tr>
					<td class="tbxxright">姓名：</td>
					<td>
						<input name="insuredName" type="text" value="${condition.insuredName!}" />
					</td>
					<td class="tbxxright">证件号码：</td>
					<td><input name="insuredIdNo" type="text" value="${condition.insuredIdNo!}" /></td>

					<td class="tbxxright">保单号码：</td>
					<td><input name="policyNo" type="text" value="${condition.policyNo!}" /></td>

				</tr>
                <tr>
					<td class="tbxxright">产品类型：</td>
                    <td>
						<input type="checkbox" name="isWaats" value="1" <#if (isWaats1 == "1")>checked</#if>>标准
						<input type="checkbox" name="isWaats" value="0" <#if (isWaats2 == "0")>checked</#if>>定制
					</td>
                      <td class="tbxxright">保单状态：</td>
                      <td>
						<input type="checkbox" name="status" value="1" <#if (status1 == "1")>checked</#if>>新增
						<input type="checkbox" name="status" value="2" <#if (status2 == "2")>checked</#if>>批改
						<input type="checkbox" name="status" value="3" <#if (status3 == "3")>checked</#if>>取消
					</td>
					
					
					<td class="tbxxright"></td>
                    <td colspan="1">
					<input type="button" value="查 询" class="sbtn" onclick="doSubmit();"/>
					<input type="button" value="清 空" class="sbtn" onclick="clearall();"/>
					</td>

				</tr>
            </table>
        </div>
		</form>
		

        <div id="tableContainer" class="tableContainer">                
			<table class="tbli" >
                <tr class="tbhead">
                    <th class="bh">保单号</th>
                    <th>凭证号码</th>
                    <th>线路/团号</th>
                    <th>分公司</th>
                    <th>客户名称</th>
					<th>姓名</th>
					<th>出生日期</th>
					<th>证件类型</th>
					<th>证件号码</th>
                    <th>签单日期</th>
                    <th>修改日期</th>
                    <th>生效日期</th>
                    <th>结束日期</th>
                    <th>保险产品</th>
                    <th>保险计划</th>
                    <th>人数</th>
                    <th>保费(含适用的增值税)</th>
                    <th>目的地</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                  <tbody id="trbs">
                    <#list list! as item>
                   <tr>
                    	<td class='tbh'>${item.policyNo!}</td>
						<td>${item.policyid!}</td>
                       	<td>${item.lineno!}</td>
                        <td>${item.branchAgencyName!}</td>
                        <td>${item.agencyName!}</td>
                       	<td>${item.insuredname!}</td>
                       	<td>${item.birthday!}</td>
                       	<td>${item.insuredIdNoType!}</td>
                       	<td>${item.insuredIdNo!}</td>
                        <td>${dateUtil.format(item.inssueDate!)}</td>
                        <td>${dateUtil.format(item.lasttime!)}</td>
                        <td>${dateUtil.format(item.effectiveDate!)}</td>
                        <td>${item.chartisExprdat!}</td>
                        <td>${item.productname!}</td>
                        <td>${item.planname!}</td>
                        <td>${item.personnum!}</td>
                        <td>${item.premium!}元</td>
                        <td>${item.furthestCity!}</td>
                        <td><#if (item.status! == "1")>新增
							<#elseif (item.status! == "2")>批改
							<#elseif (item.status! == "3")>取消
							<#elseif (item.status! == "4")>待处理</#if></td>
                        <td>
						<#if (sjisUtil.hasAction("viewPolicy"))>
							<a id='showbox' href='/china/query/view?policyid=${item.policyid!}'>查看</a>
						</#if>
						<#if (item.status! == "1" || item.status! == "2")>
						   <#if (sjisUtil.hasAction("downloadPolicy"))>
								<a id='showbox' href='/china/query/downpolicy?policyid=${item.policyid!}'>下载保单</a>
								<#if (item.isGroup! == 1)>
								<a id='showbox' href='/china/query/downpolicy?policyid=${item.policyid!}&person=1'>单人凭证</a>
								</#if>
							</#if>
						</#if>
						</td>
                    </tr>
					</#list>
                    </tbody>
            </table>
		</div>
        <div class="fy_box">
			
             <div class="page_fy">
                <a>【共${(page.rowCount)!}条记录</a>
                 <#if ((page.pageStart)! > 1)>
                 <a class="page_pre" onClick="pageaction('${(page.priPage)!}')">上一页</a>
				 </#if>
                 <#if ((page.pageStart)! < (page.lastPage)!)>
                 <a class="page_nex" onClick="pageaction('${(page.nextPage)!}')">下一页</a>
				 </#if>
                 <script>                               
                 	var k=0;  //临时记录数
                    var largetcount=10;    //每页显示多少个      10
                    var cp=${(page.pageStart)!}; //当前页         98
                    var pagecount=${(page.lastPage)!}; //页面总数 100
                    var selfcnt=Math.floor(largetcount/2);     //5
                    var lcnt=Math.floor(pagecount/2);          //50
                    var startp=cp-selfcnt;                     //93
                    if(pagecount-cp<selfcnt){ 
                    	startp=startp-(selfcnt-(pagecount-cp));
                    }
                    for(var i=startp;i<=cp;i++){
                    	if(i<1||i>cp||i>pagecount)
                           continue;
                        k=k+1;
                        if(i==cp) 
                        	document.write("<a class='page_dq' href='#'>"+i+"</a>");              
                        else
                            document.write("<a onClick=pageaction('"+i+"')>"+i+"</a>")                                    
                        }
                        if(cp<pagecount && k<largetcount){
                                     for(var i=cp+1;i<=pagecount;i++){
                                        if(k>largetcount)
                                           break;
                                        k=k+1;
                                        document.write("<a onClick=pageaction('"+i+"')>"+i+"</a>");
                                      }
                                  }
                               
                 </script>    
              <div>第 <input name="goNumber" class="tex" type="text" /> 页 <input type="submit" class="submit" value="跳转" onClick="goPage()">
              </div>
            </div>
           </div>
        </div>
    </div>

<!-- tipbox -->
<div id="msg" class="tb_box">
	<iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
       		<tr>
            	<td class="btm"></td>
                <td class="box_ct" valign="top">
                	<div id="msg_ban" class="b_til">
                    	<span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                    	<div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr> 
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end-->
<#include "../common/foot.html">
</body>
<script language="javascript">
//服务器时间
//此处决定日历默认时间
var d = new Date(); 
var zyear= d.getFullYear(); 
var zmonth=d.getMonth();
var zday=1;
var cday=d.getDate();
</script>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/mag_cp.js?v=01"></script>
<script language="javascript" src="/js/beaurl_nall.js?v=01"></script>

</html>

<!DOCTYPE HTML>
<html>
<#include "../common/header.ftl">
<#assign bPath = "/CssAndImg/${(partner.template)!}">
<#assign cPath = "/CssAndImg/common">
<head>
    <meta name="viewport" content="width=device-width,initial-scale=1.0,maximum-scale=1.0,minimum-scale=1.0,user-scalable=no">
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta http-equiv="pragma" content="no-cache" />
    <title>选择支付方式</title>
    <link rel="stylesheet" href="${cPath}/css/mobileinsure.css?v=${JS_AND_CSS_VER}" type="text/css"/>
    <script language="javascript" src="/js/jquery.js"></script>
    <style type="text/css">
        .payTypeDetail,.supportedBank{display:none;}
        #qrcode,#myCanvas{
            display: none;  }
        .ismixpay{
            position: absolute;
            top: 2.7rem;
            left: 0.05rem;
            right: 0.05rem;
            padding: 0.02rem 0.02rem 0.02rem;
            background-color: #FFB408;
            z-index: 2;
        }
        .secondismixpay {
            background-color: #000;
            opacity: .8;
            left: 0;
            bottom: 0;
            overflow: hidden;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1;
        }
        .policyhead{
            height: 0.43rem;
            border-bottom: 1px solid #ddd;
            width: 100%;
            text-align: center;
        }
        .tcenter{
            text-align: center;
        }
        .payinfirm{
            position: fixed;
            bottom: 0;
            text-align: center;
            width: 100%;
            height: 0.5rem;
            padding-top: 0.1rem;
        }
        .payinfirm input{
            width: 100%;
            border: none;
            height: 100%;
            background-color: #FF9800;
            color: #ffffff;
            font: normal 0.18rem Hei, Microsoft Yahei, SimHei, sans-serif;
        }
    </style>
</head>
<body class="insureStep3">
<div class="policyhead">
    <span style="line-height: 0.43rem;font-weight: bold; font-size: 0.175rem;color: #464545">我的保单</span>
</div>
<form action="orderRedirect" method="post" id="payForm">
    <table class="policyInfo policyBasicInfo clr666" style="font-size: 17px;width: 90%;">
        <col style="width:7em;">
        <tr><td class="detailInfo" colspan="2"><div class="titleInfo titleInfoFirst" style="border: none;color: #666;margin-bottom: 0px;padding: 0.5em 0 0.5em 0;">保单信息</div></td></tr>
        <tbody class="basicInfo" style="font-size: 15px;">
        <tr><td class="tcenter">应付金额：</td><td class="clr666"><span id="policy_pe">${receivables!}&nbsp;元</span></td></tr>
        <tr><td class="tcenter">已付金额：</td><td class="clr666"><span id="policy_pi"><#if (policy.fee)??>${policy.fee!}<#else>0.00</#if></span>&nbsp;元</td></tr>
        <input name="payway" type="hidden">
        </tbody>
    </table>
    <style type="text/css">
        #payType{padding:0;height:32px;line-height:32px;}
        .payType div{height:32px;line-height:32px;float:left;padding:0 2px;margin-left:10px;margin-bottom:-1px;border-top-left-radius:3px;border-top-right-radius:3px;}
        .payType .payOn{border-bottom:4px solid #031C71;height:29px;line-height:32px;color:#031C71;}
    </style>
    <table class="policyInfo" style="margin-top:0px;width: 90%;">
        <col style="width:5.5em;">
        <tr><td colspan="2" class="detailInfo"><div class="titleInfo"  style="color: #666;border-bottom:none;padding: 0.5em 0 0.5em 0;">请选择支付方式</div></td></tr>
        <tr>
            <td>
                <table style="width: 90%;margin: 0 auto;">
                    <thead>
                    <tr>
                        <th width="20%"></th>
                        <th></th>
                        <th  width="10%"></th>
                    </tr>
                    </thead>
                    <tbody id="payment_table">
                            <tr id="alipayTr">
                                <td>
                                    <img style="width: 50%;" src="/img/cuspayImgs/icon_alipay.png">
                                </td>
                                <td>
                                    <span>支付宝支付</span>
                                </td>
                                <td>
                                    <input type="hidden" class="platformId"  value="alipay"/>
                                    <input type="hidden" class="payment_code" value="platform"/>
                                    <img class="checked_close"   style="width: 70%;margin-top: 5px;" src="/img/cuspayImgs/GroupCopy2.png">
                                    <img class="checked_on"      style="width: 70%;margin-top: 5px;display: none" src="/img/cuspayImgs/GroupCopy3.png">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img style="width: 50%;" src="/img/cuspayImgs/icon_wxpay.png">
                                </td>
                                <td>
                                    <span>微信支付</span>
                                </td>
                                <td>
                                    <input type="hidden" class="platformId"  value="wxpay"/>
                                    <input type="hidden" class="payment_code" value="platform"/>
                                    <img class="checked_close"   style="width: 70%;margin-top: 5px;" src="/img/cuspayImgs/GroupCopy2.png">
                                    <img class="checked_on"      style="width: 70%;margin-top: 5px;display: none" src="/img/cuspayImgs/GroupCopy3.png">
                                </td>
                            </tr>
                    </tbody>
                </table>
            </td>
        </tr>

    </table>
    <input type="hidden" name="payModuleId" value="${payModuleId!}" />
    <input type="hidden" name="payModule" value="${payModule!}" />
    <input type="hidden" name="paymentType" id="selected_payment" value=""/>
    <input type="hidden" name="platPayType" id="selected_platPayType" value=""/>
    <div>
        <div id="payinfirm" class="payinfirm">
            <input class="payButton allPay" type="submit" value="确认支付" />
        </div>
    </div>
    <div class="clear"></div>
</form>
<div class="ismixpay" style="display: none;">
    <div>
        <table style="width:100%;background-color: white;">
            <tr><td colspan="2">账户余额不足以支付该订单，您可以选择余额部分支付，剩余金额用其他支付方式支付。</tr>
            <tr>
                <td><input class="insuremaxpay" type="button" style="height: 40px;width: 100%;background-color: #FFB408; border: none;" value="确认"></td>
                <td><input class="coslmaxpay" type="button" style="height: 40px;width: 100%;background-color: #FFB408; border: none;" value="取消"></td>
            </tr>
        </table>
    </div>
</div>
<div class="alipayTips">
    <img src="${cPath}/imgs/mobile/longarrow.png" />
    <div class="alipayTipsBody">
        <div style="margin-bottom:0.1rem;"><i class="fa fa-hand-o-right" aria-hidden="true" style="margin-right:0.02rem;"></i>点击右上角的<span class="alipayTipsOpen"><span style="position:relative;top:-0.03rem;">…</span></span>按钮</div>
        <div class="android"><i class="fa fa-hand-o-right" aria-hidden="true" style="margin-right:0.02rem;"></i>选择<span class="alipayTipsOpen"><span class="androidImg"></span>在浏览器中打开</span></div>
        <div class="iphone"><i class="fa fa-hand-o-right" aria-hidden="true" style="margin-right:0.02rem;"></i>选择<span class="alipayTipsOpen"><span class="iphoneImg"></span>在Safari中打开</span>&nbsp;</div>
    </div>
</div>
</body>
<script type="text/javascript">
    //微信浏览器屏蔽支付宝支付
    if(/micromessenger/i.test(navigator.userAgent)){
        $("#alipayTr").remove();
    }

    $(function(){
        //默认勾选第一行的支付方式
        $("#payment_table tr:first .checked_close").css("display","none");
        $("#payment_table tr:first .checked_on").css("display","block");
        var firstPayCode = $("#payment_table tr:first .payment_code").val();
        $("#selected_payment").val(firstPayCode);
        if("platform" == firstPayCode){ //核心支付的话，填充客户选择的支付方式
            $("#selected_platPayType").val($("#payment_table tr:first .platformId").val());
        }
    })

    //选择支付方式
    $(".checked_close").on("click",function(){
        $("#payment_table tr .checked_close").css("display","block");
        $("#payment_table tr .checked_on").css("display","none");
        var paymentCode =$(this).siblings(".payment_code").val();
        $("#selected_payment").val(paymentCode);
        if("platform" == paymentCode){ //核心支付的话，填充客户选择的支付方式
            $("#selected_platPayType").val($(this).siblings(".platformId").val());
        }
        $(this).siblings(".checked_on").css("display","block");
        $(this).css("display","none");
    })
</script>
</html>
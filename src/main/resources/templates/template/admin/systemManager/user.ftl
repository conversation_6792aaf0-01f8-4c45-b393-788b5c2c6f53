<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js"></script>
<script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
<script type="text/javascript">
function doSubmit() 
{

	if(document.formname.username.value.replace(/\s/g,"")==""){
		alert("请您输入账号名称。");
		document.formname.username.focus();
		return;
}
	if(document.formname.password.value.replace(/\s/g,"")!="" && document.formname.password.value!=document.formname.confirmpassword.value){
		alert("两次输入密码不一致。");
		document.formname.newpassword.focus();
		return;
	}
   	document.formname.action="/admin/saveUser";
   	document.formname.submit();
}	
</script>
</head>

<body>
<#include "../common/top.ftl">
    <div class="content">
		<div id="contentbar" class="contentbar">
			<span>${(user.uid?? || user.uid! == 0)?string('系统管理 > 账号管理 > 新增账号','系统管理 > 账号管理 > 修改账号')}</span>
		</div>
		<#if message??>${message!}
			<#if (saveflag! != "1")>
				<a href="/admin/user?agencyID=${user.agencyID!}">继续添加</a>
			</#if>
		</#if>
 		<form name="formname" method="post" action="/admin/saveUser">
			<input id="saveflag" name="saveflag" type="hidden" value="${saveflag!}" />
			<input id="userid" name="userid" type="hidden" value="${user.uid!}" />
			<div class="tbxx upinfo">
                <table>
					<tr>
						<td class="tbxxright">公司名称：</td>
						<td><select id="insur_jh" name="agencyID">
								<option value="0">请选择</option>
								<#list partnerlist! as item>
								<option value="${item.agencyID!}" <#if (user.agencyID! == item.agencyID!)>selected</#if>>${item.agencyName!}</option>
								</#list>
							</select>
						</td>
					</tr>
                    <tr>
                        <td align="right">子账户：</td>
                        <td><input name="username" size="50" type="text" value="${user.username!}"/></td>
                    </tr>
                    <tr>
                        <td align="right">登录名：</td>
                        <td><input name="loginname" size="50" type="text" value="${user.loginname!}"/></td>
                    </tr>
					<tr>
						<td align="right">密码：</td>
						<td><input name="password" size="50" type="password" value="${user.newPassword!}"/></td>
					</tr>
					<tr>
						<td align="right">确认密码：</td>
						<td><input name="confirmpassword" size="50" type="password" value="${user.confirmPassword!}"/></td>
					</tr>
                    <tr>
                        <td align="right">绑定地址：</td>
                        <td>
							<input name="macaddr" size="50" type="text" value="${user.macAddr!}"/>
						</td>
                    </tr>
                    <tr>
                    <td align="right">真实姓名：</td>
                        <td>
							<input name="realName" size="50" type="text" value="${user.realName!}"/>
						</td>
                    </tr>
                    <tr>
                        <td align="right">联系方式：</td>
                        <td>
							<input name="contact" size="50" type="text" value="${user.contact!}"/>
						</td>
                    </tr>
                    <tr>
                        <td align="right">工号：</td>
                        <td>
							<input name="employeeID" size="50" type="text" value="${user.employeeID!}"/>
						</td>
                    </tr>
                    <tr>
                        <td align="right">销售网点：</td>
                        <td>
							<input name="salesLocation" size="50" type="text" value="${user.salesLocation!}"/>
						</td>
                    </tr>
                    <tr>
                        <td align="right">销售部门：</td>
                        <td>
							<select id="department" name="department">
							<option value="0">请选择</option>
								<#list departmentlist! as item>
									<option value="${item.dicCode!}" <#if (user.department! == item.dicCode!)>selected</#if>>${item.dicName!}</option>
								</#list>
							</select>
						</td>
                    </tr>
                    <tr>
                        <td align="right">展业证号：</td>
                        <td>
							<input name="qualificationNO" size="50" type="text" value="${user.qualificationNO!}"/>
						</td>
                    </tr>
                    <tr>
                        <td align="right">状态：</td>
                        <td>
							<#if (user.uid! == 0 || (user.uid! != 0 && user.status! ==1))>
								<input type="checkbox" name="status" value="1" checked>是否启用
							<#elseif (user.uid! != 0 && user.status! == 0)>
								<input type="checkbox" name="status" value="1">是否启用
							</#if>
		
							<#if (user.uid! != 0 && user.admin! == 1)>
								<input type="checkbox" name="adminflag" value="1" checked>是否管理员
							<#elseif (user.uid! == 0 || (user.uid! != 0 && user.admin! == 0))>
								<input type="checkbox" name="adminflag" value="1">是否管理员
							</#if>
							
							<#if (user.uid! == 0 || (user.uid! != 0 && user.bindMac! == 1))>
								<input type="checkbox" name="bindmac" value="1" checked>是否绑定地址
							<#elseif (user.uid! != 0 && user.bindMac! == 0)>
								<input type="checkbox" name="bindmac" value="1">是否绑定地址
							</#if>
						</td>
                    </tr>
					<tr>
						<td align="right">组权限：</td>
						<td>
							<#list grouplist! as item>
								<input id="group_#${item?counter}" type="checkbox" name="groups" value="${item.groupID!}"
								<#if (item.selected! == 1)>checked</#if>>${item.groupName!}
							</#list>
					  	</td>
					</tr>
                </table>
            </div>
		</form>
        <!--div class="btn_tj"><a href="javascript:doSubmit();"><img src="/img/sure.gif"/></a><a href="/admin/userManager.action"><img src="/img/cancel.gif"/></a></div-->
		<div class="btn_tj"><input type="button" value="保 存" onclick="doSubmit();" style="margin-right:10px;"/><input type="button" value="取 消" onclick="{location.href='/admin/userManager'}"/></div>
    </div>
<div id="msg" class="tb_box">
	<iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
       		<tr>
            	<td class="btm"></td>
                <td class="box_ct" valign="top">
                	<div id="msg_ban" class="b_til">
                    	<span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                    	<div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr> 
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end-->
<#include "../common/foot.html">
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/insure.js?v=04"></script>
</html>

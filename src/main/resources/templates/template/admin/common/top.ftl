<style>
    .changeUser {
        display: inline-block;
    }

    .changeUser:hover .changeUserList {
        display: block;
    }

    .changeUserList {
        display: none;
        position: absolute;
        background-color: #f5f5f5;
        right: 0;
        padding: 10px 20px;
        border: 1px #d2d2d2 solid;
        line-height: 24px;
        list-style: none
    }

    .changeUserList li:hover {
        color: #e99411;
    }

</style>


<div class="pageHeader">
    <img class="l" src="/img/zurich_logo.png" style="height: 55px;"/>
</div>
<div class="dl_info">
    <span class="user">
        <span>${securityUser.agencyName!} | ${securityUser.loginName!}</span>
    </span>
    <#list securityUser.relatedUsersList!>
        <span>|</span>
        <div class="changeUser">
            <a href="javascript:void(0)">切换分公司</a>
            <ul class="changeUserList">
                <#items as item>
                    <li><a href="/admin/switchBranchAgency?uid=${item.uid!}">${item.loginname!}</a></li>
                </#items>
            </ul>
        </div>
    </#list>
    <span class="fgx">|</span><a href="/logout">安全退出</a><span class="zwf"></span>
</div>
<input id="branch" type="hidden" value="${securityUser.agencyId!}"/>
<input id="partnertype" type="hidden" value="${securityUser.agencyType!}"/>

<!--左侧二级菜单html部分-->
<div id="leftmenu">
    <ul>
        <#list securityUser.mainMenuList! as menu>
            <#if menu.menuName == "在线投保">
                <li>
                    <a href="#">直销出单-></a>
                    <ul>
                        <a href="/admin/batch/insure?directBranchSale=1">新增多个保单</a>
                    </ul>
                </li>
                <#if securityUser.isPayment != "1">
                    <li>
                        <a href="#">客户出单-></a>
                        <ul>
                            <#--                            <li><a href="/admin/query/insure">新增单个保单</a></li>-->
                            <li><a href="/admin/batch/insure">新增多个保单</a></li>
                        </ul>
                    </li>
                </#if>
            <#elseif menu.menuName == "保单处理">
                <li>
                    <a href="/admin/query/process">保单处理&nbsp;&nbsp;&nbsp;</a>
                </li>
            <#elseif menu.menuName == "查询服务">
                <li>
                    <a href="#">查询服务-></a>
                    <ul>
                        <li><a href="/admin/query/tongji">保单查询</a></li>
                        <li><a href="/admin/query/mingxi">被保险人查询</a></li>
                    </ul>
                </li>
            <#elseif menu.menuName == "对账服务">
                <li>
                    <a href="#">对账服务-></a>
                    <ul>
                        <li><a href="/admin/premiumBill/commission">保单交易明细</a></li>
                        <li><a href="/admin/premiumBill/commissionDetail">被保险人交易明细</a></li>
                    </ul>
                </li>
            <#elseif menu.menuName == "客户管理">
                <li>
                    <a href="/admin/client/index">客户管理&nbsp;&nbsp;&nbsp;</a>
                </li>
            <#elseif menu.menuName == "产品中心">
                <li><a href="/admin/product/listProduct">产品中心</a>
                </li>
            <#elseif menu.menuName == "公司管理">
                <li>
                    <a href="#">公司管理-></a>
                    <ul>
                        <li><a href="/admin/department/index">部门管理</a></li>
                        <li><a href="/admin/parameter/edit">系统设置</a></li>
                    </ul>
                </li>
            </#if>
        </#list>
        <#if sjisUtil.hasAction('accountManager')>
            <li>
                <a href="/admin/account/index">账户管理&nbsp;&nbsp;&nbsp;</a>
            </li>
        </#if>
        <#if sjisUtil.hasAction('bill99Manager')>
            <li>
                <a href="/admin/query/queryFee">快钱管理&nbsp;&nbsp;&nbsp;</a>
            </li>
        </#if>
        <#if sjisUtil.hasAction('blackListManager')>
            <li>
                <a href="/admin/blacklist/list">黑名单管理&nbsp;&nbsp;&nbsp;</a>
            </li>
        </#if>
        <#if sjisUtil.hasAction('blockManager')>
            <li>
                <a href="/admin/block/blockList">板块管理</a>
            </li>
        </#if>
        <#if sjisUtil.hasAction('cpsManager')>
            <li>
                <a href="/admin/web/list">CPS管理</a>
            </li>
        </#if>
        <li><a href="/admin/editpass">修改密码&nbsp;&nbsp;&nbsp;</a></li>
        <div class="clear"></div>
    </ul>
</div>



<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=${JS_AND_CSS_VER}" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="/css/zTreeStyle.css?v=${JS_AND_CSS_VER}" type="text/css">
    <script language="javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript" src="/js/jquery.lvyeSelect.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.core.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.excheck.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/checkBox.js?v=${JS_AND_CSS_VER}"></script>

    <script type="text/javascript">
        function pageaction(i) {
            document.conditionform.action = "/admin/premiumBill/commissionDetail?pagestart=" + i;
            document.conditionform.submit();
        }

        //跳转
        function goPage() {
            var num = document.all["goNumber"].value.replace(/\s/g, "");
            var lastpage = "${(page.lastPage)!}";
            if (num == "") {
                alert('请输入要跳转的页数');
                return;
            }
            if (parseInt(num) > parseInt(lastpage)) {
                alert('超出了最大页数，最大页数为' + lastpage);
                return;
            }
            pageaction(num);
        }

        function doExport() {
            document.conditionform.action = "exportCommissionDetail";
            document.conditionform.submit();
        }

        function doExportInsured() {
            document.conditionform.action = "/admin/query/exportInsured";
            document.conditionform.submit();
        }

        function selectAgency() {
            document.conditionform.action = "commissionDetail";
            document.conditionform.submit();
        }

        $(function () {	// 输入框、选择按钮、输出列表、返回值、数据
            $('#lys').lvyeSelect($('#selectInput'), $('#selectBtn'), $('#selectList'), $('#backValue'), 'partnerid');
        })
    </script>
</head>
<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar"><span>对账服务 > 被保险人交易明细查询</span></div>
    <form name="conditionform" action="" method="post">
        <input name="paginalcount" type="hidden" value="20"/>
        <div id="tbxx" class="tbxx">
            <table>
                <tr>
                    <td class="tbxxright">交易日期：</td>
                    <td width="140"><input name="startDate" id="iactivedat" value="${(condition.startDate)!}"
                                           readonly="readonly" onclick="showcalendar(event,this,false,true);"
                                           onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                                           type="text"/></td>

                    <td class="tbxxright">-------到：</td>
                    <td><input name="endDate" id="iactivedat" value="${(condition.endDate)!}" readonly="readonly"
                               onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/></td>

                    <td class="tbxxright">部门：</td>
                    <td colspan="1">
                        <input type="text" value="默认全选" id="treeInput" readonly/>
                        <div id="dptTreeBox" style="display:none;">
                            <ul id="dptTree" class="ztree"></ul>
                            <div class="treeBtns" id="dpt_btn">
                                <input type="button" value="确定" name="dptTreeAct"/>
                            </div>
                        </div>
                        <input type="hidden" name="branchDepartmentID" id="branchDepartmentID"
                               value="${branchDepartmentID!}"/>
                    </td>
                </tr>
                <tr>
                    <td class="tbxxright">生效日期：</td>
                    <td width="70">
                        <input name="effectiveBeginDate" id="effectiveBeginDate" value="${(condition.effectiveBeginDate)!}"
                               readonly="readonly" onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">-------到：</td>
                    <td>
                        <input name="effectiveEndDate" id="effectiveEndDate" value="${(condition.effectiveEndDate)!}"
                               readonly="readonly" onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">客户名称：</td>
                    <td colspan="1">
                        <select style="display:none;" id="partnerid" name="agencyID">
                            <option value="0">请选择</option>
                            <#list partnerlist! as item>
                                <option value="${item.agencyID!}"
                                        <#if condition.agencyID! == item.agencyID!>selected</#if>>[${item.abbrName!}] ${item.agencyName!}</option>
                            </#list>
                        </select>
                        <div id="lys" class="lys" style="width: 162px;">
                            <input id="selectInput" value="请选择" type="text" name="ly" autocomplete='off'>
                            <input id="backValue" value="" type="hidden">
                            <a href="javascript:;" id="selectBtn" class="selectBtn"></a>
                        </div>
                        <ul id="selectList" class="selectList"></ul>
                        <div id="ptnTreeBox" style="display:none;">
                            <ul id="ptnTree" class="ztree"></ul>
                            <div class="treeBtns" id="ptn_btn">
                                <input type="button" value="确定" name="ptnTreeAct"/>
                            </div>
                        </div>
                        <input type="hidden" id="departmentId" name="departmentId" value="${departmentId!}"/>
                    </td>
                </tr>
                <tr>
                    <td class="tbxxright">结束日期：</td>
                    <td width="140"><input name="bExpiryDate" id="bExpiryDate" value="${(condition.bExpiryDate)!}"
                                           readonly="readonly" onclick="showcalendar(event,this,false,true);"
                                           onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                                           type="text"/></td>

                    <td class="tbxxright">-------到：</td>
                    <td><input name="eExpiryDate" id="eExpiryDate" value="${(condition.eExpiryDate)!}" readonly="readonly"
                               onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/></td>
                </tr>
                <tr>
                    <td class="tbxxright">凭证号码：</td>
                    <td><input name="policyID" type="text" value="${(condition.policyID)!}"/></td>

                    <td class="tbxxright">保单号：</td>
                    <td><input name="policyNo" type="text" value="${(condition.policyNo)!}"/></td>

                    <td></td>
                    <td class="tbxxright">
                        <input type="hidden" name="webSubmit" value="1"/>
                        <input type="button" value="查 询" class="sbtn" id="doSubmit"/>
                        <input type="button" value="清 空" class="sbtn" onclick="clearall();"/>
                    </td>
                </tr>
            </table>
        </div>
    </form>
    <div class="fltil" style="height:65px; padding-left:0; margin-left:0;">
        <!--a class="dc_data" href='javascript:doExport();'></a-->
        <input class="dc_data" type="button" value="导 出" onclick="doExport();"/>
        <div id="beijing"><!--a class="dc_data3" href='javascript:doExportInsured();'></a--><input class="dc_data"
                                                                                                   type="button"
                                                                                                   value="北京导出"
                                                                                                   onclick="doExportInsured();"
                                                                                                   style="margin-right:10px; width:80px;"/>
        </div>
        <div>
            <table class="tbli2">
                <thead>
                <tr class="tbhead">
                    <th style="height:21px;">查询结果：</th>
                    <th>合计</th>
                    <th>=新增</th>
                    <th>+批改</th>
                    <th>+取消</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td align="center">交易（笔）</td>
                    <td align="right">${commission.totalSingular!}</td>
                    <td align="right">${commission.newSingular!}</td>
                    <td align="right">${commission.modifySingular!}</td>
                    <td align="right">${commission.cancelSingular!}</td>
                </tr>
                <tr>
                    <td align="center">保费(含适用的增值税)（元）</td>
                    <td align="right">${commission.totalPremium!}</td>
                    <td align="right">${commission.newPremium!}</td>
                    <td align="right">${commission.modifyPremium!}</td>
                    <td align="right">${commission.cancelPremium!}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="tableContainer" class="tableContainer">
        <table class="tbli">
            <thead>
            <tr class="tbhead">
                <th>保单号</th>
                <th>凭证号码</th>
                <th>被保险人</th>
                <th>交易金额</th>
                <th>摘要</th>
                <th>交易日</th>
                <th>签单日</th>
                <th>生效日</th>
                <th>过期日</th>
                <th>客户名称</th>
                <th>操作员</th>
                <th>证件号码</th>
                <th>生日</th>
                <th>险种</th>
                <th>计划</th>
                <th>线路/团号</th>
                <th>经办人</th>
                <th>见费出单</th>
            </tr>
            </thead>
            <tbody id="trbs">
            <#list list! as item>
                <tr>
                    <td>${item.policyNo!}</td>
                    <td>${item.policyID!}</td>
                    <td>${item.insuredName!}</td>
                    <td>${item.transactionAmount!}</td>
                    <td>
                        <#if item.status! == 1>新增
                        <#elseif item.status! == 2>批改
                        <#elseif item.status! == 3>取消
                        </#if>
                    </td>
                    <td>${dateUtil.customFormat(item.transactionDate!,"yyyy-MM-dd HH:mm:ss")}</td>
                    <td>${dateUtil.customFormat(item.inssueDate!,"yyyy-MM-dd")}</td>
                    <td>${dateUtil.customFormat(item.effectiveDate!,"yyyy-MM-dd")}</td>
                    <td>${dateUtil.customFormat(item.expiryDate!,"yyyy-MM-dd")}</td>
                    <td>${item.agencyName!}</td>
                    <td>${item.userName!}</td>
                    <td>${item.insuredIdNo!}</td>
                    <td>${item.birthday!}</td>
                    <td>${item.productName!}</td>
                    <td>${item.planName!}</td>
                    <td>${item.lineNo!}</td>
                    <td>${item.attn!}</td>
                    <td>${item.isPayment!}</td>
                </tr>
            </#list>
            </tbody>
        </table>
    </div>
    <div class="fy_box">
    <div class="fy_box">
        <div class="page_fy">
            <a>【共${(page.rowCount)!}条记录，${(policyStactics.totalPremium)!}保费(含适用的增值税)】</a>
            <#if ((page.pageStart)! > 1)>
                <a class="page_pre" href="javascript:pageaction('${(page.priPage)!}')">上一页</a>
            </#if>
            <#if (page.pageStart! < page.lastPage!)>
                <a class="page_nex" href="javascript:pageaction('${(page.nextPage)!}')">下一页</a>
            </#if>
            <script>
                var k = 0;  //临时记录数
                var largetcount = 10;    //每页显示多少个      10
                var cp = ${(page.pageStart)!}; //当前页         98
                var pagecount = ${(page.lastPage)!}; //页面总数 100
                var selfcnt = Math.floor(largetcount / 2);     //5
                var lcnt = Math.floor(pagecount / 2);          //50
                var startp = cp - selfcnt;                     //93
                if (pagecount - cp < selfcnt) {
                    startp = startp - (selfcnt - (pagecount - cp));
                }
                for (var i = startp; i <= cp; i++) {
                    if (i < 1 || i > cp || i > pagecount) continue;
                    k = k + 1;
                    if (i == cp)
                        document.write('<a class="page_dq">' + i + '</a>');
                    else
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>')
                }
                if (cp < pagecount && k < largetcount) {
                    for (var i = cp + 1; i <= pagecount; i++) {
                        if (k > largetcount) break;
                        k = k + 1;
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>');
                    }
                }
            </script>
            <div>第 <input name="goNumber" class="tex" type="text"/> 页 <input type="submit" class="submit" value="跳转"
                                                                             onClick="goPage()"></div>
        </div>
    </div>
</div>

<!-- tipbox -->
<div id="msg" class="tb_box">
    <iframe id="ifhmsg" frameborder="0"></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="msg_ban" class="b_til">
                        <span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                        <div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a
                                id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div><!-- tipbox end-->
<#include "../common/foot.html">
</body>
<script language="javascript">
    function getParent(str) {
        var arr = str.split(',');
        return arr[arr.length - 2];
    }

    function getDepartmentJson() {
        var departmentList = [
            <#list departmentList! as departmentObj>
            {
                "id": ${departmentObj.departmentID!},
                "parentid": "${departmentObj.parent!}",
                "name": "${departmentObj.departmentAbbName!}"
            }
            ,
            </#list>
            {}
        ]
        departmentList.pop();
        for (ind in departmentList) {
            departmentList[ind].parentid = getParent(departmentList[ind].parentid);
        }
        return departmentList;
    }

    //服务器时间
    //此处决定日历默认时间
    var d = new Date();
    var zyear = d.getFullYear();
    var zmonth = d.getMonth();
    var zday = 1;
    var cday = d.getDate();
    var exportdiv = document.getElementById("beijing");
    if ($("#branch").val() == 2) {
        exportdiv.style.display = "block";
    } else {
        exportdiv.style.display = "none";
    }
</script>
<script language="javascript" src="/js/common.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/mag_cp.js?v=${JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/beaurl_nall.js?v=${JS_AND_CSS_VER}"></script>
</html>

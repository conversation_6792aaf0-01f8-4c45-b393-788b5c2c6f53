<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css"/>
    <script language="javascript" src="/js/jquery.js"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar"><span>产品管理->授权客户</span>
        <input type="button" value="返回" class="dc_data" onclick="{location.href='/admin/product/listProduct'}"/></div>
    <div id="tableContainer" class="tableContainer">
        <table class="tbli">
            <tr class="tbhead">
                <th class="bh" width="45">编号</th>
                <th>客户名称</th>
                <th>产品名称</th>
                <th>客户创建时间</th>
                <th>客户到期时间</th>
                <th>操作</th>
            </tr>
            <tbody id="trbs">
            <#list partnerlist! as item>
            <tr>
                <td class='tbh'>${item?counter}</td>
                <td>${item.agencyName!}</td>
                <td>${product.productname!}</td>
                <td>${item.createdate!}</td>
                <td>${item.expiryDate!}</td>
                <td>
                    <input class="Producid" hidden value="${product.productid!}"/>
                    <input class="Clientid" hidden value="${item.agencyID!}"/>
                    <a href="javascript:void(0)" onclick=cancelAuth(this)>取消授权</a>
                    <a href="/admin/product/listProduct">返回</a>
                </td>
            </tr>
            </#list>
            </tbody>
        </table>
        <div>
        </div>
    </div>

    <div id="msg" class="tb_box">
        <iframe id="ifhmsg" frameborder="0"></iframe>
        <div class="box">
            <div class="box_t"></div>
            <table id="bzh" cellpadding="0">
                <tr>
                    <td class="btm"></td>
                    <td class="box_ct" valign="top">
                        <div id="msg_ban" class="b_til">
                            <span id="msg_til"></span>
                            <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                        </div>
                        <div class="smsg">
                            <div class="spic"></div>
                            <div id="msg_info"></div>
                        </div>
                        <div class="tb_btn">
                            <a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a>
                            <a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a>
                        </div>
                    </td>
                    <td class="btm"></td>
                </tr>
            </table>
            <div class="box_bom"></div>
        </div>
    </div>
    <!-- tipbox end-->
    <#include "../common/foot.html">
</body>
<script type="text/javascript">
    function cancelAuth(param) {
        var productid = $(param).parent("td").find(".Producid").val();
        var agencyid = $(param).parent("td").find(".Clientid").val();
        var agencyname = $(param).closest("tr").find("td").eq(1).html();
        if (productid <= 0 || agencyid <= 0) {
            alert("产品信息或者客户信息错误！");
        }
        if (confirm("确认删除 " + agencyname + " 的该产品授权嘛？")) {
            jQuery.ajax({
                url: "/admin/product/cancelAuthorization",
                type: "POST",
                data: "agencyID=" + agencyid + "&productid=" + productid,
                timeout: 50000,
                dataType: "JSON",
                error: function () {
                    alert("网络超时，操作中断！");
                    $("#alert_opacity").hide();
                    $("#img_closed").hide();
                },
                complete: function (xr) {
                    var str = xr.responseText;
                    if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
                        alert("用户超时，请重新登陆！");
                        location.assign("/admin/department/save");
                    }
                },
                success: function (data) {
                    var check = data;
                    if (check.result == 1) {
                        $(param).closest("tr").remove();
                        alert("取消授权成功！");
                    } else {
                        alert("取消授权失败！详细信息为：" + data.message);
                    }
                }
            })
        }
    }
</script>
<script language="javascript" src="/js/mag_cp.js?v=01"></script>
</html>

<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        #trbs tr:nth-child(2n) {
            background: rgb(241, 244, 249);
        }

        #user_opacity {
            position: absolute;
            z-index: 12;
            top: 0px;
            left: 0px;
            width: 100%;
            height: 100%;
            filter: alpha(opacity=0);
            opacity: 0.5;
            background-color: rgba(0, 0, 0, 255);
            display: none;
        }

        #blackuser_edit_box {
            position: absolute;
            border: 4px solid #ccc;
            display: none;
            background-color: #FFF;
            z-index: 13;
            width: 400px;
        }

        #blackuser_top, #blackuser_bottom {
            height: 30px;
            color: #FFF;
            line-height: 30px;
            border-bottom: 1px solid #ccc;
            background-color: #96C1FC;
        }

        #blackuser_body {
            margin: 15px 0;
        }

        #blackuser_body table tr {
            height: 25px;
        }

        #blackuser_body table tr td {
            text-align: center;
        }

        .hidden_birth {
            position: absolute;
            font-size: 12px;
            display: inline;
            color: #999;
            left: 60px;
            top: 3px;
        }

        .loadImg {
            display: none;
            top: 0px;
            left: 0px;
            width: 50px;
            height: 50px;
            position: absolute;
            z-index: 13;
        }

        #blackuserForm input, select {
            width: 162px;
            height: 20px;
            box-sizing: inherit;
            border-width: 1px;
        }

    </style>
    <script language="javascript" src="/js/jquery.js"></script>
    <script type="text/javascript">
        function pageaction(i) {
            document.conditionform.action = "/admin/blacklist/list?pagestart=" + i;
            document.conditionform.submit();
        }

        //跳转
        function goPage() {
            var num = document.all["goNumber"].value.replace(/\s/g, "");
            var lastpage = "${page.lastPage}";
            if (num == "") {
                alert('请输入要跳转的页数');
                return;
            }
            if (parseInt(num) > parseInt(lastpage)) {
                alert('超出了最大页数，最大页数为' + lastpage);
                return;
            }
            pageaction(num);
        }

        function doSubmit() {
            document.conditionform.action = "";
            document.conditionform.submit();
        }

        function clearall() {
            $("#tbxx").find("input[name='condition.Name']").val("");
            $("#tbxx").find("input[name='condition.IdNo']").val("");
        }
    </script>

</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar"><span>系统管理 > 黑名单管理</span>

        <input type="button" value="批量导入" class="dc_data" onclick="importExecel()"/>
        <input type="button" value="单个添加" class="dc_data" onclick="change('-1')"/>
    </div>
    <a style="float: right;margin-top: 6px;margin-right: 8px;" href="/download?file=sale/blacklist.xlsx">EXCEL导入模板</a>
    <form name="conditionform" action="" method="post">
        <div id="tbxx" class="tbxx">
            <table>
                <tr>
                    <td class="tbxxright">姓名：</td>
                    <td width="70">
                        <input name="name" value="${condition.name!}"/>
                    </td>
                    <td class="tbxxright">证件号码：</td>
                    <td>
                        <input name="idNo" value="${condition.idNo!}"/>
                    </td>

                    <td class="tbxxright"></td>
                    <td colspan="1">
                        <input type="button" value="查 询" class="sbtn" onclick="doSubmit();"/>
                        <input type="button" value="清 空" class="sbtn" onclick="clearall();"/>
                    </td>

                </tr>
            </table>
        </div>
    </form>
    <div id="tableContainer" class="tableContainer">
        <table class="tbli">
            <tr class="tbhead">
                <th class="bh">姓名</th>
                <th class="bh">性别</th>
                <th class="bh">出生日期</th>
                <th class="bh">证件类型</th>
                <th class="bh">证件号码</th>
                <th class="bh">备注</th>
                <th class="bh">更新时间</th>
                <th class="bh">操作</th>
            </tr>
            <tbody id="trbs">
            <#list blacklistUsers! as item>
                <tr>
                    <td class='tbh'>${item.name!}</td>
                    <td class='tbh'>${item.gend!}</td>
                    <td class='tbh'>${item.birthDay!}</td>
                    <td class='tbh'>${item.idNoType!}</td>
                    <td class='tbh'>${item.idNo!}</td>
                    <td class='tbh'>${item.remark!}</td>
                    <td class='tbh'>${dateUtil.formatyyyyMMddHHmmss(item.updateTime!)}</td>
                    <td class='tbh'>
                        <a href='javascript:void(0)'
                           onClick="change('${item.id!}|${item.name!}|${item.gend!}|${item.birthDay!}|${item.idNoType!}|${item.idNo!}|${item.remark!}')">修改</a>
                        <a href='javascript:void(0)' onClick="delList('${item.id!}','${item.Name!}')">删除</a>
                    </td>
                </tr>
            </#list>
            </tbody>
        </table>
    </div>

    <div class="fy_box">
        <div class="page_fy">
            <#if (page.pageStart! > 1)>
                <a class="page_pre" onClick="pageaction('${page.priPage!}')">上一页</a>
            </#if>
            <#if (page.pageStart < page.lastPage!)>
                <a class="page_nex" onClick="pageaction('${page.nextPage!}')">下一页</a>
            </#if>
            <script>
                var k = 0;  //临时记录数
                var largetcount = 10;    //每页显示多少个      10
                var cp = ${page.pageStart!}; //当前页         98
                var pagecount = ${page.lastPage!}; //页面总数 100
                var selfcnt = Math.floor(largetcount / 2);     //5
                var lcnt = Math.floor(pagecount / 2);          //50
                var startp = cp - selfcnt;                     //93
                if (pagecount - cp < selfcnt) {
                    startp = startp - (selfcnt - (pagecount - cp));
                }
                for (var i = startp; i <= cp; i++) {
                    if (i < 1 || i > cp || i > pagecount)
                        continue;
                    k = k + 1;
                    if (i == cp)
                        document.write("<a class='page_dq' href='#'>" + i + "</a>");
                    else
                        document.write("<a onClick=pageaction('" + i + "')>" + i + "</a>")
                }
                if (cp < pagecount && k < largetcount) {
                    for (var i = cp + 1; i <= pagecount; i++) {
                        if (k > largetcount)
                            break;
                        k = k + 1;
                        document.write("<a onClick=pageaction('" + i + "')>" + i + "</a>");
                    }
                }

            </script>
            <div>
                第 <input name="goNumber" class="tex" type="text"/> 页 <input type="submit" class="submit" value="跳转"
                                                                            onClick="goPage()">
            </div>
        </div>
    </div>

</div>
<#include "../common/foot.html">
<div id="user_opacity"></div>
<div id="blackuser_edit_box">
    <div id="blackuser_top">
        <table>
            <tr>
                <td width="970">
                    <span style="margin-left:20px;">添加黑名单</span></td>
                <td width="20"><img id="close_power" src="/img/department/close.gif"></img>
                </td>
            </tr>
        </table>
    </div>
    <div id="blackuser_body">
        <form id="blackuserForm" action="" method="post">
            <input type="hidden" name="id" id="user_ID" value="0"/>
            <table width="100%">
                <tr>
                    <td><span class="rqgs">*</span>姓名：</td>
                    <td><input name="name" id="user_Name"/></td>
                </tr>
                <tr>
                    <td><span class="rqgs">*</span>性别：</td>
                    <td>
                        <select name="gend" id="user_Gend">
                            <option value="女">女</option>
                            <option value="男">男</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td><span class="rqgs">*</span>出生日期：</td>
                    <td style="position: relative">
                        <input name="birthDay" id="user_BirthDay" type="text"/>
                        <span class="hidden_birth">格式：1980-12-25</span>
                    </td>
                </tr>
                <tr>
                    <td><span class="rqgs">*</span>证件类型：</td>
                    <td>
                        <select name="idNoType" id="user_IdNoType">
                            <option value="身份证">身份证</option>
                            <option value="护照">护照</option>
                            <option value="军官证">军官证</option>
                            <option value="港澳回乡证或台胞证">港澳回乡证或台胞证</option>
                            <option value="其他">其他</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td><span class="rqgs">*</span>证件号码：</td>
                    <td><input name="idNo" id="user_IdNo"/></td>
                </tr>
                <tr>
                    <td>备注：</td>
                    <td><input name="remark" id="user_remark"/></td>
                </tr>
            </table>
        </form>
    </div>
    <div id="blackuser_bottom">
        <input type="button" value="保 存" style="width:100px; height:25px;margin-left: 5px;" id="blackuser_button_save"/>
        <input type="button" value="取 消" style="width:100px; height:25px;margin-left: 185px;"
               id="blackuser_button_cancel"/>
    </div>
</div>



<form id="excelForm" enctype="multipart/form-data" action="" method="post" name="excelForm" style="display: none">
    <input id="excelfile" type="file" onchange="submitExcel()" name="excelfile">
</form>

<img class="loadImg" src="/img/department/loading.gif"/>
</body>
<script type="text/javascript">


    function delList(i, name) {
        if (confirm("确认要从黑名单中移除 ‘" + name + "’ 吗？")) {
            jQuery.ajax({
                url: "/admin/blacklist/delete",
                type: "POST",
                data: "id=" + i,
                timeout: 50000,
                dataType: "JSON",
                error: function () {
                    alert("网络超时，操作中断！");
                },
                complete: function (xr) {
                    var str = xr.responseText;
                    if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
                        alert("用户超时，请重新登陆！");
                        location.assign("/admin/department/save");
                    }
                },
                success: function (data) {
                    var check = data;
                    if (check.result == 1) {
                        window.location.reload();
                    } else {
                        alert("移除黑名单失败！详细信息为：" + data.message);
                    }
                }
            })
        }
    }


    function change(param) {
        $("#blackuserForm").find('input').val("");
        $("#blackuserForm").find('select').val("");
        if (param != "-1") {
            var userinfo = param.split('|');
            $("#user_ID").val(userinfo[0]);
            $("#user_Name").val(userinfo[1]);
            $("#user_Gend").val(userinfo[2]);
            $("#user_BirthDay").val(userinfo[3]);
            if (userinfo[3] != "") {      //生日提示隐藏
                $(".hidden_birth").css("display", "none");
            }
            $("#user_IdNoType").val(userinfo[4]);
            $("#user_IdNo").val(userinfo[5]);
            $("#user_remark").val(userinfo[6]);
        }

        scren_point($("#blackuser_edit_box"));
        $("#blackuser_edit_box").show();
        $("#user_opacity").show();
    }

    $("#close_power,#blackuser_button_cancel").on('click', function () {
        $("#blackuser_edit_box").hide();
        $("#user_opacity").hide();
    })

    function scren_point(param_object) {
        var html_width = document.body.clientWidth;
        var html_height = document.body.clientHeight;
        var user_width = param_object.css("width");
        var user_height = param_object.css("height");
        var user_top = (parseInt(html_width) - parseInt(user_width)) / 2;
        var user_left = (parseInt(html_height) - parseInt(user_height)) / 2;
        param_object.css("top", user_left);
        param_object.css("left", user_top);
    }


    $(document).on("click", ".hidden_birth", function () {
        $(this).hide();
        $("#user_BirthDay").focus();
    })
    $(document).on("focus", "#user_BirthDay", function () {
        $(".hidden_birth").css("display", "none");
    })
    $(document).on("blur", "#user_BirthDay", function () {
        if ($(this).val() == "") {
            $(".hidden_birth").css("display", "inline");
        }
    })


    $(document).on("click", ".upexcel", function () {
        var excelFile = $("#excelIframe2").contents().find('#excelfile');
        excelFile.attr("accept", ".xls");
        excelFile.click();
    })



    $("#blackuser_button_save").on('click', function () {
        if ($("#user_Name").val() == "") {
            alert("请输入姓名！");
            $("#user_Name").focus();
            return;
        }
        if ($("#user_BirthDay").val() == "") {
            alert("请输入出生日期！");
            $("#user_BirthDay").focus();
            return;
        }

        var pattern = new RegExp(/^\d{4}-\d{1,2}-\d{1,2}$/);
        if (!pattern.test($("#user_BirthDay").val())) {
            alert("出生日期格式错误，请重新填写！");
            $("#user_BirthDay").focus();
            return;
        }

        if ($("#user_IdNo").val() == "") {
            alert("请输入证件号码！");
            $("#user_IdNo").focus();
            return;
        }

        jQuery.ajax({
            url: "/admin/blacklist/save",
            type: "POST",
            data: $("#blackuserForm").serialize(),
            timeout: 50000,
            dataType: "JSON",
            error: function () {
                alert("网络超时，操作中断！");
            },
            complete: function (xr) {
                var str = xr.responseText;
                if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
                    alert("用户超时，请重新登陆！");
                    location.assign("/admin/department/save");
                }
            },
            success: function (data) {
                var check = data;
                if (check.result == 1) {
                    window.location.reload();
                } else {
                    alert("保存失败！详细信息为：" + data.message);
                }
            }
        })
    })

    function importExecel() {
        $("#excelfile").click();
    }

    function submitExcel() {
        if ($("#excelfile").val().indexOf(".xls") == -1 && $("#excelfile").val().indexOf(".xlsx") == -1) {
            alert("只能上传Excel文件，其扩展名应为*.xls或*.xlsx！");
            return;
        }
        loading();
        jQuery.ajax({
            url: "batchImport",
            type: "POST",
            cache: false,
            data: new FormData($('#excelForm')[0]),
            dataType: "JSON",
            /**
             *必须false才会自动加上正确的Content-Type
             */
            contentType: false,
            /**
             * 必须false才会避开jQuery对 formdata 的默认处理
             * XMLHttpRequest会对 formdata 进行正确的处理
             */
            processData: false,
            error: function () {
                alert("网络超时，操作中断！");
                window.location.reload();
            },
            complete: function (xr) {
                var str = xr.responseText;
                if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
                    alert("用户超时，请重新登陆！");
                    location.assign("/admin/department/save");
                }
            },
            success: function (data) {
                var check = data;
                if (check.result == 1) {

                } else {
                    alert("导入失败！详细信息为：" + check.message);
                }
                window.location.reload();
            },
        });
    }

    function loading() {
        scren_point($(".loadImg"));
        $("#user_opacity").show();
        $(".loadImg").show();
    }


</script>
</html>

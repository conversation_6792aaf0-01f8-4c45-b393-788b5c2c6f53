###parse("$!{location}/common/getversion.vm")
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns="http://www.w3.org/1999/html">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>$!{sysTitle}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
    <script language="javascript" src="/js/jquery.js"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
    <!-- <script type="text/javascript" src="/fckeditor/fckeditor.js"></script> -->
    <script type="text/javascript" src="/ckeditor/ckeditor.js"></script>
    <style type="text/css">

        /*更新页面*/
		
		select{width:174px;}
		
        #div_update {
            /* margin: 0 auto;*/
            /*width: 90%;*/
            height: 600px;
            /* border: 2px #B0B0B0 solid;*/
            clear：both;

        }

        #div_update li {
            list-style: none;
            padding: 3px;
            margin-left: 15px;
            margin-top: 2px;
            font-size: 12px;
        }

        .div_left {
            width: 20%;
            height: 100%;
            border-right: 2px #B0B0B0 dotted;
            float: left;
        }

        .div_right {
            width: 73%;
            height: 100%;
            float: right;
        }

        .main_div_left {
            padding: 3px;
            width: 85%;
            height: 85%;
            float: left;
            margin-top: 15%;
            margin-left: 10px;
            overflow: auto;
        }

        .main_div_right {
            padding: 3px;
            width: 100%;
            height: 85%;
            float: left;
            margin-top: 5%;
        }

        #china_update ul {
            margin-left: 20px;
        }

        #first_menu {
            width: auto;
            height: auto;
            margin: 0 auto;
            padding: 3px;

        }

        #first_menu img {
            display: inline-block;
            vertical-align: middle;
        }

        #china_provinces {
            margin: 0 auto;
            margin-left: 20px;

        }

        #china_cities {
            margin: 0 auto;
            margin-left: 20px;
        }

        #china_towns {
            margin: 0 auto;
            margin-left: 20px;
        }

        .update-page {
            width: 100%;
            height: 80%;
            padding: 3px;
            margin: 0 auto;
            margin-top: 10px;
            overflow: auto;

        }

        .input-div {
            /* margin: 0 auto;*/
            margin-top: 10px;
            width: 35%;
            height: 30px;
            float: left;
            margin-left: 15%;
        }

        .span-2 {
            display:inline-block;
            width: 80px;
            margin-left: 20px;
            text-align: right;
        }

        .update_title {
            margin: 0 auto;
            text-align: center;
            margin-top: -10px;
        }

        .update-button input {
            width: 70px;
            padding-right: 15px;
        }

        .update-button {
            display: inline-block;

            margin: 0 auto;
            margin-left: 37%;
            margin-bottom: 20%;
        }

        #countries_areas_update {
            margin-left: 20px;
        }

        /* 鼠标移上去*/
        #div_update img:hover {
            cursor: pointer;
        }

        #div_update span:hover {
            cursor: pointer;
        }

        .input_waring {
            color: red;
        }

        #Code_waring {
            position: absolute;
            top: 11%;
            left: 42.5%;
        }

        #NAME_waring {
            position: absolute;
            top: 11%;
            left: 71%;
        }

        .update_span_input {
            width: 30%;
        }
		
		.nospace{
			color:red;
		}

    </style>

    <script type="text/javascript">


    </script>
</head>
<body>
    #parse( "$!{location}/admin/common/top.vm" )
<div class="main_hd"><span>信息维护->修改信息</span></div>
<div id="div_update">
    <div class="div_left">
        <div id="main_div_left" class="main_div_left
            ">
            <div id="first_menu">
            </div>
        </div>
    </div>

    <div class="div_right">
        <div id="main_div_right" class="main_div_right" hidden>
            <div class="update_title" id="update-title1"><h3>请根据文本框提示进行修改</h3></div>
            <div class="update_title" id="update-title2" hidden><h3>更新后的信息如下</h3></div>
            <div id="update-page" class="update-page" hidden>
                <form id="update_from">
                    <div class="input-div" hidden>
                                <span class="span-1">
                                    <span class="span-2">分类编码</span>
                                    <span>:</span>
                                    <span>
                                        <input id="CodeSetId" name="codeItem.codeSetId"
                                               type="text" value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1">
                                        <span class="span-2">CodeItemId</span>
                                        <span>:</span>
                                        <span>
                                            <input id="CodeItemId" name="codeItem.codeItemId"
                                                   type="text" value=""/>
                                        </span>
										<span class="nospace">*</span>
                                    </span>
                    </div>

                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">明细编号</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeItem.CODE" id="CODE"
                                               value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>


                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">明细名称</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeItem.NAME" id="NAME"
                                               value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>
                #* <span class="update_span_input input_waring"  id="NAME_waring"></span>*#

                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">全称</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeItem.fullName"
                                               id="FullName" value=""/>
                                    </span>
                                </span>
                    </div>

                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">英文名称</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeItem.engName"
                                               id="EngName" value=""/>
                                    </span>
                                </span>
                    </div>

                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">英文简称</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeItem.EngShortName"
                                               id="EngShortName" value=""/>
                                    </span>
                                </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1">
                                        <span class="span-2">级数</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" id="Level"
                                                   name="codeItem.Level" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1">
                                        <span class="span-2">是否末级</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.IsLowest"
                                                   id="IsLowest" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">分级码</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.LevelCode"
                                                   id="LevelCode" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">是否可用</span>
                                        <span>:</span>
                                        <span>
                                            <select name="codeItem.IsEnable" id="IsEnable">
                                                <option value="1" selected>是</option>
                                                <option value="0">否</option>
                                            </select>
                                        #*<input type="text" name="codeItem.IsEnable" id="IsEnable" value=""/>*#
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">EnableTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.EnableTime"
                                                   id="EnableTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">UnEnableTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.UnEnableTime"
                                                   id="UnEnableTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">CreateUser</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.CreateUser"
                                                   id="CreateUser" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">CreateTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.CreateTime"
                                                   id="CreateTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">ChangeUser</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.ChangeUser"
                                                   id="ChangeUser" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">ChangeTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeItem.ChangeTime"
                                                   id="ChangeTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize1</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize1"
                                                   id="Customize1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize2</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize2"
                                                   id="Customize2" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize3</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize3"
                                                   id="Customize3" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize4</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize4"
                                                   id="Customize4" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize5</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize5"
                                                   id="Customize5" value=""/>
                                        </span>
                                    </span>
                    </div>


                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize6</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize6"
                                                   id="Customize6" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize7</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize7"
                                                   id="Customize7" value=""/>
                                        </span>
                                    </span>
                    </div>


                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize8</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize8"
                                                   id="Customize8" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize9</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeItem.Customize9"
                                                   id="Customize9" value=""/>
                                        </span>
                                    </span>
                    </div>
                </form>


            </div>

            <div id="update_codeSet_page" class="update_codeSet_page" hidden>
                <form id="update_codeSet_form">
                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">分类编码</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" id="CodeSetID" name="codeSet.codeSetId"
                                               type="text" value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>


                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">明细编号</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeSet.codeSetCode"
                                               id="CodeSetCODE" value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>


                    <div class="input-div">
                                <span class="span-1">
                                    <span class="span-2">名称</span>
                                    <span>:</span>
                                    <span>
                                        <input class="update_show_input" type="text" name="codeSet.codeSetName"
                                               id="CodeSetNAME"
                                               value=""/>
                                    </span>
									<span class="nospace">*</span>
                                </span>
                    </div>


                    <div class="input-div">
                                    <span class="span-1">
                                        <span class="span-2">是否树状结构</span>
                                        <span>:</span>
                                        <span>
                                            <select name="codeSet.isTree" id="IsTree" style="width: 161px">
                                                <option value="1" selected>是</option>
                                                <option value="0">不是</option>
                                            </select>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">备注</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.remark"
                                                   id="Remark" value=""/>
                                        </span>
                                    </span>
                    </div>


                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">CreateUser</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeSet.createUser"
                                                   id="CreateUser" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">CreateTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeSet.createTime"
                                                   id="CreateTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">ChangeUser</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeSet.changeUser"
                                                   id="ChangeUser" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div" hidden>
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">ChangeTime</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_span_input" type="text" name="codeSet.changeTime"
                                                   id="ChangeTime" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize1</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize1"
                                                   id="Customize1-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize2</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize2"
                                                   id="Customize2-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize3</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize3"
                                                   id="Customize3-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize4</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize4"
                                                   id="Customize4-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize5</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize5"
                                                   id="Customize5-1" value=""/>
                                        </span>
                                    </span>
                    </div>


                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize6</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize6"
                                                   id="Customize6-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize7</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize7"
                                                   id="Customize7-1" value=""/>
                                        </span>
                                    </span>
                    </div>


                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize8</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize8"
                                                   id="Customize8-1" value=""/>
                                        </span>
                                    </span>
                    </div>

                    <div class="input-div">
                                    <span class="span-1" id="span_LevelCode">
                                        <span class="span-2">Customize9</span>
                                        <span>:</span>
                                        <span>
                                            <input class="update_show_input" type="text" name="codeSet.customize9"
                                                   id="Customize9-1" value=""/>
                                        </span>
                                    </span>
                    </div>
                </form>
            </div>

                           <span class="update-button" id="update-button" hidden>
                              <span>
                                  <input type="submit" id="add-in-sameLevel" value="同级增加"/>
                                  <input type="submit" id="add-in-nextLevel" value="下级增加"/>
                                  <input type="submit" id="update-confirm" value="保存"/>
                                  <input type="button" id="update-cancle" value="取消"/>
                              </span>
                           </span>

                             <span class="update-button" id="update_codeSet_button" hidden>
                              <span>
                                  <input type="submit" id="add-in-sameCodeSetLevel" value="同级增加"/>
                              #*<input type="submit" id="add-in-nextCodeSetLevel" value="下级增加"/>*#
                                  <input type="submit" id="update-CodeSet-confirm" value="保存"/>
                                  <input type="button" id="update-CodeSet-cancle" value="取消"/>
                              </span>
                           </span>

        </div>
    </div>
</div>
    #parse( "$!{location}/admin/common/foot.vm" )
</body>
<script type="text/javascript" src="/js/common_b.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/maintenancequery.js?v=$!{JS_AND_CSS_VER}"></script>
</html>

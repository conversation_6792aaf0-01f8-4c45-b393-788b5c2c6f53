<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!}</title>
    <link href="/css/chartis.css?v=${JS_AND_CSS_VER}" rel="stylesheet" type="text/css"/>
    <script language="javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript"
            src="/js/jquery.lvyePlugin.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript"
            src="/js/jquery.easydrag.handler.beta2.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript"
            src="/js/checkForm.js?v=01?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript"
            src="/js/jquery.lvyeSelect.js?v=02?v=${JS_AND_CSS_VER}"></script>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar">
        <span>
            ${(policy.policyID?? || policy.getPolicy == 0)?string('新增单个保单','修改保单')}
        </span>
    </div>
    <#if message??>
        <ul class="tbzy">
            <li>${message!}</li>
        </ul>
    </#if>
    <form name="formname" method="post" enctype="multipart/form-data"/>
    <input name="directBranchSale" type="hidden" value="${policy.directBranchSale!}"/>
    <input name="insureflag" type="hidden" value="${insureflag!}"/>
    <input id="temp_policyid" name="temp_policyid" type="hidden" value="${temp_policyid!}"/>
    <input id="branchid" name="branchid" type="hidden" value="${policy.branch!}"/>
    <input id="policyholder" name="policyholder" type="hidden" value="${policy.policyholder!}"/>
    <input id="policyid" name="policyid" type="hidden" value="${policy.policyID!}"/>
    <input id="selectedplan" name="selectedplan" type="hidden" value="${policy.planID!}"/>
    <input id="adultPrice1" name="adultPrice1" type="hidden" value="${policy.adultPrice!}"/>
    <input id="minorPrice1" name="minorPrice1" type="hidden" value="${policy.minorPrice!}"/>
    <input id="totalPremium" name="totalPremium" type="hidden" value="${policy.totalPremium!}"/>
    <input id="insurednum" name="insurednum" type="hidden" value="${policy.personnum!}"/>
    <input id="insur_cp_change" type="hidden"/>
    <input id="insur_jh_change" type="hidden"/>
    <input id="iactivedat_change" type="hidden"/>
    <input id="iexprdat_change" type="hidden"/>
    <input id="maxage" type="hidden">
    <input id="minage" type="hidden">
    <input name="isSendEmail" type="hidden" value=""/>
    <input id="uid" name="uid" type="hidden" value="${policy.uid!}"/>
    <div class="tbxx">
        <table>
            <#if policy.policyID != 0>
                <tr>
                    <td class="tbxxright">流水号码：</td>
                    <td colspan="2">${policy.policyID!}</td>
                </tr>
            </#if>
            <#if policy.directBranchSale != "1">
                <tr id="agencyNameTr">
                    <td class="tbxxright">客户名称：</td>
                    <td>
                        <select style="display:none;" id="agencyID" name="agencyID">
                            <option value="0">请选择</option>
                            <#list partnerlist as item>
                                <option isPayment="${item.isPayment!}" value="${item.agencyID!}"
                                        <#if policy.agencyID == item.agencyID>selected</#if>>${item.agencyName!}</option>
                            </#list>
                        </select>
                        <div id="lys" class="lys">
                            <input id="selectInput" name="agencyCopy" value="请选择" type="text" name="ly"
                                   autocomplete='off'>
                            <input id="backValue" value="" type="hidden">
                            <a href="javascript:;" id="selectBtn" class="selectBtn"></a>
                        </div>
                        <div id="agencyAjaxMsg" class="ajaxMsgImg"></div>
                        <p id="agencyAjaxMsg" class="ajaxMsgTest"></p>
                        <div class="mustVar">*</div>
                        <ul id="selectList" class="selectList"></ul>
                        <div style="width: 1px;height: 1px;float: left;margin-left: 10px;">
                            <div id="agencyData" style="position: absolute; top: 90px;" class="hideData">
                                <table class="hideDataList">
                                    <#if policy.directBranchSale != "1">
                                        <tr id="sales_channel" style="display:none;">
                                            <td class="hideLeft">销售渠道：</td>
                                            <td>请先选择客户名称</td>
                                        </tr>
                                    </#if>
                                    <tr id="inter_salesman_name" style="display:none;">
                                        <td class="hideLeft">销售人员：</td>
                                        <td></td>
                                    </tr>
                                    <tr id="inter_salesman_staffid" style="display:none;">
                                        <td class="hideLeft">销售工号：</td>
                                        <td></td>
                                    </tr>
                                    <tr id="inter_salesman_net" style="display:none;">
                                        <td class="hideLeft">销售网点：</td>
                                        <td></td>
                                    </tr>
                                    <tr id="inter_salesman_tel" style="display:none;">
                                        <td class="hideLeft">联系方式：</td>
                                        <td></td>
                                    </tr>
                                    <#if policy.directBranchSale != "1">
                                        <tr id="ins_agency_name" style="display:none;">
                                            <td class="hideLeft">机构名称：</td>
                                            <td></td>
                                        </tr>
                                        <tr id="ins_agency_add" style="display:none;">
                                            <td class="hideLeft">机构地址：</td>
                                            <td></td>
                                        </tr>
                                        <tr id="ins_agency_tel" style="display:none;">
                                            <td class="hideLeft">联系方式：</td>
                                            <td></td>
                                        </tr>
                                    </#if>
                                </table>
                                <div class="kdb"></div>
                                <div class="jdb"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <script type="text/javascript">
                    $(function () {	// 输入框、选择按钮、输出列表、返回值、数据
                        $('#lys').lvyeSelect($('#selectInput'), $('#selectBtn'), $('#selectList'), $('#backValue'), 'agencyID');
                    })
                </script>
            </#if>
            <#if policy.directBranchSale! == "1">
                <tr id="salesTypemark">
                    <td class="tbxxright">销售方式：</td>
                    <td colspan="2" class="tb_js">
                        <select id="salesType" name="salesType">
                            <#if securityUser.isPayment == "0">
                                <option value="">请选择</option>
                                <#list salestypelist as item>
                                    <option value="${item.dicCode!}"
                                            <#if policy.salesType! == item.dicCode>selected</#if>
                                    >${item.dicName!}</option>
                                </#list>
                            </#if>
                            <#if securityUser.isPayment == "1">
                                <option value="员工销售">员工销售</option>
                            </#if>
                        </select>
                        <div class="mustVar">*</div>
                    </td>
                </tr>
            </#if>
            <tr>
                <td class="tbxxright">销售人员：</td>
                <td colspan="2">
                    <select id="ins_salesman_id"
                            name="<#if policy.directBranchSale! == "1">salesManID</#if><#if policy.directBranchSale != "1">agencySalesManID</#if>"
                            style="display:none;">
                        <option value="0">请选择</option>
                    </select>
                    <div id="ins_salesmanSelect" class="lys">
                        <input id="ins_salesmanInput" name="salesManCopy" value="请选择" type="text" name="ly"
                               autocomplete='off'>
                        <input id="ins_salesmanBackValue" value="" type="hidden">
                        <a href="javascript:;" id="ins_salesmanSelectBtn" class="selectBtn"></a>
                    </div>
                    <div class="mustVar">*</div>
                    <ul id="ins_salesmanSelectList" class="selectList"></ul>
                    <div style="width: 1px;height: 1px;float: left;margin-left: 10px;">
                        <div id="salesmanData" style="position: absolute; top: 115px;" class="hideData">
                            <table class="hideDataList">
                                <tr id="ins_agency_tel" style="display:none;">
                                    <td>联系方式：</td>
                                    <td></td>
                                </tr>
                                <#if policy.directBranchSale == "1" >
                                    <tr id="ins_salesman_dep" style="display:none;">
                                        <td>部门：</td>
                                        <td></td>
                                    </tr>
                                    <tr id="ins_salesman_staffid" style="display:none;">
                                        <td>销售工号：</td>
                                        <td></td>
                                    </tr>
                                    <tr id="ins_salesman_net" style="display:none;">
                                        <td>销售网点：</td>
                                        <td></td>
                                    </tr>
                                </#if>
                                <tr id="ins_salesman_tel" style="display:none;">
                                    <td>联系方式：</td>
                                    <td></td>
                                </tr>
                            </table>
                            <div class="kdb"></div>
                            <div class="jdb"></div>
                        </div>
                    </div>
                </td>
            </tr>
            <#if policy.directBranchSale! != "1">
                <tr id="attnmark">
                    <td class="tbxxright">经办人：</td>
                    <td colspan="2"><input id="attn" class="tbxxInput" name="attn" type="text" value="${policy.attn!}"
                                           size="40"/></td>
                </tr>
                <tr id="attnContactmark">
                    <td class="tbxxright">联系方式：</td>
                    <td colspan="2"><input id="attnContact" class="tbxxInput" name="attnContact" type="text"
                                           value="${policy.attnContact!}" size="40"/></td>
                </tr>
            </#if>
            <tr id="linenomark">
                <td class="tbxxright">线路/团号：</td>
                <td colspan="2"><input id="lineno" class="tbxxInput" name="lineno" type="text" value="${policy.lineno!}"
                                       size="28"/></td>
            </tr>
            <tr>
                <td class="tbxxright">
                    <span style="display:inline-block;width:6em;">
                        <span style="float:left;">保单备注或<br>投保人地址</span><span
                                style="float:right;line-height:40px;">：
                        </span>
                    </span>
                </td>
                <td colspan="2">
                    <input id="remark" class="tbxxInput" name="remark" type="text" value="${policy.remark!}"
                           size="28"/>
                </td>
            </tr>
            <tr>
                <td class="tbxxright">产品名称：</td>
                <td >
                    <select id="insur_cp" name="productid">
                        <option value="">请选择</option>
                        <#if policy.directBranchSale! == "1" >
                            <#list productList! as item>
                                <option value="${item.productid!}" productType="${item.productType!}"
                                        firsttime="${item.firstTime!}"
                                        iswaats="${item.isWaats!}">${item.productname!}</option>
                            </#list>
                        </#if>
                    </select>
                    <div id="productAjaxMsg" class="ajaxMsgImg"></div>
                    <p id="productAjaxMsg" class="ajaxMsgTest"></p>
                    <div class="mustVar">*</div>
                </td>
                <td colspan="2" style="DISPLAY: none">
                    <select id="yesorno" name="yesorno">
                        <option value="">请选择</option>
                        <#list productList! as item>
                            <option value="${item.isWaats!}"
                                    <#if item.isWaats == policy.isWaats>selected</#if>>${item.isWaats!}</option>
                        </#list>
                    </select>
                </td>
                <td style="color: red;font-size: 14px;">请注意，我司目前不接受以乌克兰、俄罗斯和白俄罗斯为旅行目的地或途经地的投保申请。</td>
            </tr>
            <tr>
                <td class="tbxxright">产品计划：</td>
                <td>
                    <select id="insur_jh" name="planid">
                        <option value="">请选择</option>
                    </select>
                    <div class="mustVar">*</div>
                </td>
                <td></td>
            </tr>
            <tr style="display:none;">
                <td>是否标准产品：</td>
                <td colspan="2" class="tb_js"><input name="publicmark" id="publicmark" type="text" value=""/></td>
            </tr>
            <tr>
                <td class="tbxxright">保险时间：</td>
                <td colspan="2" class="tbsj">
                    <div><input name="iactivedat" id="iactivedat" value="${iactivedat!}" readonly="readonly"
                                onclick="showcalendar(event,this,1);"
                                onfocus="showcalendar(event, this,1);if(this.value=='0000-00-00')this.value=''"
                                type="text"/></div>
                    <div class="tb_q">起</div>
                    <div id="endtime">
                        <div>至</div>
                        <div><input name="iexprdat" id="iexprdat" value="${iexprdat!}" readonly="readonly"
                                    onclick="showcalendar(event,this,3);"
                                    onfocus="showcalendar(event, this,3);if(this.value=='0000-00-00')this.value=''"
                                    type="text"/></div>
                        <div>止</div>
                    </div>
                    <div class="tb_ts">共计<span id="zda">${policy.days!}</span>天</div>
                    <div><span id="errm">保险期最多<b id="qx0"></b>日</span></div>
                </td>
            </tr>
            <tr>
                <td class="tbxxright">人均保费(含适用的增值税)：</td>
                <td colspan="3" class="perCapitaBill tb_js"><span style="color:#000;">RMB<span>成人:&nbsp;<b
                                    id="adultPrice">&nbsp;&nbsp;${policy.adultPrice!}</b>元/人</span><span>儿童:&nbsp;<b
                                    id="minorPrice">&nbsp;&nbsp;${policy.minorPrice!}</b>元/人</span></span></td>
                <td colspan="3" class="MultiPerCapitaBill tb_js" style="display: none;"></td>
            </tr>
            <tr>
                <td class="tbxxright">总保费(含适用的增值税)：</td>
                <td colspan="2" class="tb_js">RMB<span><b id="zbf"></b>元</span></td>
            </tr>
        </table>
    </div>
    <div class="fltil">
        <span>投保人/被保人列表</span>
        <a id="dall" href="javascript:void(0)"></a><a hideFocus id="showbox" class="add" href="javascript:void(0)"></a>
    </div>
    <div class="tblidiv">
        <table class="tbli" id="tbli">
            <thead>
            <tr class="tbhead">
                <th align="center" class="bh" width="40">编号</th>
                <th align="center" width="60">*&nbsp;姓名</th>
                <th align="center" width="80">性别</th>
                <th align="center" width="70">*&nbsp;出生日期</th>
                <th align="center" width="80">*&nbsp;证件类型</th>
                <th align="center" width="70">*&nbsp;证件号码</th>
                <th align="center" width="70">身故受益人</th>
                <th align="center" width="70">邮箱(选填)</th>
                <th align="center" width="80">成人或儿童</th>
                <th align="center" width="70">保费(含适用的增值税)</th>
                <th align="center" width="70">标识</th>
                <th align="center" width="90" class="relation_cell">与投保人关系</th>
                <th align="center" width="70">被保人备注</th>
                <th align="center">操作</th>
            </tr>
            </thead>
            <tbody id="trbs">
            </tbody>
        </table>
        <div class="fltil" style="background:white;">
            <input id="excelUploadBtn" name="excelUploadBtn" type="button" value="上传"/>
            <input id="excelfile" type="text" readonly/>
            <input id="excelSelect" name="excelSelect" type="button" value="选择文件"/>
            <span style="float:right;">多个被保险人，请下载<a href="/download?file=sale/Travelye_Template.xlsx">EXCEL文件</a>按要求填写后在这里上传。</span>
        </div>
    </div>
    <div class="fltil"><span>温馨提示</span></div>
    <div class="warmmind">
        <a>1、若被保险人为未成年人，请再添加其父母或监护人为投保人。</a><br/>
        <a>2、如选择境外旅游保险，“证件类型”请选择“护照”，“姓名”和“证件号码”应与护照上的信息保持一致。</a><br/>
        <a>3、如选择境内旅游保险，中国大陆居民请填写身份证号。其它国家和地区的居民请使用护照号码或其它在中国境内被认为有效的身份证件。</a><br/>
        <a>4、未领取合法身份证件的婴幼儿，证件类型请选择“其他”，证件号码请填写形如“20080101”格式的出生日期代替身份证件号码。</a><br/>
        <a>5、本人明白：任何年龄在18周岁以下的被保险人，如果其以死亡为给付保险金条件的保险金额（包括在所有商业保险公司所购买的保险）超出中国保监会所规定的限额（即不满10周岁的，为人民币20万元；已满10周岁但未满18周岁的，为人民币50万元）须特别告知，否则保险公司可能对超出限额的部分不承担保险责任。</a><br/>
        <a>6、本保障计划项下应付保险费含适用的增值税。</a><br/>
        <a style="color:red">7、特别声明：</a><br/>
        <a style="color:red">
            自2020年1月25日零点之后投保美亚财险旅行险的，新型冠状病毒肺炎疫情为已知风险。根据美亚财险旅行险项下旅行延误、旅行变更（取消或缩短）、旅行取消和旅行缩短四种附加保障对应保险合同条款的约定，该已知风险
            属于责任免除，对于由该已知风险导致的前述附加保障合同项下的保险事故，美亚财险不承担任何保险责任。</a><br/>
        <a style="color:red;background-color: yellow">8、俄罗斯、乌克兰旅行的特别提示</a><br/>
        <a style="color:red;background-color: yellow">请注意，对于往返或在俄罗斯和乌克兰军事冲突波及地区的旅行，旅行保险的承保范围和保障利益以及AIG
            Travel提供旅行相关特定援助服务的能力可能会受到影响。</a>
    </div>
    <div class="tbsm">投保声明/主要免除责任/重要提示/特别提醒 <a id="sm_btn" href="javascript:void(0)">点击查看</a></div>
    <div id="showsm" class="showsm"></div>
    <div class="tbtk"><input name="" id="SendEmail" type="checkbox"/>将电子保单发送到被保险人的电子邮箱。</div>
    <div class="tbtk"><input id="suredom" type="checkbox" value=""/>已确认被保险人一年中在境内的居住时间累计达到或超过183天。</div>
    <div class="tbtk"><input name="" id="suretk" type="checkbox" value=""/>我已阅读并同意上述声明及<a id="durl" href="#">保险合同条款</a>
        ，并知晓所有保险责任均以保险合同所载为准，所有保险合同的变更均需经保险公司正式程序修改或批注。
    </div>
    <div class="btn_tj">
        <input type="button" value="下一步" id="confirmpolicy" style="margin-right:10px;"/>
        <#if policy.policyID! != 0>
            <input type="button" value="取  消" onclick="javascript:history.go(-1);"/>
        </#if>
    </div>
</div>

<!--添加单个保险人/修改被保险人信息窗口-->
<div id="tb_box" class="tb_box">
    <iframe id="ifh" frameborder="0"></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="b_til" class="b_til"><span>添加/修改窗口</span><a id="clos_btn"
                                                                         href="javascript:void(0)"><b>×</b></a></div>
                    <form id="resinfo" name="resinfo" action="" method="get">
                        <div class="tip">
                            <div class="info"></div>
                            <div class="info_t">添加成功，您可以 继续添加 或 <a id="clos_btn" href="javascript:void(0)">离开</a>!</div>
                        </div>
                        <table id="holderattr" class="tb_fk">
                            <tbody>
                            <tr>
                                <td class="tbxxright">投保人属性：</td>
                                <td>
                                    <input type="radio" name="policyholder" value="person"/> 自然人
                                    <input type="radio" name="policyholder" value="company"/><a id="companyDiv"
                                                                                                style="color:black">企业</a>
                                </td>
                                <td><span id="applicantTypeNotice" style="color:red;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如您添加的是投保人，请在此处选择投保人属性。如您添加的是被保险人，忽略此项。</span>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                        <table class="tb_fk">
                            <tr>
                                <td style="display:none;"><input id="insuredid" name="insuredid" value="0"/></td>
                            </tr>
                            <tr>
                                <td class="tbxxright">*&nbsp;<label id="cname">姓名</label>：</td>
                                <td><input id="phname" type="text"/></td>
                                <#--                                <td colspan="2">为配合反洗钱监管，您在购买保险产品时，可自愿补充更多信息</td>-->
                            </tr>
                            <#--                            <tr>-->
                            <#--                                <td class="tbxxright">*&nbsp;<label id="ccitizen">国籍</label>：</td>-->
                            <#--                                <td><input id="phcitizen" type="text"/></td>-->
                            <#--                                <td class="tbxxright"><label>省/自治区/直辖市</label>：</td>-->
                            <#--                                <td><input id="phaddress" type="text"/></td>-->
                            <#--                            </tr>-->
                            <tr>
                                <td class="tbxxright">*&nbsp;证件类型：</td>
                                <td>
                                    <label id="ctype"></label>
                                    <select id="phiddtype">
                                        <option value="">请选择</option>
                                        <option value="身份证">身份证</option>
                                        <option value="护照">护照</option>
                                        <option value="军官证">军官证</option>
                                        <option value="港澳回乡证或台胞证">港澳回乡证或台胞证</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </td>
                                <#--                                <td class="tbxxright"><label>县/市/区</label>：</td>-->
                                <#--                                <td><input id="phoccupation" type="text"/></td>-->
                            </tr>
                            <tr>
                                <td class="tbxxright">*&nbsp;证件号码：</td>
                                <td><input id="phiddnum" type="text"/></td>
                                <#--                                <td class="tbxxright"><label>地址</label>：</td>-->
                                <#--                                <td><input id="phaddress" type="text"/></td>-->

                            </tr>
                            <#--                            <tr>-->
                            <#--                                <td class="tbxxright">证件生效日：</td>-->
                            <#--                                <td><input id="phiddeffective" type="text"/></td>-->
                            <#--                            </tr>-->
                            <#--                            <tr>-->
                            <#--                                <td class="tbxxright">证件失效日：</td>-->
                            <#--                                <td><input id="phiddexpire" type="text"/></td>-->
                            <#--                                <td class="tbxxright"><label>职业</label>：</td>-->
                            <#--                                <td><select id="occupation" type="text">-->
                            <#--                                        <option value="">请选择</option>-->
                            <#--                                        <option value="">执行官，主席</option>-->
                            <#--                                        <option value="">会计师，律师</option>-->
                            <#--                                        <option value="">建筑师，工程师</option>-->
                            <#--                                        <option value="">咨询，专业人员</option>-->
                            <#--                                        <option value="">出纳，文员</option>-->
                            <#--                                        <option value="">秘书</option>-->
                            <#--                                        <option value="">销售员</option>-->
                            <#--                                        <option value="">核保</option>-->
                            <#--                                        <option value="">农民，渔民</option>-->
                            <#--                                        <option value="">领班，技师</option>-->
                            <#--                                        <option value="">工厂工人，体力劳动者</option>-->
                            <#--                                        <option value="">公司司机</option>-->
                            <#--                                        <option value="">警察，军人</option>-->
                            <#--                                        <option value="">其它</option>-->
                            <#--                                        <option value="">失业、无业、退休等</option>-->
                            <#--                                    </select></td>-->

                            <#--                            </tr>-->

                            <tr>
                                <td class="tbxxright"><label id="cgend">性别</label>：</td>
                                <td>
                                    <select id="phgend" type="text">
                                        <option value="">请选择</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                </td>
                                如未指定则默认为“法定Legal Heir”
                                <#--                                <td class="tbxxright"><label>收入(人民币:万元/年)</label>：</td>-->
                                <#--                                <td><input id="phsalary" type="text"/></td>-->
                            </tr>
                            <tr>
                                <td class="tbxxright">*&nbsp;出生日期：</td>
                                <td><input id="birth" type="text"/> <span class="rqgs">&nbsp;格式：1980-12-25</span><input
                                            id="births" name="" value="" type="hidden"></td>
                                <#--                                <td class="tbxxright"><label>工作单位名称</label>：</td>-->
                                <#--                                <td><input id="phaddress" type="text"/></td>-->
                            </tr>
                            <tr style="display:none;">
                                <td class="tbxxright">&nbsp;与投保人关系：</td>
                                <td><input id="relationship" type="text"/></td>
                            </tr>
                            <tr>
                                <td class="tbxxright">&nbsp;&nbsp;电子邮箱：</td>
                                <td><input id="emailAddr" type="text"/></td>
                            </tr>
                            <#--                            <tr>-->
                            <#--                                <td class="tbxxright">*联系方式：</td>-->
                            <#--                                <td><input id="phone" type="text"/></td>-->
                            <#--                            </tr>-->
                            <tr>
                                <td class="tbxxright">被保人备注：</td>
                                <td><input id="remark_" type="text"/></td>
                            </tr>
                        </table>
                        <table id="cbirth" class="tb_fk">
                            <tr>
                                <td class="tbxxright">身故受益人：</td>
                                <td style="color:red;">如未指定则默认为“法定Legal Heir”</td>
                            </tr>
                            <tr>
                                <td class="tbxxright">指定受益人：</td>
                                <td>
                                    <table class="bx_rate_bene" cellspacing="">
                                        <thead>
                                        <tr>
                                            <th>姓名</th>
                                            <#--                                            <th>国籍</th>-->
                                            <th>性别</th>
                                            <th>证件类型</th>
                                            <th>证件号码</th>
                                            <#--                                            <th>证件有效期</th>-->
                                            <th>与被保险人关系</th>
                                            <th>受益份额</th>
                                            <th><a id="alldelebene" href="javascript:void(0)">删除全部</a></th>
                                        </tr>
                                        </thead>
                                        <tbody id="bene"></tbody>
                                        <tr class="add_rate_bene">
                                            <td><input id="benename" type="text" style="width:80px;"/></td>
                                            <#--                                            <td><input id="bnationnality" type="text" style="width:40px;"/></td>-->
                                            <td><select>
                                                    <option value="">请选择</option>
                                                    <option value="男">男</option>
                                                    <option value="女">女</option>
                                                </select></td>
                                            <td>
                                                <select id="benetype">
                                                    <option value="">请选择</option>
                                                    <option value="1">身份证</option>
                                                    <option value="3">护照</option>
                                                    <option value="6">其它</option>
                                                </select>
                                            </td>
                                            <td><input id="benenumber" type="text" style="width:160px;"/></td>
                                            <#--                                            <td><input id="bidexpiry" type="text" style="width:40px;"/></td>-->
                                            <td>
                                                <select id="benerelation">
                                                    <option value="">请选择</option>
                                                    <option value="本人">本人</option>
                                                    <option value="配偶">配偶</option>
                                                    <option value="父母">父母</option>
                                                    <option value="子女">子女</option>
                                                    <option value="兄弟姐妹">兄弟姐妹</option>
                                                    <option value="雇主">雇主</option>
                                                    <option value="雇员">雇员</option>
                                                    <option value="祖父母、外祖父母">祖父母、外祖父母</option>
                                                    <option value="祖孙、外祖孙">祖孙、外祖孙</option>
                                                    <option value="监护人">监护人</option>
                                                    <option value="被监护人">被监护人</option>
                                                    <option value="朋友">朋友</option>
                                                    <option value="未知">未知</option>
                                                    <option value="其它">其它</option>
                                                </select>
                                            </td>
                                            <td><input name="benepercent_count" id="benepercent" type="text"
                                                       style="width:30px;"/>%
                                            </td>
                                            <td><img id="addbene" src="/img/add2.gif"/></td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            <#--                            <tr>-->
                            <#--                                <td colspan="4">-->
                            <#--                                    <table class="bx_rate_bene" cellspacing="">-->
                            <#--                                        <thead>-->
                            <#--                                        <tr>-->
                            <#--                                            <th>手机号码</th>-->
                            <#--                                            <th>省/自治区/直辖市</th>-->
                            <#--                                            <th>县/市/区</th>-->
                            <#--                                            <th>地址</th>-->
                            <#--                                            <th>职业</th>-->
                            <#--                                            <th>收入（人命币：万/年）</th>-->
                            <#--                                            <th>工作单位</th>-->
                            <#--                                        </tr>-->
                            <#--                                        </thead>-->
                            <#--                                        <tr class="add_rate_bene">-->
                            <#--                                            <td><input id="bphone" type="text" style="width:40px;"/></td>-->
                            <#--                                            <td><input id="province" type="text" style="width:80px;"/></td>-->
                            <#--                                            <td><input id="city" type="text" style="width:40px;"/></td>-->
                            <#--                                            <td><input id="address" type="text" style="width:40px;"/></td>-->
                            <#--                                            <td>-->
                            <#--                                                <select id="boccupation">-->
                            <#--                                                    <option value="">请选择</option>-->
                            <#--                                                    <option value="">执行官，主席</option>-->
                            <#--                                                    <option value="">会计师，律师</option>-->
                            <#--                                                    <option value="">建筑师，工程师</option>-->
                            <#--                                                    <option value="">咨询，专业人员</option>-->
                            <#--                                                    <option value="">出纳，文员</option>-->
                            <#--                                                    <option value="">秘书</option>-->
                            <#--                                                    <option value="">销售员</option>-->
                            <#--                                                    <option value="">核保</option>-->
                            <#--                                                    <option value="">农民，渔民</option>-->
                            <#--                                                    <option value="">领班，技师</option>-->
                            <#--                                                    <option value="">工厂工人，体力劳动者</option>-->
                            <#--                                                    <option value="">公司司机</option>-->
                            <#--                                                    <option value="">警察，军人</option>-->
                            <#--                                                    <option value="">其它</option>-->
                            <#--                                                    <option value="">失业、无业、退休等</option>-->
                            <#--                                                </select>-->
                            <#--                                            </td>-->
                            <#--                                            <td><input id="bsalary" type="text" style="width:160px;"/></td>-->
                            <#--                                            <td><input id="bcompany" type="text" style="width:80px;"/></td>-->
                            <#--                                            <td><img id="addbene" src="/img/add2.gif"/></td>-->
                            <#--                                        </tr>-->
                            <#--                                    </table>-->
                            <#--                                </td>-->
                            <#--                            </tr>-->
                            <tr id="IsInsuredFlag2">
                                <td class="tbxxright">投保/被保人：</td>
                                <td>
                                    <select id="IsInsuredFlag">
                                        <option value="2">被保险人</option>
                                        <option value="0">投保人</option>
                                        <option value="1">投保人兼被保险人</option>
                                    </select>
                                </td>
                            </tr>
                            <tr id="relationship_main" style="display: none;">
                                <td align="right">与投保人关系：</td>
                                <td>
                                    <select id="relationship_sub">
                                        <option value="0">请选择</option>
                                        <option value="本人 Policyholder">本人 Policyholder</option>
                                        <option vlaue="配偶 Spouse">配偶 Spouse</option>
                                        <option value="父母 Parents">父母 Parents</option>
                                        <option value="子女 Child">子女 Child</option>
                                        <option value="其他 Other">其他 Other</option>
                                    </select>
                                </td>
                            </tr>
                        </table>
                        <div class="tb_btn"><a id="add_btn" href="javascript:void(0)"><img src="/img/add.gif"/></a><a
                                    id="clos_btn" href="javascript:void(0)"><img src="/img/clo.gif"/></a></div>
                    </form>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!--box end-->
<input id="windowposition" type="hidden" value="1"/>
<!--jhbene -->
<div id="jhbene" class="jhrate" onmousedown=MDown(jhbene)>
    <div class="tip_til">
        <span id="jh_bene"></span>
        <a id="clos_rat" href="javascript:void(0)"><b>×</b></a>
    </div>
    <div id="beneinfo" class="wrat">法定Legal Heir</div>
    <table id="fkbene" class="bx_rate bx_rate2">
        <thead id="benehead">
        <tr>
            <th>姓名</th>
            <th>证件类型</th>
            <th>证件号码</th>
            <th>与被保险人关系</th>
            <th>受益份额</th>
        </tr>
        </thead>
        <tbody id="bene2"></tbody>
    </table>
</div>
<!--jhbene end-->

<!-- tipbox -->
<div id="msg" class="tb_box">
    <iframe id="ifhmsg" frameborder="0"></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="msg_ban" class="b_til">
                        <span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                        <div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a
                                id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end-->
<script language="javascript">
    var agencySalesManID = ${policy.agencySalesManID!};
    var salesManID = ${policy.salesManID!};
    var bir = $("#iactivedat").val().split(" ");
    var zday = bir[0].split("-")[2];
    var zmonth = bir[0].split("-")[1] - 1;
    var zyear = bir[0].split("-")[0];
    var isSendEmail = "${policy.isSendEmail!}";
    var backwardPolicy = ${sjisUtil.hasAction("backwardPolicy")?c}; //判断是否能倒签单
</script>
<script type="text/javascript" src="/js/common.js?v=${JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/insure.js?v=${JS_AND_CSS_VER}"></script>
<#if sjisUtil.hasAction("backwardPolicy")>
    <script type="text/javascript" src="/js/beaurl_nbx_admin.js?v=${JS_AND_CSS_VER}"></script>
<#else>
    <script type="text/javascript" src="/js/beaurl_nbx.js?v=${JS_AND_CSS_VER}"></script>
</#if>
<script language="javascript">
    initselect("${policy.productID!}", "${policy.planID!}", "${iactivedat!}", "${iexprdat!}");
    <#if excelPolicy??>
    policyinfo(${excelPolicy.planCode!}, ${excelPolicy.lineno!}, ${excelPolicy.effectiveDate!}, ${excelPolicy.expiryDate!},
        ${policy.attn!}, ${excelPolicy.telephone!}, ${excelPolicy.remark!});
    </#if>
    <#list userlist! as item>
    initinfo("${item?counter}", "${item.insuredname!}", "${item.gend!}",
        "${dateUtil.format(item.birthday)}", "${item.insuredIdNoType!}",
        "${item.insuredIdNo!}", "${item.deathBeneficiary!}", "${item.relationship!}", " ${item.emailAddr!}",
        "${item.policyStatus!}", "${item.typeCD!}", " ${item.premium!}", "${item.isInsuredFlag!}",
        "${item.insuredID!}", "${item.modifyFlag!}", "${item.recordStatus!}", "${item.remark!}");
    </#list>
    var userlist = "${userlist?size}";
    <#if excelUser??>
    var excelUser = "${excelUser?size}";
    </#if>

</script>

<#include "../common/foot.html">
</body>
</html>

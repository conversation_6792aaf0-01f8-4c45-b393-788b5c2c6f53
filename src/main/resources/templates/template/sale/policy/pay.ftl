<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css"/>
    <style type="text/css">
        #creditBank {
            margin: 10px 50px;
            clear: both;
            text-align: left;
            overflow: hidden;
        }

        #creditBank_ul li {
            list-style-type: none;
            float: left;
            line-height: 3em;
            height: 3em;
            width: 200px;
            vertical-align: middle;
        }

        #creditBank_ul li input, #creditBank_ul li label {
            line-height: 20px;
            height: 20px;
        }

        #creditBank_ul li img {
            border: none;
            width: 93px;
            height: 20px;
            margin-left: 5px;
        }
    </style>
    <script language="javascript" src="/js/jquery.js"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
    <script type="text/javascript">
        function doEdit() {
            document.formname.action = "insure";
            document.formname.submit();
        }
    </script>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar"><span>在线支付确认信息</span></div>
    <table class="bt_tab" border="1" bordercolor="#999999">
        <tr>
            <td colspan="2">
                <h1>${(product.productname)!}<br/>${(product.englishname)!}</h1>
            </td>
        </tr>
        <tr>
            <th>保险凭证号码</th>
            <td>提交保单后，获得保险凭证号码</td>
        </tr>
        <tr>
            <th>线路/团号</th>
            <td>${(policy.lineno)!}</td>
        </tr>
        <tr>
            <th>经办人</th>
            <td>${(policy.attn)!}</td>
        </tr>
        <tr>
            <th>保险计划 <span>Insurance Plan</span></th>
            <td>${(plan.plancode)!}</td>
        </tr>
        <tr>
            <th>保险合同生效日 <span>Policy Effective Date</span></th>
            <td>${(policy.strEffectiveDate)!} （北京时间 Beijing Time）</td>
        </tr>
        <tr>
            <th>保险合同满期日 <span>Policy Expiry Date</span></th>
            <td>${(policy.strExpiryDate)!} （北京时间 Beijing Time）</td>
        </tr>
        <tr>
            <th>投保人 <span>Policyholder</span></th>
            <td>${(policy.policyholder)!}</td>
        </tr>
        <tr>
            <th>总保险费(含适用的增值税) <span>Total Premium</span></th>
            <td>RMB ${(policy.strTotalPremium)!}</td>
        </tr>
    </table>
    <input id="windowposition" type="hidden" value="2"/>
    <form name="formname" method="post">
        <input id="salesManID" name="salesManID" type="hidden" value="${policy.salesManID!}"/>
        <input id="agencySalesManID" name="agencySalesManID" type="hidden" value="${policy.agencySalesManID!}"/>
        <input id="directBranchSale" name="directBranchSale" type="hidden" value="${policy.directBranchSale!}"/>
        <input id="branchid" name="branchid" type="hidden" value="${policy.branch!}"/>
        <input id="agencyID" name="agencyID" type="hidden" value="${policy.agencyID!}"/>
        <input name="policyid" type="hidden" value="${policy.policyID!}"/>
        <input name="planid" type="hidden" value="${policy.planID!}"/>
        <input name="productid" type="hidden" value="${policy.productID!}"/>
        <input name="lineno" type="hidden" value="${policy.lineno!}"/>
        <input name="remark" type="hidden" value="${policy.remark!}"/>
        <input name="attn" type="hidden" value="${policy.attn!}"/>
        <input name="iactivedat" type="hidden" value="${iactivedat!}"/>
        <input name="iexprdat" type="hidden" value="${iexprdat!}"/>
        <input name="totalPremium" type="hidden" value="${policy.totalPremium!}"/>
        <input id="insurednum" name="insurednum" type="hidden" value="${policy.personnum!}"/>
        <input id="policyholder" type="hidden" value="${policy.policyholder!}"/>
        <input id="salesType" name="salesType" type="hidden" value="${policy.salesType!}"/>
        <input id="attnContact" name="attnContact" type="hidden" value="${policy.attnContact!}"/>
        <input id="salesMan" name="salesMan" type="hidden" value="${policy.salesMan!}"/>
        <input id="employeeID" name="employeeID" type="hidden" value="${policy.employeeID!}"/>
        <input id="salesContact" name="salesContact" type="hidden" value="${policy.salesContact!}"/>
        <input id="qualificationNO" name="qualificationNO" type="hidden" value="${policy.qualificationNO!}"/>
        <input id="salesLocation" name="salesLocation" type="hidden" value="${policy.salesLocation!}"/>
        <input id="department" name="department" type="hidden" value="${policy.department!}"/>
        <input id="isSendEmail" name="isSendEmail" type="hidden" value="${policy.isSendEmail!}"/>
        <#list userlist! as item>
            <input name="insuredid_${item?counter}" type="hidden" value="${item.insuredID!}"/>
            <input name="phname_${item?counter}" type="hidden" value="${item.insuredname!}"/>
            <input name="phgend_${item?counter}" type="hidden" value="${item.insuredgend!}"/>
            <input name="birth_${item?counter}" type="hidden" value="${item.birthdaystr!}"/>
            <input name="phiddtype_${item?counter}" type="hidden" value="${item.insuredIdNoType!}"/>
            <input name="phiddnum_${item?counter}" type="hidden" value="${item.insuredIdNo!}"/>
            <input name="relationship_${item?counter}" type="hidden" value="${item.relationship!}"/>
            <input name="emailAddr_${item?counter}" type="hidden" value="${item.emailAddr!}"/>
            <input name="remark_${item?counter}" type="hidden" value="${item.remark!}"/>
            <input name="deathBeneficiary_${item?counter}" type="hidden" value="${item.deathBeneficiary!}"/>
            <input name="premium_${item?counter}" type="hidden" value="${item.premium!}"/>
            <input name="policyStatus_${item?counter}" type="hidden" value="${item.policyStatus!}"/>
            <input name="modifyflag_${item?counter}" type="hidden" value="${item.modifyFlag!}"/>
            <input name="IsInsuredFlag_${item?counter}" type="hidden" value="${item.isInsuredFlag!}"/>
        </#list>

        <div class="btn_tj"><input id="alipay_confirm" type="button" onclick="doSubmit();" value="快钱支付"></div>

        <#include "../common/foot.html">
</body>
<script language="javascript" src="/js/common.js?v=01"></script>
</html>
<div id="zhezhao_pay"></div>
<div id="container_pay">
    <div id="title_pay"></div>
    <div id="content_pay">
        <table width="420">
            <tr>
                <td height="60" width="80"></td>
                <td style="font-size:16px; color:#006;" valign="middle"><b>请您在新打开的支付页面上完成支付</b></td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="0" width="420">
            <tr>
                <td width="10"></td>
                <td height="25" align="left">支付完成前请不要关闭此窗口</td>
            </tr>
            <tr>
                <td></td>
                <td height="25" align="left">完成支付后根据您的情况点击下面的按钮</td>
            </tr>
            <tr>
                <td></td>
                <td height="25" align="left">如果支付遇到问题请您尽快联系美亚客服或点击重新支付按钮</td>
            </tr>
            <tr>
                <td height="25"></td>
            </tr>
        </table>
        <table width="420" id="button_pay">
            <tr>
                <td height="30" valign="middle" align="center"><input type="button" id="pay_success"
                                                                      onclick="doComplete();" value="已完成支付"/></td>
                <td align="center"><input type="button" value="重新支付" id="pay_fail" onclick="doRest();"/></td>
            </tr>
        </table>
    </div>
</div>
<script type="text/javascript">
    function doSubmit() {
        window.open("/sale/policy/redirectBill99?policyid=${(policy.policyID)!}", "newwindow");
        document.getElementById("alipay_confirm").disabled = true;
        scor_center($("#container_pay"));
    }

    function doComplete() {
        document.formname.action = "/sale/policy/view?policyid=${(policy.policyID)!}";
        document.formname.submit();

    }

    function doRest() {
        $("#container_pay").hide();
        $("#zhezhao_pay").hide();
        $("#leftmenu").css("display", "");
        document.getElementById("alipay_confirm").disabled = false;
    }

    function scor_center(container) {
        var html_width = document.documentElement.clientWidth;
        var html_height = parseInt(document.body.clientHeight) + parseInt($(document).scrollTop());
        var user_power_width = container.css("width");
        var user_power_height = container.css("height");
        var user_power_top = (parseInt(html_height) - parseInt(user_power_height)) / 2;
        var user_power_left = (parseInt(html_width) - parseInt(user_power_width)) / 2;
        container.css("top", user_power_top);
        container.css("left", user_power_left);
        container.show();
        $("#leftmenu").css("display", "none");
    }
</script>
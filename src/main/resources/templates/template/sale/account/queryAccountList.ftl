<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=${JS_AND_CSS_VER}" rel="stylesheet" type="text/css"/>
    <script language="javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.core.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.excheck.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/checkBox.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript">
        function pageaction(i) {
            document.conditionform.action = "/sale/account/queryAccountList?accountID=" + accid + "&paginalcount=" + pagcount + "&pagestart=" + i + "&accountName=" + accountname;
            document.conditionform.submit();
        }

        //跳转
        function goPage() {
            var num = document.all["goNumber"].value.replace(/\s/g, "");
            var lastpage = "${(page.lastPage)!}";
            if (num == "") {
                alert('请输入要跳转的页数');
                return;
            }
            if (parseInt(num) > parseInt(lastpage)) {
                alert('超出了最大页数，最大页数为' + lastpage);
                return;
            }
            pageaction(num);
        }
    </script>
</head>
<body>
<div class="content2">
    <div id="contentbar" class="contentbar"><span>交易明细</span></div>
    <form name="conditionform" action="" method="post">
        <div id="tbxx" class="tbxx">
            <table>
                <tr>
                    <td class="tbxxright">交易日期：</td>
                    <td width="140">
                        <input name="startDate" id="iactivedat" value="${(condition.startDate)!}" readonly="readonly"
                               onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">------到：</td>
                    <td>
                        <input name="endDate" id="iactivedat" value="${(condition.endDate)!}" readonly="readonly"
                               onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>
                </tr>
            </table>
        </div>
    </form>
    <div class="fltil">
        <span>当前账户：</span><span id="accountname" style="line-height:22px;color:#e99411;"> </span>
        <input class="dc_data" type="button" value="清 空" onclick="clearall();"/>
        <input class="dc_data" type="button" value="查 询" id="doSubmit2"/>
    </div>
    <div class="tableContainer" id="tableContainer" style="height:470px;">
        <table class="tbli">
            <thead>
            <tr class="tbhead">
                <th>流水号</th>
                <th>交易金额</th>
                <th>余额</th>
                <th>交易流水号</th>
                <th>交易时间</th>
                <th>充值用户</th>
                <th>备注</th>
            </tr>
            </thead>
            <tbody id="trbs">
            <#list list as item>
                <tr>
                    <td style="text-indent:10px;">${item.accountListID!}</td>
                    <td>${item.fee!}</td>
                    <td>${item.balance!}</td>
                    <td>${item.feeNO!}</td>
                    <td>${dateUtil.formatyyyyMMddHHmmss(item.createTime)}</td>
                    <td>${item.operator!}</td>
                    <td>${item.remark!}</td>
                </tr>
            </#list>
            </tbody>
        </table>
    </div>

    <div class="fy_box">
        <div class="page_fy">
            <#if ((page.pageStart)! > 1)>
                <a class="page_pre" href="javascript:pageaction('${(page.priPage)!}')">上一页</a>
            </#if>
            <#if ((page.pageStart)! < (page.lastPage)!)>
                <a class="page_nex" href="javascript:pageaction('${(page.nextPage)!}')">下一页</a>
            </#if>
            <script>
                var k = 0;  //临时记录数
                var largetcount = 10;    //每页显示多少个      10
                var cp = ${(page.pageStart)!}; //当前页         98
                var pagecount = ${(page.lastPage)!}; //页面总数 100
                var selfcnt = Math.floor(largetcount / 2);     //5
                var lcnt = Math.floor(pagecount / 2);          //50
                var startp = cp - selfcnt;                     //93
                if (pagecount - cp < selfcnt) {
                    startp = startp - (selfcnt - (pagecount - cp));
                }
                for (var i = startp; i <= cp; i++) {
                    if (i < 1 || i > cp || i > pagecount)
                        continue;
                    k = k + 1;
                    if (i == cp)
                        document.write('<a class="page_dq">' + i + '</a>');
                    else
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>')
                }
                if (cp < pagecount && k < largetcount) {
                    for (var i = cp + 1; i <= pagecount; i++) {
                        if (k > largetcount)
                            break;
                        k = k + 1;
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>');
                    }
                }
            </script>
            <div>第 <input name="goNumber" class="tex" type="text"/> 页 <input type="submit" class="submit" value="跳转"
                                                                             onClick="goPage()"></div>
        </div>
    </div>
</div>

<!-- tipbox -->
<div id="msg" class="tb_box">
    <iframe id="ifhmsg" frameborder="0"></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="msg_ban" class="b_til">
                        <span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                        <div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a
                                id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end-->
<#include "../common/foot.html">
</body>
<script language="javascript">

    //服务器时间
    //此处决定日历默认时间
    var d = new Date();
    var zyear = d.getFullYear();
    var zmonth = d.getMonth();
    //var zday=1;
    var zday = d.getDate();
    $('#myAcc_sub').click(function () {
        document.myAccountForm.action = "";
        document.myAccountForm.submit();
    })

    var url = location.href;
    url = decodeURI(url);
    var accid = url.split("&")[0];
    accid = accid.split("=")[1];
    var pagcount = url.split("&")[1];
    pagcount = pagcount.split("=")[1];
    var ary = url.split("&")[3];

    var accountname = ary.split("=")[1];
    accountname = decodeURI(accountname);
    $("#accountname").append(accountname);
    $('#doSubmit2').click(function () {
        document.conditionform.action = "/sale/account/queryAccountList?accountID=" + accid + "&paginalcount=16&pagestart=1&accountName=" + accountname;
        document.conditionform.submit();
    })
</script>

<script language="javascript" src="/js/common.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/mag_cp.js?v=${JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/beaurl_nall.js?v=${JS_AND_CSS_VER}"></script>

</html>

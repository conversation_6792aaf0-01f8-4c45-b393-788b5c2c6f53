<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <link href="/css/chartis.css?v=02.css?v=${JS_AND_CSS_VER}" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="/css/zTreeStyle.css?v=${JS_AND_CSS_VER}" type="text/css">
    <script language="javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
    <script language="javascript" src="/js/jquery.easydrag.handler.beta2.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.core.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.excheck.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript" src="/js/checkBox.js?v=${JS_AND_CSS_VER}"></script>
    <script type="text/javascript">
        function pageaction(i) {
            document.conditionform.action = "/sale/query/tongji?pagestart=" + i;
            document.conditionform.submit();
        }

        //跳转
        function goPage() {
            var num = document.all["goNumber"].value.replace(/\s/g, "");
            var lastpage = "${(page.lastPage)!}";
            if (num == "") {
                alert('请输入要跳转的页数');
                return;
            }
            if (parseInt(num) > parseInt(lastpage)) {
                alert('超出了最大页数，最大页数为' + lastpage);
                return;
            }
            pageaction(num);
        }

        function doExport() {
            document.conditionform.action = "exportTongji";
            document.conditionform.submit();
        }
    </script>
</head>

<body>
<#include "../common/top.ftl">
<div class="content">
    <div id="contentbar" class="contentbar"><span>投保查询 > 按保单查询</span></div>
    <form name="conditionform" action="" method="post">
        <input name="paginalcount" type="hidden" value="20"/>
        <div class="tbxx">
            <table>
                <tr>
                    <td class="tbxxright">签单日期：</td>
                    <td width="140">
                        <input name="issueBeginDate" id="issueBeginDate" value="${condition.issueBeginDate!}"
                               readonly="readonly" onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">-------到：</td>
                    <td>
                        <input name="issueEndDate" id="issueEndDate" value="${condition.issueEndDate!}"
                               readonly="readonly" onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">线路/团号：</td>
                    <td><input name="lineno" type="text" value="${condition.lineno!}"/></td>
                </tr>
                <tr>
                    <td class="tbxxright">生效日期：</td>
                    <td width="140">
                        <input name="effectiveBeginDate" id="effectiveBeginDate"
                               value="${condition.effectiveBeginDate!}" readonly="readonly"
                               onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">-------到：</td>
                    <td>
                        <input name="effectiveEndDate" id="effectiveEndDate" value="${condition.effectiveEndDate!}"
                               readonly="readonly" onclick="showcalendar(event,this,false,true);"
                               onfocus="showcalendar(event,this,false,true);if(this.value=='0000-00-00')this.value=''"
                               type="text"/>
                    </td>

                    <td class="tbxxright">凭证号码：</td>
                    <td><input name="policyID" type="text" value="${condition.policyID!}"/></td>
                </tr>
                <tr>
                    <td class="tbxxright">保单备注：</td>
                    <td><input name="remark" type="text" value="${condition.remark!}"/></td>

                    <td class="tbxxright">保单号码：</td>
                    <td><input name="policyNo" type="text" value="${condition.policyNo!}"/></td>
                    <td class="tbxxright">部门：</td>
                    <td colspan="1">
                        <input type="text" value="默认全选" id="treeInput" readonly/>
                        <div id="dptTreeBox" style="display:none;">
                            <ul id="dptTree" class="ztree"></ul>
                            <div class="treeBtns" id="dpt_btn"><input type="button" value="确定" name="dptTreeAct"/></div>
                        </div>
                        <input type="hidden" name="departmentId" id="departmentId" value="${departmentId!}"/>
                    </td>
                </tr>
                <tr>
                    <td class="tbxxright">支付流水号：</td>
                    <td><input name="feeNo" type="text" value="${condition.feeNo!}"/></td>
                    <td class="tbxxright">凭证状态：</td>
                    <td>
                        <input type="checkbox" name="status" value="1" <#if (status1! == "1")>checked</#if>>新增
                        <input type="checkbox" name="status" value="2" <#if (status2! == "2")>checked</#if>>批改
                        <input type="checkbox" name="status" value="3" <#if (status3! == "3")>checked</#if>>取消
                    </td>
                    <td class="tbxxright">产品类型：</td>
                    <td>
                        <input type="checkbox" name="isWaats" value="1" <#if (isWaats1! == "1")>checked</#if>>标准
                        <input type="checkbox" name="isWaats" value="0" <#if (isWaats2! == "0")>checked</#if>>定制
                    </td>

                </tr>
                <tr>
                    <td class="tbxxright" colspan="6">
                        <input type="hidden" name="webSubmit" value="1"/>
                        <input type="button" value="查 询" class="sbtn" id="doSubmit"/>
                        <input type="button" value="清 空" class="sbtn" onclick="clearall();"/>
                    </td>
                </tr>
            </table>
        </div>
    </form>
    <div class="fltil" style="height:85px; padding-left:0; margin-left:0;">
        <!--a class="dc_data" href='javascript:doExport();'></a-->
        <input class="dc_data" type="button" value="导 出" onclick="doExport();"/>
        <div>
            <table class="tbli2">
                <thead>
                <tr class="tbhead">
                    <th style="height:21px;">查询结果：</th>
                    <th>合计</th>
                    <th>=新增</th>
                    <th>+批改</th>
                    <th>+取消</th>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td align="center">保单（份）</td>
                    <td align="right">${(policyStactics.totalPolicyNum)!}</td>
                    <td align="right">${(policyStactics.newPolicyNum)!}</td>
                    <td align="right">${(policyStactics.modifyPolicyNum)!}</td>
                    <td align="right">${(policyStactics.cancelPolicyNum)!}</td>
                </tr>
                <tr>
                    <td align="center">人数（个）</td>
                    <td align="right">${(policyStactics.totalInsuredNum)!}</td>
                    <td align="right">${(policyStactics.newInsuredNum)!}</td>
                    <td align="right">${(policyStactics.modifyInsuredNum)!}</td>
                    <td align="right">${(policyStactics.cancelInsuredNum)!}</td>
                </tr>
                <tr>
                    <td align="center">保费(含适用的增值税)（元）</td>
                    <td align="right">${(policyStactics.totalPremium)!}</td>
                    <td align="right">${(policyStactics.newPremium)!}</td>
                    <td align="right">${(policyStactics.modifyPremium)!}</td>
                    <td align="right">${(policyStactics.cancelPremium)!}</td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="tableContainer" class="tableContainer">
        <table class="tbli">
            <thead>
            <tr class="tbhead">
                <th class="bh">保单号</th>
                <th>凭证号码</th>
                <th>支付流水号</th>
                <th>线路/团号</th>
                <th>签单日期</th>
                <th>修改日期</th>
                <th>生效日期</th>
                <th>结束日期</th>
                <th>保险产品</th>
                <th>保险计划</th>
                <th>人数</th>
                <th>保费(含适用的增值税)</th>
                <th>目的地</th>
                <th>状态</th>
                <th>经办人</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody id="trbs">
            <#list list! as item>
                <#if item.viewMode!>
                    <tr>
                        <td class='tbh'>${item.policyNo!}</td>
                        <td>${item.policyid!}</td>
                        <td>${item.feeNo!}</td>
                        <td>${item.lineno!}</td>
                        <td>${dateUtil.format(item.inssueDate!)}</td>
                        <td>${dateUtil.format(item.lasttime!)}</td>
                        <td>${dateUtil.format(item.effectiveDate!)}</td>
                        <td>${dateUtil.format(item.expiryDate!)}</td>
                        <td>${item.productname!}</td>
                        <td>${item.planname!}</td>
                        <td>${item.personnum!}</td>
                        <td>${item.totalPremium!}元</td>
                        <td>${item.furthestCity!}</td>
                        <td><#if (item.status! =="1")>新增
                            <#elseif (item.status! == "2")>批改
                            <#elseif (item.status! == "3")>无效
                            <#else>无效
                            </#if></td>
                        <td>${item.attn!}</td>
                        <td>
                            <a id='showbox' href='/sale/policy/view?policyid=${item.policyid!}'>查看</a>
                            <#if (item.status! == "1" || item.status! == "2")>
                                <a id='showbox' href='/sale/downpolicy?policyid=${item.policyid!}'>下载保单</a>
                                <#if (securityUser.branch! == 66)>
                                    <a id='showbox' href='/sale/downpolicy?policyid=${item.policyid!}&person=1'>单人凭证</a>
                                <#elseif (securityUser.branch! == 87 || securityUser.branch! == 2)>
                                    <#if item.isGroup! == 1>
                                        <a id='showbox'
                                           href='/sale/downpolicy?policyid=${item.policyid!}&person=1'>单人凭证</a>
                                    </#if>
                                </#if>
                            </#if>
                            <#if (item.isWaats! == 1 && item.status! == "1")>
                                <a id='showbox' href='/sale/downWAATSPolicy?policyid=${item.policyid!}'>备用下载</a>
                            </#if>
                        </td>
                    </tr>
                </#if>
            </#list>
            </tbody>
        </table>
    </div>
    <div class="fy_box">
        <div class="page_fy">
            <#if ((page.pageStart)! > 1 )>
                <a class="page_pre" href="javascript:pageaction('${(page.priPage)!}')">上一页</a>
            </#if>
            <#if ((page.pageStart)! < (page.lastPage)!)>
                <a class="page_nex" href="javascript:pageaction('${(page.nextPage)!}')">下一页</a>
            </#if>
            <script>
                var k = 0;  //临时记录数
                var largetcount = 10;    //每页显示多少个      10
                var cp = ${(page.pageStart)!}; //当前页         98
                var pagecount = ${(page.lastPage)!}; //页面总数 100
                var selfcnt = Math.floor(largetcount / 2);     //5
                var lcnt = Math.floor(pagecount / 2);          //50
                var startp = cp - selfcnt;                     //93

                if (pagecount - cp < selfcnt) {
                    startp = startp - (selfcnt - (pagecount - cp));
                }

                for (var i = startp; i <= cp; i++) {
                    if (i < 1 || i > cp || i > pagecount)
                        continue;
                    k = k + 1;
                    if (i == cp)
                        document.write('<a class="page_dq">' + i + '</a>');
                    else
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>')
                }
                if (cp < pagecount && k < largetcount) {
                    for (var i = cp + 1; i <= pagecount; i++) {
                        if (k > largetcount)
                            break;
                        k = k + 1;
                        document.write('<a href="javascript:pageaction(' + i + ')">' + i + '</a>');
                    }
                }
            </script>
            <div>第 <input name="goNumber" class="tex" type="text"/> 页 <input type="submit" class="submit" value="跳转"
                                                                             onClick="goPage()"></div>
        </div>
    </div>
</div>
<div id="msg" class="tb_box">
    <iframe id="ifhmsg" frameborder="0"></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
            <tr>
                <td class="btm"></td>
                <td class="box_ct" valign="top">
                    <div id="msg_ban" class="b_til">
                        <span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                        <div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a
                                id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr>
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<#include "../common/foot.html">
</body>
<script language="javascript">
    function getParent(str) {
        var arr = str.split(',');
        return arr[arr.length - 2];
    }

    function getDepartmentJson() {
        var departmentList = [
            <#list departmentList! as departmentObj>
            {
                "id": ${departmentObj.departmentID!},
                "parentid": "${departmentObj.parent!}",
                "name": "${departmentObj.departmentAbbName!}"
            },
            </#list>
            {}
        ]
        departmentList.pop();
        for (ind in departmentList) {
            departmentList[ind].parentid = getParent(departmentList[ind].parentid);
        }
        return departmentList;
    }

    //服务器时间
    //此处决定日历默认时间
    var d = new Date();
    var zyear = d.getFullYear();
    var zmonth = d.getMonth();
    var zday = 1;
    var cday = d.getDate();
</script>
<script language="javascript" src="/js/common.js?v=${JS_AND_CSS_VER}"></script>
<script language="javascript" src="/js/mag_cp.js?v=${JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/beaurl_nall.js?v=${JS_AND_CSS_VER}"></script>

</html>

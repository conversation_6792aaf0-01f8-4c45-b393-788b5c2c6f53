<div class="pageHeader">
    <img class="l" src="/img/mynewlogo.png"/>
</div>
<div class="dl_info">欢迎您<span
            class="user">${securityUser.partnerName!}(${securityUser.username!})</span><span
            class="fgx">|</span><a href="/logout">安全退出</a><span class="zwf"></span></div>
<input id="branch" type="hidden" value="${securityUser.branch!}"/>
<input id="partnertype" type="hidden" value="${securityUser.partnertype!}"/>

<!--左侧二级菜单html部分-->
<div id="leftmenu">
    <ul>
        <#list securityUser.mainMenuList! as mainmenu>
            <#if mainmenu.menuName! == "在线投保">
                <li>
                <a href="#">在线投保-></a>
                <ul class="zxtb">
                    <#list securityUser.productList! as item>
                        <li><a href="/sale/policy/insure?productid=${item.productid!}">${item.abbname!}</a></li>
                    </#list>
                </ul>
            <#elseif mainmenu.menuName! == "保单处理">
                <li><a href="/sale/policy/toQuery">保单处理&nbsp;&nbsp;&nbsp;</a></li>
            <#elseif mainmenu.menuName! == "查询服务">
                <li>
                    <a href="#">查询服务-></a>
                    <ul>
                        <li><a href="/sale/policy/toQuery">保单查询</a></li>
                        <li><a href="/sale/query/mingxi">被保险人查询</a></li>
                    </ul>
                </li>
            <#elseif mainmenu.menuName! == "对账服务">
                <li>
                    <a href="#">对账服务-></a>
                    <ul>
                        <li><a href="/sale/premiumBill/commission">保单交易明细</a></li>
                        <li><a href="/sale/premiumBill/commissionDetail">被保险人交易明细</a></li>
                    </ul>
                </li>
            <#elseif mainmenu.menuName! == "客户管理">
                <li><a href="/sale/client/index">客户管理&nbsp;&nbsp;&nbsp;</a></li>
            <#elseif mainmenu.menuName! == "公司管理">
                <li>
                    <a href="#">公司管理-></a>
                    <ul>
                        <li><a href="/sale/department/index">部门管理</a></li>
                    </ul>
                </li>
            </#if>
        </#list>
        <#if securityUser.admin! == 1>
            <li><a href="/sale/agent/index">员工管理</a></li>
        </#if>
        <#if sjisUtil.hasAction("accountManager")>
            <li><a href="/sale/account/index">账户管理&nbsp;&nbsp;&nbsp;</a></li>
        </#if>
        <#if (securityUser.accountID! > 0)>
            <li><a href="/sale/account/myAccountList">我的账户&nbsp;&nbsp;&nbsp;</a></li>
        </#if>
        <li><a href="/sale/down">文件下载&nbsp;&nbsp;&nbsp;</a></li>
        <li><a href="editpass">修改密码&nbsp;&nbsp;&nbsp;</a></li>
        <div class="clear"></div>
    </ul>
</div>

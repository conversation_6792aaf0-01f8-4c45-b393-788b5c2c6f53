<!DOCTYPE html>
<html lang="en">
<#include "../common/header.ftl">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Pragma" content="no-cache"/>   
<meta http-equiv="Cache-Control" content="no-cache"/>   
<meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
<link href="/css/chartis.css?v=02.css" rel="stylesheet" type="text/css" />
<script language="javascript" src="/js/jquery.js"></script>
<script language="javascript" src="/js/jquery.easydrag.handler.beta2.js"></script>
<!--[if IE 6]>
<script language="javascript" src="/js/DD_belatedPNG.js"></script>
<script type="text/javascript">
DD_belatedPNG.fix('.l');
</script>
<![endif]-->
<script type="text/javascript">
function doSubmit() 
{
   	document.conditionform.action="";
   	document.conditionform.submit();
}	
</script>
</head>

<body class="newbg">
<div class="wrap">
	<div class="sb_lg"><img src="/img/mynewlogo.png"/></div>
	<div class="s_top"></div>
    <div class="s_mid">
    	<div class="s_til">查询条件</div>
			<form name="conditionform" action="" method="post"  >	
        <table class="s_tab">
        	<tr>
            	<td align="right">保单号或凭证号：</td>
                <td><input name="policyno" size="20" type="text" />*（必填）</td>
            </tr>
        	<tr>
            	<td align="right"> 投保人或被保险人证件号码 ：</td>
                <td><input name="idno" size="20" type="text" />*（必填）</td>
            </tr>
            <tr>
            	<td align="right">投保人或被保险人姓名：</td>
                <td><input name="insuredname" size="30" type="text" /></td>
            </tr>
            <tr>
            	<td></td>
            	<td><a class="nbtn" href="javascript:doSubmit();">查询</a></td>
            </tr>
        </table>
			</form>
        <div class="s_til">查询结果</div>
        <div class="s_rel" style="overflow-x: auto; overflow-y: auto;">
        <table class="s_rtb" cellspacing="1">
        	<tr>
            	<th class="bh">保单号</th>
            	<th class="bh">凭证号</th>
                <th>姓名</th>
                <!--th>性别</th-->
                <th>出生日期</th>
                <th>证件类型</th>
                <th>证件号码</th>
                <th>生效日期</th>
                <th>结束日期</th>
                <th>签单日期</th>
                <th>修改日期</th>
                <th>客户名称</th>
                <th>线路/团号</th>
                <th>保险产品</th>
                <th>保险计划</th>
                <th>保费(含适用的增值税)</th>
                <th>状态</th>
            </tr>
                    <#list list! as item>
                   <tr>
                    	<td class='tbh'>${(item.policyNo)!}</td>
                       	<td>${(item.policyid)!}</td>
                       	<td>${(item.insuredname)!}</td>
                       	<td>${(item.birthday)!}</td>
                       	<td>${(item.insuredIdNoType)!}</td>
                       	<td>${(item.insuredIdNo)!}</td>
                        <td>${dateUtil.format(item.effectiveDate!)}</td>
                        <td>${dateUtil.format(item.expiryDate!)}</td>
                        <td>${dateUtil.format(item.inssueDate!)}</td>
                        <td>${dateUtil.format(item.lasttime!)}</td>
                       	<td>${(item.policyholder)!}</td>
                       	<td>${(item.lineno)!}</td>
                        <td>${(item.productname)!}</td>
                        <td>${(item.planname)!}</td>
                        <td>${(item.premium)!}元</td>
                        <#if (item.policyStatus! == 1)>
							<td><font color="#00DB00"> 新增<font></td>
						<#elseif (item.policyStatus! == 2)>
							<td><font color="#FFD306">批改<font></td>
						<#elseif (item.policyStatus! == 3)>
							<td><font color="#FF0000">取消<font></td>
						</#if>
                    </tr>
					</#list>
        </table>
        </div>
    </div>
<div class="s_bm"></div>
    <div class="copy_rt">
    美亚客户服务电话： <span>************</span><br />
    京ICP备11043278 Copyright © 2010-2011 北京全程保科技有限公司
    </div>
</div>	

<!-- tipbox -->
<div id="msg" class="tb_box">
	<iframe id="ifhmsg" frameborder="0" ></iframe>
    <div class="box">
        <div class="box_t"></div>
        <table id="bzh" cellpadding="0">
       		<tr>
            	<td class="btm"></td>
                <td class="box_ct" valign="top">
                	<div id="msg_ban" class="b_til">
                    	<span id="msg_til"></span>
                        <a id="clos_msg" href="javascript:void(0)"><b>×</b></a>
                    </div>
                    <div class="smsg">
                    	<div class="spic"></div>
                        <div id="msg_info"></div>
                    </div>
                    <div class="tb_btn"><a id="msg_btn" href="javascript:void(0)"><img src="/img/del_btn.gif"/></a><a id="clos_msg" href="javascript:void(0)"><img src="/img/cancel.gif"/></a></div>
                </td>
                <td class="btm"></td>
            </tr> 
        </table>
        <div class="box_bom"></div>
    </div>
</div>
<!-- tipbox end--> 
</body>
<script language="javascript">
//服务器时间
//此处决定日历默认时间
var d = new Date(); 
var zyear= d.getFullYear(); 
var zmonth=d.getMonth();
var zday=1;
</script>
<script language="javascript" src="/js/common.js?v=01"></script>
<script language="javascript" src="/js/mag_cp.js?v=01"></script>
<script type="text/javascript" src="/js/beaurl_nbx.js?v=01"></script>
</html>

<!DOCTYPE html>
<html lang="en">
<#include "../../common/header.ftl">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>${sjisUtil.sysTitle!"TravelYe"}</title>
    <script type="text/javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/jquery.easydrag.handler.beta2.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.core.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/jquery.ztree.excheck.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/checkBox.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/qrcode/jquery.qrcode.min.js?v=${JS_AND_CSS_VER!}"></script>
    <script type="text/javascript" src="/js/vue/vue.min.js?v=${JS_AND_CSS_VER!}"></script>
    <link rel="stylesheet" href="/css/globalcss.css?v=${JS_AND_CSS_VER!}" type="text/css"/>
    <link rel="stylesheet" href="/css/calendar4query.css?v=${JS_AND_CSS_VER!}"/>
    <link rel="stylesheet" href="/css/global_test.css?v=${JS_AND_CSS_VER!}" type="text/css"/>
    <link rel="stylesheet" href="/css/font-awesome-4.7.0/css/font-awesome.min.css?v=${JS_AND_CSS_VER!}"
          type="text/css"/>
    <link href="/css/chartis.css?v=${JS_AND_CSS_VER!}" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="/css/zTreeStyle.css?v=${JS_AND_CSS_VER!}" type="text/css">
    <link rel="stylesheet" href="/css/best-icon/style.css?v=${JS_AND_CSS_VER!}"/>
    <style type="text/css">

        textarea, input, select {
            border: 1px solid #ccc;
            outline: none;
        }

        a:hover {
            color: #ffb408 !important;
        }

        .single_info {
            padding-top: 15px;
        }

        .bottomInfo {
            position: relative;
        }

        .status {
            width: 150px;
        }


        .titleTotal {
            margin-left: 15px;
        }

        .single_info table {
            width: auto;
            background-color: transparent !important;
        }

        .single_info table td:nth-child(2) {
            max-width: 280px;
        }

        .single_info tbody td, .single_info thead th {
            padding-left: 12px;
            padding-right: 3px;
        }

        .single_info tbody td:first-child {
            padding-left: 0;
        }

        .personInfo .applicationDetailTitle td {
            padding-left: 2px;
            font-weight: 600;
            padding-top: 20px;
        }

        .personInfo .applicationDetailTitle:first-child td {
            padding-top: 0;
        }

        .single_info .beneficiaryInfo {
            margin-left: 30px;
            display: none;
        }

        .beneficiary div {
            margin-right: 30px;
        }

        .puser div:nth-child(1), .holder div:nth-child(1), .participant div:nth-child(1) {
            margin: 8px 10px 8px 0;
        }

        .editInfoUl li:hover {
            background-color: #e4eefe;
        }

        .policyHistory {
            position: absolute;
            top: 0;
            left: 2em;
            right: 0;
            height: 100%;
            overflow: auto;
            overflow-x: hidden;
            white-space: normal;
            word-break: break-all;
        }

        /*.policyHistory span{display: inline-block;width:auto;padding-left: 20px;}*/

        .policyHistory::-webkit-scrollbar {
            transition-duration: 1s;
            background-color: transparent;
            width: 2px;
            height: 1px;
        }

        .policyHistory::-webkit-scrollbar-track {
            border-radius: 1px;
        }

        .policyHistory::-webkit-scrollbar-thumb {
            border-radius: 1px;
            background-color: #1a73e8;
            display: none;
        }

        .policyHistory:hover::-webkit-scrollbar-thumb {
            display: block;
        }

        .policyHistory .logs {
            line-height: 12px;
            margin-bottom: 15px;
        }

        .policyHistory span {
            padding-right: 20px;
        }

        .clearfix:after {
            overflow: hidden;
        }

        .registerUser span {
            padding-left: 10px;
        }

        .single_user span {
            text-align: right;
            display: inline-block;
        }

        .single_user span:nth-child(1) {
            min-width: 30%;
            max-width: 50%;
        }

        .single_user span:nth-child(2) {
            width: 35%;
        }

        .order span {
            padding-left: 15px;
        }

        @media screen and (max-width: 1440px) {
            .order span {
                padding-left: 10px;
            }
        }

        @media screen and (max-width: 1366px) {
            .order span {
                padding-left: 8px;
            }
        }

        .single_info {
            display: none;
        }

        .condition_group .fieldContainer select.field {
            background-position: 100% -3px;
        }

        /* 浮动菜单相关 */
        .hoverMenuContainer {
            position: relative;
        }

        .hoverMenuList {
            position: absolute;
            background-color: #fff;
            border-radius: 2px;
            padding: 5px 0;
            display: none;
            left: 0;
            top: 12px;
            z-index: 2;
            border: 1px solid #ccc;
            border-radius: 0 0 2px 2px;
            box-shadow: 0 2px 4px #ccc;
        }

        .hoverMenuContainer:hover .hoverMenuList {
            display: block;
        }

        .hoverMenuList a {
            display: block;
            padding: 0 15px;
            white-space: nowrap;
            line-height: 24px;
        }

        .bottomMenu {
            margin-right: 15px;
        }

        /* insured视图下，每列之间的间距保持一致 */
        .table_msg .contentCell {
            width: 1px;
        }

        .table_msg .separatorCell {
            width: 5%;
        }

        .send_group, .send_group table {
            margin: auto;
            text-align: center;
            vertical-align: middle;
        }

        .send_group input[type="text"], .send_group select {
            width: 120px;
            height: 20px;
            line-height: 20px;
            margin-right: -3px;
        }

        .send_group input[type="text"] {
            padding-left: 6px;
        }

        .send_group td {
            height: 26px;
            vertical-align: middle;
        }

        .send_group input, .send_group select, .send_group label {
            color: #777;
        }

        .info2nd {
            background-color: #f8f9fa;
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            padding: 5px 0px 8px;
            margin-top: 9px;
            height: 28px;
        }

        .tipspan {
            color: #777;
            position: absolute;
            margin-left: 3px;
            line-height: 20px;
            display: inline-block;
            width: 115px;
            text-align: left;
        }

        .localtri {
            position: absolute;
            width: 0;
            height: 0;
            padding: 0;
            margin: 0;
            border: 10px solid transparent;
            border-bottom-color: #ccc;
            margin-top: -15px;
            margin-left: 112px;
            border-top-width: 0px;
        }

        .localtri div {
            position: absolute;
            width: 0;
            height: 0;
            padding: 0;
            margin: 0;
            border: 9px solid transparent;
            border-bottom-color: #f5f5f5;
            margin-left: -9px;
            border-top-width: 0px;
            margin-top: 1px;
        }

        .infoBox {
            float: left;
            margin-left: 10px;
            margin-top: 6px;
        }

        .main_bd {
            padding: 10px 0 10px 0;
        }

        .main_bd form {
            text-align: left;
        }

        .editDate input {
            font-size: 17px;
            font-family: "microsoft yahei";
            color: rgb(161, 161, 161);
        }

        .editDate {
            font-size: 17px;
            font-style: normal;
            font-weight: 400;
            font-family: "microsoft yahei";
            color: rgb(161, 161, 161);
        }

        .sub_status {
            color: #a1a1a1;
            display: none;
            float: left;
        }

        .main_status, .sub_status {
            margin-left: 10px;
            text-align: left;
            color: #a1a1a1;
        }

        .query_group {
            float: right;
            margin-right: 10px;
            margin-top: -5px;
        }

        .fieldContainer {
            margin-top: 0;
            margin-bottom: 0;
            width: 100%;
            display: inline-block;
        }

        .fieldContainer input.field {
            color: #a1a1a1;
        }

        .fieldContainer .field {
            border: none;
            outline: none;
            box-shadow: none;
            padding: 0;
            width: 100%;
            font-size: 14px;
            letter-spacing: .2px;
            color: #5f6368;
            margin: 0;
        }

        .fieldContainer .underline {
            margin-top: 0 !important;
        }

        .fieldContainer .underline {
            margin-top: 2px;
            height: 1px;
            background-color: #e0e0e0;
            position: relative;
        }

        .fieldContainer .field ~ .underline:after {
            content: " ";
            background-color: #4285f4;
            height: 2px;
            width: 0;
            display: block;
            overflow: hidden;
        }

        .clear {
            width: 0;
            height: 0;
            margin: 0;
            padding: 0;
            clear: both;
        }

        .search, .clearall, .downall {
            color: #009ce1;
            font-size: 16px;
            height: 24px;
            line-height: 24px;
            width: 24px;
            display: inline;
            cursor: pointer;
            margin: 5px;
            vertical-align: super;
        }

        .policy_checked, .checkAll {
            margin-left: 20px !important;
            margin-right: 0 !important;
            display: inline-block;
            height: 16px;
            width: 16px;
        }

        .listContainerBorder {
            display: inline-block;
        }

        /*.content {*/
        /*    font-family: "Product Sans", HelveticaNeue, Arial, sans-serif;*/
        /*}*/

    </style>
</head>
<body>
<#assign m = module!>
<#include "../..${m!}/common/top.ftl">
<div class="content">
    <form daction="conditionform" autocomplete="off" name="formname" id="policyQueryTable" target="_self" action=""
          method="post" style="width: 100%;margin: 0 auto;">
        <input name="paginalcount" type="hidden" value="20"/>
        <input name="pagestart" type="hidden" value="1"/>
        <input name="issueBeginDate" type="hidden" id="issueBeginDate" value="${condition.issueBeginDate!}"/>
        <input name="issueEndDate" type="hidden" id="issueEndDate" value="${condition.issueEndDate!}"/>
        <div class="main_bd">
            <div class="condition_group" id="search_div">
                <div class="condition_line" style="height: 40px;margin-top: 0;">
                    <div style="width: 100%;text-align: end;font-size: 16px;">
                        <span class="editDate" onkeydown="inputDate(this,event)"
                              onclick="showNewCalendar(this,'issue')">
                                <input type="text" style="width:40px" readonly/>年<input type="text" readonly/>月<input
                                    type="text" readonly/>日 -
                                <input type="text" style="width:40px" readonly/>年<input type="text" readonly/>月<input
                                    type="text" readonly/>日
                        </span>
                    </div>
                    <div class="clear"></div>
                </div>
                <div class="condition_line">
                </div>
                <div class="condition_line" style="height: 30px;margin-top: 15px;">
                    <div class="main_status" style="display: inline;">
                        <span style="font-size: 14px;">保单状态</span>
                        <input type="checkbox" name="status" value="1"
                               <#if status1! =="1" >checked</#if>>新增
                        <#--                        <input type="checkbox" name="status" value="2" <#if status2! =="2" >checked</#if>>批改-->
                        <input type="checkbox" name="status" value="3"
                               <#if status3! =="3" >checked</#if>>取消
                        <input type="checkbox" name="status" value="4"
                               <#if status4! =="4" >checked</#if>>待处理
                        <input type="checkbox" name="status" value="5"
                               <#if status5! =="5" >checked</#if>>待支付
                    </div>
                    <div class="query_group">
                        <div class="fieldContainer" style="width: 100px;margin-left: 5px;">
                            <input placeholder="凭证号码" class="field infoipt" type="text" name="policyID"
                                   value="${condition.policyID!}">
                            <div class="underline">
                                <div class="underline1"></div>
                            </div>
                        </div>
                        <div class="fieldContainer" style="width: 120px;margin-left: 5px;">
                            <input placeholder="保单号码" class="field infoipt" type="text" name="policyNo"
                                   value="${condition.policyNo!}">
                            <div class="underline">
                                <div class="underline1"></div>
                            </div>
                        </div>
                        <div class="fieldContainer" style="width: 200px;margin-left: 5px;">
                            <input placeholder="保单备注" class="field infoipt" type="text" name="remark"
                                   value="${condition.remark!}">
                            <div class="underline">
                                <div class="underline1"></div>
                            </div>
                        </div>
                        <div class="fieldContainer" style="width: 200px;margin-left: 5px;">
                            <input placeholder="线路/团号" class="field infoipt" type="text" name="lineno"
                                   value="${condition.lineno!}">
                            <div class="underline">
                                <div class="underline1"></div>
                            </div>
                        </div>
                        <#if m! == '/admin'>
                            <div class="fieldContainer hoverTrigger" style="width: 200px;">
                                <div class="fieldAdaptiveWrapper">
                                    <div class="fieldAdaptive"></div>
                                </div>
                                <select novocer class="field selectInput searchInput" id="partnerid" name="partnerid">
                                    <option value="0">请选择</option>
                                    <#list partnerlist! as item>
                                        <option value="${item.agencyID!}"
                                                <#if condition.partnerid! == item.agencyID!>selected</#if>>${item.agencyName!}
                                        </option>
                                    </#list>
                                </select>
                            </div>
                        </#if>
                        <#--                        <div class="search materialIcons" onclick="doQuery(false);">search</div>-->
                        <#--                        <div class="clearall materialIcons" onclick="clearall();">close</div>-->
                        <#--                        <div class="downall materialIcons" onclick="downAll();">download</div>-->
                    </div>
                </div>
                <div class="condition_line" style="overflow: auto;">
                    <div class="main_status">
                        <span style="font-size: 14px;">签名状态</span>
                        <input type="checkbox" name="applicationSignStatus" value="0"
                               <#if status1! =="1" >checked</#if>>待确认
                        <#--                        <input type="checkbox" name="status" value="2" <#if status2! =="2" >checked</#if>>批改-->
                        <input type="checkbox" name="applicationSignStatus" value="1"
                               <#if status3! =="3" >checked</#if>>已确认
                    </div>
                    <div style="float: right;">
                        <div class="search" onclick="doQuery(false);">查询</div>
                        <div class="downall" onclick="downAll();">批量下载</div>
                        <div class="downall" onclick="policyListPdf();">投保证明</div>
                    </div>
                </div>
            </div>
            <#--            <div class="condition_group">-->
            <#--                <div class="send_group">-->
            <#--                    <div class="info2nd" style="margin-top: 0px;">-->
            <#--                        <div class="infoBox">-->
            <#--                            <span class="tipspan">凭证号码</span>-->
            <#--                            <input class="infoipt" type="text" name="policyID"-->
            <#--                                   value="${condition.policyID!}"-->
            <#--                                   spellcheck="false"/>-->
            <#--                            <span class="tipspan">保单备注</span>-->
            <#--                            <input class="infoipt" type="text" name="remark" value="${condition.remark!}"-->
            <#--                                   spellcheck="false"/>-->
            <#--                            <span class="tipspan">线路/团号</span>-->
            <#--                            <input class="infoipt" type="text" name="lineno" value="${condition.lineno!}"-->
            <#--                                   spellcheck="false"/>-->
            <#--                            <span class="tipspan">保单号码</span>-->
            <#--                            <input class="infoipt" type="text" name="policyNo" value="${condition.policyNo!}"-->
            <#--                                   spellcheck="false"/>-->
            <#--                        </div>-->
            <#--                        <input type="hidden" name="webSubmit" value="1"/>-->
            <#--                        <input type="button" value="查 询" class="sbtn" onclick="doQuery(false);"/>-->
            <#--                        <input type="button" value="清 空" class="sbtn" onclick="clearall();"/>-->
            <#--                        <input type="button" value="批量下载" class="sbtn" onclick="downAll();"/>-->
            <#--                    </div>-->
            <#--                </div>-->
            <#--            </div>-->
            <#--                    <tr>-->
            <#--                        <td class="tbxxright">凭证状态：</td>-->
            <#--                        <td>-->
            <#--                            <input type="checkbox" name="status" value="1" <#if status1! =="1" >checked</#if>>新增-->
            <#--                            <input type="checkbox" name="status" value="2" <#if status2! =="2" >checked</#if>>批改-->
            <#--                            <input type="checkbox" name="status" value="3" <#if status3! =="3" >checked</#if>>取消-->
            <#--                        </td>-->
            <#--                        <td></td>-->
            <#--                        <td></td>-->
            <#--                        <td></td>-->
            <#--                    </tr>-->
            <div class="result_list" id="policiesTable">
                <div class="listHeader clearfix">
                    <div style="float: left; padding: 5px 0; ">
                        <input type="checkbox" class="checkAll" id="checkAll" value=""/>
                    </div>
                    <div class="countingArea">
                        <span class="count">${page.rowCount!} results</span>
                        <span class="selectedCount"></span>
                    </div>
                </div>
                <div id="mainlist">
                    <table width=100% align="center" class="policytable"
                           style="word-break:keep-all;table-layout:auto;overflow-x: scroll;" width="100%" border="0"
                           cellpadding="3" cellspacing="1">
                        <tbody id="policyList">
                        <!-- vueLoading的class和v-cloak联用，防止页面初始加载时闪动 -->
                        <tr v-for="(policy, index) in policies" v-cloak class="vueLoading policyRow" :index="index"
                            :policyID="policy.policyid" align="center" :id="'con_' + policy.policyid">
                            <td class="policy_id_td">
                                <input type="checkbox" class="policy_checked" value=""/>
                                <div :class="createPolicyRowClass(index)">
                                    <div class="topInfo">
                                        <div :data-tooltip="policyNoTitleDescription(policy)" class="fleft policy_no"
                                             style="width: 150px;">
                                            <span v-if="StringUtils.isNotBlank(policy.policyNo)">{{policy.policyNo}}</span>
                                            <span v-else>{{policy.policyid}}</span>
                                        </div>

                                        <div class="fright titleTotal" style="user-select: none;width: 100px;">
                                            <div style="float: right;" data-tooltip="总保费">
                                                CNY {{policy.premium.toFixed(2)}}
                                            </div>
                                            <span title="Expand" class="expandContainer"><a class="expand expandPolicy"><i
                                                            class="fa fa-chevron-down close"
                                                            style="display: inline-block;"
                                                            aria-hidden="true"></i></a></span>
                                        </div>
                                        <div class="fright unImportant titleInfo">
                                            {{policy.agencyName}}-{{policy.username}}{{policySource(policy)}}
                                        </div>

                                        <span class="product_name titleInfo" data-tooltip="保障计划">
                                            {{policy.productname}}-{{policy.planname}}
                                        </span>
                                        <span class="td_activeDat titleInfo" data-tooltip="起保日期">
                                            {{policy.effectiveDate.indexOf("00:00:00") > -1 ? policy.effectiveDate.split(" ")[0]: policy.effectiveDate}}
                                        </span>
                                        <span noWrap class="td_exprDat titleInfo" data-tooltip="满期日期">
                                            {{policy.expiryDate.indexOf("23:59:59") > -1 ? policy.expiryDate.split(" ")[0]: policy.expiryDate}}
                                        </span>
                                        <span class="destination titleInfo" data-tooltip="被保险人姓名">
                                        <span>{{policy.insuredList[0].insuredname}}</span>
                                    </span>
                                    </div>
                                    <div class="toggle_info" style="display: none">
                                        <div class="single_info clearfix" style="display: block;">
                                            <table class="spanModeTable">
                                                <tr>
                                                    <td class="spanModeRow">
                                                        <div class="personInfo clearfix">
                                                            <table class="fleft" cellpadding="0" cellspacing="0">
                                                                <tr class="policyApplicantLabel applicationDetailTitle singleInfoTitleRow">
                                                                    <td colspan="2">投保人信息</td>
                                                                </tr>
                                                                <tr v-if="policy.applicant"
                                                                    class="pholderInfo rowHoverAnimate"
                                                                    :customerID="policy.applicant.customerID">
                                                                    <td v-if="policy.applicationSignStatus != -1"
                                                                        class="fleft" style="width:50px;"
                                                                        data-tooltip="电子投保单确认状态">
                                                                        <template
                                                                                v-if="policy.applicant.signConfirmStatus == 1">
                                                                            <span style="color: #00a854;">已确认</span>
                                                                        </template>
                                                                        <template v-else>
                                                                            <span style="color: #ff7b08;">未确认</span>
                                                                        </template>
                                                                    </td>
                                                                    <td data-tooltip="姓名">
                                                                        {{policy.applicant.insuredname}}</span>
                                                                    </td>
                                                                    <td data-tooltip="性别">{{policy.applicant.gend}}
                                                                    </td>
                                                                    <td data-tooltip="证件类型" nowrap>
                                                                        {{policy.applicant.insuredIdNoType}}
                                                                    </td>
                                                                    <td class="phiddnum" data-tooltip="证件号码">
                                                                        {{policy.applicant.insuredIdNo}}
                                                                    </td>
                                                                    <td data-tooltip="生日">
                                                                        {{policy.applicant.birthday}}
                                                                    </td>
                                                                    <td class="tel" data-tooltip="手机号">
                                                                        {{policy.applicant.homePhoneNo}}
                                                                    </td>
                                                                    <td class="email" data-tooltip="Email" colspan="3">
                                                                        {{policy.applicant.emailAddr}}
                                                                    </td>
                                                                    <td>
                                                                        <span class="hoverMenuContainer bottomMenu"
                                                                              v-if="policy.status === '1' || policy.applicationSignStatus != -1 || (policy.isPayment === '1' && policy.orderStats != 'paid' && policy.applicationSignStatus != 0)">
                                                                            <a href="javascript:void(0);"
                                                                               class="sendmail onmenu bottomMenu"
                                                                               menu="sendmail"
                                                                               style="font-size: 13px;vertical-align: 1px;">
                                                                                <i class="fa fa-envelope-o"
                                                                                   aria-hidden="true"></i>
                                                                        </a>
                                                                        <div class="hoverMenuList">
                                                                            <a v-if="policy.applicationSignStatus == 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showEmailPage(policy.policyid,policy.applicant.insuredID,1)">发送投保单确认邮件</a>
                                                                            <a v-if="policy.status === '1' || policy.status === '2'"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showEmailPage(policy.policyid,policy.applicant.insuredID,3)">发送电子保单邮件</a>
                                                                            <a v-if="policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showEmailPage(policy.policyid,policy.applicant.insuredID,4)">发送支付通知邮件</a>
                                                                        </div>
                                                                    </span>
                                                                        <span v-if="policy.applicationSignStatus != -1 || (policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0)"
                                                                              class="hoverMenuContainer bottomMenu">
                                                                        <a href="javascript:void(0)"
                                                                           class="besticon best-icon-sms"
                                                                           title="发送短信"></a>
                                                                        <div class="hoverMenuList">
                                                                            <a v-if="policy.applicationSignStatus == 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showSmsPage(policy.policyid,policy.applicant.insuredID,1)">发送投保单确认短信</a>
                                                                            <a v-if="policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showSmsPage(policy.policyid,policy.applicant.insuredID,2)">发送支付通知短信</a>
                                                                        </div>
                                                                    </span>
                                                                    </td>
                                                                    <td>
                                                                    </td>
                                                                </tr>
                                                                <tr class="puserLabel applicationDetailTitle singleInfoTitleRow">
                                                                    <td colspan="2">被保险人清单</td>
                                                                </tr>
                                                                <template
                                                                        v-for="(insured, insuredIndex) in policy.insuredList">
                                                                    <tr :insuredIndex="insuredIndex"
                                                                        :class="'puserInfo rowHoverAnimate' + (insured.status == 3 ? ' invalid': '')"
                                                                        :customerID="insured.customerID" align="center"
                                                                        style="color: #f333; line-height:20px;">
                                                                        <td v-if="policy.applicationSignStatus != -1"
                                                                            class="fleft" style="width:50px;"
                                                                            data-tooltip="电子投保单确认状态">
                                                                            <template
                                                                                    v-if="insured.signConfirmStatus == 1">
                                                                                <span style="color: #00a854;">已确认</span>
                                                                            </template>
                                                                            <template v-else>
                                                                                <span style="color: #ff7b08;">未确认</span>
                                                                            </template>
                                                                        </td>
                                                                        <td data-tooltip="姓名">
                                                                            {{insured.insuredname}}</span>
                                                                        </td>
                                                                        <td data-tooltip="性别" style="">
                                                                            {{insured.gend}}
                                                                        </td>
                                                                        <td data-tooltip="证件类型">
                                                                            {{insured.insuredIdNoType}}
                                                                        </td>
                                                                        <td data-tooltip="证件号码">
                                                                            {{insured.insuredIdNo}}
                                                                        </td>
                                                                        <td data-tooltip="生日">{{insured.birthday}}
                                                                        </td>
                                                                        <td data-tooltip="手机号">
                                                                            {{insured.homePhoneNo}}
                                                                        </td>
                                                                        <td data-tooltip="Email">
                                                                            {{insured.emailAddr}}
                                                                        </td>
                                                                        <td data-tooltip="与投保人的关系">
                                                                            {{insured.relationship}}
                                                                        </td>
                                                                        <#--                                                                        <td data-tooltip="是否有效">-->
                                                                        <#--                                                                            {{insured.policyStatus-->
                                                                        <#--                                                                            == 3 ?-->
                                                                        <#--                                                                            "无效":-->
                                                                        <#--                                                                            "有效"}}-->
                                                                        <#--                                                                        </td>-->
                                                                        <td data-tooltip="保费">
                                                                            {{insured.premium.toFixed(2)}}
                                                                        </td>
                                                                        <td>
                                                                        <span class="hoverMenuContainer bottomMenu"
                                                                              v-if="policy.status === '1' || policy.applicationSignStatus != -1 || (policy.isPayment === '1' && policy.orderStats != 'paid' && policy.applicationSignStatus != 0)">
                                                                            <a href="javascript:void(0);"
                                                                               class="sendmail onmenu bottomMenu"
                                                                               menu="sendmail"
                                                                               style="font-size: 13px;vertical-align: 1px;">
                                                                                <i class="fa fa-envelope-o"
                                                                                   aria-hidden="true"></i>
                                                                            </a>
                                                                            <div class="hoverMenuList">
                                                                                <a v-if="policy.applicationSignStatus == 0"
                                                                                   class="onmenu" menu="sendmail"
                                                                                   href="javascript:void(0)"
                                                                                   @click="showEmailPage(policy.policyid,policy.applicant.insuredID,1)">发送投保单确认邮件</a>
                                                                                <a v-if="policy.status == '1' || policy.status == '2'"
                                                                                   class="onmenu" menu="sendmail"
                                                                                   href="javascript:void(0)"
                                                                                   @click="showEmailPage(policy.policyid,policy.applicant.insuredID,3)">发送电子保单邮件</a>
                                                                                <a v-if="policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0"
                                                                                   class="onmenu" menu="sendmail"
                                                                                   href="javascript:void(0)"
                                                                                   @click="showEmailPage(policy.policyid,policy.applicant.insuredID,4)">发送支付通知邮件</a>
                                                                            </div>
                                                                        </span>
                                                                            <span v-if="policy.applicationSignStatus != -1 || (policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0)"
                                                                                  class="hoverMenuContainer bottomMenu">
                                                                        <a href="javascript:void(0)"
                                                                           class="besticon best-icon-sms"
                                                                           title="发送短信"></a>
                                                                        <div class="hoverMenuList">
                                                                            <a v-if="policy.applicationSignStatus == 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showSmsPage(policy.policyid,policy.applicant.insuredID,1)">发送投保单确认短信</a>
                                                                            <a v-if="policy.isPayment == '1' && policy.orderStatus != 'paid' && policy.applicationSignStatus != 0"
                                                                               class="onmenu" menu="sendmail"
                                                                               href="javascript:void(0)"
                                                                               @click="showSmsPage(policy.policyid,policy.applicant.insuredID,2)">发送支付通知短信</a>
                                                                        </div>
                                                                    </span>
                                                                        </td>
                                                                        <td v-if="insured.beneficiaryList"
                                                                            class="sExpand close"
                                                                            style="margin-left: 30px;margin-top:5px;cursor:pointer;color:#333333b3;">
                                                                            <i class="fa fa-angle-down close"
                                                                               style="font-size:18px;"></i></td>
                                                                    </tr>
                                                                    <tr v-if="insured.beneficiaryList && insured.beneficiaryList.length > 0"
                                                                        class="rowHoverAnimate">
                                                                        <td colspan="11">
                                                                            <div class="beneficiaryInfo">
                                                                                <div v-for="beneficiary in insured.beneficiaryList"
                                                                                     class="clearfix">
                                                                                    <div class="fleft"
                                                                                         style="width:50px;"
                                                                                         data-tooltip="受益人姓名">
                                                                                        {{beneficiary.insuredname}}
                                                                                    </div>
                                                                                    <div class="fleft"
                                                                                         style="width:50px;"
                                                                                         data-tooltip="证件类型">
                                                                                        {{beneficiary.insuredIdNoType}}
                                                                                    </div>
                                                                                    <div class="fleft"
                                                                                         style="width:120px;margin-right: 40px;"
                                                                                         data-tooltip="证件号码">
                                                                                        {{beneficiary.insuredIdNo}}
                                                                                    </div>
                                                                                    <div class="fleft"
                                                                                         style="width:50px;"
                                                                                         data-tooltip="与被保险人的关系">
                                                                                        {{beneficiary.relationship}}
                                                                                    </div>
                                                                                    <div class="fleft"
                                                                                         style="width:30px;"
                                                                                         data-tooltip="收益份额占比">
                                                                                        {{parseInt(beneficiary.proportion
                                                                                        *
                                                                                        100)}}%
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                </template>
                                                            </table>
                                                        </div>
                                                        <div v-if="StringUtils.isNotBlank(policy.lineno)"
                                                             class="additionalInfo"
                                                             style="padding-top:20px;display:inline-block;">
                                                            <span style="padding-left: 2px;padding-top:20px;font-weight: 600;">线路团号</span>
                                                            <div class="remarks">
                                                                <span style="margin-left: 15px;">{{policy.lineno}}</span>
                                                            </div>
                                                        </div>
                                                        <div v-if="StringUtils.isNotBlank(policy.remark)"
                                                             class="additionalInfo"
                                                             style="padding-top:20px;display:inline-block;">
                                                            <span style="padding-left: 2px;padding-top:20px;font-weight: 600;">保单备注</span>
                                                            <div class="remarks">
                                                                <span style="margin-left: 15px;">{{policy.remark}}</span>
                                                            </div>
                                                        </div>
                                                        <div v-if="StringUtils.isNotBlank(policy.feeNo)"
                                                             class="additionalInfo"
                                                             style="padding-top:20px;display:inline-block;">
                                                            <span style="padding-left: 2px;padding-top:20px;font-weight: 600;">支付流水号</span>
                                                            <div class="remarks">
                                                                <span style="margin-left: 15px;">{{policy.feeNo}}</span>
                                                                <template v-if="policy.isPayment == '1'">
                                                                    {{policy.refundStatusDescription ? '(退费：' +
                                                                    policy.refundStatusDescription + ')' : ''}}
                                                                </template>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="spanModeRow"
                                                        style="width: 100%!important;max-width: 100%;position: relative;">
                                                        <div class="policyHistory" style="">
                                                            <div v-if="logList(policy).length > 0"
                                                                 class="singleInfoTitleRow"><span>日志</span></div>
                                                            <div v-for="l in logList(policy)"
                                                                 class="logs unImportant">
                                                                {{l}}
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="bottomInfo" :policyID="policy.policyid">
                                        <div class="fleft status" :style="policyStatusColor(policy)"
                                             v-html="policyStatusDescription(policy)"></div>
                                        <div class="fleft title21">
                                            <a v-if="cancelPermit(policy)"
                                               href="javascript:void(0)"
                                               class="bottomMenu"
                                               title="取消保单"
                                               @click="tocancel(policy,'${m!}')"
                                               style="color:#3186b6!important;">取消</a>
                                            <span v-if=" policy.status === '5' " class="hoverMenuContainer bottomMenu">
                                                <a class="hoverMenuDesc onmenu" style="color:#3186b6!important;"
                                                   href="javascript:void(0)">支付</a>
                                                <div class="hoverMenuList">
                                                    <a href="javascript:void(0)" class="onmenu" title="支付单个保单"
                                                       @click="qrcode(policy,'policy')">单个保单支付(二维码)</a>
                                                    <a href="javascript:void(0)" class="onmenu" title="支付整批保单"
                                                       @click="qrcode(policy,'order')">整批保单支付(二维码)</a>
                                                </div>
                                            </span>
                                            <!--保单号为空时显示补录和确认按钮-->
                                            <!--核心系统无响应时，出现重新发送按钮，应该再加一个条件就是，是否向核心系统发送的判断条件-->
                                            <!--发送状态为拒保时也出现重新发送按钮，但需要二次确认-->
                                            <template v-if="policy.status === '4'">
                                                <a class="onmenu bottomMenu" menu="resend" href="javascript:void(0)"
                                                   :href="'${m!}/policy/resubmitWAATSPolicy?policyid='+policy.policyid">
                                                <span v-if="!StringUtils.isNotBlank(policy.policyNo)"
                                                      style="color:#3186b6">获取保单</span>
                                                    <span v-if="StringUtils.isNotBlank(policy.policyNo)"
                                                          style="color:#3186b6">获取批退</span>
                                                </a>
                                            </template>
                                            <#if sjisUtil.hasAction('applicationConfirm') && m! == '/admin'>
                                                <a v-if="policy.isWaats === 1 && policy.status === '4' && policy.applicationSignStatus !== -1"
                                                   class="onmenu bottomMenu" menu="sure"
                                                   :href="'${m!}/policy/resubmitWAATSPolicy?policyid='+policy.policyid">确认</a>
                                            </#if>
                                            <template
                                                    v-if="policy.status === '1' || policy.status === '2' || (policy.status !== '3' && policy.applicationSignStatus !== -1)">
                                               <span class="hoverMenuContainer bottomMenu">
                                                    <a class="hoverMenuDesc onmenu besticon best-icon-save"
                                                       style="font-size: 14px;" href="javascript:void(0)"> </a>
                                                    <div class="hoverMenuList">
                                                        <template v-if="policy.status === '1'">
                                                            <a class="onmenu" menu="download"
                                                               :href="'${m!}/downpolicy?policyid='+policy.policyid">电子保单</a>
                                                            <a class="onmenu" menu="download"
                                                               target="_blank"
                                                               :href="'${m!}/endorsement/down?type=medical&policyId='+policy.policyid">领馆批注</a>
                                                            <a class="onmenu" menu="download"
                                                               target="_blank"
                                                               :href="'${m!}/endorsement/down?type=area&policyId='+policy.policyid">申根国批注</a>
                                                        </template>
                                                        <template v-if="policy.applicationSignStats === 1">
                                                            <a class="onmenu" menu="download"
                                                               :href="'${m!}/application/signApplication?policyId='+policy.policyid">投保单（已签名）</a>
                                                        </template>
                                                        <template v-else>
                                                            <a class="onmenu" menu="download"
                                                               :href="'${m!}/application/noSignTemplate?policyId='+policy.policyid">投保单模板</a>
                                                        </template>
                                                    </div>
                                                </span>
                                            </template>
                                        </div>
                                        <div class="fright unImportant" data-tooltip="最后一次更新时间">
                                            {{DateUtils.format(policy.lasttime, "yyyy-MM-dd HH:mm:ss")}}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr v-if="policies.length == 0">
                            <td style="height:50px;line-height:35px;text-align:center;font-size:16px;background:white;">
                                没有数据
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </form>
</div>
<#include "../../sale/common/foot.html">
</body>
<script type="text/javascript" src="/js/bestCommon/common.js?v=${JS_AND_CSS_VER!}"></script>
<script type="text/javascript" src="/js/bestCommon/PolicyUtils.js?v=${JS_AND_CSS_VER!}"></script>
<script type="text/javascript" src="/js/policyManager.js?v=${JS_AND_CSS_VER!}"></script>
<script type="text/javascript" src="/js/bestCommon/calendar4query.js?v=${JS_AND_CSS_VER!}"></script>
<script type="text/javascript" src="/js/bestCommon/queryCommon.js?v=${JS_AND_CSS_VER!}"></script>
<script type="text/javascript">
    let module = "${m!}";

    $(".send_group").on("focus", ".infoipt", function () {//聚焦查询条件，隐藏提示
        $(this).prev(".tipspan").hide();
    }).on("blur", ".infoipt", function () {//离开查询条件，若空则显示提示
        if ($(this).val() == "") {
            $(this).prev(".tipspan").css("display", "inline");
        }
    }).on("click", ".tipspan", function () {//点击提示，聚焦产寻条件
        $(this).hide().next(".infoipt").focus();
    })
    $(".infoipt").each(function () {//初始化查询条件与提示
        if (this.value != "") {
            jQuery(this).prev(".tipspan").hide();
        }
    })

    //为请求动作的方法名称赋值
    handleMouseWheel();//鼠标滚轮滚动时加载数据

    QueryUtils.queryMethod = doQuery;

    $("#contentDetailBox").on("click", ".user_edit_cancel,.info_edit_close", function () {
        $("#contentDetailBox").hide();
        $("#opa").hide();
    });

    var policyTableVue = new Vue({
            "el": "#mainlist",
            "data": {
                viewType: "policy",
                count: "",
                policies: [],
            },
            'methods': {
                createPolicyRowClass: function (index) {
                    //生成当前policy行的行className
                    var policy = this.policies[index];
                    var className = "listContainerBorder";
                    /*
                    1、新增类投保单，创建中或取消状态时，显示为淡灰色
                    2、已经注销保单成功的保单显示为淡灰色
                     */
                    if (this.viewType == "insured") {

                    } else {
                        if (policy.status != "1") {
                            className += " invalid";
                        }
                    }
                    return className;
                },
                policyNoTitleDescription: function (policy) {

                    if (policy.policyNo && StringUtils.isNotBlank(policy.policyNo)) {

                        return "　保单号：" + policy.policyNo + "\n投保单号：" + policy.policyid;
                    } else {

                        return "投保单号";
                    }
                },
                destinationDescription: function (policy) {

                    return policy.personnum + "人，2天";
                },
                showPolicyEffectiveDate: function (effectiveDate) {
                    //格式化policy的起保时间：如果是00:00:00结尾的，那么就去掉
                    if (effectiveDate.indexOf("00:00:00") > -1) {
                        return effectiveDate.split(" ")[0]
                    }
                    return effectiveDate;
                },
                policyStatusDescription: function (policy) {
                    //用JS方法构建操作菜单最左边关于当前保单状态的描述
                    var html = "";

                    if (policy.status === "1") {
                        html += "保单<span style='color:lightgray'>(新增)</span>";
                        // if (policy.isPayment == "1") {
                        // switch (policy.action.code) {
                        //     case "new":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(新增)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "edit":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(修改)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "add":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(附增)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(附增，待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(附增，欠保费)</span>";
                        //         }
                        //         break;
                        //     case "repurchase":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(再购)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "renewal":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(续保)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "c2nn":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "保单<span style='color:lightgray'>(重出)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "c2nc":
                        //         switch (policy.orderStatus) {
                        //             case "pendingRefund":
                        //                 html += "<span style='color:#ff7b08'>保单已注销，待重出</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>(欠保费)</span>" + policy.orderStatus;
                        //         }
                        //         break;
                        //     case "endorse":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //                 html += "批单<span style='color:lightgray'>(批改)</span>";
                        //                 break;
                        //             case "overPaid":
                        //                 html += "批单<span style='color:#ff7b08'>(待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "批单<span style='color:#ff7b08'>(欠保费)</span>";
                        //         }
                        //         break;
                        //     case "cancel":
                        //         switch (policy.orderStatus) {
                        //             case "fullyPaid":
                        //             case "fullRefund":
                        //             case "pendingPayment":
                        //             case "creating":
                        //                 html += "保单<span style='color:lightgray'>(已注销)</span>";
                        //                 break;
                        //             case "pendingRefund":
                        //             case "overPaid":
                        //                 html += "保单<span style='color:#ff7b08'>(已注销，待退款)</span>";
                        //                 break;
                        //             default:
                        //                 html += "保单<span style='color:#ff7b08'>" + "</span>" + policy.orderStatus;
                        //         }
                        //         break;
                        //     default :
                        //         html += "批单<span style='color:#ff7b08'>" + policy.action.code + policy.actionStatus + ":" + policy.orderStatus + "</span>";
                        // }
                        // } else {
                        // }
                    } else if (policy.status === "3") {
                        html += "保单<span style='color:lightgray'>(已批退)</span>";
                    } else if (policy.status === "4") {
                        if (StringUtils.isNotBlank(policy.policyNo)) {
                            html += "保单<span style='color:#ff7b08'>(退保失败)</span>";
                        } else if (policy.applicationSignStatus === 0) {
                            html += "投保单<span style='color:#ff7b08'>(待确认)</span>";
                        } else {
                            html += "投保单<span style='color:#ff7b08'>(被拒保)</span>";

                        }
                    } else if (policy.status === "5") {
                        if (policy.applicationSignStatus === 1) {
                            html += "投保单<span style='color:#ff7b08'>(待支付)</span>";
                        } else if (policy.applicationSignStatus === 0) {
                            html += "投保单<span style='color:#ff7b08'>(待确认)</span>";
                        } else {
                            html += "投保单<span style='color:#ff7b08'>(待支付)</span>";
                        }
                    }
                    return html;
                },
                logList: function (policy) {
                    const logList = [];
                    logList.push(policy.inssueDate + ' 提交投保');
                    if (policy.isPayment === '1' && policy.feeTime) {
                        logList.push(policy.feeTime + ' 支付');
                    }
                    if (policy.status === '1') {
                        logList.push(policy.sendDate + ' 核保出单');
                    } else if (policy.status === '4' && policy.applicationSignStatus !== -1) {
                        logList.push(policy.sendDate + ' 拒保：' + policy.errorCode);
                    } else if (policy.status === '3') {
                        logList.push(policy.lasttime + ' 批退');
                    }
                    return logList;
                },
                policyStatusColor: function (policy) {
                    //部分状态下表述状态的字需要变成橘红色
                    if (policy.status === "4") {
                        return "color:#ff7b08";
                    }
                },
                cancelPermit: function (policy) {
                    if ('${m!}' === '/sale' && policy.status === '1' && !DateUtils.before(policy.effectiveDate, new Date()) && "${sjisUtil.hasAction('cancelPolicy')?string('yes','no')}" === 'yes') {
                        return true;
                    }
                    if ('${m!}' === '/admin' && "${sjisUtil.hasAction('cancelPolicy')?string('yes','no')}" === 'yes') {
                        return true;
                    }
                    return false;
                },
                policySource: function (policy) {
                    if (policy.toC === 1) {
                        return " (CPS)";
                    }
                    return "";
                }
            },
        })
    ;

    function newPageMethod(url, param) {
        showOverlayIframe(url);
    }

    var pageAnimationDuration = 450;

    //服务器时间
    //此处决定日历默认时间
    var d = new Date();
    var zyear = d.getFullYear();
    var zmonth = d.getMonth();
    var zday = 1;
    var cday = d.getDate();


    var beneficiaryDefaultStr = "继承人";
    var beneficiaryAddStr = "继承人";

    function getParent(str) {
        var arr = str.split(',');
        return arr[arr.length - 2];
    }

    function showEmailDialog(policyId, insuredId, oldEmail, sendType) {
        const _tr = $("#emailDialog");
        _tr.find("#policyId").val(policyId);
        _tr.find("#insuredId").val(insuredId);
        _tr.find("#sendType").val(sendType);
        _tr.find("#sendToEmail").val(oldEmail);
        if (sendType === 1) {
            _tr.find("#emailLabel").html("发送确认邮件至：");
        } else if (sendType === 3) {
            _tr.find("#emailLabel").html("发送电子保单至：")
        }
        _tr.show();
    }

    function showPhoneDialog(policyId, insuredId, oldEmail, sendType) {
        const _tr = $("#phoneDialog");
        _tr.find("#phonePolicyId").val(policyId);
        _tr.find("#phoneInsuredId").val(insuredId);
        _tr.find("#phoneSendType").val(sendType);
        _tr.find("#sendToPhone").val(oldEmail);
        if (sendType === 1) {
            _tr.find("#phoneLabel").html("发送确认短信至：");
        } else if (sendType === 2) {
            _tr.find("#phoneLabel").html("发送支付短信至：")
        }
        _tr.show();
    }

    function hideEmailDialog() {
        $("#emailDialog").hide();
    }

    function hidePhoneDialog() {
        $("#phoneDialog").hide();
    }


    function sendEmail(isUpdate) {
        const _tr = $("#emailDialog");
        const policyId = _tr.find("#policyId").val();
        const insuredId = _tr.find("#insuredId").val();
        const sendType = _tr.find("#sendType").val();
        const sendToEmail = _tr.find("#sendToEmail").val();
        const emailReg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if (!emailReg.test(sendToEmail)) {
            alert('请填写正确的电子邮箱！');
            return;
        }
        try {
            $.ajax({
                type: "post",
                url: '/application/sendEmail',
                data: {
                    "policyId": policyId,
                    "sendType": sendType,
                    "insuredId": insuredId,
                    "update": isUpdate,
                    "email": sendToEmail
                },
                success: function (data) {
                    const result = JSON.parse(data);
                    console.log(result);
                    if (result.code === 1) {
                        alert("发送成功");
                    } else {
                        alert(result.message);
                    }
                },
            });
        } catch (e) {
            alert("发送失败");
        } finally {
            const _tr = $("#emailDialog");
            _tr.find("#policyId").val(0);
            _tr.find("#insuredId").val(0);
            _tr.find("#sendType").val(0);
            _tr.find("#sendToEmail").val("");
            _tr.hide();
        }

    }

    function sendSms(update) {
        const _tr = $("#phoneDialog");
        const policyId = _tr.find("#phonePolicyId").val();
        const insuredId = _tr.find("#phoneInsuredId").val();
        const sendType = _tr.find("#phoneSendType").val();
        const sendToPhone = _tr.find("#sendToPhone").val();
        const phoneReg = /^1[3-9]\d{9}$/;
        if (!phoneReg.test(sendToPhone)) {
            alert('请填写正确的中国境内手机号码！');
            return;
        }
        try {
            $.ajax({
                type: "post",
                url: '/application/sendSms',
                data: {
                    "policyId": policyId,
                    "sendType": sendType,
                    "insuredId": insuredId,
                    "update": update,
                    "phone": sendToPhone
                },
                success: function (data) {
                    const result = JSON.parse(data);
                    console.log(result);
                    if (result.code === 1) {
                        alert("发送成功");
                    } else {
                        alert(result.message);
                    }
                },
            });
        } catch (e) {
            alert("发送失败");
        } finally {
            const _tr = $("#phoneDialog");
            _tr.find("#phonePolicyId").val(0);
            _tr.find("#phoneInsuredId").val(0);
            _tr.find("#phoneSendType").val(0);
            _tr.find("#sendToPhone").val("");
            _tr.hide();
        }
    }

    function getDepartmentJson() {
        var departmentList =
            [
                <#list departmentList! as departmentObj>
                {
                    "id": "${departmentObj.departmentID!}",
                    "parentid": "${departmentObj.parent!}",
                    "name": "${departmentObj.departmentAbbName!}"
                }
                ,
                </#list>
                {}
            ]
        departmentList.pop();
        for (ind in departmentList) {
            departmentList[ind].parentid = getParent(departmentList[ind].parentid);
        }
        return departmentList;
    }
</script>
<script type="text/javascript" src="/js/common.js?v=${JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/mag_cp.js?v=${JS_AND_CSS_VER}"></script>
</html>

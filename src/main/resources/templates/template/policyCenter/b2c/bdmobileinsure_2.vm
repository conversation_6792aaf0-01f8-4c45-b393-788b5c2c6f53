#parse("$!{location}/common/header.vm")
#set($cPath = "/assets/CssAndImg/common")##所有cps共用的图片样式路径
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html style="font-size: 16px;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="/js/jquery-1.11.3.min.js"></script>
    <link href="/css/font-awesome-4.7.0/css/font-awesome.min.css?v=$!{JS_AND_CSS_VER}" rel="stylesheet">
    <link href="/css/mobile/mobilecommen.css" rel="stylesheet">


    <title>保单填写</title>
    <style type="text/css">
        @media only screen and (device-width: 375px) and (device-height: 812px){
            .fix-iphonex-adaption {
                padding-bottom: calc(0.34rem + constant(safe-area-inset-bottom)) !important;
                padding-bottom: calc(0.34rem + env(safe-area-inset-bottom)) !important;
            }
        }
        body{margin: 0px;-webkit-tap-highlight-color:rgba(0,0,0,0);}
        .policyhead{
            height: 2.75rem;
            border-bottom: 1px solid #ddd;
            width: 100%;
            text-align: center;
        }
        .tabs{height:50px;}
        .tabs a{
            display: block;
            float: left;
            width: 33.33%;
            color: #333;
            text-align: center;
            background: #eee;
            line-height: 50px;
            font-size: 16px;
            text-decoration: none;
        }

        .sideline {
            display: block;
            position: absolute;
            border: 0;
            height: 2px;
            background-color: #48a5f4;
            left: 0;
            top: 68px;
            pointer-events: none;
        }
        .li_list{ min-height:800px; font-size:40px; line-height:800px; color:#fff; text-align:center}
        .swipe{ padding:70px 0 0 0;}
        ul{-webkit-padding-start: 0px;}
        .bkdiv{
            color: #666666;
        }
        .btdiv{
            padding: 0 1.25rem;
        }
        .datadiv input{
            /*margin: 0px 0px 0px 110px;*/
            -webkit-appearance: none;
            border: none;
            width: 100%;
            font-size: 0.875rem;
            color: #5D667A ;
            line-height: 2rem;
            outline: none;
            background-color: #fff;
            text-align: right;
        }
        .datadiv{
            float: left;
            font-size: 1rem;
        }
        /*.wrapBox_link span{line-height: 0px}*/
        /*.userdiv div span{    line-height: 40px;}*/
        .expenseBtn{
            height: 3rem;
            width: 100%;
            background-color: #FFF;
            bottom: 0;
        }
        .expenseBtn div{ float: left;}
        .expenseBtn div span{ float: left;}
        .paysum{
            width: 60%;
            height: 100%;
            color: #666;
            font-size: 1.3rem;
            line-height: 3rem;
        }
        .paybut{
            width: 100%;
            background-color: #fa9128;
            height: 100%;
            color: white;
            font-size: 1rem;
            line-height: 3rem;
        }
        .paybut span{
            text-align: center;
            width: 100%;
        }
        .showHidden .fa {
            font-size: 1.5rem;
            color: #fa9128;
            -webkit-animation: showmore 1s infinite;
            font-stretch: ultra-expanded;
        }
        .declareBox {
            position: fixed;
            z-index: 9;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-color: #fff;
            overflow: auto;
            display: none;
        }
        .forAddpHolder{
            color: #fa9128;
            font-size: 1rem;
            font-weight: 800;
            height: auto;
        }
        .goback {
            display: block;
            float: left;
            padding-left: 1.25rem;
            text-align: left;
            position: absolute;
            padding-top: 0.9rem;
        }
        .declareClose1 {
            position: absolute;
            right: 0em;
            width: 4em;
            color: #fa9128;
            font-size: 1rem;
            top: 0.7rem;
            font-weight: bold;
        }
        .header-title{
            height: 2.75rem;
            line-height: 2.75rem;
            font-size: 1.125rem;
            color: #121C32;
        }
        .wrapBox {
            overflow: hidden;
            width: 100%;
            height: 4.245rem;
            line-height: 4.245rem;
            border-bottom: 1px solid #e1e4eb;
        }
        .mainDeclare {
            height: auto;
            position: absolute;
            left: 0;
            top: 2.75rem;
            bottom: 0;
            right: 0;
            overflow: auto;
            z-index: 9;
            /*padding: 0 30px 120px 30px;*/
            font-size: 0.15rem;
            -webkit-overflow-scrolling: touch;
        }
        .wrapBox input:hover, .wrapBox input:focus{
            border: none;
        }
        .wrapBox input {
            color: #121C32;
            padding: 0;
            width: 100%;
            border: none;
            height: 2rem;
            line-height: 2rem;
            font-size: 1rem;
            -webkit-appearance: none;
            margin-top: 2rem;
            outline: none;
            font-family: PingFangSC-Regular,SourceHanSansCN-Regular;
        }
        .wrapBox select {
            color: #121C32;
            background-color: #fff;
            padding: 0;
            border: none;
            height: 2rem;
            line-height: 2rem;
            font-size: 1rem;
            width: 90%;
            -webkit-appearance: none;
            margin-top: 2rem;
            outline: none;
        }
        .enteringArrow {
            color: #ddd;
            font-size: 1.19rem;
            line-height: 0.4rem;
            margin-top: 1.87rem;
            margin-bottom: 0.41rem;
        }
        .selectBox {
            position: fixed;
            width: 90%;
            left: 5%;
            padding: 0;
            background-color: #fff;
            top: 50px;
            z-index: 100;
            color: #777;
        }
        .selectMain {
            margin: 30px;
            overflow-y: auto;
        }
        ul.selectUl {
            margin: 0;
            padding: 0;
            list-style-type: none;
        }
        .selectUl .selectedOption {
            color: #00236A;
        }
        .selectUl li {
            height: 42px;
            line-height: 42px;
            border-bottom: 1px solid #ddd;
            padding-left: 0.3em;
            overflow: hidden;
            /* padding-right: 50px; */
        }
        ul, ol, li {
            list-style-type: none;
            margin: 0;
            padding: 0;
        }
        .selectOpa {
            position: fixed;
            background-color: #000;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99;
            opacity: 0.3;
            height: 300%;
        }
        .plusOrminus{font-size: 1rem}
        /*#insureNotes {*/
            /*text-indent: 2em;*/
        /*}*/
        .declareTitle {
            font-weight: 800;
            text-align: center;
            font-size: 1.3rem;
            text-indent: 0;
            color: #fa9128;
        }
        .declareClose, .declareCloses {
            border: 1px solid #fa9128;
            width: 100%;
            padding: 0;
            height: 32px;
            line-height: 32px;
            text-align: center;
            text-indent: 0;
            color: #fa9128;
            border-radius: 2px;
        }
        .declareEditBox {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 0 1em;
            background-color: #fff;
        }
        .dwbg .dwb{
            text-shadow: none;
            background: -webkit-gradient(linear,left bottom,left top,color-stop(0.5,#FFF),color-stop(0.5,#FFFFFF));
            color: #666;
        }
        #div_3{
            float: right;
            width: 2.7rem;
            height: 1.5rem;
            border-radius: 3rem;
            position: relative;
            margin: -0.6rem 0;
        }
        #div_4 {
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 3rem;
            position: absolute;
            background: white;
            box-shadow: 0px 1px 2px rgba(0,0,0,0.4);
        }
        .open4{
            display: block;
        }
        .close4{
            display: none;
        }
        .covediv{font-size: 0.8rem;margin: 0.3rem 0 0 0;padding: 0 1.25rem;  }
        #opa{background-color: #fff;opacity: 0;z-index: 100;position: fixed;}
        #bdmsg{
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            -webkit-transform: translate3d(-50%, -50%, 0);
            transform: translate3d(-50%, -50%, 0);
            border-radius: 0.28rem;
            margin: auto;
            width: auto;
            min-height: 1.405rem;
            line-height: 1.405rem;
            font-size: 1rem;
            color: #ffffff;
            background-color: rgba(0, 0, 0, 0.7);
            text-align: center;
            z-index: 55;
            padding: 0.64rem 0.94rem;
        }
        }
        .bdmsg1{
            padding: 2rem 2rem;
            color: #666;
            font-size: 1.2rem;
            text-align: center;
        }
        .bdmsg2{
            width: 100%;
            bottom: 0;
            position: absolute;
            border-top: 1px solid #ddd;
            background-color: #FFFFFF;
            height: 3rem;
            text-align: center;
            font-size: 1.4rem;
            color: #FA6450;
        }
        .bdmsg2 span{
            line-height: 3rem;
        }
        .mainDeclare table{
            webkit-overflow-scrolling: touch;
        }
        .fir{display: none}
        .errcn{border-bottom: 1px solid #D65353;}
        .inputlabel {
            position: absolute;
            margin-top: 0;
            font-size: 0.94rem;
            color: #BEC2CC;
            width: 90%;
            height: 4.245rem;
            line-height: 4.245rem;
            font-family: PingFangSC-Regular,SourceHanSansCN-Regular;
            pointer-events: none;
        }
        .outlabel{
            position: absolute;
            margin-top: 0.935rem;
            height: 0.75rem;
            line-height: 0.75rem;
            font-size: 0.75rem;
            color: #5D667A;
            width: 90%;
            pointer-events: none;
            font-family: PingFangSC-Regular,SourceHanSansCN-Regular;
        }
        .errmsg {
            border-top: 1px solid #E1E4EB;
            font-size: 0.75rem;
            color: #D65353;
            height: 1rem;
        }
		.errmsg.errorFocus {
			border-top: 1px solid #D65353;
		}
        .errmsg div{
            line-height: 1.5rem;
        }
        .divideline{
            border-top: 0px solid #E1E4EB;
        }
        .wrapBox .phBirth{
            opacity: 1;
            -webkit-opacity: 1;
            /*-webkit-text-fill-color: #666;*/
            background-color: #fff;
        }
        .step1{display: block;}
        .step_s1{position: fixed;width: 100%;bottom: 0;}
        .step2{display: none}
        .pageheader{
            line-height: 2.75rem;
            font-size: 1.125rem;
            color: #121C32
        }

        .thisstep{
            color: #121C32;
            display: block;
            height: 2.75rem;
            line-height: 2.75rem;
            float: left;
            padding-left: 1.25rem;
            text-align: left;
            position: absolute;
            padding-top: 0.9rem;
        }
        .steps{
            float: right;
            font-size: 0.69rem;
            color: #868E9E;
        }
        .stepstitle{
            color: #121c32;
            font-size: 1.69rem;
            line-height: 1.69rem;
        }
        #insureMessage{
            color: #5D667A;
        }
        .tist{
            width:65%;
            color: #5D667A
        }
        .tist span{
            position: absolute;
            pointer-events: none;
            margin-top: -2.8rem;
            margin-left: 8rem;
            font-size: 0.95rem;
            color:#5D667A;
        }
        .pusay{
            margin-top: -1.3rem;
            padding: 0 1.25rem;
            font-size: 0.815rem;
            color: #394259;
        }
        .weert{
            transition: all 450ms cubic-bezier(0.23, 1, 0.32, 1) 0ms;
        }
        .insureex{display:none}
        .insureholder{
            border: none;
            line-height: 1.19rem;
            height: 1.19rem;
            color: #121C32;
        }
        .policysub{
            padding: 1.87rem 1.25rem 0;
        }
        .policysub_1{
            line-height: 1.19rem;
            height: 1.19rem;
            font-size: 1.19rem;
            padding: 0 0 1.27rem;
            color: #121C32;
        }
        .policysub_2{
            background-color: #F7F8FA;
            padding: 0.725rem 1.25rem;
        }
        .policysub_4{
            font-size: 12px;
            border-bottom: 1px dashed #E1E4EB;
            padding: 0.4rem 0 1rem;
            color:#5D667A;
        }
        .policysub_2 td{
            font-size: 0.75rem;
        }
        .policysub_2 tr td:first-child{
           width: 4rem;
        }
        .delinput{
            position: absolute;
            margin-top: 2rem;
            margin-left: -1.4rem;
        }
        .delinputhid{
            display:none
        }
        .delinput img{width: 1.065rem;margin-top:0.45rem;}
        .gopay1{height: 2.75rem;line-height: 2.75rem; background-color: #fa9128;text-align: center; border-radius: 3rem;color: #ffffff;}
        .opa1{
            background-color: #000000;
            opacity: 0.5;
            z-index: 9999;
            position: fixed;
            top: 0;
            left: 0;
            bottom: 4.5rem;
            right: 0;
            display:none;
        }
        .paybox{
            position: fixed;
            z-index: 9999;
            bottom: 0rem;
            height: 24rem;
            width: 100%;
            background-color: #ffffff;
            display: none;
            font-size: 1rem;
        }
        .gopay2 {
            line-height: 2.75rem;
            height: 2.75rem;
            background-color: #fa9128;
            text-align: center;
            border-radius: 2rem;
            color: #ffffff;
            margin-top: 1rem;
        }
        .errmsg_1{
            padding-top: 0.5rem;color: #E63617;font-size: 0.6rem
        }
        .tdleft{
            padding: 0.4375rem 0;
        }
        .tdright{
            padding: 0.3rem 0;
            font-family:DINPro-Medium;
            text-align: right;
        }
        #less_1,#less_2,#less_3{
            display: none;
        }
        .plantag{
            width: 33.33%;
            text-align: center;
        }
        .dfg{
            height: 2.5rem;
            line-height: 2.5rem;
        }
        .w_2{
            border-top: 3px solid #FA9128;
        }
        .v_1{
            color: #FA9128;
        }
        .plan_1{
            margin: 0.94rem 0;
            padding: 0.94rem 0.94rem 0.74rem;
            border: 1px solid #E6E9F0;
            box-shadow: 0 6px 20px 0 rgba(209,213,222,0.15);
        }
        .plan_2{
            line-height: 1.315rem;
            font-size: 0.815rem;
        }
        .plan_2 p{
            margin: 0.625rem 0 0 0;
            color: #5D667A;
        }
        .plan_4{
            font-size: 0.875rem;
        }

        .opa2{
            position: fixed;
            z-index: 9998;
            height: 100%;
            top: 0rem;
            background-color: #000000;
            padding: 3.25rem 2.22rem;
            opacity: 0.7;
            width: 100%;
            display: none;
        }
        .policyknow{
            position: fixed;
            z-index: 9999;
            height: 24.56rem;
            width: 81.3%;
            top: 10%;
            left: 10%;
            background-color: #ffffff;
            display: none;
        }
        .policyknow_1{
            line-height: 1.125rem;
            height: 1.125rem;
            font-size: 1.125rem;
            color: #293040;
            margin-top: 1.565rem;
            text-align: center;
        }
        .policyknow_2{
            height: 3.125rem;
            text-align: center;
            color: #FA6450;
            border-top:1px solid #E1E4EB;
            font-size:1rem
        }
        .policyknow_3{
            height: 3.125rem;
            line-height: 3.125rem;
            text-align: center;
            color: #394259;
            letter-spacing: 0;
            font-family: PingFangSC-Medium,SourceHanSansCN-Medium;
            font-size: 1.2rem;
        }
        .policyknow_4{
            background-image: -webkit-linear-gradient(rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
            background-image: -moz-linear-gradient(rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
            background-image: -o-linear-gradient(rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
            background-image: linear-gradient(rgba(255,255,255,0) 0%,rgba(255,255,255,1) 100%);
            border-top: none;
            position: absolute;
            bottom: 3.125rem;
            width: 100%;
            height: 3.125rem;
            border-bottom: 1px solid #E1E4EB;
        }
        .isourself{
            display:none;
        }
        .ismy_1{
            display:none;
        }
        .otherPayType{
            display:none;
        }
    </style>
</head>
<body style="background-color: #ffffff">
<div class="policybody" style="margin-bottom: 4rem;">
    <form name="manyinsur" method="post" enctype="multipart/form-data" action="nextStep.action" autocomplete="off">
        <input type="hidden" name="partnerId" id="partnerId" value="$!{partner.partnerId}">
        <input type="hidden" name="planCode" id="planCode" value="">
        <input type="hidden" name="ipcid" id="ipcid" value="">
        <input type="hidden" name="planId" id="planId" value=""/>
        <input type="hidden" name="policyPrice" id="policyPrice" value=""/>
        <input type='hidden' name='sts' id='sts' value="" />
        <input type="hidden" name="pinsure" id="pinsure" value="" />
        <input type="hidden" name="pusers" id="pusers" value="" />
        <input type="hidden" name="uid" id="uid" value="$!{uid}"/>
        <input type="hidden" id="activeDatHidden" name="activeDat"/>
        <input type="hidden" id="exprDatHidden" name="exprDat"/>
        <input type="hidden" id="insureLastTime" value="">
        <input type="hidden" name="reference" id="reference" value="$!{reference}">
        <input type="hidden" name="state" id="state" value="$!{state}">
        <input type="hidden" name="sendState" id="sendState" value="$!{sendState}">
        <input type="hidden" name="orderId" id="orderId" value="$!{orderId}" >
        <input type="hidden" name="actType" id="actType" value="$!{actType}">
        <input type="hidden" name="policyNum" id="policyNum" value="$!{policyNum}">
        <input type="hidden" name="policyId" id="policyId" value="$!{policyId}">
        <textarea style="display:none;" name="paramDTO" id="paramDTO">$!{paramDTO}</textarea>
        <textarea style="display:none;" id="jsonPolicy">$!{jsonPolicy}</textarea>
        <textarea style="display:none;" id="jsonPinsure" name="jsonPinsure">$!{jsonPinsure}</textarea>
        <textarea style="display:none;" id="jsonPuserlist">$!{jsonPuserlist}</textarea>
        <textarea id="getProduct" style="display:none;">#if($!{icid}!=""){"icid":"$!{icid}","ipcid":"$!{ipcid}","planId":"$!{planId}"}#end</textarea>
        <div class="step1" #if($!{step}=="step2") style="display:none" #end>
            <div class="bkdiv ft_1">
                <div style="line-height: 1.69rem;padding: 1.905rem 1.25rem;">
                    <span class="stepstitle ft_1">请选择保障计划</span><span class="steps ft_3">第1/2步</span>
                </div>
                <div class="demo theme-gray ft_2">
                    <div class="tabs-gray-less"></div>
                </div>
                <div>
                    <div class="covediv">
                        <table style='border-collapse: collapse;border: 0px solid #fff;width: 100%;font-size: 0.875rem; border-bottom: 1px solid #E1E4EB; color: #5d667a;margin-top: 0.7125rem;'>
                            <tbody id="less_1" class="ft_4 0620114IJSC3WD" plancode="0620114IJSC3WD">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">全球</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医药补偿</td><td class="tdright ft_2">100万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">个人责任</td><td class="tdright ft_2">100万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">旅行变更</td><td class="tdright ft_2">2万元</td>
                            </tr>
                            </tbody>
                            <tbody id="less_2" class="ft_4 0610114OJSA1ASIAEXNP" plancode="0610114OJSA1ASIAEXNP">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">亚洲(除尼泊尔)</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医疗运送和送返</td><td class="tdright ft_2">40万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">意外身故及伤残</td><td class="tdright ft_2">20万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">银行卡盗刷</td><td class="tdright ft_2">5000元</td>
                            </tr>
                            </tbody>
                            <tbody id="less_3" class="ft_4 0620114IJSB1WDEXAAN" plancode="0620114IJSB1WDEXAAN">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">全球(除美洲非洲尼泊尔)</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医药补偿</td><td class="tdright ft_2">50万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">个人责任</td><td class="tdright ft_2">35万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">旅行变更</td><td class="tdright ft_2">8000元</td>
                            </tr>
                            </tbody>
                            <tfoot>
                            <tr class="showHidden ft_3">
                                <td style="    padding: 0.8125rem 0 1.865rem 0; color: #FA9128;" colspan="2">
                                <span>
                                    <div style="float: left">查看保障详情&nbsp;</div>
                                    <div style="padding-top: 0.4rem;"><img style="width: 1.6%;" src="/img/cuspayImgs/icon_og.png"></div>
                                </span>
                                </td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="bkdiv btdiv">
                <div class="datadiv" style="width: 100%">
                    <div style="height: 3.5rem;line-height: 3.5rem;border-bottom: 1px solid #E1E4EB;" class="calendarTrigger">
                        <table style="width:100%;border-collapse: collapse;">
                            <tr>
                                <td class="ft_3" style="width:60%;">
                                    <span style="color:#121C32">起保时间</span>
                                </td>
                                <td class="ft_2 tist">
##                                    <input onblur="changeDate()" readonly id="activeDat" style="text-align: right;font-family: DINPro-Regular" /><span>&nbsp;00:00:00</span>
                                    <input type="date" onchange="datachange()" id="activeDat" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;" />
                                </td>
                                <td>
                                    <img style="width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png">
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div style="height: 3.5rem;line-height: 3.5rem;border-bottom: 1px solid #E1E4EB;" class="exprDatTr calendarTrigger" id="exprDatTr">
                        <table style="width:100%;border-collapse: collapse;">
                            <tr>
                                <td class="ft_3" style="width:60%;">
                                    <span style="color:#121C32">结束时间</span>
                                </td>
                                <td class="ft_2 tist">
    ##                                <input type="text" id="exprDat" readonly style="text-align: right;font-family: DINPro-Regular;"/><span>&nbsp;23:59:59</span>
                                    <input type="date" id="exprDat" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;"/>
                                </td>
                                <td>
                                    <img style="width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png">
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div style="clear: both"></div>
            </div>
        </div>
        <div class="step2" #if($!{step}=="step2") style="display:block" #end>
            <div class="bkdiv">
                <div style="line-height: 1.69rem;padding:1.905rem 1.25rem">
                    <span class="stepstitle ft_1">请填写投保信息</span><span class="steps ft_3">第2/2步</span>
                </div>
                <div class="pusay ft_4">
                    <span>您填写的信息已安全加密，仅用于投保使用</span>
                </div>
            </div>
            <div class="bkdiv userdiv pHoldermode holderInfo btdiv">
                <div class="">
                    <div class="enteringArrow">
                        <div class="insureholder ft_2">
                            <span>投保人信息</span>
                        </div>
                    </div>
                    <div class="demo-input-holder">
                        <div class="wrapBox">
                            <div class="inputlabel weert">请输入投保人姓名</div>
                            <input erroindex="1" oninput="phiddcheck(this)" onpropertychange="OnPropChanged()" type="text" class="phName"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                        </div>
                        <div class="divideline erro_1"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel">请选择投保人性别</div>
                            <select erroindex="4" class="phGend">
                                <option hidden value=""></option>
                                <option value="男">男</option>
                                <option value="女">女</option>
                            </select>
                            <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                        </div>
                        <div class="divideline erro_4"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel weert">请输入投保人出生日期</div>
                            <input oninput="phiddcheck(this)" onpropertychange="OnPropChanged()" onchange="getprice()" type="date" style="width:90%;" erroindex="5" fmt="birth" class="phBirth"/>
                            <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                        </div>
                        <div class="divideline erro_5"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel weert">请选择投保人证件类型</div>
                            <select erroindex="2" class="phIddtype">
                                <option hidden value=""></option>
                                <option value="身份证">身份证</option>
                                <option value="护照">护照</option>
                                <option value="其他">其他</option>
                            </select>
                            <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                        </div>
                        <div class="divideline erro_2"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel weert">请输入投保人证件号码</div>
                            <input erroindex="3"  fmt="idcard" style="ime-mode:disabled;" oninput="phiddcheck(this)" onpropertychange="OnPropChanged()" type="text" class="phIddnum"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                        </div>
                        <div class="divideline erro_3"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel weert">请填写投保人手机号码</div><input oninput="phTelcheck(this)" onpropertychange="OnPropChanged()"  erroindex="6" maxlength="13" type="tel" fmt="tel"  class="phTel"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                        </div>
                        <div class="divideline erro_6"><div></div></div>
                        <div class="wrapBox">
                            <div class="inputlabel weert">请填写邮箱，用于发送保单</div>
                            <input oninput="phEmailcheck(this)" erroindex="7" type="text" class="phEmail"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                        </div>
                        <div class="divideline erro_7"><div></div></div>
                    </div>
                </div>
            </div>
            <div class="bkdiv userdiv pHoldermode insureInfo btdiv">
                <div class="">
                    <div class="enteringArrow isnures">
                        <div class="insureholder ft_2" style="margin-bottom: 1rem;">
                            <span>被保险人信息</span>
                        </div>
                        <div class="myself demo-input lastinsure" style="margin:1rem 0 1.345rem 0;">
                            <div class="ismy_1">
                                <div class="ft_2" style="color:#121C32;font-size:1rem;padding: 0.4rem 0; float: left;"><span>被保险人</span><span class="insureIndex">1</span></div>
                                <div class="detelbtn" id="detelbtn" onclick="removeInsurant(this)" style="color: #394259;width: 2.625rem;line-height: 1.19rem;text-align: center;height: 1.19rem;float: right;border: 1px solid #5D667A ;border-radius: 10px;font-size: 0.69rem;">删除</div>
                            </div>
                            <div class="wrapBox">
                                <div class="outlabel weert">与投保人关系</div>
                                <select erroindex="1" onchange="holdmag(this)" class="phRelation">
                                    <option value="本人 Policyholder" selected>本人</option>
                                    <option value="配偶 Spouse">配偶</option>
                                    <option value="父母 Parents">父母</option>
                                    <option value="子女 Child">子女</option>
                                    <option value="其他 Other">其他</option>
                                </select>
                                <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                            </div>
                            <div class="divideline erro_1"><div></div></div>
                        </div>
                    </div>
                </div>
                <div class="forAddpHolder">
                    <div class="plusOrminus">
                        <span><img style="margin-bottom: -0.15rem;width: 4.5%;" src="/img/cuspayImgs/icon_addinsure.png"></img></span><span> 添加被保险人</span>
                    </div>
                </div>
            </div>
            <div class="policysub">
                <div class="policysub_1 ft_2"><span>保单信息</span></div>
                <div class="policysub_2">
                    <table style="width: 100%;">
                        <tr><td colspan="2" class="policyproduct ft_2" style="font-size: 1rem;color:#394259"></td></tr>
                        <tr><td colspan="2" class="policysub_4 ft_4">本产品由美亚保险公司承保</td></tr>
                        <tr><td class="ft_4" style="height: 1.6rem; padding-top: 0.8rem;color: #5D667A;">保险计划</td><td class="policyplan ft_2" style="padding-top: 0.8rem;color: #5D667A;"></td></tr>
                        <tr><td class="ft_4" style="height: 1.6rem;color: #5D667A;">起保日期</td><td style="color: #5D667A;" class="policyactiveDat ft_2">$!{activeDat}</td></tr>
                        <tr><td class="ft_4" style="height: 1.6rem;color: #5D667A;">结束日期</td><td style="color: #5D667A;" class="policyexprDat ft_2">$!{exprDat}</td></tr>
                    </table>
                </div>
            </div>
            <div class="opa2"></div>
            <div class="policyknow">
                <div class="policyknow_3 ft_3">
                    <span>投保须知</span>
                </div>
                <div style="height: 74.2%;padding: 0 1.25em; overflow: auto;-webkit-overflow-scrolling: touch;">
                    <div class="ft_3" style="font-size: 0.94rem;color:#5D667A ;">
                        <div class="xz_a xz_0610114OJSA1ASIAEXNP">
                            <p style="-webkit-margin-before: 0.01em;">1.本保险不承保任何直接或间接由于计划或实际前往或途经古巴、伊朗、叙利亚、苏丹、朝鲜、克里米亚地区，或在上述国家或地区旅行期间发生的保险事故。</p>
                            <p>2.本保险不承保任何国家或国际组织认定的恐怖分子或恐怖组织成员，或非法从事毒品、核武器、生物或化学武器交易人员。</p>
                            <p>3.本保险仅承保在境内常住的被保险人，不承保非在境内常住的任何人。本保险所称的境内常住是指一年中在境内的居住时间累计达到或超过183天。</p>
                            <p>4. 本保险不承保在投保本保障计划时已置身于境外的被保险人。</p>
                            <p>5. 投保人需在首次旅行出发前投保并交付保险费以保证保险及时起保。</p>
                            <p>6. 本保险的投保年龄：成年人为18-80周岁，未成年人为1-17周岁。</p>
                            <p>7.因任何原因而取消或改变旅行计划，必须在保险合同生效前电话联系本保险公司客服热线(+86 400-820-8858)，或以书面形式通知到本保险公司，否则本保险公司将不予受理。</p>
                            <p>8.若被保险人为同一旅行自愿投保由本保险公司承保的多种综合保险（不包括团体保险），且在不同保障产品中有相同保险利益的，则本保险公司仅按其中保险金额最高者做出赔偿，并退还其它保险项下已收取的相应保险利益的保险费。</p>
                            <p>9. 保险合同项下应付保险费含适用的增值税。</p>
                            <p>10. 单次旅行的保障期限最长为90天。</p>
                        </div>
                        <div class="xz_a xz_0620114IJSB1WDEXAAN xz_0620114IJSC3WD">
                            <p style="-webkit-margin-before: 0.01em;">1.本保险不承保任何直接或间接由于计划或实际前往或途经古巴、伊朗、叙利亚、苏丹、朝鲜、克里米亚地区，或在上述国家或地区旅行期间发生的保险事故。</p>
                            <p>2.本保险不承保任何国家或国际组织认定的恐怖分子或恐怖组织成员，或非法从事毒品、核武器、生物或化学武器交易人员。</p>
                            <p>3.本保险仅承保在境内常住的被保险人，不承保非在境内常住的任何人。本保险所称的境内常住是指一年中在境内的居住时间累计达到或超过183天。</p>
                            <p>4. 本保险不承保在投保本保障计划时已置身于境外的被保险人。</p>
                            <p>5. 投保人需在首次旅行出发前投保并交付保险费以保证保险及时起保。</p>
                            <p>6. 本保险的投保年龄：成年人为18-80周岁，未成年人为1-17周岁。</p>
                            <p>7.因任何原因而取消或改变旅行计划，必须在保险合同生效前电话联系本保险公司客服热线(+86 400-820-8858)，或以书面形式通知到本保险公司，否则本保险公司将不予受理。</p>
                            <p>8.若被保险人为同一旅行自愿投保由本保险公司承保的多种综合保险（不包括团体保险），且在不同保障产品中有相同保险利益的，则本保险公司仅按其中保险金额最高者做出赔偿，并退还其它保险项下已收取的相应保险利益的保险费。</p>
                            <p>9.若被保险人因同一原因于旅行延误保障和旅行变更保障项下可同时获得赔偿的，则本保险公司仅按其中保险金额较高者做出赔偿。</p>
                            <p>10. 保险合同项下应付保险费含适用的增值税。</p>
                            <p>11. 单次旅行的保障期限最长为182天。</p>
                        </div>
                    </div>
                </div>
                <div class="policyknow_4">
                </div>
                <div class="policyknow_2 ft_2">
                    <span style="line-height: 3.125rem;">我知道了</span>
                </div>
            </div>
            <div class="insureex">
                <div class="demo-input" style="margin:1rem 0 1.345rem 0;">
                    <div>
                        <div class="ft_2" style="color:#121C32;font-size:1rem;padding: 0.4rem 0; float: left;"><span>被保险人</span><span class="insureIndex"></span></div>
                        <div class="detelbtn" id="detelbtn" onclick="removeInsurant(this)" style="color: #394259;width: 2.625rem;line-height: 1.19rem;text-align: center;height: 1.19rem;float: right;border: 1px solid #5D667A ;border-radius: 10px;font-size: 0.69rem;">删除</div>
                    </div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">与投保人关系</div>
                        <select erroindex="1" onchange="holdmag(this)" class="phRelation">
                            <option hidden selected value=""></option>
                            <option value="本人 Policyholder">本人</option>
                            <option value="配偶 Spouse">配偶</option>
                            <option value="父母 Parents">父母</option>
                            <option value="子女 Child">子女</option>
                            <option value="其他 Other">其他</option>
                        </select>
                        <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                    </div>
                    <div class="divideline erro_1"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">被保险人姓名</div>
                        <input erroindex="2" oninput="phiddcheck(this)" onpropertychange="OnPropChanged()" type="text" class="phName"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                    </div>
                    <div class="divideline erro_2"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">性别</div>
                        <select erroindex="5" onchange="isuremag(this)" class="phGend">
                            <option hidden selected value=""></option>
                            <option value="男">男</option>
                            <option value="女">女</option>
                        </select>
                        <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                    </div>
                    <div class="divideline erro_5"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">出生日期</div>
                        <input oninput="phiddcheck(this)" onpropertychange="OnPropChanged()" style="width: 90%" type="date" erroindex="6" fmt="birth" onchange="getprice()" class="phBirth"/>
                        <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                    </div>
                    <div class="divideline erro_6"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">证件类型</div>
                        <select erroindex="3" onchange="isuremag(this)" class="phIddtype">
                            <option hidden selected value=""></option>
                            <option value="身份证">身份证</option>
                            <option value="护照">护照</option>
                            <option value="其他">其他</option>
                        </select>
                        <div style="float: right;"><img style="margin-top: 1.9rem;width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png"></div>
                    </div>
                    <div class="divideline erro_3"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">证件号码</div>
                        <input erroindex="4" oninput="phiddcheck(this)"  fmt="idcard" style="ime-mode:disabled;" type="text" class="phIddnum"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                    </div>
                    <div class="divideline erro_4"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">手机号码(非必填)</div><input oninput="phTelcheck(this)" onpropertychange="OnPropChanged()" erroindex="8" maxlength="13" type="tel" fmt="tel" class="phTel"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                    </div>
                    <div class="divideline erro_8"><div></div></div>
                    <div class="wrapBox">
                        <div class="inputlabel weert">电子邮箱（收取保单用，非必填）</div>
                        <input erroindex="7" oninput="phEmailcheck(this)" type="text" class="phEmail"/><span class="delinput delinputhid"><img src="/img/cuspayImgs/icon_del.png"></span>
                    </div>
                    <div class="divideline erro_7"><div></div></div>
                </div>
            </div>
            <div class="bkdiv btdiv" btdiv style="border: none;margin-top: 1.56rem">
                <div class="policyInfo" cellspacing="0" cellpadding="0" style="margin-top:0;height: 2.5rem; line-height: 0.75rem;font-size: 0.75rem;padding: 0rem 0 0.94rem;">
                    <div class="enteringArrow isok" style=" margin-top: -0.1rem;">
                        <span id="notok" class="open4" style="width: 1rem;float: left;margin-top: 2px"><img style="width: 1rem;" src="/img/cuspayImgs/icon_notok.png"></span>
                        <span id="ok" hidden class="close4" style="width: 1rem;float: left;margin-top: 2px"><img style="width: 1rem;" src="/img/cuspayImgs/icon_ok.png"></span>
                        <span style="font-size: 0.75rem;    line-height: 1.25rem;color: #5D667A;">
                            &nbsp;本人已阅读和确认，理解并同意<a class="showNotes" style="color: #397be6 !important;" onclick="showNotes()">《投保申明及重要事项》</a>
                            <a class="showDeclare" style="color: #397be6 !important;" onclick="showClauseDeclare()">《保险条款》</a>
                            <a class="showDeclare" style="color:#397be6 !important;" onclick="showDeclare()">《理赔流程》</a>
                        </span>
                    </div>
                    <div style="clear: both"></div>
                </div>
            </div>
            <div style="padding: 0 1.25rem;margin: 0.19rem 0 6.75rem;">
                <div class="gopay1 ft_3" onclick="submitInfo()">
                    <span class="ft_2" style="line-height: 2.75rem;font-size: 1.065rem;;">￥</span><span class="subamount ft_1" style="font-size: 1.065rem; font-family:DINPro-Bold">- -</span><span style="font-size: 1rem;">&nbsp;支付完成投保</span>
                </div>
            </div>
        </div>
    </form>
</div>
    #if($!{step}!="step2")
        <div class="step_s1 step1 fix-iphonex-adaption">
            <div class="expenseBtn">
                <div class="paybut" onclick="nextstep()">
                    <span>下一步</span>
                </div>
            </div>
        </div>
    #end
<div id="opa" class="declareBox" style="">
</div>
<div class="opa1">
</div>
<div style="display: none;">
    <li class="toa"></li>
</div>
<div id="bdmsg" style="">
    <div class="bdmsg1 ft_3"><span class="bdmsgspan"></span></div>
</div>
<div class="paybox ft_3">
##    微信内置浏览器接收不到post数据？
    <form action="/payCenter/redirect" method="get" id="payForm" target="_blank">
    <div style="padding: 1.25rem;height: 15rem">
        <div style="text-align: center;margin-top: 0.315rem;">
            <span>选择支付方式</span><span style="float: right;position: absolute;margin-left: 5.6rem;"><img class="closepay" style="width: 60%;" src="/img/cuspayImgs/icon_payclose.png"></img></span>
        </div>
        <table style="width: 100%;margin-top:0.9rem;border-collapse: collapse;">
            <thead>
            <tr>
                <th width="13%"></th>
                <th></th>
                <th  width="1.25rem"></th>
            </tr>
            </thead>
            <tbody id="payment_table">
                    <tr style="border-bottom: 1px solid #eeeeee;">
                        <td style="height: 4.19rem;">
                            <img style="width: 2rem" src="/img/cuspayImgs/icon_baidupay.png">
                        </td>
                        <td>
                            <span>百度钱包</span>
                        </td>
                        <td>
                            <input type="hidden" class="payment_code" value="baidupurse"/>
                            <img class="checked_close"  style="width:1.25rem;margin-top: 5px;" src="/img/cuspayImgs/GroupCopy2.png">
                            <img class="checked_on" style="width:1.25rem; margin-top: 5px;display: none" src="/img/cuspayImgs/GroupCopy3.png">
                        </td>
                    </tr>
                    ##允许其他支付方式
##                    #if($!{baiduPayType} == "2")
                    <tr style="border-bottom: 1px solid #eeeeee;" class="otherPayType">
                        <td style="height: 4.19rem;">
                            <img style="width:2rem;" src="/img/cuspayImgs/icon_alipay.png">
                        </td>
                        <td>
                            <span>支付宝</span>
                        </td>
                        <td>
                            <input type="hidden" class="platformId"  value="alipay"/>
                            <input type="hidden" class="payment_code" value="platform"/>
                            <img class="checked_close"   style="width:1.25rem;margin-top: 5px;" src="/img/cuspayImgs/GroupCopy2.png">
                            <img class="checked_on"      style="width:1.25rem;margin-top: 5px;display: none" src="/img/cuspayImgs/GroupCopy3.png">
                        </td>
                    </tr>
                    <tr class="otherPayType">
                        <td style="height: 4.19rem;">
                            <img style="width:2rem;" src="/img/cuspayImgs/icon_wxpay.png">
                        </td>
                        <td>
                            <span>微信</span>
                        </td>
                        <td>
                            <input type="hidden" class="platformId"  value="wxpay"/>
                            <input type="hidden" class="payment_code" value="platform"/>
                            <img class="checked_close"   style="width:1.25rem;margin-top: 5px;" src="/img/cuspayImgs/GroupCopy2.png">
                            <img class="checked_on"      style="width:1.25rem;margin-top: 5px;display: none" src="/img/cuspayImgs/GroupCopy3.png">
                        </td>
                    </tr>
##                    #end
            </tbody>
        </table>
        <input type="hidden" id="payId" name="payId" value="" />
        <input type="hidden" name="paymentType" id="selected_payment" value="baidupurse"/>
        <input type="hidden" name="platPayType" id="selected_platPayType" value=""/>
    </div>
    </form>
    <div  style=" bottom: 3rem;position: absolute;width: 100%;">
        <div class="gopay1 ft_3" id="toPayButton" style="    margin: 0 1.25rem;">
            <span class="ft_2" style="font-size: 1.065rem;">￥</span><span class="subamount ft_2" style="font-size: 1.065rem; font-family:DINPro-Bold;">__</span><span class="ft_3" style="font-size: 1rem;">&nbsp;去付款</span>
        </div>
    </div>
</div>
##<script type="text/javascript" src="/js/mobile/calendarnew.js"></script>
<script type="text/javascript">

    window.onpageshow = function(event) {
        if (event.persisted) {
            window.location.reload();
        }
    };

    var plans = eval("(" + sessionStorage.getItem("plans") + ")");

    var CheckPuserPhoneRepeat = false;//配置项，是否验证重复的手机号，1验重，0不验重
    var productCvrgShowList = {}, planCvrgDetailShowList = {};//页面版本的保险条款需要保障项目信息，在取的时候做缓存
    var planlist = {};
    var maxAge=80000000,minAge=0,maxAgeShow="80岁",minAgeShow="0岁",minAdultAge=18000000,minAdultShow="18岁",maxAdultAge="80000000",maxAdultShow="80岁",agerang=[];
    var company="",pro="",pla="",cha="",jxandlbday="",toCountry="",countryList = {},firstinsuretime="",isYear,planMaxDays=0,maxPersonNum;
    var proInfo = [] , plaInfo = [] , planInfo=[],firstload = true;
    var totalprice = new Array();
    var beneficiaryDefaultStr = "法定Legal Heir";
    // jQuery.mobile.autoInitializePage = false;//!!!important，其解决引入生日控件时导致页面整体css发生紊乱的问题

    $(window).load(function () {
        if(((sessionStorage.getItem("pusers")!=null&&sessionStorage.getItem("pusers")!="")||(sessionStorage.getItem("pinsure")!=null&&sessionStorage.getItem("pinsure")!=""))){
            var pusers="",pinsure="";
            if(sessionStorage.getItem("pusers")!=""&&sessionStorage.getItem("pusers")!=null) {
                pusers = eval("(" + sessionStorage.getItem("pusers") + ")");
            }
            if(sessionStorage.getItem("pinsure")!=""&&sessionStorage.getItem("pinsure")!=null) {
                pinsure = eval("(" + sessionStorage.getItem("pinsure") + ")");
            }
            var isincludeinsure = sessionStorage.getItem("isincludeinsure");
            if(pinsure!=null&&pinsure!=""){
                if(pinsure.insuredname!=""){
                    $(".demo-input-holder").find(".phName").val(pinsure.insuredname)
                }
                if(pinsure.gend!="") {
                    $(".demo-input-holder").find(".phGend").val(pinsure.gend)
                }
                if(pinsure.birthday!=""){
                    $(".demo-input-holder").find(".phBirth").val(pinsure.birthday)
                }
                if(pinsure.insuredIdNoType!="") {
                    $(".demo-input-holder").find(".phIddtype").val(pinsure.insuredIdNoType)
                }
                if(pinsure.insuredIdNo!=""){
                    $(".demo-input-holder").find(".phIddnum").val(pinsure.insuredIdNo)
                }
                if(pinsure.homePhoneNo!=""){
                    $(".demo-input-holder").find(".phTel").val(pinsure.homePhoneNo)
                }
                if(pinsure.emailAddr!=""){
                    $(".demo-input-holder").find(".phEmail").val(pinsure.emailAddr)
                }
                $(".demo-input-holder").find(".inputlabel").each(function () {
                    var o = $(this);
                    if ($(o).siblings("input,select").val() != ""&&$(o).siblings("input,select").val() != null) {
                        $(o).removeClass("inputlabel").addClass("outlabel")
                    }
                })
            }

            if(pusers!=null&&pusers!=""){
                for(var j=0;j<pusers.length;j++){
                    if(isincludeinsure!="1"&&j==0){
                        var insure = $(".insureex").find(".demo-input").clone();
                        var insureindex_1=1;
                        insure.children("div").each(function(){
                            if(insureindex_1>3){
                                $(this).appendTo($(".myself"));
                            }
                            insureindex_1=insureindex_1+1;
                        })
                        $(".ismy_1").show();
                        if (pusers[j].relationship != "") {
                            $(".myself").find(".phRelation").val(pusers[j].relationship)
                        }
                        if (pusers[j].insuredname != "") {
                            $(".myself").find(".phName").val(pusers[j].insuredname);
                        }
                        if (pusers[j].gend != "") {
                            $(".myself").find(".phGend").val(pusers[j].gend)
                        }
                        if (pusers[j].birthday != "") {
                            $(".myself").find(".phBirth").val(pusers[j].birthday);
                        }
                        if (pusers[j].insuredIdNoType != "") {
                            $(".myself").find(".phIddtype").val(pusers[j].insuredIdNoType)
                        }
                        if (pusers[j].insuredIdNo != "") {
                            $(".myself").find(".phIddnum").val(pusers[j].insuredIdNo);
                        }
                        if (pusers[j].homePhoneNo != "") {
                            $(".myself").find(".phTel").val(pusers[j].homePhoneNo);
                        }
                        if (pusers[j].emailAddr != "") {
                            $(".myself").find(".phEmail").val(pusers[j].emailAddr);
                        }
                        $(".myself").find(".inputlabel").each(function () {
                            var o = $(this);
                            if (!($(o).siblings("input").val() == "" || $(o).siblings("select").val() == "")) {
                                $(o).removeClass("inputlabel").addClass("outlabel")
                            }

                        })
                        if (j + 1 == pusers.length) {
                            $(".myself").removeClass("lastinsure")
                            insure.addClass("lastinsure")
                        }
                    }else {
                        $(".ismy_1").show();
                        var insure = $(".insureex").find(".demo-input").clone();
                        if (pusers[j].relationship != "") {
                            insure.find(".phRelation").val(pusers[j].relationship)
                        }
                        if (pusers[j].insuredname != "") {
                            insure.find(".phName").val(pusers[j].insuredname);
                        }
                        if (pusers[j].gend != "") {
                            insure.find(".phGend").val(pusers[j].gend)
                        }
                        if (pusers[j].birthday != "") {
                            insure.find(".phBirth").val(pusers[j].birthday);
                        }
                        if (pusers[j].insuredIdNoType != "") {
                            insure.find(".phIddtype").val(pusers[j].insuredIdNoType)
                        }
                        if (pusers[j].insuredIdNo != "") {
                            insure.find(".phIddnum").val(pusers[j].insuredIdNo);
                        }
                        if (pusers[j].homePhoneNo != "") {
                            insure.find(".phTel").val(pusers[j].homePhoneNo);
                        }
                        if (pusers[j].emailAddr != "") {
                            insure.find(".phEmail").val(pusers[j].emailAddr);
                        }
                        insure.find(".inputlabel").each(function () {
                            var o = $(this);
                            if (!($(o).siblings("input").val() == "" || $(o).siblings("select").val() == "")) {
                                $(o).removeClass("inputlabel").addClass("outlabel")
                            }

                        })
                        if (j + 1 == pusers.length) {
                            $(".myself").removeClass("lastinsure")
                            insure.addClass("lastinsure")
                        }
                        var index = $(".isnures").find(".demo-input").length;
                        insure.find(".insureIndex").html(index + 1);
                        insure.appendTo(".isnures");
                    }
                }
            }
        }

        planlist =   eval("(" + sessionStorage.getItem("planlist") + ")");
        productlist =  eval("(" + sessionStorage.getItem("prolist") + ")");
        for(var i=0;i<productlist.length;i++){
            proInfo[productlist[i]["id"]] = productlist[i];
        }
        var x = $(".phRelation");
        x.selectedIndex = -1;
        var insureLastTime = proInfo[productlist[0]["id"]]["insureLastTime"];
        firstinsuretime = proInfo[productlist[0]["id"]].firstinsuretime.split(" ")[0];
        maxPersonNum = proInfo[productlist[0]["id"]].maxPersonNum;
        $("#activeDat").prop("min",firstinsuretime);
        if($("#activeDat").val() == "" || new Date($("#activeDat").val().replace(/-/g,"/")) - new Date(firstinsuretime.replace(/-/g,"/")) < 0){
            $("#activeDat").val(sessionStorage.getItem("activeDat")==null||""?firstinsuretime:sessionStorage.getItem("activeDat"));
        }
        if($("#exprDat").val() == "" || new Date($("#exprDat").val().replace(/-/g,"/")) - new Date(firstinsuretime.replace(/-/g,"/")) < 0){
            $("#exprDat").val(sessionStorage.getItem("exprDat")==null||""?firstinsuretime:sessionStorage.getItem("exprDat"));
        }
        for (var i = 0; i < planlist.length; i++) {
            plaInfo[planlist[i].planId] = planlist[i];//把计划信息数组转化为以计划id为键值的json数据
        }
        var recentplanid=""
        recentplanid = sessionStorage.getItem("planid")==null? "$!{sdPlanId}" :sessionStorage.getItem("planid");
        jQuery("#planId").val(recentplanid);


        for(var i = 0; i< plans.length; i++){
            if(plans[i].planid == recentplanid){
                jQuery("#planCode").val(plans[i].plancode);
            }
        }

        $(".policyplan").html(plaInfo[recentplanid].planName);

        var productId = sessionStorage.getItem("productid");
        var pro = JSON.parse(sessionStorage.getItem("prolist"))
        for(var i=0;i<pro.length;i++){
            if(pro[i].id==productId){
                $(".policyproduct").html(pro[i].name)
            }
        }
        showsingleprice(productId,recentplanid)
    })

    $(".step2").on("change",".demo-input-holder input,select",function () {
        var weddc = $(this).closest(".demo-input").attr("class");
//            if(weddc.indexOf("myself")==-1) {
            var insurants = formatBackInsurant();
            sessionStorage.setItem("pinsure", insurants["pinsure"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
//        }
    })

    $(".step2").on("change",".insureInfo input,select",function () {
        var insurants = formatBackInsurant();
        sessionStorage.setItem("pusers",insurants["pusers"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
    })


    var allowPayType = 1;

    $(function(){
        //默认勾选第一行的支付方式
        $("#payment_table tr:first .checked_close").css("display","none");
        $("#payment_table tr:first .checked_on").css("display","block");
        var firstPayCode = $("#payment_table tr:first .payment_code").val();
        $("#selected_payment").val(firstPayCode);
        if("platform" == firstPayCode){ //核心支付的话，填充客户选择的支付方式
            $("#selected_platPayType").val($("#payment_table tr:first .platformId").val());
        }


        //投保日期
        var stDay=new Date((sessionStorage.getItem("activeDat")+" 00:00:00").replace(/-/gi,"/"));
        var edDay=new Date((sessionStorage.getItem("exprDat")+" 23:59:59").replace(/-/gi,"/"));

        jQuery("#activeDatHidden").val(stDay);
        jQuery("#exprDatHidden").val(edDay);

        $(".policyactiveDat").html(sessionStorage.getItem("activeDat"));
        $(".policyexprDat").html(sessionStorage.getItem("exprDat"));

        t=parseInt((edDay - stDay + 1000),10)/86400000;
        $("#sts").val(t);

        //
        var paramDto = sessionStorage.getItem("paramDTO");
        jQuery("#paramDTO").val(paramDto);
        try {
            var paramJson = jQuery.parseJSON(paramDto);
            allowPayType = paramJson.PayType;
        }catch (e){
            console.log(e);
        }

        if(allowPayType == 2){
            $(".otherPayType").show();
        }else {
            $(".otherPayType").hide();
        }
    })

    //选择支付方式
    $(".checked_close").on("click",function(){
        $("#payment_table tr .checked_close").css("display","block");
        $("#payment_table tr .checked_on").css("display","none");
        var paymentCode =$(this).siblings(".payment_code").val();
        $("#selected_payment").val(paymentCode);
        if("platform" == paymentCode){ //核心支付的话，填充客户选择的支付方式
            $("#selected_platPayType").val($(this).siblings(".platformId").val());
        }
        $(this).siblings(".checked_on").css("display","block");
        $(this).css("display","none");
    })


    function changeDate(productid,planid,plancode){//加载时间
        if(isYear){
            var t = 365;
            $("#sts").val(365);
            $("#totalday").html("共计1年");
        }else{
            var stDay=new Date((jQuery("#activeDat").val()+" 00:00:00").replace(/-/gi,"/"));
            var edDay=new Date((jQuery("#exprDat").val()+" 23:59:59").replace(/-/gi,"/"));
            t=parseInt((edDay - stDay + 1000),10)/86400000;
            $("#sts").val(t);
            $("#totalday").html("共计"+t+"天");
        }
        datachange()
        showsingleprice(productid,planid,plancode);
    }

    var all_birth=new Array(),all_birth1=new Array(),all_pri=new Array();
    var m="";
    var allBirthMin = [], allBirthMax = [], allPri = []; //非自定义保额产品，计划各年龄价格区间（同时只存在一个计划）
    var allBirthCvrg = [], allPriCvrg = []; //自定义保额产品，各计划年龄价格区间（同时存在若干个计划；且儿童和成人的保额区间不一定相同）
    var priceBuffer={};//定义一个缓冲对象，用来存储某些计划或某些保障项目和产品与日期的价格
    function showsingleprice(productid,planid){//加载价格
        var recentPlan = planid;
        var recentProduct = productid;
        var t=$("#sts").val();
        var p="；";
        var queryData = {"planId": recentPlan, "days": t};
        var queryDataString=recentPlan+t;//定义存储缓冲对象中的属性名的字符串类型变量

        allBirthMin = new Array(), allBirthMax = new Array(), allPri = new Array();
        allBirthCvrg = new Array(), allPriCvrg = new Array();

        var flag=false;           //定义标记，用来判断从前台缓冲拿数据还是从后台拿数据
        var data={};              //存储从前台缓冲中查找到的数据

        //从前台缓冲中查找数据
        if(priceBuffer[queryDataString]){
            flag=true;
            data=priceBuffer[queryDataString];
        }

        if(flag){//使用从前台缓冲中查到的数据
            usePriceData(data);
        }
        else {//从后台拿数据
            jQuery.ajax({
                url: "/policyCenter/insure/ajaxPrices.action",
                type: "post",
                dataType: "json",
                contentType: 'application/x-www-form-urlencoded',
                data: queryData,
                error: function () {
                    //alert("通信失败！");
                },
                success: function (data) {
                    if (data.success != "1") {
                        alertBdMsg("获取保费价格失败，错误：" + data.message + "，请联系客服!");
                        return false;
                    }
                    priceBuffer[queryDataString]=data;//在缓冲对象中追加该属性，把某些计划或某些保障项目和产品与日期的价格为该属性赋值
                    usePriceData(data);
                }
            })
        }
        function usePriceData(data){
            //如果是扩展计划的话，那么就取扩展计划的信息
            var backups = null;

            productCvrgShowList[recentProduct] = data["messageName"];//查看保险条款时需要用到的数据

            //获取保险人信息，并将其反序列化
            var obj = null;
            if (data.messageRemarkes) {
                for (var i = 0; i < data.messageRemarkes.length; i++) {
                    var m = data.messageRemarkes[i].remarkes;
                    obj = jQuery.parseJSON(m);
                }
            }


            if (data.jsFormula != undefined && data.jsFormula != ""){
                var priceJson=[];
                eval("getPrice = function(json){if(typeof(json) == 'string'){json = eval('('+json+')')};"+data.jsFormula+";}");//定义取价格函数，从实际价格转换为价格公式
                priceJson.days = t;//计费参数的天数，年龄到具体再赋予
                //正则提取出费率中的年龄.....
                var jsFormulaAges =data.jsFormula.match(/json.age\s*(<|>)=\s*\d+/g);
                for (var i=0;i<jsFormulaAges.length;i++){
                    jsFormulaAges[i] = parseInt(jsFormulaAges[i].substring(jsFormulaAges[i].indexOf('=')+1));
                }
                jsFormulaAges.sort();

                agerang.minage = jsFormulaAges[0]
                agerang.maxage = jsFormulaAges[jsFormulaAges.length-1]
                for (var i = 0;i <= (jsFormulaAges.length/2)-1; i++){
                    priceJson.age = jsFormulaAges[2*i];
                    p +=  ""+jsFormulaAges[2*i]+"-"+jsFormulaAges[2*i+1]+"岁："+getPrice(priceJson)+"元/人；";
                    var everyprice = []
                    everyprice.minage = jsFormulaAges[2*i];
                    everyprice.maxage = jsFormulaAges[2*i+1];
                    everyprice.price = getPrice(priceJson);
                    totalprice[i] = everyprice;
                }

            }else {
                for (var i = 0; i < data.message.length; i++) {
                    p += data.message[i].minAge + "-" + data.message[i].maxAge + "岁：" + data.message[i].price + "元/人；";
                    all_birth.push(data.message[i].minAge);
                    all_birth1.push(data.message[i].maxAge);
                    all_pri.push(data.message[i].price);
                }
            }
            p = p.slice(0, -1);
            $(".subamount").html(gttotalprice());
        }
    }


    function zerofill(s) {
        var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
        s = isNaN(s) ? 0 : s;
        return (s < 10 ? '0' : '') + s.toString();
    }

    var allCount = new Array();



    function showpolicyholder(){
        jQuery("html,body").css({"height":"auto","overflow":"hidden","position":"fixed","width":"100%"});
        if($(".policyHolder .phName").val()!=""|| $(".policyHolder .phIddnum").val()!="") {
            $(".holderInfodelete").show();
        }else{
            $(".holderInfodelete").hide();
        }
        $("#holderEditBox").find(".inputlabel").each(function(){
            var o = $(this);
            if ($(o).siblings("input").val()!="") {
                $(o).removeClass("inputlabel").addClass("outlabel")
            }
        })
        $("#holderEditBox").show();
    }

    //投保人信息修改或者新加
    function updateOraddPholder(e){
        //检查投保人信息
        var productId = sessionStorage.getItem("productid");
        var pEle = $(e).parent().parent().find(".mainDeclare");
        $("#holderEditBox").find(".errmsg").addClass("divideline").removeClass("errmsg").find("div").html("")//---------------------------如果是个人--------------------------------
        var altMsg = {"phName":"请填写投保人姓名！","phIddtype":"请选择投保人证件类型！","phIddnum":"请填写投保人证件号码！","phGend":"请选择投保人性别！","phBirth":"请填写投保人出生日期！","phTel":"请填写投保人电话号码！","phEmail":"请填写电子邮箱！"};
        var isok = true;
        $(".pHolder").find("input,select").each(function(){//检查是否已经完善基本信息
            if($(this).val() == "" && altMsg[this.className]) {
                alertBdMsg(altMsg[this.className]);
                holdererroshow($(this),altMsg[this.className])
                isok = false;
                return false;
            }
        });
        if (!isok){
            return false;
        }
            if($(".pHolder").find(".phIddtype").val()=="身份证"){//自动根据身份证号识别生日性别
                if($(".pHolder").find(".phIddnum").val()!=""){
                    var _id = analyzeId($(".pHolder").find(".phIddnum").val());
                    if(_id){
                    }else{
                        alertBdMsg("身份证号码有误");
                        holdererroshow($(".pHolder").find(".phIddnum"),"身份证号码错误")
                        return;
                    }
                }
            }
        if(jQuery(".pHolder .phBirth").val()!=""&&getAge(jQuery(".pHolder .phBirth").val(),proInfo[productId].calAgeRule) < 18){
            alertBdMsg("未成年人不能作为投保人");
            holdererroshow(jQuery(".pHolder .phBirth"),"未成年人不能作为投保人")
            return false;
        }

            if(jQuery(".pHolder .phEmail").val()!=""&&!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(jQuery(".pHolder .phEmail").val())){//检查邮箱信息
                alertBdMsg("请填写正确的投保人电子邮箱");
                holdererroshow(jQuery(".pHolder .phEmail"),"请填写正确的投保人电子邮箱")
                return false;
            }


            if(isok){
                pEle.find(".phName").val() && jQuery(".policyHolder .phName").val(pEle.find(".phName").val());
                pEle.find(".phName").val() && jQuery(".policyHolder .phNameStr").html(pEle.find(".phName").val());
                pEle.find(".phIddnum").val() && jQuery(".policyHolder .phIddnum").val(pEle.find(".phIddnum").val());
                pEle.find(".phIddnum").val() && jQuery(".policyHolder .phIddnumStr").html(pEle.find(".phIddnum").val());
                pEle.find(".phIddtype").val() && jQuery(".policyHolder .phIddtype").val(pEle.find(".phIddtype").val());
                pEle.find(".phIddtype").val() && jQuery(".policyHolder .phIddtypeStr").html(pEle.find(".phIddtype").val());
                pEle.find(".phGend").val() && jQuery(".policyHolder .phGend").val(pEle.find(".phGend").val());
                pEle.find(".phBirth").val() && jQuery(".policyHolder .phBirth").val(pEle.find(".phBirth").val());
                pEle.find(".phTel").val() && jQuery(".policyHolder .phTel").val(pEle.find(".phTel").val());
                pEle.find(".phEmail").val() && jQuery(".policyHolder .phEmail").val(pEle.find(".phEmail").val());
                pEle.find(".phGend").val() && jQuery(".policyHolder .phGendStr").html(pEle.find(".phGend").val());
                pEle.find(".phBirth").val() && jQuery(".policyHolder .phBirthStr").html(pEle.find(".phBirth").val());
                pEle.find(".phTel").val() && jQuery(".policyHolder .phTelStr").html(pEle.find(".phTel").val());
                pEle.find(".phEmail").val() && jQuery(".policyHolder .phEmailStr").html(pEle.find(".phEmail").val());
                $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
                $(".pHoldermode").show();
                $(".policyHolder").show();
                $(".holderInfo").find(".forAddpHolder").hide()
                $(".holderInfo").find(".liberStrline").show();
                $(".holderInfo").find(".wrapBox_link").removeClass("Boxautoheight");
                $(".holderInfo").find(".enteringArrow").removeClass("arrowautoheight");
                $("#holderEditBox").hide();
                document.body.scrollTop = 100;

                var phIddnum=$(".policyHolder .phIddnum").val();
                var list=[];
                list = $("#insurantInfoTbody").find(".phRelation");
                if($("#isinsure").attr("checked")=="checked") {//如果是被保险人是本人，则录制到被保险人列表，否则则移除。
                    if(list.length==0){//如果暂未选择被保险人时，则直接添加至被保险人。
                        dupPHtoI();
                    }else{
                        var iseql=false;
                        for(var i=0;i<list.length;i++){//如果已选择被保险人时，则判断其是否与投保人重复，重复则不再添加，反之添加
                            if(list.eq(i).val()=="本人 Policyholder"){
                                dupPHtoI2()
                                iseql=false;
                                return;
                            }else{
                                iseql=true;
                            }
                        }
                        if(iseql==true){
                            dupPHtoI();
                        }
                    }

                }else{
                    list = $("#insurantInfoTbody").find(".phIddnum");
                    for(var i=0;i<list.length;i++){
                        if(list.eq(i).val()==phIddnum){//如果是被保险人不选本人,则移除。
                            list.eq(i).closest(".insurantInfo_tr").remove();
                            $(".contactsBox input").each(function(){//如果常用联系人中也存在投保人本人，同时取消选中状态
                                var str =$(this).parent().siblings().find(".contactsCertificateNo").text();
                                var num = str.replace(/[^a-zA-Z0-9]/ig,"");
                                if(phIddnum==num){
                                    $(this).prop("checked",false).change();
                                }
                            })
                        }
                    }

                }
            }
        // policyUserCount();
    }

    //复制投保人信息至被保险人中
    function dupPHtoI() {
        var productId = sessionStorage.getItem("productid");
        var listcount=[];
        listcount = $("#insurantInfoTbody").find(".phRelation");//或者phIddnum、phIddtype等等insurantInfo_tr的对象即可
        for(var j=0;j<listcount.length;j++){
            if(listcount.eq(j).val()=="本人 Policyholder" &&  listcount.length>0  ){
                alertBdMsg("被保险人列表中已存在本人，请重新确认与投保人的关系！");
                return;
            }
        }
        var index="c"+$(".policyHolder .phIddnum").val();
        var insureIndex = listcount.length+1
        var insurantInfoTemplate = $("#insurantInfoTemplate").find(".insurantInfo_tr").clone();
        insurantInfoTemplate.find(".index").val(index);
        insurantInfoTemplate.find(".insureIndex").html("被保险人 "+insureIndex);
        insurantInfoTemplate.find(".phName").val($(".policyHolder .phName").val());
        insurantInfoTemplate.find(".phNameStr").html($(".policyHolder .phName").val());
        insurantInfoTemplate.find(".phBirth").val($(".policyHolder .phBirth").val());
        insurantInfoTemplate.find(".phGend").val($(".policyHolder .phGend").val());
        insurantInfoTemplate.find(".phBirthStr").html($(".policyHolder .phBirth").val());
        insurantInfoTemplate.find(".phGendStr").html($(".policyHolder .phGend").val());
        insurantInfoTemplate.find(".phIddnum").val($(".policyHolder .phIddnum").val());
        insurantInfoTemplate.find(".phIddnumStr").html($(".policyHolder .phIddnum").val());
        insurantInfoTemplate.find(".phIddtype").val($(".policyHolder .phIddtype").val());
        insurantInfoTemplate.find(".phIddtypeStr").html($(".policyHolder .phIddtype").val());
        insurantInfoTemplate.find(".phEmail").val($(".policyHolder .phEmail").val());
        insurantInfoTemplate.find(".phEmailStr").html($(".policyHolder .phEmail").val());
        insurantInfoTemplate.find(".phTelStr").html($(".policyHolder .phTel").val());
        insurantInfoTemplate.find(".phRelationStr").html("本人 Policyholder");
        insurantInfoTemplate.find(".phTel").val($(".policyHolder .phTel").val());
        insurantInfoTemplate.find(".phRelation").val("本人 Policyholder");
        insurantInfoTemplate.find(".PUBeneficiaryButton").val($(".policyHolder .PUBeneficiary").val());
        insurantInfoTemplate.find(".PUBeneficiary").val($(".policyHolder .PUBeneficiary").val());
        insurantInfoTemplate.find(".PUBeneficiaryBak").val($(".policyHolder .PUBeneficiaryBak").val());
        insurantInfoTemplate.appendTo("#insurantInfoTbody");
        if(proInfo[productId].haveLiabilityConfig == 1){
            $(".forAddper").hide();
        }else{
            $(".forAddper").show();
        }
    }

    //复制投保人信息至被保险人中(修改后的投保人信息复制到被保险人是本人的tab中)
    function dupPHtoI2() {
        var productId = sessionStorage.getItem("productid");
//        var insureIndex = listcount.length+1
        $("#insurantInfoTbody").find(".insurantInfo_tr").each(function(){
            if ($(this).find(".phRelation").val()=="本人 Policyholder"){
                var insurantInfoTemplate = $(this)
                insurantInfoTemplate.find(".phName").val($(".policyHolder .phName").val());
                insurantInfoTemplate.find(".phNameStr").html($(".policyHolder .phName").val());
                insurantInfoTemplate.find(".phBirth").val($(".policyHolder .phBirth").val());
                insurantInfoTemplate.find(".phGend").val($(".policyHolder .phGend").val());
                insurantInfoTemplate.find(".phBirthStr").html($(".policyHolder .phBirth").val());
                insurantInfoTemplate.find(".phGendStr").html($(".policyHolder .phGend").val());
                insurantInfoTemplate.find(".phIddnum").val($(".policyHolder .phIddnum").val());
                insurantInfoTemplate.find(".phIddnumStr").html($(".policyHolder .phIddnum").val());
                insurantInfoTemplate.find(".phIddtype").val($(".policyHolder .phIddtype").val());
                insurantInfoTemplate.find(".phIddtypeStr").html($(".policyHolder .phIddtype").val());
                insurantInfoTemplate.find(".phEmail").val($(".policyHolder .phEmail").val());
                insurantInfoTemplate.find(".phEmailStr").html($(".policyHolder .phEmail").val());
                insurantInfoTemplate.find(".phTelStr").html($(".policyHolder .phTel").val());
                insurantInfoTemplate.find(".phRelationStr").html("本人 Policyholder");
                insurantInfoTemplate.find(".phTel").val($(".policyHolder .phTel").val());
                insurantInfoTemplate.find(".phRelation").val("本人 Policyholder");
                insurantInfoTemplate.find(".PUBeneficiaryButton").val($(".policyHolder .PUBeneficiary").val());
                insurantInfoTemplate.find(".PUBeneficiary").val($(".policyHolder .PUBeneficiary").val());
                insurantInfoTemplate.find(".PUBeneficiaryBak").val($(".policyHolder .PUBeneficiaryBak").val());
                if(proInfo[productId].haveLiabilityConfig == 1){
                    $(".forAddper").hide();
                }else{
                    $(".forAddper").show();
                }
            }
        })

    }

    $(".forAddpHolder").on("click",function(){
        var productId = sessionStorage.getItem("productid");
        var altMsg = {"phName":"请填写投保人姓名","phGend":"请选择投保人性别","phBirth":"请填写投保人出生日期","phIddtype":"请选择投保人证件类型","phIddnum":"请填写投保人证件号","phTel":"请填写投保人手机号码","phEmail":"请填写投保人电子邮箱"}
        var isre=false;

        var alIndex = 0
        $(".demo-input-holder").find("select,input").each(function(){
            if($(this).val()==""){
                holdererroshow($(this), altMsg[this.className])
                isre=true;
                if(alIndex==0) {
//                    document.body.scrollTop = 1500;
                    $('html, body').animate({
                        scrollTop: $(this).offset().top-300
                    },0)
                    alertBdMsg(altMsg[this.className])
                    alIndex=alIndex+1;
                }
            }
        })
        if(isre){
            return;
        }
        if(getAge(jQuery(".demo-input-holder .phBirth").val(),proInfo[productId].calAgeRule) < 18){
            $('html, body').animate({
                scrollTop: $(".demo-input-holder .phBirth").offset().top-300
            },0)
            holdererroshow(jQuery(".demo-input-holder .phBirth"), "未成年人不能作为投保人！")
            alertBdMsg("未成年人不能作为投保人！");
            return false;
        }
        if($(".isnures").find(".demo-input").length>0) {
            var isworm=false
            $(".isnures").find(".demo-input").each(function(){
                if($(this).attr("class").indexOf("lastinsure")!=-1&&$(this).children("div").length>3){
                    var altMsg = {
                        "phRelation": "请选择与投保人关系",
                        "phName": "请填写被保人姓名",
                        "phIddtype": "请选择被保人证件类型",
                        "phIddnum": "请填写被保人证件号码",
                        "phGend": "请选择被保人性别",
                        "phBirth": "请填写被保人出生日期"
                    };
                    var alInsureIndex = 0

                    $(this).find("input,select").each(function () {
                        if ($(this).className != "phEmail" && $(this).className != "phTel" && $(this).className != "phIddnum") {
                            if (($(this).val() == "" || $(this).val() == null) && altMsg[this.className]) {
                                isre = true;
                                isworm = true;
                                insureerroshow($(this), altMsg[this.className])
                                if(alIndex==0) {
                                    $('html, body').animate({
                                        scrollTop: $(this).offset().top-300
                                    },0)
                                    alertBdMsg(altMsg[this.className])
                                    alIndex=alIndex+1;
                                }
                            }
                        }
                    })
                    if (isre) {
                        return;
                    }
                    var birthday = $(this).find(".phBirth").val()
                    var insureage = gtinsureage(birthday);
                    if (insureage < agerang.minage || insureage > agerang.maxgae) {
                        alertBdMsg("被保险人年龄不在承保范围");
                        insureerroshow($(this).find(".phBirth"), "被保险人年龄不在承保范围");
                        isok = false;
                        return false;
                    }
                    if ($(this).find(".phIddtype").val() == "身份证") {//自动根据身份证号识别生日性别
                        if ($(this).find(".phIddnum").val() != "") {
                            var _id = analyzeId($(this).find(".phIddnum").val());
                            if (_id) {
                            } else {
                                $('html, body').animate({
                                    scrollTop: $(this).find(".phIddnum").offset().top-300
                                },0)
                                alertBdMsg("身份证号码有误");
                                insureerroshow($(this).find(".phIddnum"), "身份证号码错误")
                                return;
                            }
                        }
                    }
					if(!checkBirthAndId($(this).find(".phBirth"))){
						$('html, body').animate({
							scrollTop: $(this).find(".phBirth").offset().top-300
						},0)
						alertBdMsg("出生日期与身份证号码上的出生日期不一致");
						return;
					}
					if($(this).find(".phTel").val() != "" && !/^1[3|4|5|7|8][0-9]{9}$/.test($(this).find(".phTel").val())){//检查手机号是否11位
						$('html, body').animate({
							scrollTop: $(this).find(".phTel").offset().top-300
						},0)
						alertBdMsg("请填写正确的被保人手机号");
						insureerroshow($(".isnures").find(".lastinsure").find(".phTel"),"请填写正确的被保人手机号")
						return false;
					}
                    if ($(this).find(".phEmail").val() != "" && !/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test($(".isnures").find(".lastinsure").find(".phEmail").val())) {//检查邮箱信息
                        $('html, body').animate({
                            scrollTop: $(this).find(".phEmail").offset().top-300
                        },0)
                        alertBdMsg("请填写正确的被保人电子邮箱");
                        insureerroshow($(this).find(".phEmail"), "请填写正确的被保人电子邮箱")
                        return false;
                    }
                }
            })
            if(isworm){
                return false;
            }
            if ($(".isnures").find(".lastinsure").find(".phRelation").val() != "本人 Policyholder") {
                var birthday = $(".isnures").find(".lastinsure").find(".phBirth").val()
                var insureage = gtinsureage(birthday);
                if (insureage < agerang.minage || insureage > agerang.maxgae) {
                    alertBdMsg("被保险人年龄不在承保范围");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phBirth"), "被保险人年龄不在承保范围");
                    isok = false;
                    return false;
                }
                var altMsg = {
                    "phRelation": "请选择与投保人关系",
                    "phName": "请填写被保人姓名",
                    "phIddtype": "请选择被保人证件类型",
                    "phIddnum": "请填写被保人证件号码",
                    "phGend": "请选择被保人性别",
                    "phBirth": "请填写被保人出生日期"
                };
                var alInsureIndex = 0
                $(".isnures").find(".lastinsure").find("input,select").each(function () {
                    if ($(this).className != "phEmail" && $(this).className != "phTel" && $(this).className != "phIddnum") {
                        if (($(this).val() == "" || $(this).val() == null) && altMsg[this.className]) {
                            isre = true;
                            insureerroshow($(this), altMsg[this.className])
                            if(alIndex==0) {
                                $('html, body').animate({
                                    scrollTop: $(this).offset().top-300
                                },0)
                                alertBdMsg(altMsg[this.className])
                                alIndex=alIndex+1;
                            }
                        }
                    }
                })
                if (isre) {
                    return;
                }
                var birthday = $(".isnures").find(".lastinsure").find(".phBirth").val()
                var insureage = gtinsureage(birthday);
                if (insureage < agerang.minage || insureage > agerang.maxgae) {
                    alertBdMsg("被保险人年龄不在承保范围");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phBirth"), "被保险人年龄不在承保范围");
                    isok = false;
                    return false;
                }
                if ($(".isnures").find(".lastinsure").find(".phIddtype").val() == "身份证") {//自动根据身份证号识别生日性别
                    if ($(".isnures").find(".lastinsure").find(".phIddnum").val() != "") {
                        var _id = analyzeId($(".isnures").find(".lastinsure").find(".phIddnum").val());
                        if (_id) {
                        } else {
                            $('html, body').animate({
                                scrollTop: $(".isnures").find(".lastinsure").find(".phIddtype").offset().top-300
                            },0)
                            alertBdMsg("身份证号码有误");
                            insureerroshow($(".isnures").find(".lastinsure").find(".phIddnum"), "身份证号码错误")
                            return;
                        }
                    }
                }
				if(!checkBirthAndId($(".isnures").find(".lastinsure").find(".phBirth"))){
					$('html, body').animate({
						scrollTop: $(".isnures").find(".lastinsure").find(".phBirth").offset().top-300
					},0)
					alertBdMsg("出生日期与身份证号码上的出生日期不一致");
					return;
				}
				if($(".isnures").find(".lastinsure").find(".phTel").val() != "" && !/^1[3|4|5|7|8][0-9]{9}$/.test($(".isnures").find(".lastinsure").find(".phTel").val())){//检查手机号是否11位
					$('html, body').animate({
						scrollTop: $(".isnures").find(".lastinsure").find(".phTel").offset().top-300
					},0)
					alertBdMsg("请填写正确的被保人手机号");
					insureerroshow($(".isnures").find(".lastinsure").find(".phTel"),"请填写正确的被保人手机号")
					return false;
				}
                if ($(".isnures").find(".lastinsure").find(".phEmail").val() != "" && !/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test($(".isnures").find(".lastinsure").find(".phEmail").val())) {//检查邮箱信息
                    $('html, body').animate({
                        scrollTop: $(".isnures").find(".lastinsure").find(".phEmail").offset().top-300
                    },0)
                    alertBdMsg("请填写正确的被保人电子邮箱");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phEmail"), "请填写正确的被保人电子邮箱")
                    return false;
                }
            }else{
                $(".ismy_1").show();
            }
        }
        if($(".demo-input-holder").find(".phIddtype").val()=="身份证"){//自动根据身份证号识别生日性别
            if($(".demo-input-holder").find(".phIddnum").val()!=""){
                var _id = analyzeId($(".demo-input-holder").find(".phIddnum").val());
                if(_id){
                }else{
                    $('html, body').animate({
                        scrollTop: $(".demo-input-holder").find(".phIddnum").find(".phEmail").offset().top-300
                    },0)
                    alertBdMsg("身份证号码有误");
                    holdererroshow($(".demo-input-holder").find(".phIddnum"),"身份证号码错误")
                    return false;
                }
            }
        }
		if(!checkBirthAndId($(".demo-input-holder").find(".phBirth"))){
			$('html, body').animate({
				scrollTop: $(".demo-input-holder").find(".phBirth").offset().top-300
			},0)
			alertBdMsg("出生日期与身份证号码上的出生日期不一致");
			return;
		}
		if(!/^1[3|4|5|7|8][0-9]{9}$/.test(jQuery(".demo-input-holder .phTel").val())){//检查手机号是否11位
            alertBdMsg("请填写正确的投保人手机号");
            holdererroshow(jQuery(".demo-input-holder .phTel"),"请填写正确的投保人手机号")
            return false;
        }
        if(jQuery(".demo-input-holder .phEmail").val()!=""&&!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(jQuery(".demo-input-holder .phEmail").val())){//检查邮箱信息
            $('html, body').animate({
                scrollTop: $(".demo-input-holder .phEmail").find(".phEmail").offset().top-300
            },0)
            alertBdMsg("请填写正确的投保人电子邮箱");
            holdererroshow(jQuery(".demo-input-holder .phEmail"),"请填写正确的投保人电子邮箱")
            return false;
        }

        $(".isnures").find(".demo-input").each(function(){
            $(this).removeClass("lastinsure");
        })
        var insure = $(".insureex").find(".demo-input").clone();
        insure.addClass("lastinsure")
        var index = $(".isnures").find(".demo-input").length;
        insure.find(".insureIndex").html(index+1);
        insure.find(".phIddtype").val("");
        insure.find(".phGend").val("");
        insure.find(".phBirth").val("");
        insure.appendTo(".isnures");
    })

	function checkFormInfo(){
		
	}
    //计算年龄函数
    function getAge(bir,calAgeRule){
        var getAgeStartTime;
        if (calAgeRule == 1) {
            getAgeStartTime = $("#activeDat").val();
        } else {
            var _today = new Date();
            var year = _today.getFullYear();
            var month = _today.getMonth() + 1 + "";
            var day = _today.getDate() + "";
            month = (month.length == 2) ? month : "0" + month;
            day = (day.length == 2) ? day : "0" + day;
            getAgeStartTime = year + "-" + month + "-" + day + " 00:00:00";
        }
        try{
            var _date = new Date(bir.split(" ")[0].replace(/-/gi,"/"));
            var _y = _date.getFullYear();
            var _m = _date.getMonth()+1;
            var _d = _date.getDate();
            var _bir = bir.split(" ")[0].split("-"),_now = getAgeStartTime.split(" ")[0].split("-");
            var _birth = _now[0]-_bir[0];
            if(_y == _bir[0]*1 &&_m == _bir[1]*1 &&_d == _bir[2]*1){
                if(_now[1]*100+_now[2]*1-_bir[1]*100-_bir[2]*1 < 0){//月系数为100，日系数为1，两者相减，大于等于0岁数为_birth，否则为_birth-1
                    _birth = _birth - 1;
                }
            }else{
                _birth = -1;
            }
        }catch(e){
            var _birth = -1;
        }
        _birth = isNaN(_birth)?-1:_birth;
        return _birth;
    }
    function getMinAge(bir,calAgeRule){
        var alllist = new Array();
        var getAgeStartTime;
        if (calAgeRule == 1) {
            getAgeStartTime = $("#activeDat").val();
        } else {
            var _today = new Date();
            var year = _today.getFullYear();
            var month = _today.getMonth() + 1 + "";
            var day = _today.getDate() + "";
            month = (month.length == 2) ? month : "0" + month;
            day = (day.length == 2) ? day : "0" + day;
            getAgeStartTime = year + "-" + month + "-" + day + " 00:00:00";
        }
        try{
            var _date = new Date(bir.split(" ")[0].replace(/-/gi,"/"));
            var _y = _date.getFullYear();
            var _m = _date.getMonth()+1;
            var _d = _date.getDate();
            var _bir = bir.split(" ")[0].split("-"),_now = getAgeStartTime.split(" ")[0].split("-");
            var _birth = _now[0]-_bir[0];
            var _mm = (12+_now[1]-_bir[1])%12;
            var _dd = (new Date(getAgeStartTime.split(" ")[0].replace(/-/gi,"/"))-new Date(bir.split(" ")[0].replace(/-/gi,"/")))/86400000;
            if(_y == _bir[0]*1 &&_m == _bir[1]*1 &&_d == _bir[2]*1){
                if(_now[1]*100+_now[2]*1-_bir[1]*100-_bir[2]*1 < 0){//月系数为100，日系数为1，两者相减，大于等于0岁数为_birth，否则为_birth-1
                    _birth = _birth - 1;
                }
                if(_now[2]*1<_bir[2]*1){
                    _mm = _mm -1;
                    if (_mm < 0) {
                        _mm += 12;
                    }
                }
            }else{
                _birth = -1;
                _mm = 0;
                _dd = 0;
            }
        }catch(e){
            var _birth = -1;
            _mm = 0;
            _dd = 0;
        }
        _yyy = isNaN(_birth)?-1:_birth*1000000;
        _mmm = isNaN(_mm)?-1:_mm*1000+_birth*12*1000;
        _ddd = isNaN(_dd)?-1:_dd;
        alllist["yy"] = _yyy;
        alllist["mm"] = _mmm;
        alllist["dd"] = _ddd;
        return alllist;
    }

    //完成被保险人信息修改或添加
    function updatePinsurant(o){
        //检查被保险人信息
        var productId = sessionStorage.getItem("productid");
        var pant = $(o).parent().siblings().find(".insurant");
        var altMsg = {"phName":"请填写姓名","phRelation":"请选择与投保人的关系","phIddtype":"请选择证件类型","phIddnum":"请填写证件号码","phGend":"请选择性别","phBirth":"请填写出生日期"};
        var isok = true;
        $("#insurantEditBox").find(".errmsg").addClass("divideline").removeClass("errmsg").find("div").html("")
        $("#insurantEditBox").find("input,select").each(function(){
            if(($(this).val() == ""||$(this).val() == null) && altMsg[this.className]){
                alertBdMsg("请完善被保险人信息");
                insureerroshow($(this),altMsg[this.className])
                isok = false;
                return false;
            }
        })
            var p=[];
            if($(".insurant").find(".phIddtype").val()=="身份证"){//自动根据身份证号识别生日性别
                if($(".insurant").find(".phIddnum").val()!=""){
                    var _id = analyzeId($(".insurant").find(".phIddnum").val());
                    if(_id){
                    }else{
                        alertBdMsg("身份证号码错误");
                        insureerroshow(jQuery(".insurant").find(".phIddnum"),"身份证号码错误");
                        return;
                    }
                }
            }
        if(jQuery(".insurant").find(".phEmail").val()!=""&&!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(jQuery(".insurant").find(".phEmail").val())){//检查邮箱信息
            alertBdMsg("邮箱格式错误");
            insureerroshow(jQuery(".insurant").find(".phEmail"),"邮箱格式错误");
            return;
        }
            var birthday = $(".insurant").find(".phBirth").val()
            var insureage = gtinsureage(birthday);
            if (insureage<agerang.minage||insureage>agerang.maxgae){
                alertBdMsg("被保险人年龄不在承保范围");
                insureerroshow(jQuery(".insurant").find(".phBirth"),"被保险人年龄不在承保范围");
                isok = false;
                return false;
            }
            if(isok){
                //被保险人信息修改
                var idx=$(o).parent().siblings().find(".index1").val();
                var list=[];
                var continueadd=false;
                list = $("#insurantInfoTbody").find(".index");
                if(idx=="" && list.length==0){//判断加载页面是否为空，无保险人则新建
                    addPinsurant(p) ;
                }else {//有保险人则进行修改或者继续添加
                    for(var i=0;i<list.length;i++){
                        if(list.eq(i).val()==idx){

                            var listcount=[];
                            var productId = sessionStorage.getItem("productid");
                            listcount = $("#insurantInfoTbody").find(".phRelation");//或者phIddnum、phIddtype等等insurantInfo_tr的对象即可
                            for(var j=0;j<listcount.length;j++){
                                if(listcount.eq(j).val()=="本人 Policyholder" &&  listcount.length>0 && pant.find(".phRelation").val()=="本人 Policyholder" ){
                                    alertBdMsg("被保险人列表中已存在本人，请重新确认与投保人的关系！");
                                    return;
                                }
                            }

                            pant.find(".index").val() && list.eq(i).closest(".insurantInfo_tr").find(".index").val(pant.find(".index").val());
                            pant.find(".phName").val() && list.eq(i).closest(".insurantInfo_tr").find(".phName").val(pant.find(".phName").val());
                            pant.find(".phName").val() && list.eq(i).closest(".insurantInfo_tr").find(".phNameStr").html(pant.find(".phName").val());
                            pant.find(".phIddnum").val() && list.eq(i).closest(".insurantInfo_tr").find(".phIddnum").val(pant.find(".phIddnum").val());
                            pant.find(".phIddnum").val() && list.eq(i).closest(".insurantInfo_tr").find(".phIddnumStr").html(pant.find(".phIddnum").val());
                            pant.find(".phIddtype").val() && list.eq(i).closest(".insurantInfo_tr").find(".phIddtype").val(pant.find(".phIddtype").val());
                            pant.find(".phIddtype").val() && list.eq(i).closest(".insurantInfo_tr").find(".phIddtypeStr").html(pant.find(".phIddtype").val());
                            pant.find(".phGend").val() && list.eq(i).closest(".insurantInfo_tr").find(".phGend").val(pant.find(".phGend").val());
                            pant.find(".phBirth").val() && list.eq(i).closest(".insurantInfo_tr").find(".phBirth").val(pant.find(".phBirth").val());
                            pant.find(".phTel").val() && list.eq(i).closest(".insurantInfo_tr").find(".phTel").val(pant.find(".phTel").val());
                            pant.find(".phEmail").val() && list.eq(i).closest(".insurantInfo_tr").find(".phEmail").val(pant.find(".phEmail").val());
                            pant.find(".phEmail").val() && list.eq(i).closest(".insurantInfo_tr").find(".phEmailStr").html(pant.find(".phEmail").val());
                            pant.find(".phRelation").val() && list.eq(i).closest(".insurantInfo_tr").find(".phRelation").val(pant.find(".phRelation").val());
                            pant.find(".phGend").val() && list.eq(i).closest(".insurantInfo_tr").find(".phGendStr").html(pant.find(".phGend").val());
                            pant.find(".phBirth").val() && list.eq(i).closest(".insurantInfo_tr").find(".phBirthStr").html(pant.find(".phBirth").val());
                            pant.find(".phTel").val() && list.eq(i).closest(".insurantInfo_tr").find(".phTelStr").html(pant.find(".phTel").val());
                            pant.find(".phRelation").val() && list.eq(i).closest(".insurantInfo_tr").find(".phRelationStr").html(pant.find(".phRelation").val());
                            pant.find(".PUBeneficiaryButton").val() && list.eq(i).closest(".insurantInfo_tr").find(".PUBeneficiaryButton").val(pant.find(".PUBeneficiaryButton").val());
                            pant.find(".PUBeneficiary").val() && list.eq(i).closest(".insurantInfo_tr").find(".PUBeneficiary").val(pant.find(".PUBeneficiary").val());
                            pant.find(".PUBeneficiaryBak").val() && list.eq(i).closest(".insurantInfo_tr").find(".PUBeneficiaryBak").val(pant.find(".PUBeneficiaryBak").val());
                            continueadd=false;
                            $("#insurantInfoTbody").find(".liberStrline").show();
                            $("#insurantInfoTbody").find(".travelStrline").hide();
                            $("#insurantInfoTbody").find(".wrapBox_link").removeClass("Boxautoheight");
                            $("#insurantInfoTbody").find(".plusOrminus").removeClass("Boxautoheight");
                            $("#insurantInfoTbody").find("#detelbtn").removeClass("delautoheight");
                            $("#insurantInfoTbody").find(".deleterows").removeClass("spanautoheight");
                            $("#insurantInfoTbody").find(".enteringArrow").addClass("arrowheight").removeClass("arrowautoheight");
                            $("#insurantEditBox").hide();
                            document.body.scrollTop = 500;
                            $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
                            policyUserCount();
                            return;
                        }else{//继续添加
                            continueadd=true;
                        }
                    }
                    if(continueadd){
                        var ispoh=true;
                        var listre = $("#insurantInfoTbody").find(".phRelation");
                        for(var i=0;i<list.length;i++){
                            if(listre.eq(i).val()=="本人 Policyholder" &&  listre.length>0 && pant.find(".phRelation").val()=="本人 Policyholder" ){
                                alertBdMsg("被保险人列表中已存在本人，请重新确认与投保人的关系！");
                                pant.find(".phRelation").focus();
                                ispoh=false;
                                return;
                            }
                        }
                        if(ispoh){
                            addPinsurant(p);
                        }else{
                            return;
                        }

                    }
                }
                $("#insurantInfoTbody").find(".liberStrline").show();
                $("#insurantInfoTbody").find(".travelStrline").hide();
                $("#insurantInfoTbody").find(".wrapBox_link").removeClass("Boxautoheight");
                $("#insurantInfoTbody").find(".plusOrminus").removeClass("Boxautoheight");
                $("#insurantInfoTbody").find("#detelbtn").removeClass("delautoheight");
                $("#insurantInfoTbody").find(".deleterows").removeClass("spanautoheight");
                $("#insurantInfoTbody").find(".enteringArrow").addClass("arrowheight").removeClass("arrowautoheight");
                $("#insurantEditBox").hide();
                document.body.scrollTop = 500;
            }

        // policyUserCount();
    }

    //新加被保险人（含两种情况，从常用联系人中添加和新建窗口中添加）
    function addPinsurant(per){
        if(per.length==0 || per==null || per=="undefined"){//新加被保险人的情形
            var index="c"+$(".insurant").find(".phIddnum").val();
            var phname=$(".insurant").find(".phName").val();
            var phiddnum=$(".insurant").find(".phIddnum").val();
            var phiddtype=$(".insurant").find(".phIddtype").val();
            var phgend=$(".insurant").find(".phGend").val();
            var phbirthday=$(".insurant").find(".phBirth").val();
            var phtel=$(".insurant").find(".phTel").val();
            var relation=$(".insurant").find(".phRelation").val();
            var PUBbtn=$(".insurant").find(".PUBeneficiaryButton").val();
            var PUB=$(".insurant").find(".PUBeneficiary").val();
            var PUBbak=$(".insurant").find(".PUBeneficiaryBak").val();
            var phemail=$(".insurant").find(".phEmail").val();
            per=[{"index":index, "phname":phname ,"phiddnum":phiddnum ,"phiddtype":phiddtype ,"phgend":phgend,"phbirthday":phbirthday,"phtel":phtel,"relation":relation,"PUBeneficiaryButton":PUBbtn,"PUBeneficiary":PUB,"PUBeneficiaryBak":PUBbak,"phemail":phemail}];
            $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
            $("#insurantEditBox").hide();
            document.body.scrollTop = 200;
        }
        //责任险的被保险人数量限定
        var list=[];
        var productId = sessionStorage.getItem("productid");
        list = $("#insurantInfoTbody").find(".phRelation");//或者phIddnum、phIddtype等等insurantInfo_tr的对象即可
        if(proInfo[productId].haveLiabilityConfig == 1 && list.length>0 ){
            alertBdMsg("责任险的被保险人不超过1个");
            return;
        }
        var insureIndex=$("#insurantInfoTbody").find(".insurantInfo_tr").length+1;
        for(var i = 0; i< per.length; i++){//采用for循环的原因：若系统后期需要一次性添加多个被保险人时亦可以继续使用
            var insurantInfoTemplate = $("#insurantInfoTemplate").find(".insurantInfo_tr").clone();

                for(var j=0;j<list.length;j++){
                    if(list.eq(j).val()=="本人 Policyholder" &&  list.length>0 && per[i].relation=="本人 Policyholder" ){
                        alertBdMsg("被保险人列表中已存在本人，请重新确认与投保人的关系！");
                        return;
                    }
                }
                insurantInfoTemplate.find(".index").val(insureIndex);
                insurantInfoTemplate.find(".insureIndex").html("被保险人 "+insureIndex);
                insurantInfoTemplate.find(".phName").val(per[i].phname);
                insurantInfoTemplate.find(".phNameStr").html(per[i].phname);
                insurantInfoTemplate.find(".phBirth").val(per[i].phbirthday);
                insurantInfoTemplate.find(".phGend").val(per[i].phgend);
                insurantInfoTemplate.find(".phBirthStr").html(per[i].phbirthday);
                insurantInfoTemplate.find(".phGendStr").html(per[i].phgend);
                insurantInfoTemplate.find(".phIddnum").val(per[i].phiddnum);
                insurantInfoTemplate.find(".phIddnumStr").html(per[i].phiddnum);
                insurantInfoTemplate.find(".phIddtype").val(per[i].phiddtype);
                insurantInfoTemplate.find(".phIddtypeStr").html(per[i].phiddtype);
                insurantInfoTemplate.find(".phTel").val(per[i].phtel);
                insurantInfoTemplate.find(".phEmail").val(per[i].phemail);
                insurantInfoTemplate.find(".phEmailStr").html(per[i].phemail);
                insurantInfoTemplate.find(".phRelation").val(per[i].relation);
                insurantInfoTemplate.find(".phTelStr").html(per[i].phtel);
                insurantInfoTemplate.find(".phRelationStr").html(per[i].relation);
                insurantInfoTemplate.find(".PUBeneficiaryButton").val(per[i].PUBeneficiaryButton);
                insurantInfoTemplate.find(".PUBeneficiary").val(per[i].PUBeneficiary);
                insurantInfoTemplate.find(".PUBeneficiaryBak").val(per[i].PUBeneficiaryBak);
                insurantInfoTemplate.appendTo("#insurantInfoTbody");
                $("#insurantInfoTbody").find(".liberStrline").show();
                $("#insurantInfoTbody").find(".travelStrline").hide();
                $("#insurantInfoTbody").find(".wrapBox_link").removeClass("Boxautoheight");
                $("#insurantInfoTbody").find(".plusOrminus").removeClass("Boxautoheight");
                $("#insurantInfoTbody").find("#detelbtn").removeClass("delautoheight");
                $("#insurantInfoTbody").find(".deleterows").removeClass("spanautoheight");
                $("#insurantInfoTbody").find(".enteringArrow").addClass("arrowheight").removeClass("arrowautoheight");

            if(proInfo[productId].haveLiabilityConfig == 1){
                $(".forAddper").hide();
            }else{
                $(".forAddper").show();
            }
        }
    }

    //打开被保险人信息编辑页面
    NewContacts = {};
    var getContacts = false;
    function insurantmodified(e){
        var idx=$(e).parent().parent().parent().siblings(".index").val();
        var contactsList1 = [];
        getContacts[e] && contactsList1.push(getContacts[e]);
        if(contactsList1.length > 0){
            listInsurantinfo(contactsList1);
            $("#insurantEditBox").show();
            $(".contactsBox").hide();
            jQuery("html,body").css({"height":"auto","overflow":"hidden","position":"fixed","width":"100%"});
            return;
        }
        var contactsList = [];
        var productId = sessionStorage.getItem("productid");
        e=$(e).parent().siblings("#insurech");
        if(!(getContacts[idx]) || getContacts[idx].PUBeneficiary=="" || !getContacts[idx].PUBeneficiary){//新加联系人
            NewContacts[idx] = {};
            NewContacts[idx]["index"] = idx;
            NewContacts[idx]["phname"] = $(e).find(".phName").val();
            NewContacts[idx]["phiddnum"] =$(e).find(".phIddnum").val();
            NewContacts[idx]["phiddtype"] =$(e).find(".phIddtype").val();
            NewContacts[idx]["phgend"] = $(e).find(".phGend").val();
            NewContacts[idx]["phbirthday"] =$(e).find(".phBirth").val();
            NewContacts[idx]["phtel"] =$(e).find(".phTel").val();
            NewContacts[idx]["relation"] =$(e).find(".phRelation").val();
            NewContacts[idx]["phemail"] =$(e).find(".phEmail").val();
            NewContacts[idx]["PUBeneficiaryButton"] =$(e).find(".PUBeneficiaryButton").val();
            NewContacts[idx]["PUBeneficiary"] =$(e).find(".PUBeneficiary").val();
            NewContacts[idx]["PUBeneficiaryBak"] =$(e).find(".PUBeneficiaryBak").val();
            if(proInfo[productId].haveLiabilityConfig == 1 && ($(e).find(".PUCorpName").val()!=""||$(e).find(".PUCorpCertificateNumber").val()!="") ){
                NewContacts[idx]["phiddtype"] =$(e).find(".phIddtypeStr").html();
                NewContacts[idx]["phname"] = $(e).find(".PUCorpName").val();
                NewContacts[idx]["phiddnum"] =$(e).find(".PUCorpCertificateNumber").val();
                NewContacts[idx]["phtel"] =$(e).find(".PUCorpMobile").val();
                NewContacts[idx]["phemail"] =$(e).find(".PUCorpEmail").val();
            }
        }
        NewContacts[idx] && contactsList.push(NewContacts[idx]);

        if(contactsList.length > 0){
            listInsurantinfo(contactsList);
        }
        jQuery("html,body").css({"height":"auto","overflow":"hidden","position":"fixed","width":"100%"});
        $("#insurantEditBox").show();
        $(".contactsBox").hide();
    }

    //对所选的被保险人信息进行编辑操作

    function listInsurantinfo(insurant){
        var productId = sessionStorage.getItem("productid");
        var num = $(".insurant").length;
        var PUBeneficiary = "";
        for(var i=0;i<insurant.length;i++){
            $(".insurant").find(".index1").val(insurant[i].index);
            $(".insurant").find(".phName").val(insurant[i].phname);
            $(".insurant").find(".phIddnum").val(insurant[i].phiddnum);
            $(".insurant").find(".phIddtype").val(insurant[i].phiddtype);
            $(".insurant").find(".phGend").val(insurant[i].phgend);
            $(".insurant").find(".phBirth").val(insurant[i].phbirthday);
            $(".insurant").find(".phTel").val(insurant[i].phtel);
            $(".insurant").find(".phRelation").val(insurant[i].relation);
            $(".insurant").find(".phEmail").val(insurant[i].phemail);
            $(".insurant").find(".PUBeneficiaryButton").val(insurant[i].PUBeneficiaryButton);
            $(".insurant").find(".PUBeneficiary").val(insurant[i].PUBeneficiary);

            if(insurant[i].phiddtype=="身份证"){//自动根据身份证号识别生日性别
                if(insurant[i].phiddnum!=""){
                    var _id = analyzeId(insurant[i].phiddnum);
                    if(_id){
                        $(".insurant").find(".phGend").val(_id.sex);
                        $(".insurant").find(".phBirth").val(_id.bir);
                    }else{
                        $(".insurant").find(".phIddtype").val("");
                        console.log("身份证号码错误！");
                    }
                }
            }
            if(!insurant[i].PUBeneficiaryButton || insurant[i].PUBeneficiaryButton=="" ||insurant[i].PUBeneficiaryButton=="法定Legal Heir" ){
                $(".insurant").find(".PUBeneficiaryButton").val(beneficiaryDefaultStr);
                $("#PUBeneficiarybodylist").find("#PUBeneficiaryTempletelist").remove();
            }else{
                splitPUBeneficiaryStr(insurant[i].PUBeneficiary);
            }
        }
        $("#insurantEditBox").find(".inputlabel").each(function(){
            var o = $(this);
            if ($(o).siblings("input").val()!="") {
                $(o).removeClass("inputlabel").addClass("outlabel")
            }
        })
    }

    //预览被保险人列表信息时移除被保险人
    function removeInsurant(e){
        if($(".isnures").find(".demo-input").length==1){
            var index_n=1
            $(".isnures").find(".demo-input").children("div").each(function(){
                if(index_n==1){
                    if($(this).attr("class")!=null&&$(this).attr("class").indexOf("ismy_1")==-1||$(this).attr("class")==null) {
                        $(this).addClass("ismy_1");
                    }
                }else if(index_n==2){
                    $(this).find(".phRelation").val("本人 Policyholder");
                    $(this).find(".weert").addClass("outlabel").removeClass("inputlabel");
                }else if(index_n>3){
                    $(this).remove();
                }
                index_n=index_n+1;
            })
            $(".ismy_1").hide();
        }else {
//            if($(this).siblings("div").attr("class")=="ismy_1"){
//                $(".isnures").find(".demo-input")[1].children("div")[0].addClass("ismy_1");
//            }
            var a = $(e).parent().find(".insureIndex").val();
            $(e).closest(".demo-input").remove();

            $(".subamount").html(gttotalprice());
            var index = 1
            var o = "";
            $(".isnures").find(".demo-input").each(function () {
                $(this).find(".insureIndex").html(index);
                o = $(this);
                index = index + 1;
            })
            if (o != "") {
                o.addClass("lastinsure");
            }
        }
        var insurants = formatBackInsurant();
        sessionStorage.setItem("pusers",insurants["pusers"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
        $(".subamount").html(gttotalprice());
    }

    //计算年龄
    function gtinsureage(birthday){
        var age = '';
        var d = new Date();
        var year = d.getFullYear();
        var month = d.getMonth() + 1;
        var day = d.getDate();
        if (month < 10) {
            month = '0'+month;
        }
        if(day < 10){
            day = '0'+day;
        }
        var now = year+'-'+month+'-'+day;
        if (now.substring(0,4) >= birthday.substring(0,4) && now.substring(5,7) >=birthday.substring(5,7)
                && now.substring(8,10)>=birthday.substring(8,10)) {
            age = year - parseInt(birthday.substring(0,4));
        }else{
            age = year - parseInt(birthday.substring(0,4)) - 1;
        }
        return age
    }

    function getprice(){
        var productId = sessionStorage.getItem("productid");
        var planid =  sessionStorage.getItem("planid");
        showsingleprice(productId,planid);
    }

    //计算总保费
    function gttotalprice(){
        var stDay=new Date(jQuery("#activeDat").val().replace(/-/gi,"/"));
        var edDay=new Date(jQuery("#exprDat").val().replace(/-/gi,"/"));
        var t=parseInt((edDay - stDay + 1000),10)/86400000;

        var productId =  sessionStorage.getItem("productid");
        var totalPremium = 0;
        for (var i = 0; i < allCount.length; i++) {
            var totalEach = allCount[i] * allPri[i] || 0;
            totalPremium += totalEach;
        }


        var price=0.00;

        $(".isnures").find(".phBirth").each(function(){
            var age = gtinsureage($(this).val());
            for(var i=0;i<totalprice.length;i++){
                if(age>=totalprice[i].minage&&age<=totalprice[i].maxage){
                    price=price+totalprice[i].price;
                }
            }
        })
        $(".isnures").find(".phRelation").each(function(){
            if($(this).val()=="本人 Policyholder"&&$(this).parent().parent().children("div").length==3){
                var age = gtinsureage($(".demo-input-holder").find(".phBirth").val());
                for(var i=0;i<totalprice.length;i++){
                    if(age>=totalprice[i].minage&&age<=totalprice[i].maxage){
                        price=price+totalprice[i].price;
                    }
                }
            }
        })
        return price.toFixed(2);
    }

    //防止用户重复点击
    var isInsuringFlag = false;

    function submitInfo(){
        var productId =  sessionStorage.getItem("productid");
//        if($("#isinsure").attr("checked")=="checked"){
            var altMsg = {"phName":"请填写姓名！","phIddnum":"请填写证件号码！","phIddtype":"请选择证件类型！","phGend":"请选择性别！","phBirth":"请填写出生日期！","phRelation":"请选择与投保人的关系！"};
            var isok = true;
            $(".isnures").find("input,select").each(function(){
                if($(this).val() == "" && altMsg[this.className]){

                    $('html, body').animate({
                        scrollTop: $(this).offset().top-300
                    },0)
                    insureerroshow($(this),altMsg[this.className])
                    alertBdMsg(altMsg[this.className]);
                    isok = false;
                    return false;
                }
            })
            if(!isok){return false;}

            $(".isnures").find(".phBirth").each(function(){
                var age = getAge($(this).val(),proInfo[productId].calAgeRule);
                var mAge = getMinAge($(this).val(),proInfo[productId].calAgeRule);

                if(mAge["yy"]<minAge || mAge["yy"]>maxAge){
                    insureerroshow($(this),altMsg[this.className])
                    alertBdMsg("被保险人年龄必须在"+minAgeShow+"-"+maxAgeShow+"之间");
                    isok = false;
                    return false;
                }

            })
//            $(".isnures .phRelation").filter(function(){return $(this).val() == "本人 Policyholder"}).each(function(){
//                if($(this).closest(".demo-input").find(".phName").val() != $(".demo-input-holder .phName").val()){
//                    alertBdMsg("关系为本人的被保险人"+ $(this).closest(".demo-input").find(".insureIndex").html() +"信息与投保人信息不一致");
//                    isok = false;
//                    return;
//                }
//            })
//        }

        altMsg = {"phName":"请填写投保人姓名！","phIddnum":"请填写投保人证件号码！","phIddtype":"请选择投保人证件类型！","phGend":"请选择投保人性别！","phBirth":"请填写投保人出生日期！","phTel":"请填写投保人电话号码！","phEmail":"请填写邮箱地址！"};


        $(".demo-input-holder").find("input,select").each(function(){
            var _this = $(this);
            if(_this.val() == ""){
                $('html, body').animate({
                    scrollTop: $(this).offset().top-300
                },0)
                holdererroshow($(this),altMsg[this.className])
                alertBdMsg(altMsg[this.className]);
                isok = false;
                return false;
            }
        })
        if (!isok){
            return;
        }


        if($(".isnures").find(".demo-input").length==0){
            alertBdMsg("请添加被保险人");
            isok = false;
            return false;
        }
        if(getAge(jQuery(".demo-input-holder .phBirth").val(),proInfo[productId].calAgeRule) < 18){
			$('html, body').animate({
                scrollTop: $(".demo-input-holder .phBirth").offset().top-300
            },0)
            holdererroshow(jQuery(".demo-input-holder .phBirth"),"未成年人不能作为投保人！")
            alertBdMsg("未成年人不能作为投保人！");
            return false;
        }
        if($(".demo-input-holder").find(".phIddtype").val()=="身份证"){//自动根据身份证号识别生日性别
            if($(".demo-input-holder").find(".phIddnum").val()!=""){
                var _id = analyzeId($(".demo-input-holder").find(".phIddnum").val());
                if(_id){
                }else{
                    $('html, body').animate({
                        scrollTop: $(".demo-input-holder").find(".phIddnum").offset().top-300
                    },0)
                    alertBdMsg("身份证号码有误");
                    holdererroshow($(".demo-input-holder").find(".phIddnum"),"身份证号码错误")
                    return;
                }
            }
        }
		if(!checkBirthAndId($(".demo-input-holder").find(".phBirth"))){
			$('html, body').animate({
				scrollTop: $(".demo-input-holder").find(".phBirth").offset().top-300
			},0)
			alertBdMsg("出生日期与身份证号码上的出生日期不一致");
			return;
		}
		if(!/^1[3|4|5|7|8][0-9]{9}$/.test(jQuery(".demo-input-holder .phTel").val())){//检查手机号位数
            $('html, body').animate({
                scrollTop: $(".demo-input-holder .phTel").offset().top-300
            },0)
            alertBdMsg("请填写正确的投保人手机号");
            holdererroshow(jQuery(".demo-input-holder .phTel"),"请填写正确的投保人手机号")
            isok=false;
            return false;
        }
        if(jQuery(".demo-input-holder .phEmail").val()!=""&&!/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(jQuery(".demo-input-holder .phEmail").val())){//检查邮箱信息
            $('html, body').animate({
                scrollTop: $(".demo-input-holder .phEmail").offset().top-300
            },0)
            alertBdMsg("请填写正确的投保人电子邮箱");
            holdererroshow(jQuery(".demo-input-holder .phEmail"),"请填写正确的投保人电子邮箱")
            isok=false;
            return false;
        }
		var isre=false;
		if($(".isnures").find(".demo-input").length>0) {
            var isworm=false
            $(".isnures").find(".demo-input").each(function(){
                if($(this).attr("class").indexOf("lastinsure")!=-1&&$(this).children("div").length>3){
                    var altMsg = {
                        "phRelation": "请选择与投保人关系",
                        "phName": "请填写被保人姓名",
                        "phIddtype": "请选择被保人证件类型",
                        "phIddnum": "请填写被保人证件号码",
                        "phGend": "请选择被保人性别",
                        "phBirth": "请填写被保人出生日期"
                    };
                    var alInsureIndex = 0

                    $(this).find("input,select").each(function () {
                        if ($(this).className != "phEmail" && $(this).className != "phTel" && $(this).className != "phIddnum") {
                            if (($(this).val() == "" || $(this).val() == null) && altMsg[this.className]) {
                                isre = true;
                                isworm = true;
                                insureerroshow($(this), altMsg[this.className])
                                if(alIndex==0) {
                                    $('html, body').animate({
                                        scrollTop: $(this).offset().top-300
                                    },0)
                                    alertBdMsg(altMsg[this.className])
                                    alIndex=alIndex+1;
                                }
                            }
                        }
                    })
                    if (isre) {
                        return;
                    }
                    var birthday = $(this).find(".phBirth").val()
                    var insureage = gtinsureage(birthday);
                    if (insureage < agerang.minage || insureage > agerang.maxgae) {
                        alertBdMsg("被保险人年龄不在承保范围");
                        insureerroshow($(this).find(".phBirth"), "被保险人年龄不在承保范围");
                        isok = false;
                        return false;
                    }
                    if ($(this).find(".phIddtype").val() == "身份证") {//自动根据身份证号识别生日性别
                        if ($(this).find(".phIddnum").val() != "") {
                            var _id = analyzeId($(this).find(".phIddnum").val());
                            if (_id) {
                            } else {
                                $('html, body').animate({
                                    scrollTop: $(this).find(".phIddnum").offset().top-300
                                },0)
                                alertBdMsg("身份证号码有误");
                                insureerroshow($(this).find(".phIddnum"), "身份证号码错误")
                                return;
                            }
                        }
                    }
					if(!checkBirthAndId($(this).find(".phBirth"))){
						$('html, body').animate({
							scrollTop: $(this).find(".phBirth").offset().top-300
						},0)
						alertBdMsg("出生日期与身份证号码上的出生日期不一致");
						return;
					}
					if($(this).find(".phTel").val() != "" && !/^1[3|4|5|7|8][0-9]{9}$/.test($(this).find(".phTel").val())){//检查手机号是否11位
						$('html, body').animate({
							scrollTop: $(this).find(".phTel").offset().top-300
						},0)
						alertBdMsg("请填写正确的被保人手机号");
						insureerroshow($(".isnures").find(".lastinsure").find(".phTel"),"请填写正确的被保人手机号")
						return false;
					}
                    if ($(this).find(".phEmail").val() != "" && !/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test($(".isnures").find(".lastinsure").find(".phEmail").val())) {//检查邮箱信息
                        $('html, body').animate({
                            scrollTop: $(this).find(".phEmail").offset().top-300
                        },0)
                        alertBdMsg("请填写正确的被保人电子邮箱");
                        insureerroshow($(this).find(".phEmail"), "请填写正确的被保人电子邮箱")
                        return false;
                    }
                }
            })
            if(isworm){
                return false;
            }
            if ($(".isnures").find(".lastinsure").find(".phRelation").val() != "本人 Policyholder") {
                var birthday = $(".isnures").find(".lastinsure").find(".phBirth").val()
                var insureage = gtinsureage(birthday);
                if (insureage < agerang.minage || insureage > agerang.maxgae) {
                    alertBdMsg("被保险人年龄不在承保范围");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phBirth"), "被保险人年龄不在承保范围");
                    isok = false;
                    return false;
                }
                var altMsg = {
                    "phRelation": "请选择与投保人关系",
                    "phName": "请填写被保人姓名",
                    "phIddtype": "请选择被保人证件类型",
                    "phIddnum": "请填写被保人证件号码",
                    "phGend": "请选择被保人性别",
                    "phBirth": "请填写被保人出生日期"
                };
                var alInsureIndex = 0
                $(".isnures").find(".lastinsure").find("input,select").each(function () {
                    if ($(this).className != "phEmail" && $(this).className != "phTel" && $(this).className != "phIddnum") {
                        if (($(this).val() == "" || $(this).val() == null) && altMsg[this.className]) {
                            isre = true;
                            insureerroshow($(this), altMsg[this.className])
                            if(alIndex==0) {
                                $('html, body').animate({
                                    scrollTop: $(this).offset().top-300
                                },0)
                                alertBdMsg(altMsg[this.className])
                                alIndex=alIndex+1;
                            }
                        }
                    }
                })
                if (isre) {
                    return;
                }
                var birthday = $(".isnures").find(".lastinsure").find(".phBirth").val()
                var insureage = gtinsureage(birthday);
                if (insureage < agerang.minage || insureage > agerang.maxgae) {
                    alertBdMsg("被保险人年龄不在承保范围");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phBirth"), "被保险人年龄不在承保范围");
                    isok = false;
                    return false;
                }
                if ($(".isnures").find(".lastinsure").find(".phIddtype").val() == "身份证") {//自动根据身份证号识别生日性别
                    if ($(".isnures").find(".lastinsure").find(".phIddnum").val() != "") {
                        var _id = analyzeId($(".isnures").find(".lastinsure").find(".phIddnum").val());
                        if (_id) {
                        } else {
                            $('html, body').animate({
                                scrollTop: $(".isnures").find(".lastinsure").find(".phIddtype").offset().top-300
                            },0)
                            alertBdMsg("身份证号码有误");
                            insureerroshow($(".isnures").find(".lastinsure").find(".phIddnum"), "身份证号码错误")
                            return;
                        }
                    }
                }
				if(!checkBirthAndId($(".isnures").find(".lastinsure").find(".phBirth"))){
					$('html, body').animate({
						scrollTop: $(".isnures").find(".lastinsure").find(".phBirth").offset().top-300
					},0)
					alertBdMsg("出生日期与身份证号码上的出生日期不一致");
					return;
				}
				if($(".isnures").find(".lastinsure").find(".phTel").val() != "" && !/^1[3|4|5|7|8][0-9]{9}$/.test($(".isnures").find(".lastinsure").find(".phTel").val())){//检查手机号是否11位
					$('html, body').animate({
                        scrollTop: $(".isnures").find(".lastinsure").find(".phTel").offset().top-300
                    },0)
					alertBdMsg("请填写正确的被保人手机号");
					insureerroshow($(".isnures").find(".lastinsure").find(".phTel"),"请填写正确的被保人手机号")
					return false;
				}
                if ($(".isnures").find(".lastinsure").find(".phEmail").val() != "" && !/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test($(".isnures").find(".lastinsure").find(".phEmail").val())) {//检查邮箱信息
                    $('html, body').animate({
                        scrollTop: $(".isnures").find(".lastinsure").find(".phEmail").offset().top-300
                    },0)
                    alertBdMsg("请填写正确的被保人电子邮箱");
                    insureerroshow($(".isnures").find(".lastinsure").find(".phEmail"), "请填写正确的被保人电子邮箱")
                    return false;
                }
            }
        }
        if(!isok){return false;}
        var policyinfo = formatInsurant();
        if(policyinfo["pusers"]==""){return false;}
        jQuery("#pinsure").val(policyinfo["pinsure"]);
        jQuery("#pusers").val(policyinfo["pusers"]);

        if($("#ok").attr("class")=="close4"){
            alertBdMsg("请确认您已勾选[已阅读并且同意相关投保须知,声明]");
            return false;
        };
        $("#pExtendStr").val("");

        jQuery("#activeDatHidden").val(jQuery("#activeDat").val()+" 00:00:00");
        jQuery("#exprDatHidden").val(jQuery("#exprDat").val()+" 23:59:59");
        jQuery.when(
                insuredRatingCheck(policyinfo["pusers"])
        ).then(function (data) {
            if (data.success == "1") {
                var startTime = new Date($("#activeDat").val().replace(/-/gi, "/"));
                var now = new Date();
                var daysNum = Math.ceil((startTime - now) / 86400000, 10);
                var m = $("#insureLastTime").val();
                if (daysNum>$("#insureLastTime").val()&&$("#insureLastTime").val()>0){
                    alertBdMsg("起保时间超过最大起保时间！")
                    return;
                }
                if (!$(".daysforday").is(":hidden") && $(".daysforday input").val() == "1") {
                    if (!confirm("保险时间只选择了1天！确认投保吗？")) {
                        return;
                    }
                }
                $("#tsTag").val((new Date().getTime())+""+parseInt(Math.random()*1e5, 10));
                if(isInsuringFlag){
                    return;
                }
                $(".opa2").show()
                scren_point(jQuery(".policyknow"));
            }
        });
    }


    function scren_point(param_object){//设置弹出层位置
        var html_height = jQuery(window).height();
        var user_height = param_object.css("height");
        var scroTop = jQuery(document).scrollTop();
        var user_top = (parseInt(html_height) - parseInt(user_height))/2+scroTop;
        user_top = user_top < 0 ? 0 : user_top;
        param_object.css("top",user_top);
        param_object.show();
    }


    function showNotes(){//打开投保须知
        var insurants = formatBackInsurant();
        sessionStorage.setItem("pusers",insurants["pusers"]);
        sessionStorage.setItem("pinsure",insurants["pinsure"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
        var partnerId  = $("#partnerId").val();
        if(partnerId == null || partnerId == ""){
            partnerId =  sessionStorage.getItem("partnerId");
        }

        window.open("/baidu/baiduDetail.action?partnerId="+partnerId+"&detailType=2"+"&planCode="+$("#planCode").val(),"_self")
    }
    function showClauseDeclare(){
        var insurants = formatBackInsurant();
        sessionStorage.setItem("pusers",insurants["pusers"]);
        sessionStorage.setItem("pinsure",insurants["pinsure"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
        var partnerId  = $("#partnerId").val();
        if(partnerId == null || partnerId == ""){
            partnerId =  sessionStorage.getItem("partnerId");
        }
        window.open("/baidu/baiduDetail.action?partnerId="+partnerId+"&detailType=3"+"&planCode="+$("#planCode").val(),"_self")
    }
    function showDeclare(){//打开投保声明
        var insurants = formatBackInsurant();
        sessionStorage.setItem("pusers",insurants["pusers"]);
        sessionStorage.setItem("pinsure",insurants["pinsure"]);
        sessionStorage.setItem("isincludeinsure", insurants["isincludeinsure"]);
        var partnerId  = $("#partnerId").val();
        if(partnerId == null || partnerId == ""){
            partnerId =  sessionStorage.getItem("partnerId");
        }
        window.open("/baidu/baiduDetail.action?partnerId="+partnerId+"&detailType=4"+"&planCode="+$("#planCode").val(),"_self")
    }

    function showClauseDeclare_pay(){
        submitInfo();
    }

    $(".declareClose").on("click",function(){//关闭投保声明和须知
        $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
        document.body.scrollTop =100000;
        setTimeout(function(){
            $(".declareBox").hide();
        },100);
    })
    $(".declareCloses").on("click",function(){//关闭保障计划表
        $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
        setTimeout(function(){
            $("#insureMessage").hide();
        },100);
    })

    function formatInsurant(){
        var productId =  sessionStorage.getItem("productid");
        var ih = $(".demo-input-holder");
        var _pn = {};
        var _pr = {};
        var _pu= {};  //参与人与被保险人证件号码验重
        var insureList = [];
        var policyHolder = [];
        var policyinsure = [];
        var PHType = "1";
        var tbrname=jQuery.trim(ih.find(".phName").val().replace(/"/gi,"\\\"").replace(/'/gi,"\\\'").replace(/(&nbsp;| | |　)+/gi," "));
        var tbrbirth=ih.find(".phBirth").val();
        var tbrtype=ih.find(".phIddtype").val();
        var tbrnumber=ih.find(".phIddnum").val();
        var tbrgend=ih.find(".phGend").val();
        var tbrtel=ih.find(".phTel").val();
        var tbremail=ih.find(".phEmail").val();
        var insuredtype = "1";
        var policyinsureid=jQuery("#policyinsureid").val() || "";
        var isbreak = "";
        var phonerepret = "";
        var perNum = 0;
        policyinsure["pinsure"]='{"insuredname":"'+tbrname+'","birthday":"'+tbrbirth+'","insuredIdNoType":"'+tbrtype+'","insuredIdNo":"'+tbrnumber+'","gend":"'+tbrgend+'","homePhoneNo":"'+tbrtel+'","emailAddr":"'+tbremail+'","phadrs":"'+""+'","insuredtype":"'+insuredtype+'"}';

        policyinsure["pusers"]='[';

        $(".isnures").find(".demo-input").each(function(){
            if($(this).find(".phRelation").val()=="本人 Policyholder"&&$(this).children("div").length==3){
                policyinsure["pusers"]=policyinsure["pusers"]+'{"birthday":"'+tbrbirth+'","insuredname":"'+tbrname+'","isInsuredFlag":"'+2+'","insuredIdNoType":"'+tbrtype+'","insuredIdNo":"'+tbrnumber+'","gend":"'+tbrgend+'","relationship":"'+'本人 Policyholder'+'","homePhoneNo":"'+tbrtel+'","emailAddr":"'+tbremail+'","deathBeneficiary":"'+beneficiaryDefaultStr+'","policyuserid":"'+""+'"},'
            }
        })

            jQuery(".insureInfo").find(".demo-input").each(function(){
                if($(this).find(".phRelation").val()!="本人 Policyholder"||$(this).find(".phRelation").val()=="本人 Policyholder"&&$(this).children("div").length>3) {
                    var _tr = jQuery(this);
                    var phiddnum = _tr.find(".phIddnum").val();
                    var phtel = _tr.find(".phTel").val();
                    if (typeof(_pn["str" + phiddnum]) == "undefined") {
                        _pn["str" + phiddnum] = _tr.find(".phIddnum");
                    } else {
                        isbreak = _pn["str" + phiddnum].add(_tr.find(".phIddnum"));
                        return false;
                    }
                    _pu["str" + phiddnum] = _tr.find(".phIddnum");
                    if (typeof(_pr["str" + phtel]) == "undefined") {
                        _pr["str" + phtel] = _tr.find(".phTel");
                    } else {
                        if (phtel != "") {
                            phonerepret = _pr["str" + phtel].add(_tr.find(".phTel"));
                            return false;
                        }
                    }
                    //var policyuserid = _tr.find(".policyuserid").val();
                    var phname = jQuery.trim(_tr.find(".phName").val().replace(/"/gi, "\\\"").replace(/'/gi, "\\\'").replace(/(&nbsp;| | |　)+/gi, " "));
                    var birth = _tr.find(".phBirth").val();
                    var phiddtype = _tr.find(".phIddtype").val();
                    var phgend = _tr.find(".phGend").val();
                    var relation = _tr.find(".phRelation").val();
                    var phsyr = _tr.find(".PUBeneficiary").val();
                    var phemail = _tr.find(".phEmail").val();
                    policyinsure["pusers"] += '{"birthday":"' + birth + '","insuredname":"' + phname + '","isInsuredFlag":"' + 2 + '","insuredIdNoType":"' + phiddtype + '","insuredIdNo":"' + phiddnum + '","gend":"' + phgend + '","relationship":"' + relation + '","homePhoneNo":"' + phtel + '","emailAddr":"' + phemail + '","deathBeneficiary":"' + beneficiaryDefaultStr + '","policyuserid":"' + "" + '"},';
                    perNum++;
                }
            });
            if(perNum > maxPersonNum){
                alertBdMsg("保单最大人数为"+maxPersonNum+"人！");
                policyinsure["pusers"] = '';
                return policyinsure;
            }
            if(isbreak!=""){
                alertBdMsg("被保险人证件号码重复");
                policyinsure["pusers"] = '';
                return policyinsure;
            }
            policyinsure["pusers"]=policyinsure["pusers"].slice(0,-1);

        policyinsure["pusers"]+=']';
        return policyinsure;
    }

    //被保险人信用评级检查
    function insuredRatingCheck(jsonStr) {
        return jQuery.ajax({
            url: "/policyCenter/insure/blacklistUser.action",
            type: "post",
            dataType: "json",
            data: {"jsonStr": jsonStr}
        }).fail(function () {
            //alert("通信失败！");
        }).done(function (data) {
            if (data.success == "0") {
                alertBdMsg(data.message);
            }
        });
    }

    $(".policyknow_2").on("click",function(){
        var partnerId  = $("#partnerId").val();
        if(partnerId == null || partnerId == ""){
            $("#partnerId").val(sessionStorage.getItem("partnerId"));
        }
        $(".opa2").hide();
        $(".policyknow").hide();
        savePolicnInfo();
    })

    //保存投保单
    function savePolicnInfo() {
        isInsuringFlag = true;
        jQuery.ajax({
            url: "/policyCenter/b2c/saveInsure.action",
            type: "post",
            dataType: "json",
            data:  $("[name='manyinsur']").serialize(),
            erro:function () {
                alertBdMsg("通信失败！");
                isInsuringFlag = false;
            },
            success:function (data) {
                isInsuringFlag = false;
                if (data.success == "0") {
                    alertBdMsg(data.message);
                }else{
                    jQuery("#payId").val(data.id);
                    jQuery("#policyId").val(data.policyId);
                    // jQuery("html,body").css({"height":"auto","overflow":"hidden"});
                        // jQuery(".gopay1").addClass("gopay2").removeClass("gopay1");
                    var payNums = jQuery("#payment_table tr");
                    if(allowPayType == 2 ){
                        jQuery(".opa1").show();
                        jQuery(".paybox").show();
                    }else {
                        $("#payForm").attr("target","_self");
                        $("#payForm").submit();
                    }
                }
            }
        });
    }


    $("#notinsure").on("click",function(){
        $("#notinsure").attr("checked","checked")
        $("#isinsure").attr("checked",false)
    })
    $("#isinsure").on("click",function(){
        $("#isinsure").attr("checked","checked")
        $("#notinsure").attr("checked",false)
    })

    function phiddcheck(e){
        var _this = $(e)
        var erroindex = $(e).attr("erroindex");
        if(_this.attr("class")=="phName clearFormat"||_this.attr("class")=="phTel clearFormat"||_this.attr("class")=="phBirth"){
            if(_this.val()!=""){
                _this.parent().css("border-bottom","1px solid rgb(57,66,89)");
                _this.parent().parent().find(".erro_"+erroindex).removeClass("errmsg").addClass("divideline").find("div").html("");
            }
        }
        var phiddnum = _this.val();
        if(_this.parent().parent().find(".phIddtype").val()=="身份证"){
            if(phiddnum!=""){
                var _id = analyzeId(phiddnum);
                if(_id){
                    _this.parent().css("border-bottom","1px solid rgb(57,66,89)");
                    $(e).parent().parent().find(".erro_"+$(e).attr("erroindex")).removeClass("errmsg").addClass("divideline").find("div").html("");
                }
            }
        }else{
            if(_this.val()!=""){
                _this.parent().css("border-bottom","1px solid rgb(57,66,89)");
                _this.parent().parent().find(".erro_"+erroindex).removeClass("errmsg").addClass("divideline").find("div").html("");
            }
        }
		checkBirthAndId($(e));
    }
	function phTelphonecheck(e,type){
		if($(e).val().length == 11 || ($(e).val() == "" && !type)){
			$(e).parent().parent().find(".erro_"+$(e).attr("erroindex")).removeClass("errmsg").addClass("divideline").find("div").html("");
            $(e).parent().css("border-bottom","1px solid rgb(57,66,89)");
		}
	}
    function phEmailcheck(e){
        var peEmail = $(e).val();
        if(peEmail!=""&&/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/.test(jQuery(e).val())){
            $(e).parent().parent().find(".erro_"+$(e).attr("erroindex")).removeClass("errmsg").addClass("divideline").find("div").html("");
            $(e).parent().css("border-bottom","1px solid rgb(57,66,89)");
        }
    }

    function phTelcheck(e){
        var peTel = $(e).val();
        if(peTel!=""&&/^1[3|4|5|7|8][0-9]{9}$/.test(jQuery(e).val())){
            $(e).parent().parent().find(".erro_"+$(e).attr("erroindex")).removeClass("errmsg").addClass("divideline").find("div").html("");
            $(e).parent().css("border-bottom","1px solid rgb(57,66,89)");
        }
    }


    function holdmag(o){
        var _this = $(o);
        var erroIndex = _this.attr("erroindex");
        if(_this.val()!=""&&_this.parent().parent().find(".erro_"+erroIndex).find("div").html()!=""){
            _this.parent().parent().find(".erro_"+erroIndex).addClass("divideline").removeClass("errmsg").find("div").html("");
            _this.parent().css("border-bottom", "1px solid #E1E4EB");
        }
        if($(o).val()=="本人 Policyholder"){
            var inwe=0;
            $(".isnures").find(".phRelation").each(function(){
                if($(this).val()=="本人 Policyholder") {
                    inwe=inwe+1
                }
            })
            if(inwe>1){
                alertBdMsg("已存在关系为本人的被保险人，请重新选择");
                insureerroshow($(o),"已存在关系为本人的被保险人，请重新选择")
                return;
            }
            $(o).closest(".demo-input").addClass("myself");
        }
        if($(o).closest(".demo-input").attr("class").indexOf("myself")!=-1){
            if($(o).closest(".demo-input").find(".wrapBox").length==1){
                if($(o).val()!="本人 Policyholder"){
                    $(".ismy_1").show();
                    var insure = $(".insureex").find(".demo-input").clone();
                    var insuredivs=insure.children("div")
                    var index_1=1
                    insuredivs.each(function(){
                        if(index_1>3){
                            $(this).appendTo($(".myself"))
                        }
                        index_1 = index_1+1;
                    })
                }
            }else{
                if($(o).val()=="本人 Policyholder"&&$(o).parent().parent().find(".insureIndex").html()=="1"){
                    var insuredivs=$(".myself").children("div")
                    var index_1=1
                    insuredivs.each(function(){
                        if($(".isnures").find(".demo-input").length==1){
                            if(index_1==1){
                                $(this).hide();
                            }
                        }
                        if(index_1>3){
                            $(this).remove();
                        }
                        index_1 = index_1+1;
                    })
                }
            }
        }
        if ($(o).val()=="本人 Policyholder"){
            var selfindex=0
            $(".isnures").find(".phRelation").each(function(){
                if($(this).val()=="本人 Policyholder"){
                    selfindex = selfindex+1;
                }
            })
            if(selfindex>1){
                $(o).prop('selectedIndex', 0);
                var index = $(o).attr("erroindex");
                $(o).closest(".demo-input").find(".erro_"+index).addClass("errmsg").removeClass("divideline").find("div").html("请重新选择与投保人关系");
                alertBdMsg("存在关系为本人的被保险人，请重新选择与投保人关系")
                $(o).closest(".demo-input").removeClass("myself");
            }else {
                $(o).closest(".demo-input").find(".phName").val($(".demo-input-holder").find(".phName").val());
                $(o).closest(".demo-input").find(".phIddtype").val($(".demo-input-holder").find(".phIddtype").val());
                $(o).closest(".demo-input").find(".phIddnum").val($(".demo-input-holder").find(".phIddnum").val());
                $(o).closest(".demo-input").find(".phGend").val($(".demo-input-holder").find(".phGend").val());
                $(o).closest(".demo-input").find(".phBirth").val($(".demo-input-holder").find(".phBirth").val());
                $(o).closest(".demo-input").find(".phTel").val($(".demo-input-holder").find(".phTel").val());
                $(o).closest(".demo-input").find(".phEmail").val($(".demo-input-holder").find(".phEmail").val());
                $(o).closest(".demo-input").find(".weert").each(function () {
                    $(this).addClass("outlabel").removeClass("inputlabel");
                })
            }
        }
        var productId =  sessionStorage.getItem("productid");
        var planid =   sessionStorage.getItem("planid");
        showsingleprice(productId,planid);
    }

    function isuremag(o){
        var _this = $(o);
        var erroIndex = _this.attr("erroindex");
        if(_this.val()!=""&&_this.parent().parent().find(".erro_"+erroIndex).find("div").html()!=""){
            _this.parent().parent().find(".erro_"+erroIndex).addClass("divideline").removeClass("errmsg").find("div").html("");
            _this.parent().css("border-bottom", "1px solid #E1E4EB");
        }
    }


    $(".isok").on("click",function(){
        if($("#notok").attr("class")=="open4"){
            $("#notok").removeClass("open4").addClass("close4");
            $("#ok").removeClass("close4").addClass("open4");
        }else{
            $("#ok").removeClass("open4").addClass("close4");
            $("#notok").removeClass("close4").addClass("open4");
        }
    })


    function alertBdMsg(msg){
        $(".toa").html(msg);
        $(".toa").click();
//        jQuery("html,body").css({"height":"auto","overflow":"hidden"});
//        $("#opa").show();
//        $("#bdmsg").show();
//        $(".bdmsgspan").html(msg);
    }
    $("#opa").on("click",function(){
        $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
        $("#opa").hide();
        $("#bdmsg").hide();
        $(".bdmsgspan").html("");
    });

    $("#toPayButton").on("click",function(){

        $("#payForm").submit();
    });
    $(".closepay").on("click",function(){
        $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
        document.body.scrollTop = 1200
        $(".gopay2").addClass("gopay1").removeClass("gopay2");
        $(".paybox").hide();
        $(".opa1").hide();
    })
    $(".showHidden").on("click",function(){
        window.open("/baidu/baiduDetail.action?partnerId="+$("#partnerId").val()+"&detailType=1"+"&planCode="+$("#planCode").val(),"_self")
    })
    function plclerchange(o,plancode){
        $(".mainDeclare").find(".xq").each(function(){
            $(this).hide();
        })
        $(".mainDeclare .w_2").removeClass("w_2")
        $(".mainDeclare .v_1").removeClass("v_1")
        $(o).addClass("v_1")
        $(o).siblings("div").find(".w_1").addClass("w_2")
        $(".mainDeclare").find(".w1")
        $(".xq_"+plancode).show()
    }
    function datachange(){
        var planid = $(".current").find(".bottom").attr("planid");

        var acmintime = $("#activeDat").val();
        $("#exprDat").attr("min",acmintime);

        var exprDat=$("#exprDat").val();
        var d1 = new Date(acmintime.replace(/\-/g, "\/"));
        var d2 = new Date(exprDat.replace(/\-/g, "\/"));
        if(acmintime!=""&&exprDat!=""&&d1 >d2) {
            $("#exprDat").val(acmintime);
        }
        var d = new Date(acmintime.replace(/-/g,"-"));
        d.setDate(d.getDate()+plaInfo[planid].maxDays-1);
        var m=d.getMonth()+1;
        if(m<10){
            m="0"+m;
        }
        $("#exprDat").attr("max",d.getFullYear()+'-'+m+'-'+(d.getDate()));
    }
    function formatBackInsurant(){
        var ih = $(".demo-input-holder");
        var _pn = {};
        var _pr = {};
        var _pu= {};  //参与人与被保险人证件号码验重
        var insureList = [];
        var policyHolder = [];
        var policyinsure = [];
        var PHType = "1";
        var tbrname=jQuery.trim(ih.find(".phName").val().replace(/"/gi,"\\\"").replace(/'/gi,"\\\'").replace(/(&nbsp;| | |　)+/gi," "));
        var tbrbirth=ih.find(".phBirth").val();
        var tbrtype=ih.find(".phIddtype").val();
        var tbrnumber=ih.find(".phIddnum").val();
        var tbrgend=ih.find(".phGend").val();
        var tbrtel=ih.find(".phTel").val();
        var tbremail=ih.find(".phEmail").val();
        var insuredtype = "1";
        var policyinsureid=jQuery("#policyinsureid").val() || "";
        var isbreak = "";
        var phonerepret = "";
        var perNum = 0;

        var isincludeinsure="0"
        $(".isnures").find(".phRelation").each(function(){
            if($(this).val()=="本人 Policyholder"){
                isincludeinsure="1"
            }
        })
        policyinsure["isincludeinsure"]=isincludeinsure;
        policyinsure["pinsure"]='{"insuredname":"'+tbrname+'","birthday":"'+tbrbirth+'","insuredIdNoType":"'+tbrtype+'","insuredIdNo":"'+tbrnumber+'","gend":"'+tbrgend+'","homePhoneNo":"'+tbrtel+'","emailAddr":"'+tbremail+'","phadrs":"'+""+'","insuredtype":"'+insuredtype+'"}';

        policyinsure["pusers"]='[';


        jQuery(".insureInfo").find(".demo-input").each(function(){
            if($(this).find(".phRelation").val()!="本人 Policyholder") {
                var _tr = jQuery(this);
                var phiddnum = _tr.find(".phIddnum").val();
                var phtel = _tr.find(".phTel").val();
                if (typeof(_pn["str" + phiddnum]) == "undefined") {
                    _pn["str" + phiddnum] = _tr.find(".phIddnum");
                } else {
                    isbreak = _pn["str" + phiddnum].add(_tr.find(".phIddnum"));
                    return false;
                }
                _pu["str" + phiddnum] = _tr.find(".phIddnum");
                if (typeof(_pr["str" + phtel]) == "undefined") {
                    _pr["str" + phtel] = _tr.find(".phTel");
                } else {
                    if (phtel != "") {
                        phonerepret = _pr["str" + phtel].add(_tr.find(".phTel"));
                        return false;
                    }
                }
                //var policyuserid = _tr.find(".policyuserid").val();
                var phname = jQuery.trim(_tr.find(".phName").val().replace(/"/gi, "\\\"").replace(/'/gi, "\\\'").replace(/(&nbsp;| | |　)+/gi, " "));
                var birth = _tr.find(".phBirth").val();
                var phiddtype = _tr.find(".phIddtype").val();
                var phgend = _tr.find(".phGend").val();
                var relation = _tr.find(".phRelation").val();
                var phsyr = _tr.find(".PUBeneficiary").val();
                var phemail = _tr.find(".phEmail").val();
                policyinsure["pusers"] += '{"birthday":"' + birth + '","insuredname":"' + phname + '","isInsuredFlag":"' + 2 + '","insuredIdNoType":"' + phiddtype + '","insuredIdNo":"' + phiddnum + '","gend":"' + phgend + '","relationship":"' + relation + '","homePhoneNo":"' + phtel + '","emailAddr":"' + phemail + '","deathBeneficiary":"' + beneficiaryDefaultStr + '","policyuserid":"' + "" + '"},';
                perNum++;
            }
        });
        policyinsure["pusers"]=policyinsure["pusers"].slice(0,-1);
        if(policyinsure["pusers"]!="") {
            policyinsure["pusers"] += ']';
        }


        return policyinsure;
    }
    $("body").on("click","select",function(){
        var select = $(this);
        select.find("option").each(function () {
            if ($(this).val() == "" || $(this).val() == null) {
                $(this).remove();
            }
        })
    })
</script>
<script src="/js/b2c/baidu_example/vendor.js?v=$!{JS_AND_CSS_VER}"></script>
<script src="/js/b2c/baidu_example/bd_toast.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/touchslider.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/mobile/mobilecommon.js?v=$!{JS_AND_CSS_VER}"></script>
    #parse( "/template/policyCenter/b2c/googleAnalytics.vm" )
</body>
</html>

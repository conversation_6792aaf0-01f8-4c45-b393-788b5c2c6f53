#parse("$!{location}/common/header.vm")
#set($cpsRoot = "/cps/$!{partner.getPartnerId()}")##各cps的根路径
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="Pragma" content="no-cache"/>
    <meta http-equiv="Cache-Control" content="no-cache"/>
    <meta http-equiv="Expires" content="0"/>
    <title>$!{sysTitle}</title>
    <script type="text/javascript" src="/js/jquery.js?v=$!{JS_AND_CSS_VER}"></script>
    <link href="/assets/CssAndImg/common/css/globalcss.css?v=$!{JS_AND_CSS_VER}" rel="stylesheet">
    <link rel="stylesheet" href="/assets/CssAndImg/baichuan/css/insure.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
    <link rel="stylesheet" href="/assets/CssAndImg/baichuan/css/calendar.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
    <link rel="stylesheet" href="/assets/CssAndImg/common/css/insure_b2c.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
    <link rel="stylesheet" href="/assets/CssAndImg/common/css/paste.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
    <link rel="stylesheet" href="/assets/CssAndImg/baichuan/css/zonesAndJobs.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
	<link rel="stylesheet" href="/assets/CssAndImg/common/css/zonesAndJobs.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
	<link rel="stylesheet" href="/assets/CssAndImg/common/css/multipleSelect.css?v=$!{JS_AND_CSS_VER}" type="text/css"/>
    <script type="text/javascript">


        var baseDir = "$!{baseDir}$!{cpsRoot}";//相对根目录，便于模块间转换时用到
        var uploadAction = "$!{cpsRoot}/policyCenter/insure/uploadValXls";//上传文件路径
        var uploadparAction = "$!{cpsRoot}/policyCenter/insure/uploadparticipantXls";//上传参与人文件路径
        var beneficiaryDefaultStr = "法定Legal Heir";
        var beneficiaryAddStr = "身故受益人";
    </script>
    <style type="text/css">
        .insure_left {
            width: 100%;
        }

        #addpa {
            height: 18px;
        }

        .tabpack input[type=text] {
            height: 24px;
            padding: 0px;
        }
        .tabpack select {
            padding: 0px;
        }

        .tbrInfoTitle th {
            border-top-width: 0px;
        }


        #assured input, #assured select {
            height: 22px;
			_height:18px;
			_line-height:18px;
            line-height: 22px;
            font-size: 12px;
            text-align: left;
            border: none;
			border-width:0;
            margin-left: 1px;
			padding:0;
        }

        #assured input[type=button] {
            line-height: 0;
            border: 1px solid #ccc;
            margin-left: 1px;
        }

        .tbrmessage input[type=text], .tbrmessage select {
            width: 98%;
        }

        .tbrname, .phname {
            width: 121px;
        }

        .insurant .phgend {
            width: 70px;
        }

        .insurant select, .policy_holder select {
            width: 97px;
        }

        .tbrmessage .phiddtype, .add_rate_tbr .tbrtype {
            width: 77px;
        }

        #opa {
            background-color: #000000;
            display: none;
            left: 0;
            opacity: 0.7;
            position: fixed;
            top: 0;
            z-index: 7;
        }

        

        button, input[type=button] {
            background-color: #f3f3f3;
            border: 1px solid #ccc;
            border-radius: 2px;
            cursor: pointer;
        }

        button:focus, input[type=button]:focus {
            border: 1px solid #ccc;
        }

        button:hover, input[type=button]:hover {
            background-color: #f0f0f0;
        }

        .assured {
            padding: 0px;
            margin: 0px;
        }

        .assured .insurant {
            padding: 0px;
            margin: 0px auto;
        }

        .insurant {
            border-color: #ccc;
            width: 94%;
        }

        .jhrate {
            margin: auto;
            position: fixed;
        }

        /*#assured td select, #assured td input[type=text] {*/
            /*width: 98%*/
        /*}*/

        .div3 a {
            color: #0080ff;
        }

        .tabpack a {
            color: #0080ff;
        }

        #edituser td {
            border: none;
        }

        #edituser input[type="button"] {
            padding: 2px 5px;
            line-height: 18px;
            height: auto;
            width: auto;
            color: #222;
        }

        #edituser input[type="button"]:hover {
            border: 1px solid #ccc;
        }

        select.policyInfoDiv {
            width: 320px;
        }

        #activeDat, #exprDat {
            width: 151px;
            text-align: left;
            padding: 0 3px;
        }

        /*.process li {*/
            /*padding: 0 90px 0 80px;*/
        /*}*/

        

        .tabpack .assured input[type=text] {
            margin: 0;
        }

        #pri {
            margin-left: 10px;
        }

        .del_icon,.par_del_icon{
            width: 12px;
            height: 12px;
            background: url("$!{baseDir}/assets/CssAndImg/baichuan/imgs/del_icon.png") no-repeat;
            margin: 3px;
        }

        .del_icon:hover {
            background-position: 0 -50px;
        }
        .par_del_icon:hover {
            background-position: 0 -50px;
        }
        .par_tbrmessage:hover .par_del_icon {
            visibility: visible;
        }
        .par_tbrmessage .par_del_icon {
            visibility: hidden;
        }
        .com_tbrmessage:last-child {
            border-bottom: 1px solid #ccc;
        }
        .com_tbrmessage {
            border-top: 1px solid #ccc;
        }
        .com_tbrmessage td{
            height:24px;
        }

        #assured td select, #assured td input[type=text] {
            width: 98%
        }

        .expandFold {
            display: inline-block;
            margin-top: 5px;
            height: 20px;
            line-height: 20px;
            width: 20px;
            float: right;
            display: inline-block;
            background: url("$!{baseDir}/assets/CssAndImg/baichuan/imgs/expand-fold.png") 0px 0px no-repeat;
        }

        .statementsTitle {
            font-family: Microsoft Yahei;
        }

        #sm_tbsms{
            z-index: 100;
            position: fixed;
            empty-cells: show;
            width: 800px;
            border: 1px solid #969696;
            border-radius: 3px;
            padding: 0px;
            background-color: #fff;
        }
        .div_table{
            margin-bottom: 20px;
        }
        .div_table th{
            font-size: 12px;
            color: #333;
            font-weight: bold;
            background-color: #f5f5f5;
            height: 25px;
            padding: 2px 5px;
            text-align: center;
        }
        .div_table td{
            color: #666666;
            font-size: 12px;
            height: 18px;
            background-color: rgb(255, 255, 255);
            padding: 2px 5px;
        }

        .benefit_price{

            OVERFLOW-Y: auto;
            OVERFLOW-X: hidden;
            max-height: 300px;
            padding: 0px 35px;
        }
        .info_edit_head {
            border-bottom:  1px solid #969696;
            height: 39px;
            line-height: 35px;
            padding: 0px 15px;
            background-color:#e7e7e7;
        }
        .info_edit_foot{
            border-top:  1px solid #969696;
            background-color:#e7e7e7;
            height: 39px;
        }
        .info_edit_title {
            float: left;
            font-weight: 800;
            font-size: 14px;
        }
        #cancel{
            float: right;margin-top: 8.5px;margin-right: 10px;height: 25px; width: 50px;
        }
        .benefit_table,.price_table{
            width: 90%;
        }

        .par_hidden_nickname,.par_hidden_birth,.par_hidden_phname,.par_hidden_phtel{
            position: absolute;
            width: 100px;
            font-size: 12px;
            line-height: 22px;
            display: inline;
            color: #999;
        }

        .participant {
            padding: 0px;
            margin: 0px;
        }

        .participant .par_table {
            padding: 0px;
            margin: 0px auto;
        }

        .par_table {
            border-color: #ccc;
            width: 94%;
        }

        #participant td select, #participant td input[type=text] {
            width: 98%
        }
        .par_table {
            margin-bottom: 10px;
            border-spacing: 0px;
            padding-bottom: 10px;
        }
        .par_table th {
            height: 25px;
            line-height: 25px;
            padding: 0px;
            text-align: center;
            font-size: 13px;
            font-family: Microsoft Yahei;
        }
        .par_tbrmessage:hover td{
            border-top:1px solid #FFBB00;
            border-bottom:1px solid #FFBB00;
        }
        .par_tbrmessage td {
            padding-left: 3px;
            padding-right: 3px;
            text-align: left;
            padding-right: 3px;
            border: 1px solid #ccc;
            border-left-width: 1px;
            border-right-width: 1px;
            /*border-top: none;*/
            /*border-bottom: none;*/
            height: 24px;
        }
        #participant input, #participant select {
            height: 22px;
            line-height: 22px;
            font-size: 12px;
            text-align: left;
            border: none;
            margin-left: 1px;
            margin: 0;
        }
        #editparticipant td {
            border: none;
        }
        .par_tbrmessage:last-child {
            border-bottom: 1px solid #ccc;
        }
        .par_tbrmessage {
            border-top: 1px solid #ccc;
        }
        .par_tbrInfoTitle th {
            border-top-width: 0px;
        }
        .par_tbrInfoTitle th {
            text-align: center;
        }
        #insurant_company .hidden_phname{
            width: 200px;
        }
        #insurant_company tfoot tr{
            height: 45px;
        }
        .hidden_company_email, .hidden_company_phtel, .hidden_company_phname, .hidden_company_phiddnum{
            position: absolute;
            width: 100px;
            font-size: 12px;
            line-height: 22px;
            display: inline;
            color: #999;
        }
        .hidden_insureCompany{
            position: absolute;
            width: 100px;
            font-size: 12px;
            line-height: 34px;
            display: inline;
            color: #999;
            margin-left: 6px;
        }

        .company_person{
            font-size: 14px;
            font-family:Microsoft Yahei;
            color: #3b7bea;
            cursor: pointer;
            text-decoration:none;
            font-weight: normal;
        }
        .select_on{
            font-size: 14px;
            font-family:Microsoft Yahei;
            font-weight: 900;
            color: #666666;
        }
        #multiUpdate_editor, .par_upexcel, .par_delall {
            padding: 2px 5px;
        }
        .insure_company,.product_liability{
            display: none;
        }
        #policyuser_num,#participant_num{
            width: 15px;
            height: 18px;
            text-align: center;
            padding:0px;
        }
		#policyExtendPlanBlock,#policyExtendPlanTitle{display:none;}
        .hidden_tips{
            position: absolute;
            font-size: 12px;
            line-height: 35px;
            color: #999;
            margin-left: 5px;
        }
        #infos{
            /*text-align: 10px;*/
            padding:0px;
            font-size: 12px;
            font-weight: bold;
        }
        .defaultAddr span {
            display: none;
            cursor: pointer;
        }

        .onAddr span {
            display: inline;
        }

        .defaultAddr:hover span {
            display: inline;
        }
        #site{
            cursor: pointer;
            float: right;
            color: #0080ff;
            font-weight:bolder;
        }
        #planDiv{
            max-width: 400px;
        }
        .radioLabel{
            margin-right: 20px;
            min-width: 180px;
            display: inline-block;
            cursor: pointer;
        }
        .radioLabel span{
            position: relative;
            top: -3px;
        }
        #assured  .tbrmessage input[type='radio']{
          height: 14px;
        }
        #assured .male{
           margin-left: 8px;
        }
        .tbrmessage .genderSpan{
            position: relative;
            top: -4px;
        }
        .clickGender{
            cursor: pointer;
        }
        .clickGender input[type='radio']{
            cursor: pointer;
        }
        #insured_sex_select  .clickGender{
            width: 60px;
            display: inline-block;
        }
        #insured_sex_select .genderSpan{
            position: relative;
            top: -3px;
            margin-left: 10px;
        }
        #insured_sex_select .male{
            margin-left: 20px;
        }
        .forWhich span{
            float: left;
            width: 15%;
            margin-left: 10%;
            text-align: center;
            height: 30px;
            line-height: 30px;
            border: 1px solid #ccc;
            border-radius: 5px;
            color: #333;
            margin-top: 8.5px;
            font-size: 15px;
            cursor: pointer;
        }

        .forWhich .forWhichOn{
            border-color:#0080ff;
        }
        .policyUsersDiv{
            display: none;
        }

    </style>
</head>
<body>

<div class="bgShadow">
    <div class="page">
        <div class="bgHead">
            <div class="head">
                #set($newJQery = "true")
                #parse("$!{location}/cpsPath/$!{partner.template}/header.vm")
            </div>
        </div>
        <div class="main">
            <div class="insure_left">
                <div class="process">
                    <ul>
                        <li class="on">填写投保单</li>
                        <li>核对信息及报价</li>
                        <li>支付保费</li>
                        <li>下载保单及条款</li>
                    </ul>
                </div>

                <div id="policy_info">
                    <div class="info_title" style="border-top-width:0;">订单信息</div>
                    <!--[if IE 6]><input type="text" style="width:0;height:0;border:0;padding:0;margin:0;" id="forFocus">
                <script type="text/javascript">
                    document.getElementById("forFocus").focus();
                </script><![endif]-->
                    <form name="manyinsur" method="post" enctype="multipart/form-data"  action="saveInsure.action" id="check" autocomplete="off">
                        <input type="hidden" id="tsTag" name="tsTag">
                        <input type="hidden" name="partnerId" id="partnerId" value="$!{partner.partnerId}">
                        <input type="hidden" name="ipcid" id="ipcid" value="">
                        <input type="hidden" name="planId" id="planId" value=""/>
                        <input type="hidden" name="policyPrice" id="policyPrice" value=""/>
                        <input type='hidden' name='sts' id='sts' value=""/>

                        <input type="hidden" name="pinsure" id="pinsure" value=""/>
                        <input type="hidden" name="pusers" id="pusers" value=""/>
                        <input type="hidden" name="uid" id="uid" value="$!{uid}"/>
						<input type="hidden" name="pExtend" id="pExtend" value=""/>
                        <input type="hidden" id="insureLastTime" value="">

                        <input type="hidden" name="reference" id="reference" value="$!{reference}">
                        <input type="hidden" name="state" id="state" value="$!{state}">
                        <input type="hidden" name="sendState" id="sendState" value="$!{sendState}">
                        <input type="hidden" name="orderId" id="orderId" value="$!{orderId}" >
                        <input type="hidden" name="actType" id="actType" value="$!{actType}">
                        <input type="hidden" name="policyNum" id="policyNum" value="$!{policyNum}">
                        <input type="hidden" name="policyId" id="policyId" value="$!{policyId}">

                        <div class="tabpack">
							<div class="compareProduct" style="display:none;"></div>
                            <textarea style="display:none;" id="jsonPolicy">$!{jsonPolicy}</textarea>
                            <textarea style="display:none;" id="jsonPinsure">$!{jsonPinsure}</textarea>
                            <textarea style="display:none;" id="jsonPuserlist">$!{jsonPuserlist}</textarea>
                            <span id="getProduct" style="display:none;">#if($!{icid}!=""){"icid":"$!{icid}","ipcid":"$!{ipcid}","planId":"$!{planId}"}#end</span>
                            <!-- 产品名称+公司名称 -->
                            <table class="tab1">
                                <tr>
                                    <td class="item_title">保险产品：</td>
                                    <td>
                                        <select class="policyInfoDiv" id="prodcategory" onchange="changepro()">
                                        </select>
                                        <!-- <select class="subsidiary" id="channels" style="display:none;" name="sendcode"></select> -->
                                    </td>
                                </tr>
                                <!-- 选择产品计划 -->
                                <tr>
                                    <td class="item_title">保障计划：</td>
                                    <td>
                                        <input type="hidden" class="policyInfoDiv" id="plancoderadio"></input>
                                        <div  id="planDiv"></div>
                                    </td>
                                </tr>
                                <!-- 选择保险时间 -->
                                <tr id="timeLength" style="display:none;">
                                    <td class="item_title">起止时间：</td>
                                    <input name="jxqc_month2" id="jxqc_month2" type="hidden" value=""/>
                                    <td id="month_select">
                                        <select style="width:48px;min-width:50px;">
                                            <option value="0" selected="true">选择</option>
                                            <option value="2">二个月</option>
                                            <option value="3">三个月</option>
                                            <option value="4">四个月</option>
                                            <option value="5">五个月</option>
                                            <option value="6">六个月</option>
                                            <option value="7">七个月</option>
                                            <option value="8">八个月</option>
                                            <option value="9">九个月</option>
                                            <option value="10">十个月</option>
                                            <option value="11">十一个月</option>
                                            <option value="12">十二个月</option>
                                        </select>
                                        <input name="jxqc_month" id="jxqc_month" type="hidden"/>
                                    </td>
                                </tr>

                                <!-- 选择保险时间 -->
                                <tr>
                                    <td class="item_title">起止时间：</td>
                                    <td id="jstb">
                                        <input fmt="ct" id="activeDat" value="#if("$!{activeDat}"!="")$!{activeDat}#end" type="text"  size="20" name="activeDat">
								<span id="end_date">
								-
								<input fmt="ct" id="exprDat" value="#if("$!{exprDat}"!="")$!{exprDat}#end" type="text"  size="20" name="exprDat">
								</span>
                                        <select id="daysSelector" name="daysSelector" class="select" style="width:80px;display:none;min-width:48px;">
                                            <option value="5" selected="true">5</option>
                                            <option value="7">7</option>
                                            <option value="10">10</option>
                                            <option value="14">14</option>
                                            <option value="21">21</option>
                                            <option value="30">30</option>
                                            <option value="45">45</option>
                                            <option value="60">60</option>
                                            <option value="75">75</option>
                                            <option value="90">90</option>
                                            <option value="180">180</option>
                                            <option value="365">365</option>
                                        </select>
                                        <br /><span id="aheadTime" style="color:#0066ff;">需在0000-00-00 00:00:00前提交投保</span>
                                    </td>
                                </tr>
                                <!-- 人均保费 -->
                                <tr class="product_not_liability">
                                    <td class="item_title">人均保费：</td>
                                    <td>
                                        <span id="tsx" style="display:none;">1天</span>
                                        <span style="padding-top:10px;"><span id="pri">60元/成人 50元/儿童 </span></span>
                                        <span style="padding-top:10px;display:none;"><a href="javascript:false;" target="blank">详细</a></span>
                                        <span style="text-right:50px;display:none;" id="extendedCoverage"><a name="notice" onclick="yc('sm_tbsms')" href="javascript:void(0)">保障范围和保费价格</a>
                                    </td>
                                </tr>
                            </table>
                            <!--<div style=" text-indent:1.5em;"class="smf"><span class="statementsTitle">计划保障范围和保费价格</span> <a name="notice" onclick="yc('sm_tbsms')" href="javascript:void(0)">点击查看</a></div>-->
                            <div class="insure_notice" id="sm_tbsms" style="display:none;">
                                <div class="info_edit_head"><span class="info_edit_title">保障范围和保费价格</span>
                                    <a  title="关闭" href="javascript:void(0)">
                                        <b id="uInfo_close" style="font-size:18px;font-weight:800;float: right; color: blue; ">×</b>
                                    </a>
                                </div>
                                <div class="benefit_price" id="benefit_price">
                                </div>
                                <div class="info_edit_foot">
                                    <input  type="button" value="关 闭"   id="cancel"/>
                                </div>
                            </div>
                        </div>
						<!-- 计划增强 -->
						<div class="info_title" style="height: 50px;">

                            <div class="titleInfo forWhich" >
                                <span class="forSelf forWhichOn">给自己投保</span><span class="forOther">给他人投保</span>
                                <div class="clear"></div>
                            </div>

                        </div>



                        <div class="info_title policyuser_info_title policyUsersDiv">被保险人信息
                            <span onclick="change_user_type(1)" class="company_person select_on">&nbsp;个人</span>
                            <span onclick="change_user_type(2)" class="company_person">&nbsp;机构</span>
                            <input type="hidden" id="policyuser_type" value="1">
                            <span class="maxPersonTips">（最多 <span class="maxPersonNums">0</span> 人）</span>
                        </div>
                        <div class="tabpack policyUsersDiv">
                            <div class="assured" id="assured">
                                <table class="insurant" id="insurant" cellspacing="0" cellpadding="0">
                                    <tr class="tbrInfoTitle">
                                        <th style="width:30px;">序号</th>
                                        <th title="例：李明 LI MING" >姓名<span style="color:red;">&nbsp;*&nbsp;</span></th>
                                        <th class="IDth" title="点击可以按证件号码排序" style="width:180px;cursor:pointer;">证件号码<span style="color:red;">&nbsp;*&nbsp;</span><span><img height="11" src="/assets/CssAndImg/baichuan/imgs/sortbg.png"></span></th>
                                        <th style="width:56px;">证件类型</th>
                                        <th style="width:75px;">性别</th>
                                        <th style="width:95px;" title="例：1980-01-01">出生日期<span style="color:red;">&nbsp;*&nbsp;</span></th>
                                        <th style="width:120px;">手机号码</th>
                                        <th style="width:70px;">是投保人的</th>
                                        <th class="syr_td">$!{beneficiaryAddStr}</th>
                                        <th style="display:none;" class="moneyTxt insuredPrice">保费</th>
                                        <th style="width:20px;"></th>
                                    </tr>
                                    <tr class="tbrmessage">
                                        <td style="border-left:none;"><input class="serial" type="text" value="1" onkeydown="checkkey(this.value,event)"/></td>
                                        <td><span class="hidden_phname">例：李明 LI MING</span><input class="phname" type="text" alt="例：李明 LI MING" title="例：李明 LI MING" value=""/></td>
                                        <td><input fmt="idcard" maxlength="22" class="phiddnum" type="text" value=""/></td>
                                        <td>
                                            <select class="phiddtype" value="">
                                                <option value="">选择</option>
                                                <option value="护照">护照</option>
                                                <option value="身份证">身份证</option>
                                                <option value="其他">其他</option>
                                            </select>
                                        </td>
                                        <td class="genderTd">
                                           <span class="clickGender"><input class="female genderChoosen" type="radio" value="女"><span class="genderSpan">女</span></span><span class="clickGender"><input class="male genderChoosen" type="radio" value="男"><span class="genderSpan">男</span></span>
                                        </td>
                                        <td><span class="hidden_birth">例:1980-01-01</span><input fmt="birth" maxlength=0 class="birth imeMode" type="text" alt="例：1980-01-01" title="例：1980-01-01" value=""/>
                                        </td>
                                        <td><span class="hidden_phtel">可选填</span><input maxlength="18" fmt="tel" class="phtel" type="text"/></td>
                                        <td>
                                            <select class="relation" value="">
                                                <option value="">选择</option>
                                                <option value="本人 Policyholder" class="brgx">本人</option>
                                                <option value="配偶 Spouse">配偶</option>
                                                <option value="父母 Parents">父母</option>
                                                <option value="子女 Child">子女</option>
                                                <option value="其他 Other">其他</option>
                                            </select>
                                        </td>
                                        <td class="syr_td">
                                            <input type="button" class="phsyr" value="$!{beneficiaryDefaultStr}" readonly="true"/>
                                            <input class="phsyrhide" value="" type="hidden"/>
                                            <input class="PUBeneficiaryBak" value="" type="hidden"/>
                                        </td>
                                        <td style="display:none;" class="moneyTxt insuredPrice"></td>
                                        <td style="border-right:none;"><p class="delalone del_icon"></p></td>
                                        <td style="display:none;"><input type="hidden" class="policyuserid"/></td>
                                    </tr>
                                    <tfoot >
                                    <tr id="edituser" style="height: 35px;" >
                                        <td colspan="4" style="height:40px;line-height:40px;text-align:left;display:none;">
                                            <label><input type="checkbox">每人一张保单</label>
                                            &nbsp;&nbsp;
                                            <label><input type="checkbox">共同使用一个投保人</label>
                                        </td>
                                        <td class="edituser_FillIn" colspan="100" style="height:40px;line-height:40px;text-align:right;">
#*
                                            <a id="frequentContacts" href="javascript:void(0)" onclick="contacts()" title="从常用联系人中选择被保险人" #if("$!{maioTool.nickname()}" != "未登录" && !$!{insuranceCompany}) style="position:relative;top:2px;" #else style="position:relative;top:2px;display:none;" #end>常用联系人</a>
*#
                                            <a href="javascript:void(0);" id="manyUpdate_editor" title="粘贴多个被保险人请逐一添加到被保险人列表" style="position:relative;top:2px;">批量粘贴</a>
                                            <a href="javascript:void(0);" class="upexcel" style="position:relative;top:2px;">批量上传</a>
                                            <a href="javascript:void(0);" class="delall" style="position:relative;top:2px;">删除全部</a>
                                            <a href="$!{baseDir}/assets/other/template.xls" target="_blank" style="position:relative;top:2px;">模板下载</a>
                                        </td>
                                    </tr>
                                    </tfoot>
                                </table>
                                <!--指定身故受益人-->
                                <div class="jhrate" style="display: none;">
                                    <div class="tip_til" style="padding:5px 10px 1px;">
                                        <span style="font-size:12px;">添加$!{beneficiaryAddStr}</span>
                                        <a class="cancel" title="关闭" href="javascript:void(0)">
                                            <b style="font-size:16px;font-weight:800;">×</b>
                                        </a>
                                    </div>
                                    <table class="bx_rate bx_rate2">
                                        <thead class="benehead">
                                        <tr>
                                            <th style="height:22px;">姓名</th>
                                            <th>证件类型</th>
                                            <th>证件号码</th>
                                            <th>与被保险人关系</th>
                                            <th>受益份额</th>
                                            <th><div style="min-width:24px;"></div></th>
                                        </tr>
                                        </thead>
                                        <tbody class="bene2">
                                        <tr class="add_rate_bene">
                                            <td class="firstTd">
                                                <input class="benename" type="text" style="width:80px;" value="">
                                            </td>
                                            <td>
                                                <select class="benetype">
                                                    <option value="">选择</option>
                                                    <option value="身份证">身份证</option>
                                                    <option value="护照">护照</option>
                                                    <option value="其他">其他</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input fmt="idcard" class="benenumber" type="text" style="width:150px;" value="">
                                            </td>
                                            <td>
                                                <select class="benerelation">
                                                    <option value="">选择</option>
                                                    <option value="配偶">配偶</option>
                                                    <option value="父母">父母</option>
                                                    <option value="子女">子女</option>
                                                    <option value="其他">其他</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input class="benepercent" type="text" value="" style="width:30px;"/> %
                                            </td>
                                            <td class="lastTd">
                                                <input class="benedel" type="button" value="删除" style="border:none;"/>
                                            </td>
                                        </tr>
                                        </tbody>
                                        <tbody>
                                        <tr style="height:30px;">
                                            <td colspan="6" style="text-align:left;border:0px;">
                                                <span class="bene_message">提示：共计1个受益人，合计：0%，您还需要再输入100%</span>
                                                <div class="bene_all_edit">
                                                    <input class="closeandsave" type="button" value="保 存"/>
                                                    <input class="deleallbene" type="button" value="删除全部"/>
                                                </div>
                                                <div class="clear"></div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <!--身故受益人结束-->


                                <!--被保险人为机构-->
                                <table class="insurant" id="insurant_company" cellspacing="0" cellpadding="0" style="display: none">
                                    <tr class="tbrInfoTitle">
                                        <th style="width:30px;">序号</th>
                                        <th style="" title="例：北京百川保险经纪有限公司">机构名称<span style="color:red;">&nbsp;*&nbsp;</span></th>
                                        <th style="width: 190px;">机构代码<span style="color:red;">&nbsp;*&nbsp;</span></th>
                                        <th style="width: 180px">联系方式</th>
                                        <th style="width: 220px;">邮箱</th>
                                    </tr>
                                    <tr class="com_tbrmessage">
                                        <td style="border-left:none;"><input class="serial" type="text" value="1" onkeydown="checkkey(this.value,event)"/></td>
                                        <td><span class="hidden_company_phname" style="width: 190px">#if("$!{insuranceCompanyId}"=="6")例：利宝保险有限公司 #else 例：北京百川保险经纪有限公司#end</span><input class="company_phname" type="text" alt="例：北京百川保险经纪有限公司" title="北京百川保险经纪有限公司" value=""/></td>
                                        <td><span class="hidden_company_phiddnum" style="width: 190px">营业执照(三证合一)/税务登记号</span><input class="organization_code" type="text" value=""/></td>
                                        <td><span class="hidden_company_phtel">可选填</span><input class="company_phtel" maxlength="15"   type="text"/></td>
                                        <td style="border-right: none;"><span class="hidden_company_email">可选填</span><input class="company_email" type="text"  value=""/>
                                        </td>
                                        <td style="border-right:none;"><p class=""></p></td>
                                        <td style="display:none;"><input type="hidden" class="policyuserid"/></td>
                                    </tr>
                                    <tfoot>
                                    <tr >
                                    </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>


                        <!-- 投保人信息 -->
                        <div class="info_title policyInsure_info_title">投保人信息
                            <span  class="company_person" style="color: #666666;#if(!${insuranceCompany} == true) display: none #end"> :</span>
                            <span onclick="change_insured_type(1)" class="company_person select_on">&nbsp;个人</span>
                            <span onclick="change_insured_type(2)" class="company_person">&nbsp;机构</span>
                        </div>
                        <div class="tabpack">
                            <input name="insureuser_id" type="hidden" value=""/>
                            <input type="hidden" id="insured_type" value="1"/>
                            <!-- 利宝产品显示_start -->
                            <table id="insureuser_com" class="tab3 insurer_table" style="display: none;width: 80%;">
                                <tr>
                                    <td class="item_title">机构名称：</td>
                                    <td>
                                        <span class="hidden_insureCompany_phname hidden_insureCompany" style="width: 190px">#if("$!{insuranceCompanyId}"=="6")例：利宝保险有限公司 #else 例：北京百川保险经纪有限公司#end</span>
                                        <input id="company_name" class="input_test" style="width: 200px" type="text" value="">
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                    </td>
                                    <td class="item_title">机构代码：</td>
                                    <td>
                                        <span class="hidden_insureCompany_phiddnum hidden_insureCompany" style="width: 190px">营业执照(三证合一)/税务登记号</span>
                                        <input id="company_code" class="input_test" style="width: 200px"  type="text" value="">
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="item_title">联系方式：</td>
                                    <td><span class="hidden_insureCompany_phone hidden_insureCompany" style="width: 190px">可选填</span>
                                        <input id="company_phone" class="input_test" style="width: 200px"  type="text" value="">
                                    </td>
                                    <td class="item_title">电子邮箱：</td>
                                    <td><span class="hidden_insureCompany_email hidden_insureCompany" style="width: 190px">可选填</span>
                                        <input id="company_email" class="input_test" style="width: 200px"  type="text" value="">
                                    </td>
                                </tr>
                            </table>
                            <!-- 利宝产品显示_end -->

                            <table id="validate" id="tbform" class="tab3 insurer_table">
                                <input type="hidden" class="policyinsureid" id="policyinsureid"/><!--保单修改所需的投保人id-->
                                <tr>
                                    <td class="item_title" id="insured_name_label">姓　　名：</td>
                                    <td id="insured_name_input">
                                        <input hdSign="tbxm" class="input_test" id="tbxm" type="text" value=""/>
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                    </td>
                                    <td class="item_title" id="insured_idnum_label">证件号码：</td>
                                    <td id="insured_idnum_input">
                                        <input fmt="idcard" maxlength="22" hdSign="tbnum" id="tbnum" type="text" value=""/>
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                        <input id="hidden_tbnum" type="hidden" value=""/>
                                    </td>
                                    <td class="item_title" id="insured_idtype_label">证件类型：</td>
                                    <td id="insured_idtype_select">
                                        <select style="width:174px;_width:160px;z-index:1" id="tbtype">
                                            <option value="护照">护照</option>
                                            <option value="身份证">身份证</option>
                                            <option value="其他">其他</option>
                                        </select>
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                    </td>
                                </tr>
                                <tr class="hiddentr" id="insure_company_insert">
                                    <td>&nbsp;</td>
                                    <td><label hdSign="tbxm" class="name_year_position1" id="pass_username_tip" style="visibility:visible;">例:李明 LI MING</label></td>
                                    <td>&nbsp;</td>
                                    <td><label hdSign="tbnum" class="name_year_position1" id="pass_card" style="visibility:visible;">如是身份证，先填这里</label></td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="item_title" id="insured_sex_label">性　　别：</td>
                                    <td id="insured_sex_select">
                                        <span class="clickGender"><input class="female genderChoosen" type="radio" value="女"><span class="genderSpan">女</span></span><span class="clickGender"><input class="male genderChoosen" type="radio" value="男"><span class="genderSpan">男</span></span>
                                        <span style="color:red; margin-left: 54px">&nbsp;*&nbsp;</span>
                                    </td>
                                    <td class="item_title" id="insured_birthday_label">出生日期：</td>
                                    <td id="insured_birthday_input">
                                        <input hdSign="tbsr" fmt="birth" maxlength=0 class="input_test imeMode" id="tbsr" type="text" value=""/>
                                        <span style="color:red;">&nbsp;*&nbsp;</span>
                                        <input id="tbsr2" type="hidden" value=""/></td>
                                    <td class="item_title" id="insured_phone_label">联系电话：</td>
                                    <td id="insured_phone_input"><input maxlength="18" fmt="tel" id="tbtel" type="text" value=""/>
                                        </td>
                                </tr>
                                <tr class="hiddentr">
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td><label hdSign="tbsr" class="name_year_position1" id="pass_year_tip" style="visibility:visible;">例:1980-03-15</label></td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td class="item_title" id="insured_email_label">电子邮箱：</td>
                                    <td colspan="3" id="insured_email_input" style="position:relative;">
                                        <input hdSign="email" id="email" type="text" value="" style="width:474px;"/>
										<span style="color:red;" id="emailMustBeFilledMark">&nbsp;*&nbsp;</span>
                                    </td>
                                    #if(!$!{insuranceCompany})
                                    <td colspan="3" style="display:none;padding-left:75px;" class="update_user_info">
                                        <label title="若证件填写为护照，请及时到个人信息页面完善护照有效期等信息！"><input id="isUpdateUserInfo" type="checkbox" value=""/>同时更新我的<a href="/user/userinfo" target="blank">个人信息</a></label>
                                    </td>
                                    #end
                                </tr>
                                <tr class="hiddentr">
                                    <td>&nbsp;</td>
                                    <td>
                                        <label hdSign="email" class="name_year_position1" id="pass_email_tip" style="visibility:visible;">
                                            自动接收电子保单
                                        </label>
                                    </td>
                                </tr>
                                <tr id="tbadrs_div" style="display:none;">
                                    <td class="item_title">投保人住址：</td>
                                    <td colspan="3"><input hdSign="tbadrs" class="" id="tbadrs" type="text" value="" style="width:470px;"/><span style="color:red;">&nbsp;*&nbsp;</span></td>
                                </tr>
                                <tr class="hiddentr" style="display:none;">
                                    <td>&nbsp;</td>
                                    <td><label hdSign="tbadrs" class="name_year_position1" id="pass_adrs" style="visibility:visible;">例：XX省 XX市 XX县 XX街道或门牌号</label></td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </table>
                        </div>


                        <div class="div3">
                            #if(!$!{insuranceCompany})
                            <div class="smf"><span class="statementsTitle">投保须知</span> <a name="notice" onclick="yc('insure_Notes')" href="javascript:void(0)">点击查看</a></div>
                            <div class="insure_notice" id="insure_Notes" style="display:none;">
                                <table width="100%">
                                    <tr>
                                        <td>
                                            1、投保旅游保险办理欧洲申根签证，被保险人证件信息请与护照信息保持一致。<br/>
                                            2、被保险人是未成年人，投保人必须是其父母或监护人。<br/>
                                            3、未领取合法身份证件的婴幼儿，请填写形如“20140101”格式的出生日期代替身份证件号码。<br/>
                                            4、本产品身故保险金受益人默认为被保险人法定继承人，如需调整或变更，请点击被保险人信息后面的“修改”按钮，在“指定受益人”处添加。<br/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            #end
                            <div class="smf"><span class="statementsTitle">投保声明</span> <a name="notice" onclick="yc('sm_tbsm')" href="javascript:void(0)">点击查看</a></div>
                            <div class="insure_notice" id="sm_tbsm" style="display:none;">
                                #foreach($pro in $!{prolist})
                                    <input type="hidden" id="pro_iprdctCode" value="$!{pro.iprdctCode}"/>
                                    <div id="tbsm_$!{pro.iprdctCode}" style="display:none;height:100%;" class="bx_smlist">$!{pro.insureceDeclare}</div>
                                #end
                            </div>
                            <div>
                                <label><input id="declareChbox" type="checkbox" value="1"/>我已阅读并同意上述须知、声明、<img src="/assets/CssAndImg/baichuan/imgs/download.gif"/><a id="declare_href" href="#" target="_blank">保险合同条款</a>#if(!$!{insuranceCompany})及<img src="/assets/CssAndImg/baichuan/imgs/download.gif"/><a
                                        href="/upload/khgzs.pdf" target="_blank">客户告知书</a>#end，#if(!$!{insuranceCompany})<a href="/doc/besttrav-basicInfo.htm" target="_blank">信息披露专栏</a>，#end 并知晓所有保险责任均以保险合同所载为准，所有保险合同的变更均需经保险公司正式程序修改或批注。</label>
                            </div>
                        </div>
                    </form>
                    <div class="but">
                        <input type="hidden" name="submit_status" id="submit_status" value="0"/>
                        <input class="submit_btn" value="提交投保" name="insure_submit" id="insure_submit" onclick="suball()" type="button"/>
                        <span class="checkNotice" style="display:none;line-height:30px;float:right;">正在检查投保信息，请稍候...</span>
                        <!-- <input type="hidden" class="back_btn" value="返  回" onclick="window.history.go(-1)" name="" type="button"/> -->
                    </div>
                </div>
            </div>
        </div>
        <div class="bgFoot">
            <div class="footer">
                #parse( "$!{location}/cpsPath/$!{partner.template}/footer.vm" )
            </div>
        </div>
    </div>

    <!--遮罩-->
    <div class="opa" id="opa" style="display:none;z-index:99;">
        <iframe class="opa_iframe" style="width:100%;height:100%;margin:0px;padding:0px; margin: 0px;" frameborder=0 onload="this.contentWindow.document.body.style.backgroundColor='#000'"></iframe>
    </div>
    <!--粘贴添加开始-->
    <div id="beginpaste">
        <input type="hidden"  id="paste_type"  value="">
        <div class="section" style="display:none;z-index:1002;"><textarea id="preview" style="display:none;" class="xheditor {forcePtag:false,cleanPaste:0}" name="preview" rows="8" cols="120"></textarea></div>
        <div id="secod_splice" style="display:none;z-index:1002;">
            <div id="secod_splice_top"><span id="close_secod">&times;</span><span id="secod_splice_text">被保险人编辑</span><span style="color:#333;margin-left:10px;">单击某个单元格即可修改内容；单击序号可选中行,单击列标可选中列，选中后可进行合并或删除操作。</span></div>
            <div id="secod_tools_list" style="padding-top:3px;">
                <table>
                    <tr>
                        <td><span style="font-weight:bold;">字段分隔符:</span></td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="0" checked="checked"/>空格</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="1"/>Tab</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="3"/>逗号</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="5"/>冒号</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="4"/>分号</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="field_splice" value="2"/></td>
                        <td>其他</td>
                        <td><input id="file_notation" type="text" style="width:20px;"/></td>

                        <td style="padding-left:15px;"><span style="font-weight:bold;">日期分隔符:</span></td>
                        <td style="padding-left:5px;"><input type="checkbox" name="date_splice" value="0" checked="checked"/>中横线(-)</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="date_splice" value="1"/>正斜线(/)</td>
                        <td style="padding-left:5px;"><input type="checkbox" name="date_splice" value="2"/></td>
                        <td>其他</td>
                        <td><input type="text" style="width:20px;" id="date_notation"/></td>
                        <td style="padding-left:15px;"><span style="font-weight:bold;">操作:</span></td>
                        <td><input id="merge" type="button" class="insure_btn2" value="合并所选列" style="margin-left:5px;"/></td>
                        <td><input id="delete_table" class="insure_btn2" type="button" value="删 除" style="margin-left:5px;"/></td>
                    </tr>
                </table>
            </div>
            <div id="secod_splice_content" style="height:443px;">
                <table id="finish_tr_browse"></table>
            </div>
            <div id="secod_splice_bottom"><span><input type="button" id="secod_prev_step" class="insure_btn" value="返回修改"/></span><span><input type="button" id="three_next_step" class="insure_btn3" value="下一步"/></span></div>
        </div>
        <div id="three_splice" style="display:none;z-index:1002;overflow:auto;">
            <div id="three_splice_top"><span id="three_close_secod">&times;</span><span id="secod_splice_text" style="font-weight:bold;">选取字段名称</span><span id="statu_message" style="color:red;"></span></div>
            <div id="three_splice_content">
                <table id="finish_brows"></table>
            </div>
            <div id="three_splice_bottom"><span><input type="button" id="three_prev_step" class="insure_btn" value="上一步"/></span><span><input type="button" id="improt_data" class="insure_btn3" value="确定添加"/></span>
                <div class="gif_jiazai loadimg" id="jiazai" style="display:none;"></div>
                <span style="display:none;font-size:14px;font-weight:bold;" id='pro-mid'></span></div>
            <!--
            <div id="three_splice_bottom"><span><input type="button" id="three_prev_step" value="上一步"/></span><span><input type="button" id="improt_data"  value="导入数据"/></span><span><input type="button" value="设置与投保人关系" id="set_relation"/></span><span><input type="button" value="导出Word文档" id="ex_excel"/>(只适用IE内核浏览器,并且已安装Word程序)</span></div>
            -->
        </div>
        <!--进度条-->

        <div id="jdt_box">
            <div id="loading">
                <span class="pro-left"></span>
                <span class="pro-mid"></span>
                <span class="pro-right"></span>
            </div>
        </div>
    </div>
    <!-- 省市区选择框 -->
    <div class="div1" id="div1" hidden>
        <div id="div1_1" hidden>
            <ul id="china_result" class="second_search_line" hidden>
                <li id="china_result_li_clear"><h3>清除</h3></li>
                <li id="china_result_li"><h3>省/直辖市</h3></li>
                <li id="china_result_li_city"><h3>市</h3></li>
                <li id="china_result_li_town"><h3>区/县</h3></li>
            </ul>
            <ul id="countries_result" class="second_search_line" hidden>
                <li id="countries_result_li"><h3>国际</h3></li>
            </ul>
            <ul id="third_search_line"></ul>
        </div>
    </div>
	<!--常用联系人-->
	<div class="info_edit_box" id="policy_edit_box" style="width:960px;">
		<div class="info_edit_head"><span class="info_edit_title">常用联系人</span><span class="gif_close  info_edit_close" id="policy_close">×</span></div>
		<div class="info_edit_body" style="padding-bottom:5px;height:500px;">
			<table class="policy_edit_table">
				<thead>
						<th style="width:3%;"></th>
						<th style="width:13%;">昵称</th>
						<th style="width:13%;">真实姓名</th>
						<th style="width:5%;">性别</th>
						<th style="width:10%;">出生日期</th>
						<th style="width:19%;">证件类型及号码</th>
						<th style="width:15%;">手机号码</th>
						<th style="width:25%;">邮箱</th>
				</thead>
				<tbody class="policy_edit_tbody">
				</tbody>
			</table>
			<div class="policy_edit_div" style="color:red;text-align:center;height:50px;line-height:50px;">无常用联系人</div>
		</div>
		<div class="info_edit_foot">
			<input type="button" value="关 闭" style="width:100px; height:25px;margin:10px;" id="policy_cancel" />
			<input type="button" value="添加为被保险人" style="width:120px; height:25px;margin:10px;" id="addToInsureds" />
		</div>
	</div>
</body>
<script type="text/javascript" src="/js/b2c/common.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/b2c/beaurl_nall.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/b2c/insure_b2c.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/b2c/calendar_d.js?v=$!{JS_AND_CSS_VER}"></script>
</html>

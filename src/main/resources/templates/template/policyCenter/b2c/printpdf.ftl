<!DOCTYPE HTML>
<html style="font-size: 16px;">
<#include "../../common/header.ftl">
<#assign bPath = "/assets/CssAndImg/${(partner.template)!}">
<#assign cPath = "/assets/CssAndImg/common">
<#assign cpsRoot = "/cps/${(partner.partnerId)!}">
<head>
    <title>支付结果</title>
    <script type="text/javascript" src="/js/jquery.js?v=${JS_AND_CSS_VER}"></script>
    <style type="text/css">
        .policyhead{
            height: 2.75rem;
            border-bottom: 1px solid #E1E4EB;
            width: 100%;
            text-align: center;
        }
        .policyinfo{
            margin: auto;
            width: 90%;
            background-color: #F9F9F9
        }
        .policyifobd td{
            font-size: 0.75rem;
            color: #5D667A;
            padding: 0.375rem 0;
        }
        .paybt {
            position: fixed;
            bottom: 0;
            text-align: center;
            width: 100%;
            height: 3rem;
            background-color: #FF9800;
            color: #ffffff;
            font-size: 1rem;
        }
        .d_1{
            padding: 1.625rem 1.25rem 0.625rem;
            font-size: 1.69rem;
            color: #121C32;
        }
        .s_1{
            font-size:0.815rem;
            color: #868E9E;
        }
        .d_2{
            padding: 0 1.25rem 1.405rem;
        }
        .d_3{
            padding: 0 1.25rem;
        }
        .policyifobd tr td:first-child{
            width: 25%;
        }
        .pdfdown{
            border: 1px solid #1291dd;
            height: 50px;
            text-align: center;
            width: 115px;
            color: #1291dd;
            float: right;
        }
    </style>
</head>
<body class="insureStep4" style="background-color: #f1f9ff;margin: 0;">
<div style="width: 980px;margin: 0 auto; background-color: #fff;">
    <div class="d_1">
        <div class="ft_1">
            <span>支付成功<br></span>
            <span>恭喜您获得一份保障！</span>
        </div>
    </div>
    <div class="d_3">
        <div style="background-color: #F7F8FA;padding:1.25rem 1.25rem 1.03rem">
            <table class="policyinfo" style="width: 100%; border-collapse:collapse;">
                <tr>
                    <td class="ft_2" colspan="2" style="font-size: 0.94rem;color: #394259;padding-bottom: 0.625rem;">${(product.productname)!}&nbsp;${(plan.planname)!}</td>
                </tr>
                <tr>
                    <td class="ft_4" colspan="2"  style="border-bottom: 1px dashed #E1E4EB;padding: 0 0 0.94rem;font-size: 0.75rem;color: #5D667A">
                        <span>本产品由美亚保险公司承保</span>
                    </td>
                </tr>
                <tbody class="policyifobd">
                <tr>
                    <td style="padding-top: 0.94rem;">起始时间</td>
                    <td class="effectiveDate" style="padding-top: 0.94rem;"></td>
                </tr>
                    <#if (plan.minday)! == 1>
                    <tr>
                        <td>结束时间</td>
                        <td class="expiryDate"></td>
                    </tr>
                    </#if>
                <tr>
                    <td>投保人</td>
                    <td>${(pinsure.insuredname)!}</td>
                </tr>
                    <#assign pindex=1>
                    <#assign pEmails="">
                    <#list puserlist! as puser>
                    <tr>
                        <td style="padding-bottom: 0.94rem;border-bottom: 1px dashed  #E1E4EB">被保人${pindex!}</td>
                        <td style="padding-bottom: 0.94rem;border-bottom: 1px dashed  #E1E4EB">${puser.insuredname!}</td>
                    </tr>
                        <#assign pindex=pindex+1>
                        <#if (puser.emailAddr??)>
                            <#assign pEmails = pEmails + puser.emailAddr! + ",">
                        </#if>
                    </#list>
                <tr>
                    <td colspan="2" style="text-align: right;padding: 1.03rem 0.03rem 0 0;">
                        <span class="ft_3">支付金额：</span><span class="ft_1 amount" style="color: #FA9128;font-size: 0.94rem;font-family: DINPro-Medium;"></span>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div style="padding: 1.25rem 1.25rem;height: 72px;">
        <div class="pdfdown">
            <input type="hidden" id="policyid" value="${id!}" />
            <span style="height: 50px;line-height: 50px;font-size: 18px;">下载保单</span>
        </div>
    </div>
</div>
</body>
<script type="text/javascript">
    var amount = ${(policy.fee)!};
    var effectiveDate = "${(policy.effectiveDate)!}";
    var expiryDate = "${(policy.expiryDate)!}";
    var pusersEmails = "${pEmails!}";
    jQuery(function(){
        amount = amount.toFixed(2)
        $(".amount").html("￥"+amount)
        effectiveDate = effectiveDate.substring(0, 10);
        expiryDate = expiryDate.substring(0, 10);
        $(".effectiveDate").html(effectiveDate);
        $(".expiryDate").html(expiryDate);
        if (pusersEmails != ""){
            pusersEmails = pusersEmails.substring(0,pusersEmails.length -1);
            $("#policyUserEmails").html(pusersEmails);
        }
    })
    $(".pdfdown").on("click",function(){
        window.open("/policyCenter/b2c/doPrintPdf?id="+$("#policyid").val())
    })
</script>
</html>

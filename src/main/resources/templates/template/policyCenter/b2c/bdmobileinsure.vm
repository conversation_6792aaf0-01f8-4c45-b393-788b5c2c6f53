#parse("$!{location}/common/header.vm")
#set($cPath = "/assets/CssAndImg/common")##所有cps共用的图片样式路径
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html style="font-size: 16px;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <script type="text/javascript" src="/js/jquery-1.11.3.min.js?v=$!{JS_AND_CSS_VER}"></script>


    <link href="/css/font-awesome-4.7.0/css/font-awesome.min.css?v=$!{JS_AND_CSS_VER}" rel="stylesheet">
    <link href="/css/mobile/mobilecommen.css" rel="stylesheet">


    <title>投保选择</title>
    <style type="text/css">
        @media only screen and (device-width: 375px) and (device-height: 812px){
            .fix-iphonex-adaption {
                padding-bottom: calc(0.34rem + constant(safe-area-inset-bottom)) !important;
                padding-bottom: calc(0.34rem + env(safe-area-inset-bottom)) !important;
            }
        }
        body{margin: 0px;-webkit-tap-highlight-color:rgba(0,0,0,0);}
        .policyhead{
            height: 2.75rem;
            border-bottom: 1px solid #ddd;
            width: 100%;
            text-align: center;
        }
        .tabs{height:50px;}
        .tabs a{
            display: block;
            float: left;
            width: 33.33%;
            color: #333;
            text-align: center;
            background: #eee;
            line-height: 50px;
            font-size: 16px;
            text-decoration: none;
        }



        .swipe{ padding:70px 0 0 0;}
        ul{-webkit-padding-start: 0px;}
        .bkdiv{
            color: #666666;
        }
        .btdiv{
            padding: 0 1.25rem;
        }
        .datadiv input{
            /*margin: 0px 0px 0px 110px;*/
            -webkit-appearance: none;
            border: none;
            width: 100%;
            font-size: 0.875rem;
            color: #5D667A ;
            line-height: 2rem;
            outline: none;
            background-color: #fff;
            text-align: right;
			right:0;
        }
        .datadiv{
            float: left;
            font-size: 1rem;
        }
		body .datePickerAssist{
			width:80%;
			margin-right:0.345rem;
		}
		.datePickerAssist::-webkit-input-placeholder {
			color:#BEC2CC;
		}
        /*.wrapBox_link span{line-height: 0px}*/
        /*.userdiv div span{    line-height: 40px;}*/
        .expenseBtn{
            height: 3rem;
            width: 100%;
            background-color: #FFF;
            bottom: 0;
        }
        .expenseBtn div{ float: left;}
        .expenseBtn div span{ float: left;}
        .paysum{
            width: 60%;
            height: 100%;
            color: #666;
            font-size: 1.3rem;
            line-height: 3rem;
        }
        .paybut{
            width: 100%;
            background-color: #fa9128;
            height: 100%;
            color: white;
            font-size: 1rem;
            line-height: 3rem;
        }
        .paybut span{
            text-align: center;
            width: 100%;
        }
        .showHidden .fa {
            font-size: 1.5rem;
            color: #fa9128;
            -webkit-animation: showmore 1s infinite;
            font-stretch: ultra-expanded;
        }



        .covediv{font-size: 0.8rem;margin: 0.3rem 0 0 0;padding: 0 1.25rem;  line-height:.875rem;}
        #opa{background-color: #fff;opacity: 0;z-index: 100;position: fixed;}
        #bdmsg{
            display: none;
            position: fixed;
            left: 50%;
            top: 50%;
            -webkit-transform: translate3d(-50%, -50%, 0);
            transform: translate3d(-50%, -50%, 0);
            border-radius: 0.28rem;
            margin: auto;
            width: auto;
            min-height: 1.405rem;
            line-height: 1.405rem;
            font-size: 1rem;
            color: #ffffff;
            background-color: rgba(0, 0, 0, 0.7);
            text-align: center;
            z-index: 55;
            padding: 0.64rem 0.94rem;
        }
        }
        .bdmsg1{
            padding: 2rem 2rem;
            color: #666;
            font-size: 1.2rem;
            text-align: center;
        }
        .bdmsg2{
            width: 100%;
            bottom: 0;
            position: absolute;
            border-top: 1px solid #ddd;
            background-color: #FFFFFF;
            height: 3rem;
            text-align: center;
            font-size: 1.4rem;
            color: #FA6450;
        }
        .bdmsg2 span{
            line-height: 3rem;
        }

        .fir{display: none}
        .errcn{border-bottom: 1px solid #D65353;}
        .inputlabel {
            position: absolute;
            margin-top: 1.5rem;
            font-size: 0.94rem;
            color: #BEC2CC;
            width: 90%;
            height: 1.5rem;
            line-height: 1.5rem;
            font-family: PingFangSC-Regular,SourceHanSansCN-Regular;
            pointer-events: none;
        }
        .outlabel{
            position: absolute;
            margin-top: 0.935rem;
            height: 0.75rem;
            line-height: 0.75rem;
            font-size: 0.75rem;
            color: #5D667A;
            width: 90%;
            pointer-events: none;
            font-family: PingFangSC-Regular,SourceHanSansCN-Regular;
        }
        .errmsg {
            border-top: 1px solid #D65353;
            font-size: 0.75rem;
            color: #D65353;
            height: 1rem;
        }
        .errmsg div{
            line-height: 1.5rem;
        }


        .step1{display: block;}
        .step_s1{position: fixed;width: 100%;bottom: 0;}
        .step2{display: none}
        .pageheader{
            line-height: 2.75rem;
            font-size: 1.125rem;
            color: #121C32
        }

        .thisstep{
            color: #121C32;
            display: block;
            height: 2.75rem;
            line-height: 2.75rem;
            float: left;
            padding-left: 1.25rem;
            text-align: left;
            position: absolute;
            padding-top: 0.9rem;
        }
        .steps{
            float: right;
            font-size: 0.69rem;
            color: #868E9E;
        }
        .stepstitle{
            color: #121c32;
            font-size: 1.69rem;
            line-height: 1.69rem;
        }

        .tist{
            width:65%;
            color: #5D667A
        }
        .tist span{
            position: absolute;
            pointer-events: none;
            margin-top: -2.8rem;
            margin-left: 8rem;
            font-size: 0.95rem;
            color:#5D667A;
        }
        .pusay{
            margin-top: -1.3rem;
            padding: 0 1.25rem;
            font-size: 0.815rem;
            color: #394259;
        }

        .insureex{display:none}
        .insureholder{
            border: none;
            line-height: 1.19rem;
            height: 1.19rem;
            color: #121C32;
        }
        .policysub{
            padding: 1.87rem 1.25rem 0;
        }
        .policysub_1{
            line-height: 1.19rem;
            height: 1.19rem;
            font-size: 1.19rem;
            padding: 0 0 1.27rem;
            color: #121C32;
        }
        .policysub_2{
            background-color: #F7F8FA;
            padding: 0.725rem 1.25rem;
        }
        .policysub_4{
            font-size: 12px;
            border-bottom: 1px dashed #E1E4EB;
            padding: 0.4rem 0 1rem;
            color:#5D667A;
        }
        .policysub_2 td{
            font-size: 0.75rem;
        }
        .delinput{
            position: absolute;
            margin-top: 2rem;
            margin-left: -1.4rem;
        }
        .delinputhid{
            display:none
        }
        .delinput img{width: 1.065rem;margin-top:0.45rem;}

        .opa1{
            background-color: #000000;
            opacity: 0.5;
            z-index: 9999;
            position: fixed;
            top: 0;
            left: 0;
            bottom: 4.5rem;
            right: 0;
            display:none;
        }
        .paybox{
            position: fixed;
            z-index: 9999;
            bottom: 0rem;
            height: 24rem;
            width: 100%;
            background-color: #ffffff;
            display: none;
            font-size: 1rem;
        }

        .errmsg_1{
            padding-top: 0.5rem;color: #E63617;font-size: 0.6rem
        }
        .tdleft{
            padding: 0.4375rem 0;
        }
        .tdright{
            padding: 0.3rem 0;
            font-family:DINPro-Medium;
            text-align: right;
        }
        #less_1,#less_2,#less_3{
            display: none;
        }
        .opa2{
            position: fixed;
            z-index: 9998;
            height: 100%;
            top: 0rem;
            background-color: #000000;
            padding: 3.25rem 2.22rem;
            opacity: 0.7;
            width: 100%;
            display: none;
        }
        .policyknow{
            position: fixed;
            z-index: 9999;
            height: 33.125rem;
            width: 80%;
            top: 2rem;
            left: 10%;
            background-color: #ffffff;
            display: none;
        }
        .policyknow_1{
            line-height: 1.125rem;
            height: 1.125rem;
            font-size: 1.125rem;
            color: #293040;
            margin-top: 1.565rem;
            text-align: center;
        }
        .policyknow_2{
            height: 3.125rem;
            text-align: center;
            color: #FA6450;
            border-top:1px solid #E1E4EB;
            font-size:1rem
        }
    </style>
</head>
<body style="background-color: #ffffff">

<div class="policybody" style="margin-bottom: 4rem">
    <form name="manyinsur" method="post" enctype="multipart/form-data" action="nextStep.action" autocomplete="off">
        <input type="hidden" name="partnerId" id="partnerId" value="$!{partner.partnerId}">
        <input type="hidden" name="planCode" id="planCode" value="">
        <input type="hidden" name="ipcid" id="ipcid" value="">
        <input type="hidden" name="planId" id="planId" value=""/>
        <input type="hidden" name="policyPrice" id="policyPrice" value=""/>
        <input type='hidden' name='sts' id='sts' value="" />
        <input type="hidden" name="pinsure" id="pinsure" value="" />
        <input type="hidden" name="pusers" id="pusers" value="" />
        <input type="hidden" name="uid" id="uid" value="$!{uid}"/>
        <input type="hidden" id="activeDatHidden" name="activeDat"/>
        <input type="hidden" id="exprDatHidden" name="exprDat"/>
        <input type="hidden" id="insureLastTime" value="">
        <input type="hidden" name="reference" id="reference" value="$!{reference}">
        <input type="hidden" name="state" id="state" value="$!{state}">
        <input type="hidden" name="sendState" id="sendState" value="$!{sendState}">
        <input type="hidden" name="orderId" id="orderId" value="$!{orderId}" >
        <input type="hidden" name="actType" id="actType" value="$!{actType}">
        <input type="hidden" name="policyNum" id="policyNum" value="$!{policyNum}">
        <input type="hidden" name="policyId" id="policyId" value="$!{policyId}">
        <textarea style="display:none;" name="paramDTO" id="paramDTO">$!{paramDTO}</textarea>
        <textarea style="display:none;" id="jsonPolicy">$!{jsonPolicy}</textarea>
        <textarea style="display:none;" id="jsonPinsure" name="jsonPinsure">$!{jsonPinsure}</textarea>
        <textarea style="display:none;" id="jsonPuserlist">$!{jsonPuserlist}</textarea>
        <textarea id="getProduct" style="display:none;">#if($!{icid}!=""){"icid":"$!{icid}","ipcid":"$!{ipcid}","planId":"$!{planId}"}#end</textarea>
        <div class="step1">
            <div class="bkdiv ft_1">
                <div style="line-height: 1.69rem;padding: 1.905rem 1.25rem;">
                    <span class="stepstitle ft_1">请选择保障计划</span><span class="steps ft_3">第1/2步</span>
                </div>
                <div class="demo theme-gray ft_2">
                    <div class="tabs-gray-less"></div>
                </div>
                <div>
                    <div class="covediv">
                        <table style='border-collapse: collapse;border: 0px solid #fff;width: 100%;font-size: 0.875rem; border-bottom: 1px solid #E1E4EB; color: #5d667a;margin-top: 0.7125rem;'>
                            <tbody id="less_1" class="ft_4 0620114IJSC3WD" plancode="0620114IJSC3WD">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">全球</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医药补偿</td><td class="tdright ft_2">100万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">个人责任</td><td class="tdright ft_2">100万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">旅行变更</td><td class="tdright ft_2">2万元</td>
                            </tr>
                            </tbody>
                            <tbody id="less_2" class="ft_4 0610114OJSA1ASIAEXNP" plancode="0610114OJSA1ASIAEXNP">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">亚洲(除尼泊尔)</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医疗运送和送返</td><td class="tdright ft_2">40万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">意外身故及伤残</td><td class="tdright ft_2">20万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">银行卡盗刷</td><td class="tdright ft_2">5000元</td>
                            </tr>
                            </tbody>
                            <tbody id="less_3" class="ft_4 0620114IJSB1WDEXAAN" plancode="0620114IJSB1WDEXAAN">
                            <tr>
                                <td class="tdleft ft_4">保障区域</td><td class="tdright ft_2" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;">全球(除美洲非洲尼泊尔)</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">医药补偿</td><td class="tdright ft_2">50万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">个人责任</td><td class="tdright ft_2">35万元</td>
                            </tr>
                            <tr>
                                <td class="tdleft ft_4">旅行变更</td><td class="tdright ft_2">8000元</td>
                            </tr>
                            </tbody>
                            <tfoot>
                            <tr class="showHidden ft_3">
                                <td style="    padding: 0.8125rem 0 1.865rem 0; color: #FA9128;" colspan="2">
                                <span>
                                    <div style="float: left">查看保障详情&nbsp;</div>
                                    <div style="padding-top: 0.25rem;"><img style="width: 1.6%;" src="/img/cuspayImgs/icon_og.png"></div>
                                </span>
                                </td>
                            </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
            <div class="bkdiv btdiv">
                <div class="datadiv" style="width: 100%">
                    <div style="height: 3.5rem;line-height: 3.5rem;border-bottom: 1px solid #E1E4EB;" class="calendarTrigger">
                        <table style="width:100%;border-collapse: collapse;">
                            <tr>
                                <td class="ft_3" style="width:35%;">
                                    <span style="color:#121C32">起保时间</span>
                                </td>
                                <td class="ft_2 tist" style="position:relative;text-align:right;">
##                                  <input onblur="changeDate()" readonly id="activeDat" style="text-align: right;font-family: DINPro-Regular" /><span>&nbsp;00:00:00</span>
                                    <input type="date" onchange="datachange()" id="activeDat" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;position:absolute;opacity:0;" />
									<input class="datePickerAssist" type="text" id="activeDatShow" readonly noclear placeholder="请选择" />
                                </td>
                                <td>
                                    <img style="width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png">
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div style="height: 3.5rem;line-height: 3.5rem;border-bottom: 1px solid #E1E4EB;" class="exprDatTr calendarTrigger" id="exprDatTr">
                        <table style="width:100%;border-collapse: collapse;">
                            <tr>
                                <td class="ft_3" style="width:35%;">
                                    <span style="color:#121C32">结束时间</span>
                                </td>
                                <td class="ft_2 tist" style="position:relative;text-align:right;">
    ##                              <input type="text" id="exprDat" readonly style="text-align: right;font-family: DINPro-Regular;"/><span>&nbsp;23:59:59</span>
                                    <input type="date" id="exprDat" style="font-family: PingFangSC-Medium,SourceHanSansCN-Medium;position:absolute;opacity:0;"/>
									<input class="datePickerAssist" type="text" id="exprDatShow" readonly noclear placeholder="请选择" />
                                </td>
                                <td>
                                    <img style="width: 0.315rem;" src="/img/cuspayImgs/icon_bg.png">
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div style="clear: both"></div>
            </div>
        </div>
    </form>
</div>
<div class="step_s1 step1 fix-iphonex-adaption">
    <div class="expenseBtn">
        <div class="paybut" onclick="nextstep()">
            <span>下一步</span>
        </div>
    </div>
</div>

<div id="bdmsg" style="">
    <div class="bdmsg1 ft_3"><span class="bdmsgspan"></span></div>
</div>
<script type="text/javascript">
    //ios 的fix ,app内回退后，下一步不见了，暂时判断回退重新设置一下display
    if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent) && document.addEventListener) {
        window.addEventListener('pageshow', function (event) {
                    if (event.persisted || window.performance &&
                            window.performance.navigation.type == 2)
                    {
                        location.reload();
                    }
                },
                false);
    }
    var plans = [];
        #foreach($plan in $!{plans})
            var strtmon = "";
            #if("$!{plan.plancode}"=="0610114 OJS A1 ASIA EX NP")
                strtmon = "30";
            #elseif("$!{plan.plancode}"=="0620114 IJS B1 WD EX AAN")
                strtmon = "115";
            #elseif("$!{plan.plancode}"  == "0620114 IJS C3 WD")
                strtmon = "235";
            #end
            plans.push({"productid":$!{plan.productid},"planid":$!{plan.planid},"planname":"$!{plan.planname}","plancode":"$!{plan.plancode}".replace(/\s/g, ""),"strtmon":strtmon})
        #end

    sessionStorage.setItem("plans",JSON.stringify( plans ));

    var CheckPuserPhoneRepeat = false;//配置项，是否验证重复的手机号，1验重，0不验重
    var productCvrgShowList = {}, planCvrgDetailShowList = {};//页面版本的保险条款需要保障项目信息，在取的时候做缓存
    var planlist = {}, productlist = {};
    var maxAge=80000000,minAge=0,maxAgeShow="80岁",minAgeShow="0岁",minAdultAge=18000000,minAdultShow="18岁",maxAdultAge="80000000",maxAdultShow="80岁",agerang=[];
    var company="",pro="",pla="",cha="",jxandlbday="",toCountry="",countryList = {},firstinsuretime="",isYear,planMaxDays=0,maxPersonNum;
    var proInfo = [] , plaInfo = [] , planInfo=[],firstload = true;


    $(window).load(function () {
        planlist = $!{planlist};
        productlist = $!{prolist};
        sessionStorage.setItem("prolist",JSON.stringify( productlist));
        sessionStorage.setItem("planlist", JSON.stringify(planlist));


        for(var i=0;i<productlist.length;i++){
            proInfo[productlist[i]["id"]] = productlist[i];
        }
        var x = $(".phRelation");
        x.selectedIndex = -1;
        var insureLastTime = proInfo[productlist[0]["id"]]["insureLastTime"];
        firstinsuretime = proInfo[productlist[0]["id"]].firstinsuretime.split(" ")[0];
        maxPersonNum = proInfo[productlist[0]["id"]].maxPersonNum;
        $("#activeDat").prop("min",firstinsuretime);
        if($("#activeDat").val() == "" || new Date($("#activeDat").val().replace(/-/g,"/")) - new Date(firstinsuretime.replace(/-/g,"/")) < 0){
            $("#activeDat").val(sessionStorage.getItem("activeDat")==null||""?firstinsuretime:sessionStorage.getItem("activeDat")).trigger("triggerchange");
        }
        if($("#exprDat").val() == "" || new Date($("#exprDat").val().replace(/-/g,"/")) - new Date(firstinsuretime.replace(/-/g,"/")) < 0){
            $("#exprDat").val(sessionStorage.getItem("exprDat")==null||""?firstinsuretime:sessionStorage.getItem("exprDat")).trigger("triggerchange");
        }
        for (var i = 0; i < planlist.length; i++) {
            plaInfo[planlist[i].planId] = planlist[i];//把计划信息数组转化为以计划id为键值的json数据
        }
        var recentplanid=""
        recentplanid = isSessionKeyNull("planid") ? $!{sdPlanId}: sessionStorage.getItem("planid");
        $(".bottom").each(function(){
            if($(this).attr("planid")==recentplanid){
                $(this).closest(".tab").click();
            }
        });
        var pla = "";
        var productId = jQuery(".current").find(".bottom").attr("productid");
        var planid =  jQuery(".current").find(".bottom").attr("planid");
        var plancode =  jQuery(".current").find(".bottom").attr("plancode");
    })

    function isSessionKeyNull(key) {
        if( sessionStorage.getItem(key) == null || sessionStorage.getItem(key) == "" ){
            return true;
        }
        return false;
    }



    function changePlan(productid,planid,plancode){//改变计划，判定年单
        $("#planCode").val(plancode);
        sessionStorage.setItem("planid", planid);
        sessionStorage.setItem("productid", productid);
        var pl= sessionStorage.getItem("planid");
        $(".sm_a").each(function(){
            $(this).hide();
        })
        $(".xz_a").each(function(){
            $(this).hide();
        })
        $(".sm_"+plancode).show();
        $(".xz_"+plancode).show();
        $(".covediv").find("tbody").each(function(){
            $(this).hide();
        })
        $("."+plancode).show()

        $("#clauseDeclare").find(".clauses").each(function(){
            $(this).hide();
        })
        $(".clauses_"+plancode).show();

        var productId = productid;
        var recentPlan = planid;
        $(".policyproduct").html(proInfo[productId].name)
        $(".policyplan").html(plaInfo[recentPlan].planName)
        $("#planId").val(recentPlan);
        var maxDays = plaInfo[recentPlan].maxDays+1;
        var d=new Date();
        d.setDate(d.getDate()+maxDays);
        var m=d.getMonth()+1;
        m=m<10?"0"+m:m
//        $("#activeDat").prop("max",d.getFullYear()+'-'+m+'-'+d.getDate());
//        $("#exprDat").prop("min",proInfo[productlist[0]["id"]].firstinsuretime.split(" ")[0]);
//        $("#exprDat").prop("max",d.getFullYear()+'-'+m+'-'+d.getDate());

        //年龄限制
        maxAge=plaInfo[recentPlan].maxAge*1000000;
        minAge=plaInfo[recentPlan].minAge*1000000;
        if(minAge<1000 && minAge>0){
            minAgeShow = minAge+"天";
            catchMinAge = "dd";
        }else if(minAge>=1000 && minAge<1000000){
            minAgeShow = minAge/1000+"月";
            catchMinAge = "mm";
        }else{
            minAgeShow = minAge/1000000+"岁";
            catchMinAge = "yy";
        }
        if(maxAge<1000 && maxAge>0){
            maxAgeShow = maxAge+"天";
        }else if(maxAge>=1000 && maxAge<1000000){
            maxAgeShow = maxAge/1000+"月";
        }else{
            maxAgeShow = maxAge/1000000+"岁";
        }

        planMaxDays = plaInfo[recentPlan].maxDays || 100000000;

        isYear = plaInfo[recentPlan].isYear;
        isYear ? $("#exprDatTr").hide() : $("#exprDatTr").show();
        //提前投保时间是整数天的时分秒为固定值
        if (proInfo[productId].insureAheadTime != 0 && proInfo[productId].insureAheadTime % 10000 == 0) {
            $("#activeDat").val($("#activeDat").val().split(" ")[0] + " 00:00:00").trigger("triggerchange");
            $("#exprDat").val($("#exprDat").val().split(" ")[0] + " 23:59:59").trigger("triggerchange");
            $("#activeDat,#exprDat").siblings(".times").prop("disabled", true);
        } else {
            if(proInfo[productId].insureAheadTime == 0){
                var d = new Date()*1 + 10*60*1000;
                var d1 = d + 24*60*60*1000-1000;
                d = new Date(d);
                d1 = new Date(d1);
                if(new Date($("#activeDat").val().replace(/-/g,"/")) - new Date(proInfo[productId]["firstinsuretime"].replace(/-/g,"/")) < 10*60*1000){
                    $("#activeDat").val(d.getFullYear() +"-"+ zerofill(d.getMonth()+1) + "-" + zerofill(d.getDate()) + " " + zerofill(d.getHours()) + ":" + zerofill(d.getMinutes()) + ":" + zerofill(d.getSeconds())).trigger("triggerchange");
                }
                if(new Date($("#exprDat").val().replace(/-/g,"/")) - new Date($("#activeDat").val().replace(/-/g,"/")) > 24*60*60*1000){
                    $("#exprDat").val($("#exprDat").val().split(" ")[0]  + " " + zerofill(d1.getHours()) + ":" + zerofill(d1.getMinutes()) + ":" + zerofill(d1.getSeconds())).trigger("triggerchange");
                }else{
                    var d1 = new Date(new Date($("#activeDat").val().replace(/-/g,"/"))*1 + 24*60*60*1000-1000);
                    $("#exprDat").val(d1.getFullYear() +"-"+ zerofill(d1.getMonth()+1) + "-" + zerofill(d1.getDate()) + " " + zerofill(d1.getHours()) + ":" + zerofill(d1.getMinutes()) + ":" + zerofill(d1.getSeconds())).trigger("triggerchange");
                }
            }
            $("#activeDat,#exprDat").siblings(".times").prop("disabled", false);
        }

        changeDate(productid,planid,plancode);
    }

    function changeDate(productid,planid,plancode){//加载时间
        if(isYear){
            var t = 365;
            $("#sts").val(365);
            $("#totalday").html("共计1年");
        }else{
            var stDay=new Date((jQuery("#activeDat").val()+" 00:00:00").replace(/-/gi,"/"));
            var edDay=new Date((jQuery("#exprDat").val()+" 23:59:59").replace(/-/gi,"/"));
            t=parseInt((edDay - stDay + 1000),10)/86400000;
            $("#sts").val(t);
            $("#totalday").html("共计"+t+"天");
        }
        datachange()
    }
	$("#activeDat").on("change triggerchange",function(){
		try{
			$("#activeDatShow").val(new Date($("#activeDat").val()).format("yyyy-MM-dd"));
		}catch(e){
			$("#activeDatShow").val("");
		}
	})
	$("#exprDat").on("change triggerchange",function(){
		try{
			$("#exprDatShow").val(new Date($("#exprDat").val()).format("yyyy-MM-dd"));
		}catch(e){
			$("#exprDatShow").val("");
		}
	})
	
    function zerofill(s) {
        var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
        s = isNaN(s) ? 0 : s;
        return (s < 10 ? '0' : '') + s.toString();
    }


    $("#notinsure").on("click",function(){
        $("#notinsure").attr("checked","checked")
        $("#isinsure").attr("checked",false)
    })
    $("#isinsure").on("click",function(){
        $("#isinsure").attr("checked","checked")
        $("#notinsure").attr("checked",false)
    })
    function nextstep(){
        jQuery("#activeDatHidden").val(jQuery("#activeDat").val()+" 00:00:00");
        jQuery("#exprDatHidden").val(jQuery("#exprDat").val()+" 23:59:59");
        var stDay=new Date((jQuery("#activeDat").val()+" 00:00:00").replace(/-/gi,"/"));
        var edDay=new Date((jQuery("#exprDat").val()+" 23:59:59").replace(/-/gi,"/"));
        var t=parseInt((edDay - stDay + 1000),10)/86400000;
        $("#sts").val(t);

        var productId = jQuery(".current").find(".bottom").attr("productid");
        var planid =  jQuery(".current").find(".bottom").attr("planid");

        $(".step").find(".thisstep").addClass("upstep").removeClass("thisstep");
        var activeDat=$("#activeDat").val();
        var exprDat=$("#exprDat").val();

        if(activeDat == null || activeDat == ""){
            alertBdMsg("请选择起保时间");
            return false;
        }

        if(exprDat == null || exprDat == ""){
            alertBdMsg("请选择结束时间");
            return false;
        }

        var timestamp1 = Date.parse(new Date());
        var timestamp2 = Date.parse(activeDat);
        var timestamp3 = timestamp2-timestamp1;
        var timestamp4 = Math.floor(timestamp3 / (24 * 3600 * 1000))+1;
        if(timestamp4<1){
            alertBdMsg("必须提前一天投保");
            return false;
        }

        var d1 = new Date(activeDat.replace(/\-/g, "\/"));
        var d2 = new Date(exprDat.replace(/\-/g, "\/"));
        if(activeDat!=""&&exprDat!=""&&d1 >d2) {
            alertBdMsg("起保时间不能大于结束时间");
            return false;
        }

        var recentplanid=$(".current").find(".bottom").attr("planid");
        var maxDays = plaInfo[recentplanid].maxDays;
        var dateSpan, iDays;
        activeDat = Date.parse(activeDat);
        exprDat = Date.parse(exprDat);
        dateSpan = exprDat - activeDat;
        dateSpan = Math.abs(dateSpan);
        iDays = Math.floor(dateSpan / (24 * 3600 * 1000))+1;
        if(iDays>maxDays){
            alertBdMsg("保险期间超过最大保险期限"+maxDays+"天");
            return false;
        }

        $(".demo-input-holder").find(".inputlabel").each(function(){
            var o = $(this);
            if ($(o).siblings("input").val()!="") {
                $(o).removeClass("inputlabel").addClass("outlabel")
            }
        })
        sessionStorage.setItem("activeDat", jQuery("#activeDat").val());
        sessionStorage.setItem("exprDat", jQuery("#exprDat").val());
        sessionStorage.setItem("paramDTO", jQuery("#paramDTO").val());
        sessionStorage.setItem("partnerId", jQuery("#partnerId").val());
        if(isSessionKeyNull("pinsure")){
            sessionStorage.setItem("pinsure", jQuery("#jsonPinsure").val());
            sessionStorage.setItem("isincludeinsure", "1");
        }
        $("[name='manyinsur']").submit();
    }




    function alertBdMsg(msg){
        jQuery("html,body").css({"height":"auto","overflow":"hidden"});
        $("#opa").show();
        $("#bdmsg").show();
        $(".bdmsgspan").html(msg);
        setTimeout(function(){
            $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
            $("#opa").hide();
            $("#bdmsg").hide();
            $(".bdmsgspan").html("");
        },2000)
    }


//    $("#opa").on("click",function(){
//        $("html,body").css({"height":"auto","overflow":"auto","position":"static","width":"100%"});
//        $("#opa").hide();
//        $("#bdmsg").hide();
//        $(".bdmsgspan").html("");
//    });
    $(".showHidden").on("click",function(){
        sessionStorage.setItem("activeDat", jQuery("#activeDat").val());
        sessionStorage.setItem("exprDat", jQuery("#exprDat").val());
        window.open("/baidu/baiduDetail.action?partnerId="+$("#partnerId").val()+"&detailType=1"+"&planCode="+$("#planCode").val(),"_self")
    })

    function datachange(){
        var planid = $(".current").find(".bottom").attr("planid");

        var acmintime = $("#activeDat").val();
        $("#exprDat").attr("min",acmintime);

        var exprDat=$("#exprDat").val();
        var d1 = new Date(acmintime.replace(/\-/g, "\/"));
        var d2 = new Date(exprDat.replace(/\-/g, "\/"));
        if(acmintime!=""&&exprDat!=""&&d1 >d2) {
            $("#exprDat").val(acmintime).trigger("triggerchange");
        }
        var d = new Date(acmintime.replace(/-/g,"-"));
        d.setDate(d.getDate()+plaInfo[planid].maxDays-1);
        var m=d.getMonth()+1;
        if(m<10){
            m="0"+m;
        }
        $("#exprDat").attr("max",d.getFullYear()+'-'+m+'-'+(d.getDate()));
    }
	
	(function(){
		Date.prototype.format = function(fmt) { 
			var o = {
				"M+" : this.getMonth()+1,                 //月份 
				"d+" : this.getDate(),                    //日 
				"h+" : this.getHours(),                   //小时 
				"m+" : this.getMinutes(),                 //分 
				"s+" : this.getSeconds(),                 //秒 
				"q+" : Math.floor((this.getMonth()+3)/3), //季度 
				"S"  : this.getMilliseconds()             //毫秒 
			}; 
			if(/(y+)/.test(fmt)) {
				fmt=fmt.replace(RegExp.$1, (this.getFullYear()+"").substr(4 - RegExp.$1.length)); 
			}
			for(var k in o) {
				if(new RegExp("("+ k +")").test(fmt)){
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : (("00"+ o[k]).substr((""+ o[k]).length)));
				}
			}
			return fmt; 
		}
	})()
</script>
<script src="/js/b2c/baidu_example/vendor.js?v=$!{JS_AND_CSS_VER}"></script>
<script src="/js/b2c/baidu_example/bd_tab.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/touchslider.js?v=$!{JS_AND_CSS_VER}"></script>
<script type="text/javascript" src="/js/mobile/mobilecommon.js?v=$!{JS_AND_CSS_VER}"></script>
    #parse( "/template/policyCenter/b2c/googleAnalytics.vm" )
</body>
</html>

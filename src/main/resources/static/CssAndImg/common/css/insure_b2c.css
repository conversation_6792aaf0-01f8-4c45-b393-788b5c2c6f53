@charset "utf-8";
/* input错误信息的背景start */
.errbackground{background-color:red;}
/* input错误信息的背景end */
/*投保页面信息*/
a,input,img{outline:none;}
input,select,textarea{color:#666666;}
address, caption, cite, code, dfn, th, var {font-style: normal;font-weight: normal;}
button {padding:2px;margin:2px;height:22px;line-height:22px;font-size:12px;text-align:center;}
#img_closed {position:absolute;z-index:100;padding:10px 40px;text-align:center;background-color:#fff;border:1px solid #aaa}
#img_closed span{margin-right: 10px;float: left; height: 24px; line-height: 26px;display: block;}
.content a {color: #0080FF;outline-style: none;text-decoration: none;}
.content input[type='text']{height: 23px;*height: 20px;}
.tabpack td,th{padding: 2px 0;vertical-align:middle;}
.tabpack input[type='text']{padding: 0px;margin: 5px 0;*padding:0px 2px;*margin:0px;width:174px;}
/*.tabpack input[type='text']:focus{background-color:#BBDDFF;}*/
#listpa input{width:auto;}
/*.tabpack .item_title{text-align:justify;}*/
.tabpack .item_title{height:30px;}
.content select{padding: 0px 2px 2px;}
.tabpack select{height: 24px;width: 250px;_width:auto;}
#plancoderadio{min-width: 200px;_width:200px;}
#addpa{width:316px;height:24px;margin-bottom:0px;}
#listpa{float:left;width:380px;height:120px;overflow:hidden;overflow-y:auto;background-color:#fff;border-top:1px solid #ccc;padding-left:10px;}
#addpadiv{width:390px;position:absolute;background-color:#fff;border:1px solid #ccc;padding:0px;margin:0px;}
#showpa{width:100%;float:left;}
.content{color: #000066;font: 100 12px;padding-left: 147px;}
/*.info_title{background-color:#f5f5f5;margin-left:-20px;padding:3px 0px 3px 20px;margin-bottom: 5px;}*/
.assured{padding-right:20px;}
.tbrname{width:174px;}
.tbrmessage td{padding-left: 3px;padding-right: 3px;text-align: left;padding-right: 3px; border: 1px solid #ccc;border-left-width: 1px;border-right-width: 1px;border-top:1px solid #ccc;}
.tbrmessage:last-child td{border-bottom:1px solid #ccc;}
.tbrmessage:hover td{border-top:1px solid #FFBB00;border-bottom:1px solid #FFBB00;}
.tbrmessage .del_icon{visibility:hidden;_visibility:visible;}
.tbrmessage:hover .del_icon{visibility:visible;}
.tbrmessage .phiddtype,.add_rate_tbr .tbrtype{width:98px;}
.tbrmessage .phiddtype option,.add_rate_tbr .tbrtype option{width:57px;}
.tbrmessage input[type=text],.tbrmessage select{height:22px;line-height:22px;}
#assured .tbrmessage .serial{width:24px;}
#assured .tbrmessage .phname{width:100%;}
#assured .tbrmessage .phiddnum{width:170px;}
#assured .tbrmessage .phiddtype{width:48px;}
#assured .tbrmessage .phgend{width:40px;}
#assured .tbrmessage .birth{width:110px;}
#assured .tbrmessage .phtel{width:110px;}
#assured .tbrmessage .relation{width:61px;}
#assured .tbrmessage .phsyr {width: 90px;border: none;color: #0080ff;background-color: #ffffff;text-align: center;}
.tbrnumber{width:160px;}
.tbrbirth{width:90px;}
.hidden_birth,.hidden_phname,.hidden_phtel{position: absolute; width:100px;font-size: 12px; line-height: 22px; display: inline;color:#999;}
.insurant thead th ,.policy_holder thead th{background: none repeat scroll 0 0 #F4F8FD;border-bottom:1px solid #d2d2d2;text-align:left;}
.benehead tr th,.tbrhead tr th{text-align:center;}
.insurant th ,.policy_holder th{height: 25px;line-height: 25px;padding: 0px;text-align:center;font-size:13px;font-family:Microsoft Yahei;}
.insurant td,.policy_holder td{text-align:left;border:1px solid #ccc;border-left-width:1px;border-right-width:1px;}
.insurant .seTd{border-width:0px;overflow:hidden;}
.gendTd{width:60px;}
.idTypeTd,.insurant .phgend{width:80px;}
.relationTd{width:120px;}
.tbrInfoTitle th{text-align:center;}
.insurant input[type=button]{line-height:18px; }
.serial{width:30px;}
.policy_holder{border:1px solid #d2d2d2;margin-bottom:10px;border-spacing:0px;padding-bottom:10px;width: 954px;}
.insurant{margin-bottom:10px;border-spacing:0px;padding-bottom:10px;}
.insurant select,.policy_holder select{width:140px;}
.insurant input,.insurant select,.policy_holder input,.policy_holder select{border:none;margin: 1px 0px;}
.insurant input:hover,.insurant select:hover,.policy_holder input:hover,.policy_holder select:hover{border:none;}
.insurant input:focus,.insurant select:focus,.policy_holder input:focus,.policy_holder select:focus{border:none;}
.applicant{width:100px;}
.applicant1{width:80px;}
#delalone{margin:0px 40px;}
.add_rate_bene{height:21px;overflow:hidden;}
.add_rate_bene td{border:1px solid #ccc;}
.add_rate_bene .firstTd{border-left:none;}
.add_rate_bene .lastTd{border-right:none;}
.jhrate input,.jhrate select ,.jhtoubao input,.jhtoubao select{padding:0px;margin:0px}
.jhrate input[type="button"],.jhrate select ,.jhtoubao input[type="button"],.jhtoubao select{padding:3px 8px;}
.bene_message {margin: 5px;float:left;height: 22px;line-height: 22px;}
.bene_all_edit{float:right;}
#assured .bene_all_edit input{margin:5px;padding:0 8px;}
.jhrate{margin-top: 0px; margin-left: -400px;background: none repeat scroll 0 0 #fff;border: 1px solid #d2d2d2;display: none;position: absolute;z-index: 100;}
.tip_til {background-color: #e7e7e7;background-image: linear-gradient(to bottom, #f2f2f2 0px, #e0e0e0 100%);border-bottom: 1px solid #c2c2c2;overflow: hidden;padding: 3px 10px;}
.bx_rate, .bx_rate2{margin:5px;}
.smf{color: #666666;background: none repeat scroll 0 0 #FFF;height: 30px;line-height: 30px;font-size: 16px;font-family: Microsoft Yahei;}

.b_til span, .tip_til span {float: left;font-size: 14px;font-weight: 700;}
.b_til a, .tip_til a {float: right;text-decoration: none;}
.jhtoubao {background: none repeat scroll 0 0 #FFFFFF;border: 1px solid #f4f8fd;display: none;position: absolute;z-index: 100;margin-top: 0px;}
#assured  .benedel {width: 46px;text-align:center;}
.bene2 input[type=text],.bene2 select{border:none;width:99%;}
#manyUpdate_editor,.upexcel,.delall{padding: 2px 5px;*padding:0px;}

/*国家选择控件*/
#addCountryDiv{width:390px;position:absolute;background-color:#fff;border:1px solid #333;padding:0px;margin:0px;}
#showCountry{width:100%;float:left;}
#listCountry{float:left;width:380px;height:120px;overflow:hidden;overflow-y:auto;background-color:#fff;border-top:1px solid #ccc;padding-left:10px;}
.cyspan{display:inline-block;}
/*弹出层样式*/
.info_edit_box{width:600px;position:absolute;background-color:#fff;border:1px solid #ccc;left:220px;z-index:8;display:none;/* box-shadow:0 0 5px #ccc; */}
.info_edit_head{background-color: #E7E7E7;background-image: linear-gradient(to bottom, #F2F2F2 0px, #E0E0E0 100%); border-bottom: 1px solid #C2C2C2;height:35px;line-height:35px;padding:0px 15px;}
.info_edit_title{float:left;font-weight:800;font-size:14px;}
.info_edit_close{font-size:18px;float:right;cursor:pointer;font-weight:800;height:35px;}
.info_edit_body{}
.policy_edit_tbody td{text-align:center;}
.info_edit_label{height:35px;text-align:left;border-bottom:1px solid #ccc;position:relative;z-index:0;}
.info_edit_label span { position:relative;z-index:1;margin-left:1px;top:10px;height:24px;line-height:24px;display:inline-block;padding-left:15px;padding-right:15px;cursor:pointer;border:1px solid #ccc; background-color:#ECEEFF;}
.info_edit_label .info_edit_select {border-bottom:1px solid #fff;background-color:#FFF;}
.info_edit_box tr{height:1em;line-height:1em;}
.info_edit_box td{height:1em;line-height:1em;padding:5px 0px;vertical-align:middle;}
.beneRows td{padding:0;}
.info_edit_body table{width:100%}
.info_edit_body thead{border-bottom:1px solid #ccc;}
.info_edit_body input:text,.info_edit_body select{width:200px;}
.info_edit_body textarea{width:400px;height:100px;}
.info_edit_body a:hover{text-decoration: underline;}
.info_edit_foot{background-color:#F0F0EE;height:45px;line-height:45px;text-align:center;border-top: 1px solid #CCCCCC;}
.info_edit_foot input{margin:0 10px;}
.policy_edit_tbody tr input:hover{background:gary;}
.policy_edit_table th{height:30px;line-height:30px;background-color:#f5f5f5;}
.policy_edit_table tr:hover{background-color:#f5f5f5;}
.policy_edit_table tr:hover input{background-color:#f5f5f5;}
.policy_edit_table tr:hover select{background-color:#f5f5f5;}
.contactListChecked, .contactListChecked input, .contactListChecked select{background-color:#f5f5f5;}
/*弹出层样式结束*/


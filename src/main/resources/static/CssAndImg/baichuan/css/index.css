table{border-collapse:collapse;}
a,img{outline:none;}
body{padding:0;margin:0;list-style:none;text-decoration:none;}
div.main,div.con_main{
	background-color: #FFFFFF;
	padding: 10px;
	border: white 1px solid;
	overflow: hidden;
	min-height: 240px;
	height:100%;
}

div.main .left{
	float: left;
	width: 74%;
}
div.main .right{
	float: right;
	width: 24%;
}
/*主页底部左边点的bug*/
.offline{
	list-style-type: none;
}
/*保险产品展示块*/
.insurbx{
	float: left;
	width: 47%;
	margin: 0 7px 20px 7px;
	display: inline;/*IE6margin加倍BUG*/
}
.insurbx .pro_name{
	width: 100%;
	height: 40px;
	line-height: 40px;
	overflow:hidden;
 	text-overflow:ellipsis;
 	white-space:nowrap;
}
.insurbx .pro_name a{
	font-size: 17px;
	text-decoration: none;
}
.insurbx .pro_name a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.insurbx .pro_name a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */
.insurbx dl{
	/*border-right: 1px solid #C4C4C4;
	border-bottom: 1px solid #C4C4C4;*/
	box-shadow:1px 1px 4px rgba(0,0,0,.26);
	box-shadow:1px 1px 4px rgba(0,0,0,.26);
	-webkit-box-shadow:1px 1px 4px rgba(0,0,0,.26);
	moz-box-shadow:1px 1px 4px rgba(0,0,0,.26); 
}
.insurbx dt{
	height: 132px;
	position: relative;
}
.insurbx dt .pro_pic{
	width: 100%;
	height: 100%;
	position: absolute;
	z-index: 20;
	top:0px;
	left:0px;	
}
.insurbx dt .pro_pic a{
	width: 100%;
	height: 100%;
	position: relative;
	display: block;
}
.insurbx dt .pro_pic a img{
	width: 100%;
	height: 100%;
}
.insurbx dt .info{
	color: #FFF;
	position: absolute;
	z-index: 30;
	top:0px;
	left:0px;
	margin:0px;
	width: 100%;
	height: 117px;
	padding-top: 15px;
	display: none;
	background:#111; 
	filter:progid:DXImageTransform.Microsoft.Alpha(opacity=70); 
	filter: Alpha(opacity=70); 
	-moz-opacity:.7; 
	opacity:0.7;
}
.insurbx dt .info ul{
	margin-left: 20px;
	overflow: hidden;
}
.insurbx dt .info li{
	height: 22px;
	line-height: 22px;
	width: 100%;
	overflow:hidden;
 	text-overflow:ellipsis;
 	white-space:nowrap;
}
.insurbx dt .info a{
	color: #409F89;
}

.insurbx dd{
	height: 110px;
	padding: 0 10px ;
	/*background: white url("/imgs/bg-work_shadow.png") top left no-repeat;*/
}
.insurbx .intro{
	margin: 0;
	padding-top: 10px;
	height: 40px;
	line-height: 20px;
	overflow:hidden;
 	text-overflow:ellipsis;
 	color: gray;
}
.insurbx .intro a{
	color: #0080FF;
	text-decoration: none;
}
.insurbx .intro a:hover{
	color: #0080FF;
	text-decoration: underline;
}
.insurbx dd ul{
	overflow: hidden;
}
.insurbx dd li{
	float: left;
	display: inline;
	height: 20px;
	line-height: 20px;
	margin-top: 20px;
	width: 25%;
}
.insurbx dd li a{
	float: left;
	display: block;
	height: 20px;
	line-height: 20px;
	text-decoration: none;
	text-align: center;
}
.insurbx dd li.buy{
	width: 24%;
}
.insurbx dd li.buy a{
	padding: 1px 10px;
}
.insurbx dd a:link {color: #000066;}		/* δ���ʵ����� */
.insurbx dd a:visited {color: #000066;}	/* �ѷ��ʵ����� */
.insurbx dd a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.insurbx dd a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */
.insurbx dd li.buy a:link {color: white;background: white url("../imgs/btn_bg.png");}		/* δ���ʵ����� */
.insurbx dd li.buy a:visited {color: white;background: white url("../imgs/btn_bg.png");}	/* �ѷ��ʵ����� */
.insurbx dd li.buy a:hover {color: white;cursor: pointer;background: gray;}	/* ����ƶ��������� */
.insurbx dd li.buy a:active {color: white;cursor: wait;background: gray;}	/* ѡ�������� */

.btn{
	width: 70px;
	height: 20px;
	line-height: 20px;
	padding-bottom: 2px;
	color: white;
	cursor: pointer;
	background: none;
	background: white url("../imgs/btn_bg.png");
	border: #DEDDDD 1px solid;
}
.btn_over{
	width: 70px;
	height: 20px;
	line-height: 20px;
	padding-bottom: 2px;
	color: white;
	cursor: pointer;
	background: none;
	background-color: gray;
	border: #DEDDDD 1px solid;
}

/*右侧模块*/

.right .pro_bg{
	filter: progid:DXImageTransform.Microsoft.Shadow(color='#BBBBBB', Direction=135, Strength=5);
	background-color: #eee; 
	/*height: 243px;*/
	margin-bottom: 20px;
	box-shadow:1px 1px 4px rgba(0,0,0,.26);
	/*box-shadow:2px 2px 5px #969696;
	box-shadow:2px 2px 8px #969696\0;
	-webkit-box-shadow:2px 2px 8px #969696;
	moz-box-shadow:2px 2px 5px #969696; */
}
.right .pro{
	background-color: white;
}
.right .dt{
	color: #9C9C9C;
	font-size: 1.8em;
	line-height: 1.8em;
	padding: 15px 0 0 15px;
	font-family: SimSun,Arial,sans-serif;
}
.right .dd{
	padding: 0 10px 15px 10px;
}
.right .dd li{
	padding-left: 10px;
	line-height: 22px;
}
.right .dd li img{
	position: relative;
	top: 4px;
}
.right .dd li a{
	text-decoration: none;
}
.right .dd li a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.right .dd li a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */

.right .company{
	overflow: hidden;
	margin-bottom: 20px;
}
.company .leftdiv{
	float: left;
	width: 106px;
	height: 104px;
	margin-bottom: 5px;
	background: url("../imgs/white_bg.png") repeat top left;
	filter: progid:DXImageTransform.Microsoft.Shadow(color='#BBBBBB', Direction=145, Strength=4);
}
.company .rightdiv{
	float: right;
	width: 106px;
	height: 104px;
	margin-bottom: 5px;
	background: url("../imgs/white_bg.png") repeat top left;
	filter: progid:DXImageTransform.Microsoft.Shadow(color='#BBBBBB', Direction=145, Strength=4);
}
.company a{
	display: block;
	width: 105px;
	height: 103px;
	color: #000066;
	font: 500 17px/30px SimSun,Arial,sans-serif;
	text-decoration: none;
	text-align: center;
	border:1px solid #f5f5f5;
	/*box-shadow: 2px 2px 4px rgb(187,187,187);
	box-shadow: 2px 2px 7px rgb(187,187,187)\0;
	-webkit-box-shadow: 2px 2px 7px rgb(187,187,187);
	-moz-box-shadow: 2px 2px 4px rgb(187,187,187);*/
}
.company a span{
	display: block;
	padding: 35px 15px;
}
.company a span.sun{
	display: block;
	padding: 20px 15px;
}
/*合作伙伴列表*/
.right .partner{
	width: 100%;
	box-shadow: 2px 2px 5px rgb(187,187,187);
	-webkit-box-shadow: 2px 2px 5px rgb(187,187,187);
	-moz-box-shadow: 2px 2px 5px rgb(187,187,187);
	margin: 20px 0;
}
.partner_title{
	padding: 5px 0 5px 15px;
	background: url("../imgs/yellow_bg.png") repeat bottom left;
	font: 500 14px SimSun,Arial,sans-serif;
	border-bottom: solid 1px #CCCCFF;
}
.partner_list{
	width: 100%;
	padding: 10px 0 1px 0;
}
.partner_list .item{
	margin: 0 10px 10px 10px;
	height: 70px;
	overflow: hidden;
	background: url("../imgs/white_bg.png") repeat bottom left;
}
.partner_list .item .left{
	float: left;
	width: 32%;
	height: 100%;
}
.partner_list .item .left a{
	width: 100%;
	height: 100%;
}
.partner_list .item .left img{
	width: 100%;
	height: 95%;
	border: solid 1px #CCCCFF;
}
.partner_list .item .right{
	float: right;
	width: 57%;
	height: 100%;
	line-height: 20px;
	margin-right: 4%;
}
div.tlgg_bg{
	width: 100%;
	height: 80px;
	margin-top: 20px;
	background-color: #eee;
	filter: progid:DXImageTransform.Microsoft.Shadow(color='#BBBBBB', Direction=180, Strength=5);
	/*box-shadow: 1px 3px 5px rgb(187,187,187);
	box-shadow: 1px 3px 9px rgb(187,187,187)\0;
	-webkit-box-shadow: 1px 3px 9px rgb(187,187,187);
	-moz-box-shadow: 1px 3px 5px rgb(187,187,187);*/
}
div.tlgg{
	width: 100%;
	height: 100%;
}
div.tlgg img{
	width: 100%;
	height: 100%;
}

/*保险知识部分*/
div.xnews{
	width: 100%;
	margin-top: 20px;
	overflow: hidden;
}
div.xnews .rns{
	float: left;
	display: inline;
	width: 32%;
	/*box-shadow:1px 1px 5px #969696;
	box-shadow:1px 1px 8px #969696\0;
	-webkit-box-shadow:1px 1px 8px #969696;
	moz-box-shadow:1px 1px 5px #969696; */
	margin-right: 16px;
	margin-bottom: 5px;
}
div.xnews .r{
	margin: 0px;
}
.rns .dt{
	font-size: 1.7em;
	line-height: 2em;
	padding-left: 15px;
}
.rns .dd ul{
	margin: 5px 15px 15px 15px;
}
.rns .dd li{
	width: 100%;
	line-height: 22px;
	overflow:hidden;
 	text-overflow:ellipsis;
 	white-space:nowrap;
}
.rns .dd li a{
	text-decoration: none;
}
.rns .dd li a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.rns .dd li a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */

/*首页结束*/

/*温馨提示模块_start*/
.tbtl {
	color: #555;
	line-height: 22px;
	padding: 10px;
	margin-bottom: 10px;
	box-shadow:1px 1px 4px rgba(0,0,0,.26);
}
.mind-ico {
	background: url(../imgs/icons.jpg) no-repeat 50% 50%;
	margin-right: 5px;
	width: 22px;
	height: 22px;
	float: left;
}
.alert-ico {
	background: url(../imgs/alert.jpg) no-repeat 50% 50%;
	margin-right: 5px;
	width: 22px;
	height: 22px;
	float: left;
}
.file-ico {
	background: url(../imgs/file.jpg) no-repeat
}
/*温馨提示模块_end*/

/*保险产品分类主页面*/
/*筛选条件_start*/
.left .fl{
	margin: 10px 0;
	height: auto;
	width: 652px;
	line-height: 24px;
	overflow: hidden;
}
.select_box {
	float: left;
	height: auto;
	width: 652px;
	padding: 5px 10px 5px 10px;
	line-height: 24px;
}
p.title_tags {
    float: left;
    font-weight: bold;
    margin: 0;
    padding: 0 10px 0 0;
}
p.tags {
	padding: 0;
	margin: 0;
	height: 24px;
	width: 71px;
	position: relative;
	cursor: pointer;
	float: left;
}
p.tags a {
	float: left;
	padding: 0 0 0 20px;
	width: 41px;
	cursor: pointer;
	color: #555555;
	background: url(../imgs/tags.p.png) no-repeat left 0;
	text-decoration: none;
}
p.tags a:hover {
	background: url(../imgs/tags.phover.png) no-repeat left 0;
	color: #555555;
}
.left_Products {
	width: 100%;
	overflow: hidden;
}
/*筛选条件_end*/
/*保险排行榜保险知识_start*/
.right .delimiter {
	margin: 0;
	height: 1px;
	line-height: 1px;
	background: url(../imgs/delimiter.bg.b.png) repeat-x 0 0;
}
.right .wid{
	width:220px;
	float:right;
	overflow: hidden;
	padding-top: 20px;
}
.right .widget-title{
	height: 40px;
	line-height: 40px;
	font-size: 17px;
	color: gray;
}
.right .pm{
	line-height: 20px;
	text-decoration: none;
	font-size: 12px;
	width:220px;
	position: relative;

}
.right .pm li{
	width:220px;
	padding: 3px 0;
	overflow:hidden;
 	text-overflow:ellipsis;
 	white-space:nowrap;
}
.right .pm_cion li{
	padding: 3px 0;
}
.right .pm_cion li img{
	position: relative;
	top: 4px;
}
.right .pm a{
	text-decoration: none;
}
.right .pm a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.right .pm a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */
/*保险排行榜保险知识_end*/
/*理赔明细_start*/
.tbinfo {
    border-radius: 5px 5px 0 0;
    margin: 10px -1px 10px 0;
    text-align: left;
    border-collapse: separate;
    border-spacing: 0;
    padding: 10px;
}
.tbinfo th {
	text-align: center;
	font-size: 13px;
	color: #333333;
	font-weight: normal;
}
.tbinfo td {
	color: #6C6C6C;
}
.tbinfo .titl {
	border-radius: 5px 5px 0 0;
	height: 35px;
	line-height: 35px;
	font-weight: bold;
	color: black;
	font-size:15px;
}

.claiminfo:hover{
	cursor:pointer;
	background-color:#efefef;
}
.tab {
	margin-bottom: 10px;
	background-color: white;
}
.tab tr{
	height: 56px;
	line-height: 56px;
}
.tab th {
	height: 26px; 
}
.tab td {
	color: #6C6C6C;
	padding: 0;
	margin: 0;
	text-align: center;
}
.tbinfo .tab td img {
	vertical-align: 3px;
	cursor: pointer;
}
.tipinfo {
	position: absolute;
}
.tipimg {
	position: absolute;
	left: 553px;
}
.tipbk {
	border: #ff6600 solid 1px;
	background: #FFF;
	padding: 5px;
	width: 905px;
	position: absolute;
	top: 11px;
}
.tipinfog {
	width: 100px;
}
.tipinfog div {
	float: left;
	overflow: hidden;
	width: 60px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-left: 10px !important;
	margin-left: 5px;
}
.tipinfog img {
	float: left;
	margin-top: 6px;
}
#tipbk {
	float: left;
}
.tipgb {
	float: right;
}
.tipgb a {
	text-decoration: none;
}
/*理赔明细_end*/
/*分页_start*/
.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.clearfix {
	display: inline-block;
}
/* Hide from IE Mac */
.clearfix {
	display: block;
}
.ft {
	margin-top: 30px;
	clear: both;
}
.xbj {
	float: left;
}
.xbj input {
	color: white;
	font-weight: 600;
	background: url(../imgs/bj_btn.jpg) no-repeat;
	width: 52px;
	height: 20px;
	padding-bottom: 3px;
	font-size: 12px;
	border: none;
	cursor: pointer;
}
.fy {
	float: right;
	clear: both;
}
.fy a {
	background: url("../imgs/pagging.gif") repeat;
	display: block;
	float: left;
	height: 20px;
	border: #AFAFAF solid 1px;
	text-decoration: none;
	color: #666;
	line-height: 20px;
	padding: 0 5px 0 5px;
	margin-left: 3px;
	font-family: sans-serif, Arial, "????";
}
.fy a:hover {
	background: #F2C73C none;
	border: #F99C04 solid 1px;
	color: #EEE;
}

.con_main .fy a:hover {
	background: #F2C73C none;
	border: #F99C04 solid 1px;
	color: #EEE;
}
.fy .xz{
	background:#F99C04;
	border:#F2C73C solid 1px;
	color:#eee;
}
/*分页_end*/
/*计划对比_start*/
.main_left{
	float: left;
	width: 20%;
	display: inline;
}
.main_left dl{
	margin: 10px 0 10px 15px ;
}
.main_left dt{
	height: 35px;
	line-height: 35px;
	font-size: 16px;
	color: gray;
}
.main_left dd{
	line-height: 20px;
	text-decoration: none;
	font-size: 12px;
}
.main_left dd li, .con_main .left dd li{
	background: url("../imgs/ico.gif") no-repeat 5px 50%;
	padding: 3px 0 3px 25px;
}
.main_left dd li img{
	position: relative;
	top: 4px;
}
.main_left dd li a{
	text-decoration: none;
}
.main_left dd li a.bc{
	color: #FFB402;
	font-weight: 600;
}
.main_left dd a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.main_left dd a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */
.main_left .left .bno{
	border-bottom: none;
	padding-bottom: 0px;
}

.main_right{
	float: right;
	width: 79%;
	overflow: hidden;
	margin-top: 10px;
	display: inline;
}
.main_right select{
	height: 20px;
}
.main_right .bj {
	background: url("../imgs/xzbg.png") repeat-x;
	height: 30px;
	overflow: hidden;
	margin-top: 7px;
}
.main_right .bj table {
	border-collapse: collapse;
	height: 30px;
	text-align: left;
}
.main_right .bj input {
	color: white;
	font-weight: 600;
	background: url("../imgs/bj_btn.jpg") no-repeat;
	width: 52px;
	height: 20px;
	padding-bottom: 3px;
	font-size: 12px;
	border: none;
	cursor: pointer;
}
.main_right .list {
	border-bottom: #dfdfdf solid 1px;
	width: 100%;
	padding: 10px 0;
	border-collapse: separate;
    border-spacing: 0;
}
.main_right .list td {
	line-height: 20px;
	color: #666;
}
.main_right .list a{
	text-decoration: none;
}
.main_right .list .til {
	font-weight: bold;
	color: #990099;
	margin-top: 10px;
	white-space: nowrap;
}
.main_right .list .til a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.main_right .list .til a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */
.main_right .lb a {
	color: #cc6600;
	display: block;
	width: 60px;
	height: 20px;
	padding: 0 6px;
	margin-right: 10px;
	line-height: 20px;
	background: url(../imgs/ico2.gif) no-repeat left;
}

.main_right .lb .btn {
	background: url(../imgs/buybtn.png) no-repeat;
	width: 69px;
	height: 20px;
	display: block;
	line-height: 20px;
	text-align: right;
	color: #FFF;
	text-decoration: none;
	font-weight: bold;
	border: none;
	margin-top: 5px;
}
.main_right .lb .btn:hover{
	background: url(../imgs/buybtn_over.png) no-repeat;
}
/*计划对比_end*/
/*服务中心相关页面_start*/
.th_left dt, .con_main .left dt {
    color: #45688E;
    font: 14px/30px "SimSun",Tahoma,Arial,Helvetica,STHeiti;
    height: 30px;
}
.th_left dd li, .con_main .left dd li {
	line-height: 26px;
}

.con_main a:hover {color: #FFB402;cursor: pointer;}	/* ����ƶ��������� */
.con_main a:active {color: #FFB402;cursor: wait;}	/* ѡ�������� */

.faqct {
    line-height: 22px;
    margin-top: 10px;
}
.faqct dt {
    color: #0082D6;
    font-family: "????";
    font-size: 15px;
    font-weight: bold;
    text-align: center;
}
.cxbod {
    background-color: #FAFAFA;
    border: medium none;
    color: #666666;
    font: 12px/12px "SimSun",Tahoma,Arial,Helvetica,STHeiti;
    height: 21px;
    margin-bottom: 10px;
    margin-left: 10px;
    margin-top: 5px;
    padding-bottom: 2px;
    width: 50%;
}
.cxbod th {
    background-color: #FAFAFA;
    border-bottom: 1px solid #EFEFEF;
    color: #45688E;
    font: 13px/13px "SimSun",Tahoma,Arial,Helvetica,STHeiti;
    height: 30px;
    text-align: left;
    text-indent: 10px;
}
.xzzx {
    background: url("../imgs/xzbgh.gif") no-repeat scroll 0 0 transparent;
    color: #666666;
    height: 35px;
    line-height: 35px;
    margin: 10px;
    text-indent: 65px;
    width: 500px;
}
.xzzx a {
    color: #6C6C6C;
    text-decoration: none;
}
/*产品对比_start*/
.bjcont {
    margin-top: 10px;
    text-align: center;
}
.bd {
    border-left: 1px solid #6699CC;
}
#lev1 {
    margin: 0;
}

.bxt {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background: none repeat scroll 0 0 #EEEEEE;
    border-color: #6699CC #6699CC -moz-use-text-color -moz-use-text-color;
    border-image: none;
    border-style: solid solid none none;
    border-width: 1px 1px medium medium;
    float: left;
    width: 110px;
	
}
.bxt div {
    border-bottom: 1px solid #6699CC;
    height: auto !important;
    line-height: 25px;
    text-align: right;
}
.lie {
    border-right: 1px solid #6699CC;
    border-top: 1px solid #6699CC;
    float: left;
    overflow: hidden;
    width: 261px;
}
.lie .cpmc, .liew .cpmc {
    color: #990099;
    font-size: 14px;
    font-weight: bold;
}
.lie .cpg, .liew .cpg {
    color: red;
    font-size: 12px;
}
.lie div, .liew div {
    border-bottom: 1px solid #6699CC;
    height: 25px;
    line-height: 25px;
    text-align: center;
}
.rdc {
    color: #FF0000;
    font-weight: bold;
}
.liew {
    border-right: 1px solid #6699CC;
    border-top: 1px solid #6699CC;
    float: left;
    overflow: hidden;
    width: 261px;
}
.gl {
    background: none repeat scroll 0 0 #CCFFFF;
}
#lev2 {
    background: url("../imgs/bb.gif") repeat-y scroll right center transparent;
    float: left;
}
.bxt2 {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    background: none repeat scroll 0 0 #EEEEEE;
    border-color: -moz-use-text-color #6699CC -moz-use-text-color -moz-use-text-color;
    border-image: none;
    border-right: 1px solid #6699CC;
    border-style: none solid none none;
    border-width: medium 1px medium medium;
    float: left;
    width: 110px;
}
.bxt2 div {
    border-bottom: 1px solid #6699CC;
    
    line-height: 25px;
    text-align: right;
}
.lie2 {
    border-right: 1px solid #6699CC;
    border-top: medium none;
    float: left;
    overflow: hidden;
    width: 261px;
    word-break: break-all;
}
.lie2 div {
    border-bottom: 1px solid #6699CC;
    
    line-height: 25px;
    text-align: center;
    height:auto;
    
    
}
.lie2m {
    border-right: 1px solid #6699CC;
    border-top: medium none;
    float: left;
    overflow: hidden;
    width: 150px;
    word-break: break-all;
}
.lie2m div {
    border-bottom: 1px solid #6699CC;
    height: 25px;
    line-height: 25px;
    text-align: center;
}
#lev3 {
	width: 900px;
    float: left;
    margin-top: -1px;
}
.nnl {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #6699CC #6699CC #6699CC -moz-use-text-color;
    border-image: none;
    border-right: 1px solid #6699CC;
    border-style: solid solid solid none;
    border-width: 1px 1px 1px medium;
    float: left;
    height: 35px;
    padding-top: 15px;
    width: 110px;
}
.nnl1 {
    -moz-border-bottom-colors: none;
    -moz-border-left-colors: none;
    -moz-border-right-colors: none;
    -moz-border-top-colors: none;
    border-color: #6699CC #6699CC #6699CC -moz-use-text-color;
    border-image: none;
    border-right: 1px solid #6699CC;
    border-style: solid solid solid none;
    border-width: 1px 1px 1px medium;
    float: left;
    height: 30px;
    line-height: 30px;
    padding: 10px 0;
    text-align: center;
    width: 261px;
}
#lev3 .tb {
    margin-right: 6px;
}
#lev3 .xx {
    margin: 6px 0 0 6px;
}
.but {
	position: relative;
	overflow: hidden;
	margin: 10px 0;
}
input.tb {
    background: url("../imgs/nextbtn_bg1.png") no-repeat scroll left top transparent;
    border: medium none;
    color: white;
    cursor: pointer;
    float: right;
    font: 500 14px SimSun,Arial,sans-serif;
    height: 30px;
    padding: 0 0 2px 30px;
    width: 132px;
}
input.tb:hover{
	background: url("../imgs/nextbtn_bg.png") no-repeat scroll left top transparent;
}
input.xx {
    background: url("../imgs/backbtn_bg.png") no-repeat scroll left top transparent;
    border: medium none;
    color: #001D73;
    cursor: pointer;
    font: 500 13px SimSun,Arial,sans-serif;
    height: 21px;
    margin: 10px 0 0 20px;
    text-align: center;
    width: 105px;
}
input.xx:hover{
	background: url("../imgs/backbtn_bg1.png") no-repeat scroll left top transparent;
}
/*产品对比_end*/

/*按钮_start*/
.insure_btn, .addr_dele {
	background: #F1F1F1;
	border: 1px solid #bbb;
	color: #333;
	padding: 5px;
	width: 100px;
	*width: 100px;
	*height: 28px;
	text-align: center;
	text-shadow: 0 1px 0 #fff;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #f6f6f6;
	box-shadow: inset 0 0 1px 1px #f6f6f6;
	cursor: pointer;
}
.insure_btn:hover {
	background: #d9d9d9;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #eaeaea;
	box-shadow: inset 0 0 1px 1px #eaeaea;
	color: #222;
	
}
.insure_btn:active {
	background: #d0d0d0;
	-webkit-box-shadow: inset 0 0 1px 1px #e3e3e3;
	box-shadow: inset 0 0 1px 1px #e3e3e3;
	color: #000;
}
.addr_dele:hover {
	background: #d9d9d9;
	-webkit-box-shadow: inset 0 0 1px 1px #eaeaea;
	box-shadow: inset 0 0 1px 1px #eaeaea;
	color: #222;
}
.addr_dele:active {
	background: #d0d0d0;
	-webkit-box-shadow: inset 0 0 1px 1px #e3e3e3;
	box-shadow: inset 0 0 1px 1px #e3e3e3;
	color: #000;
}
/*按钮_end*/

/*保险条款_start*/
.dh {
    color: #444444;
}
.dh a:link {
    color: #0080FF;
}
.dh a {
	color: #0080FF;
    font-weight: bold;
}
.con_main p{
	margin: 10px 0;
}
.con_main .left{
	float: left;
    overflow: hidden;
    width: 20%;
}
.wztil {
    border-bottom: 1px dashed #CCCCCC;
    color: green;
    font-weight: bold;
    line-height: 18px;
    margin-top: 10px;
    padding-bottom: 5px;
    position: relative;
}
.xul {
    margin-bottom: 10px;
    margin-top: 5px;
    padding: 0;
}
.xul li {
    color: #666666;
    line-height: 25px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.xul a {
    color: #3F4950;
    text-decoration: none;
}
.xul .bob {
    color: #aaa;
}
.con_main .right {
    float: left;
    margin: 10px 10px 0 10px;
    padding: 10px 10px 0 10px;
    border:1px solid #CCCCCC;
    overflow: hidden;
    width: 75%;

}
.clausetil dt {
    border-bottom: 1px solid #DDDDDD;
    cursor: pointer;
    height: 22px;
    line-height: 22px;
    margin-top: 1px;
    text-indent: 10px;
}
.clausetil dt a {
    color: green;
    text-decoration: none;
}
.clausetil dt a:hover{
	text-decoration: underline;
}
.clause {
    color: #555555;
    font-family: Tahoma,Helvetica,Arial,"宋体",sans-serif;
    font-size: 12px;
    line-height: 20px;
}
.clause p {
    margin: 10px 0;
}
.clause li {
    list-style: decimal outside none;
    margin-left: 24px;
}
.wgt {
    color: #6C6C6C;
    font-weight: bold;
}
/*保险条款_end*/
/*保障利益_start*/
.wznr {
    clear: both;
}
.wznr a{
	color:#0080FF;
}
.wznr p {
    text-indent: 28px;
}
.wznr dt {
    color: #000000;
    font-size: 18px;
    font-weight: bold;
    margin: 10px 0;
    text-align: center;
}
.wznr dd {
    font-size: 12px;
    color:#666;
    line-height: 22px;
}
.baoxianTabBox {
    background-color: white;
    border: 1px solid #E7E7E7;
    border-collapse: separate;
/*    border-radius: 5px 5px 0 0;
    border-spacing: 0;
    box-shadow: 0 2px 10px -4px rgba(0, 0, 0, 0.5);*/
    color: #666666;
    cursor: default;
    font-size: 10pt;
    margin: 0 -1px 10px 0;
    width: 100%;
    word-break: break-all;
}
.bxtitleBg {
    background: none repeat scroll 0 0 #F9F9F9;
    border: 1px solid gray;
    font-weight: bolder;
    height: 22px;
    text-align: center;
}
.baoxianTabBox td {
    padding: 10px;
    text-align: center;
}
/*保障利益_end*/
/*保障计划介绍_start*/
.infotb dt{
	text-align: center;
	color: #313131;
    font-size: 21px;
    font-weight: 800;
    line-height: 30px;
    text-transform: uppercase;
    margin-bottom: 20px;
}
.infotb dd div {
    float: left;
    margin-right: 60px;
    line-height: 30px;
    font-size: 13px;
}
.infotb dd a {
    color: #0080FF;
    white-space: nowrap;
}
.infodel {
    margin-top: 20px;
}
.infodel dt {
    color: #6C6C6C;
    height: 30px;
    line-height: 30px;
    font-weight: bold;
}
.infodel dt div {
    float: left;
    color: #313131;
    font-size: 14px;
    font-weight: 800;
    text-transform: uppercase;
}
.infodel a {
    color: #0080FF;
}
.infodel dt a {
	font-size: 13px;
    float: right;
    font-weight: normal;
}
.infodel dd {
    color: #444444;
    line-height: 20px;
}
.infodel dd li {
    background: url("../imgs/tico.gif") no-repeat scroll 5px 5px transparent;
    padding-left: 20px;
}
tbody[id="setbg"][name="setbg"] tr:hover{
	background-color:#EECC55;
}
/*保障计划介绍_end*/
/*登录_start*/
.log {
    color: #666666;
    font-size: 13px;
    margin: 20px auto;
}
.log tr {
    height: 25px;
    line-height: 25px;
}
.lg {
    font-size: 15px;
    width: 200px;
}
.log a {
    font-size: 12px;
    padding-left: 10px;
}
/*登录_end*/
/*保险公司_start*/
.faqct dd p {
    margin: 5px;
}
.wyjc {
    color: #6C6C6C;
    font-weight: bold;
}
/*保险公司_end*/
/*保单管理_start*/
.menu{
  padding-top: 5px;
}
.menu ul{
  overflow: hidden;
  height: 26px;
  line-height: 26px;
  border-bottom: #999999 1px solid;
}
.menu li.menu_default{
  float: left;
  width: 95px;
  text-align: center;
  background: url("../imgs/tab_default.png") no-repeat center 1px;
  cursor: pointer;  
  position: relative;
  z-index: 10;
}
.menu li.menu_checked{
  float: left;
  width: 95px;
  text-align: center;
  background: url("../imgs/tab_checked.png") no-repeat center 1px;
  cursor: pointer;
  position: relative;
  z-index: 10;
}
.menu li a{
  text-decoration: none;
}
.menu li a:hover{
  text-decoration: underline;
}
.policytable a{
  text-decoration: none;
  font-weight: bold;
}
.policytable a:hover{
  color: #666666;
  text-decoration: underline;
}
.toggletable{
  cursor: pointer;
  position: relative; 
  top: 3px; 
}
.ope{
  width: 20%;
  float: left;
  padding-top: 10px;
}
.operate{
  margin: 10px 0;
}

#radio{

}
.potab table td{  
    border: none;
    overflow: hidden;
	line-height:12px;
}
.potab tbody{
	border: none;
}
.potab{
	    border: 1px solid #d9d9d9;
z-index: 0;
position: relative;
}
.potab .policytable .title{
	background:#ffffff;
	border-bottom: 1px dashed #d9d9d9;
}
.potab .policytable th{ 
	color: #818181; 
	text-align: center;
	padding: 10px 0px;
	line-height:12px;
}
.potab td{ 
	background:#ffffff;  
	border-bottom:solid 1px #e0e0e0; 
	padding: 5px 0px;
	text-align: center; 
}
.policytable .td1st{padding-left:16px;text-align:left;}
.td1st img{position:absolute;margin-left:-14px;margin-top:-1px;top:auto;}
.potab tr:hover td{ 
	background:#cfe4f9; 
}
/*保单管理_end*/
/*用户登录注册密码找回等_start*/
.wrapper{
    width: 80%;
    margin: 10px auto;
}
.legend{
    font-size: 16px;
    padding-left: 10px;
    margin-bottom: 10px;
}
.tologin{
    font-size: 11px;
    padding-left: 10px;
    margin-bottom: 15px;
}
.tologin a{
    color: gray;
    text-decoration: underline;
}
.tologin a:hover{
    color: #FFB811;
}
.error_box{
	color: #CC0000;
	font-size: 12px;
    padding-left: 10px;
    margin-bottom: 15px;
}
.user_form{
    background-color: #EBEBEB;
    border: 1px solid #B4B4B4;
    padding: 20px 0;
}
.user_form table{
    width: 96%;
    margin: 0 auto;
}
.user_form table tr{
    line-height: 30px;
}
.user_form .text{
    padding: 2px;
    margin-left: 10px;
    width: 200px;
}
.user_form .trlabel{
    width: 13%;
    text-align: right;
}
.sub_box{
    background-color: #EBEBEB;
    text-align: right;
    font-weight: 600;
    padding: 5px;
}
.sub_box a{
	cursor: pointer;
    padding-left: 15px;
    background: url("../imgs/arrow-right-small.gif") no-repeat scroll 0 -97px transparent;
}
.sub_box a:hover{
	cursor: pointer;
    color: #666666;
    background-position: 0 -297px;
}
.warn_box{
     font-size: 11px;
    padding-left: 10px;
    margin-top: 20px;
}
.error_msg{
	color: #CC0000;
	font-size: 12px; 
}
/*用户登录注册密码找回等_end*/




/* 弹出层的标准样式start */
.info_edit_box{position:absolute;background-color:#fff;border:1px solid #ccc;left:220px;z-index:8;display:none;}
.info_edit_head{background-color: #E7E7E7;background-image: linear-gradient(to bottom, #F2F2F2 0px, #E0E0E0 100%); border-bottom: 1px solid #C2C2C2;height:35px;line-height:35px;padding:0px 15px;}
.info_edit_title{float:left;font-weight:800;font-size:14px;}
.info_edit_close{font-size:18px;float:right;cursor:pointer;font-weight:800;height:35px;}
.info_edit_body{padding:15px;}
.info_edit_label{height:35px;text-align:left;border-bottom:1px solid #ccc;position:relative;z-index:0;}
.info_edit_label span { position:relative;z-index:1;margin-left:1px;top:10px;height:24px;line-height:24px;display:inline-block;padding-left:15px;padding-right:15px;cursor:pointer;border:1px solid #ccc; background-color:#ECEEFF;}
.info_edit_label .info_edit_select {border-bottom:1px solid #fff;background-color:#FFF;}
.info_edit_box tr{height:1em;line-height:1em;}
.info_edit_box td{height:1em;line-height:1em;padding:3px 0px;}
.info_edit_body table{width:100%}
.info_edit_body thead{border-bottom:1px solid #ccc;}
.info_edit_body input:text,.info_edit_body select{width:200px;}
.info_edit_body textarea{width:400px;height:100px;}
.info_edit_foot{background-color:#F0F0EE;height:40px;line-height:40px;text-align:center;border-top: 1px solid #CCCCCC;}
.info_edit_foot input{margin:0 10px;padding:2px 10px;}
/* 弹出层的标准样式end */
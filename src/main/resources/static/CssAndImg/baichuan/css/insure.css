@charset "UTF-8";
/* 日历与时间混搭的控件 */
.imeMode{ime-mode:disabled;}
.calendarTime{border:1px solid #A9A9A9;display:inline-block;width:160px;}
.calendarTime input,.calendarTime input:hover,.calendarTime input:focus{border:none;padding:0 !important;margin:0 !important;}
.calendarTime .times{width:58px !important;text-align:center !important;}
.calendarTime .calendarTrigger{display:inline-block;width:14px;height:14px;vertical-align:-2px;margin:0 5px 0 8px;background:url(data:image/png;base64,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) no-repeat 50% 0;}
.calendarTime .calendarTrigger:hover{cursor:pointer;}
.calendarTime input:focus{background-color:#fff !important;}
/* 重复与错误信息提示 */
.repeatMessage{
	background-color:#ffb408 !important;
	
}
.errorMessage{
	background-color:red !important;
	
}
ul{list-style:none;margin:0px;padding:0px;}
a{
    outline-style: none;
    text-decoration: none;
    color: #0080FF;
}
a:hover{
    color: #FFB402;
}
table{
	border-collapse:collapse;
	border-spacing:0;
}
.main{ 
	width: 920px;
	padding: 20px; 
	background: #FFFFFF;
	overflow: hidden;
	outline-color: -moz-use-text-colkor;
	min-height: 250px;
	position: relative;
}
.main td,th{
	padding: 2px 0;
}
.main input{
	padding: 2px;
	margin: 5px 0;
}
.main select{
	padding: 2px;
}
.insurant .moneyTxt{
	text-align:right;
}
/*左模块*/
.insure_left {
    display: inline;
    float: left;
    width: 78%;
}
/*进度条*/
.process {
	height: 20px;
	font-size: 13px;
	color: #000055;
	margin-bottom: 20px;
	z-index: 2;
}
.process li{
	float: left;
    height: 20px;
    line-height: 20px;
    padding: 0;
	width:24.99%;
	text-align:center;
    position: relative;
    white-space: nowrap;
	color:gray;
	border-bottom:4px solid #ccc;
}
.process .over{
	border-bottom-color:orange;
}
.process .on{
	border-bottom-color:#006;
	color:#006;
	font-weight:800;
	background-image:none;
}
/*.on{
	background: url("../imgs/insure/progress_on.gif") no-repeat scroll right center transparent;
	color: white;
}
.off{
	background: url("../imgs/insure/progress_off.gif") no-repeat scroll right center transparent;
}
.start{
	border-left: 1px solid #B4B4B4;
}
.z1{
	z-index: 10;
}
.z2{
	z-index: 9;
	margin-left: -9px;
}
.z3{
	z-index: 8;
	margin-left: -9px;
}
.z4{
	z-index: 7;
	margin-left: -9px;
}*/
/*标题块*/
.info_title {
	border-top: 1px #C4C4C4 solid;
	color: #666666;
	background: none repeat scroll 0 0 #FFF;
	height: 30px;
	line-height: 30px;
	padding-left: 10px;
	font-size: 16px;
	font-family: Microsoft Yahei;
}
/* 标题内提示信息 */
.maxPersonTips{
	margin-left:10px;
	font-size:12px;
	display:none;
}
.maxPersonNums{
	color:red;
}
/*内容块*/

.tabpack {
	border-top: 1px #C4C4C4 dashed;
	*padding: 10px;
	padding: 10px;
	_padding: 10px;
	color: #666666;
	background-color: white;
}

.tab1, .tab3 {
	width: 100%;
	height: auto;
	border-collapse: separate;
}
.tab1 td, .tab3 td,.tab4 td {
	height: 20px;
	line-height: 20px;
	vertical-align: middle;
}
.tab1 .radio {
	font-weight: normal;
	width: 130px;
	height: 25px;
	line-height: 25px;
	text-align: left;
	float: left;
	margin-left: 0px;
	margin-right: 10px;
}
.tab2 span {
	font-weight: bold;
	color: #6C6C6C;
	margin-left: 20px;
}
.tab2 span b {
	color: #F00;
}
.tab2 div {
	font-weight: bold;
	color: #000;
}
.tab2 div b {
	color: #F00;
}
.tab4 th {
	text-align: center;
}

/*日历组件*/
.tableborder {
    background: none repeat scroll 0 0 white;
    border: 1px solid #86B9D6;
}
.tableborder .header {
    background: none repeat scroll 0 0 #C2DEED;
    color: #154BA0;
    font: bold 12px Arial,Tahoma !important;
    height: 25px;
    padding-left: 10px;
}
.tableborder .header td {
    padding-left: 10px;
}
.tableborder .header a {
    color: #154BA0;
    text-decoration: none;
}
#year, #month {
    padding-right: 10px;
}
.tableborder .category {
    background-color: #FFFFD9;
    color: #92A05A;
    font: 12px Arial,Tahoma !important;
    height: 20px;
}
.tableborder .category td {
    border-bottom: 1px solid #DEDEB8;
}
.expire, .expire a:link, .expire a:visited {
    color: #999999;
    text-decoration: none;
}
.altbg2 a {
    font: 11px Arial,Tahoma;
}
.zmo, .zmo a {
    color: #009900;
    text-decoration: none;
}
.today, .today a:link, .today a:visited {
    color: #00BB00;
    text-decoration: none;
}
.default, .default a:link, .default a:visited {
    color: #333333;
    text-decoration: none;
}
.checked, .checked a:link, .checked a:visited {
    color: #FF0000;
    text-decoration: none;
}
#calendar_year {
    background: none repeat scroll 0 0 #FFFFFF;
    display: none;
    line-height: 130%;
    position: absolute;
    z-index: 10;
}
#calendar_year .col {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #86B9D6;
    float: left;
    margin-left: 1px;
    padding: 4px;
}
#calendar_year .col a {
    color: #666666;
    text-decoration: none;
}
#calendar_month {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #86B9D6;
    display: none;
    line-height: 130%;
    padding: 4px;
    position: absolute;
    z-index: 11;
}
#calendar_month a {
    color: #666666;
    text-decoration: none;
}
/*保障范围及价格表*/
.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.clearfix {
	display: block;
}
.clearfix b{
	font-weight: 500;
}
.clearfix table{
	border-collapse: separate;
	border-spacing: 1px;
}
.clearfix th{
	text-align: center;
}
.cb_box2 {
    float: left;
}
.cb_box3 {
    float: left;
}
.TabA {
	font-size: 12px;
	font-weight: normal;
	background: #999;
	color: #666;
	margin-right: 20px;
}
.TabA th {
	font-weight: bolder;
	background: #e7e7e7;
	height: 24px
}
.TabA td {
	padding: 2px;
	background: #fff;
}

.check_detail a {
	margin-right: 10px;
}
.check_detail a:hover {
	text-decoration: underline;
	color: #0080FF;
}
.item_title {
	text-align: right;
	width: 90px;
	height: 25px;
	line-height: 25px;
}
/*快递模块*/
#express-info em {
	font-style: normal;
	padding-right: 10px;
}
#express_info_ok ul li {
	display: inline;
}

#credits_ok ul li {
	display: inline;
}

#express_info_ok  content_field {
	font-size: 14px;
}

/*投保须知和投保声明*/
.tbtl, .com_inro {
	color: #555;
	line-height: 22px;
	padding: 10px;
	margin-bottom: 10px;
	box-shadow:1px 1px 4px rgba(0,0,0,.26);
}
.mind-ico {
	background: url("../imgs/insure/icons.jpg") no-repeat 50% 50%;
	margin-right: 5px;
	width: 22px;
	height: 22px;
	float: left;
}
.alert-ico {
	background: url("../imgs/insure/alert.jpg") no-repeat 50% 50%;
	margin-right: 5px;
	width: 22px;
	height: 22px;
	float: left;
}
.file-ico {
	background: url("../imgs/insure/file.jpg") no-repeat
}
/*保障范围及价格表*/
#detail_td{
	background: #EBEBEB;
	padding: 10px;
	border: solid 1px #e5c5b6;
	-moz-border-radius: 5px;
	-khtml-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}
.insure_notice {
	margin-bottom: 10px;
	padding: 10px;
	padding-left: 30px;
	color: #555;
	-moz-border-radius: 5px;
	-khtml-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	line-height: 22px;
}
.insure_notice table th{
	font-size: 12px;
	color: #333;
	font-weight: bold;
	background-color: #f5f5f5;
	height: 25px;
	padding:2px 5px;
	text-align: center;
}
.insure_notice table td{
	color: #666666;
	font-size: 12px;
	height: 18px;
	background-color: rgb(255, 255, 255);
	padding:2px 5px;
}
.exprenotice{
	background: #EBEBEB;
	padding: 10px;
	border: solid 1px #e5c5b6;
	color: #555;
	-moz-border-radius: 5px;
	-khtml-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	line-height: 22px;
}

.bx_smlist {
	border: #e5c5b6 1px solid;
	background: #EBEBEB;
	padding: 10px;
	margin: 10px 0;
	color: #6C6C6C;
	-moz-border-radius: 5px;
	-khtml-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
	line-height: 22px;
}
.sm_list {
	overflow: hidden;
	zoom: 1;
}
.cb_box2 {
	float: left;
}
.cb_box3 {
	float: left;
	margin-top: 10px;
}

.div3 {
	border-top: 1px solid #C4C4C4;
	padding: 10px;
	*padding: 10px;
	_padding: 10px;
	_padding-bottom: 0;
	_height: 140px;
	color: #666666;
	background-color: white;
}
.xzwd {
	width: 300px;
}

.fd {
	width: 100%;
	margin-top: 5px;
	background: #fefefe;
	border: #ccc solid 1px;
}
.fd td {
	padding: 5px;
	font-size: 13px;
}


.beibao{
	border: 1px #C4C4C4 solid;
	border-bottom:none;
	border-top:none;
	padding:10px;
	background-color: white;
}
.name_year_position{
	cursor: text;
	position: relative;
	left: 5px;
	bottom: 18px;
	*bottom: 20px;
    color: #CBCBCB;
    clear: both;
}
.name_year_position1{
	cursor: text;
	position: relative;
	left: 5px;
	bottom: 18px;
	*bottom: 20px;
    color: #CBCBCB;
    clear: both;
}
tr.hiddentr td{
	height: 0px;
	line-height: 0px;
	padding: 0;
	margin: 0;
}
.red {
	color: red;
}
.totalfee_tr{
	font-size: 14px;
	font-weight: 600;
	font-family: Microsoft Yahei;
}

/*右模块*/
.insure_right {
    display: inline;
    float: right;
    width: 20%;
}

/*弹出层*/
.pop_div td,th{
	padding: 2px 0;
}
.pop_div input{
	padding: 2px;
	margin: 5px 0;
}
.pop_div select{
	padding: 2px 1px;
}
.item_title_leo{
	text-align: right;
	font-weight: 600;
}
#container_pay {
	display:none; 
	position: fixed; 
	background-color: rgb(255, 255, 255); 
	z-index:150; 
	width: 430px;
	border: 2px solid #03F;
	height: 200px;
}
#showinsurer{
	
	display: none;
	position:absolute;
	z-index: 150;
	color: #3D3D3D;
	background: #FFF url("/imgs/insure/pop_bg.jpg") repeat;
	width: 840px;
	box-shadow: 1px 1px 20px #FFF;
	-webkit-box-shadow: 1px 1px 35px #FFF;
}
#showinsurer_title{
	color: rgb(255, 255, 255); 
	font-weight: 600; 
	text-align: center;
	font-size: 17px; 
	cursor: move;
	background-color: #FFB403; 
	height: 40px;
	text-shadow: 3px 2px 3px #28A1DE;
	box-shadow:0px 4px 4px #F78B1E;
	-webkit-box-shadow:0px 4px 8px #F78B1E; 
	-moz-box-shadow: 0px 4px 4px #F78B1E; 
}
#showbox{
	display:none;
	position:absolute;
	z-index:150;  
	color: #3D3D3D;
	background: #FFF url("/imgs/insure/pop_bg.jpg") repeat;
	width: 840px;
	line-height: 36px;
	box-shadow: 1px 1px 20px #FFF;
	-webkit-box-shadow: 1px 1px 35px #FFF;
}
#showbox_title{
	color: rgb(255, 255, 255); 
	font-weight: 600; 
	text-align: center;
	font-size: 17px; 
	cursor: move;
	background-color: #FFB403; 
	height: 40px;
	text-shadow: 3px 2px 3px #28A1DE;
	box-shadow:0px 4px 4px #F78B1E;
	-webkit-box-shadow:0px 4px 8px #F78B1E; 
	-moz-box-shadow:0px 4px 4px #F78B1E; 
}
#addr_container {
	overflow:auto;
	display:none;
	position:absolute;
	z-index:148; 
	color: #3D3D3D; 
	background: #FFF url("/imgs/insure/pop_bg.jpg") repeat;
	width: 840px;
	line-height: 36px;
	box-shadow: 1px 1px 20px #FFF;
	-webkit-box-shadow: 1px 1px 35px #FFF;
}
#addr_title {
	margin-bottom: 5px;
	color: rgb(255, 255, 255); 
	font-weight: 600; 
	text-align: center;
	font-size: 17px; 
	cursor: move;
	background-color: #FFB403; 
	height: 40px;
	text-shadow: 3px 2px 3px #28A1DE;
	box-shadow:0px 4px 4px #F78B1E;
	-webkit-box-shadow:0px 4px 8px #F78B1E; 
	-moz-box-shadow: 0px 4px 4px #F78B1E; 
}
#addr_title span {
	float: right;
	margin-right: 10px;
	height: 20px;
	cursor: pointer;
}
#addr_prototype_list {
	border-bottom:1px solid #ccc;
}
/* 修正IE6振动bug */
* html,* html body{background-image:url(about:blank);background-attachment:fixed}
* html #addr_container{position:absolute;left:expression(eval((document.body.offsetWidth-840)/2));top:expression(eval(document.documentElement.scrollTop+170))}
* html #showbox{position:absolute;left:expression(eval((document.body.offsetWidth-840)/2));top:expression(eval(document.documentElement.scrollTop+150))}
* html #showinsurer{position:absolute;left:expression(eval((document.body.offsetWidth-840)/2));top:expression(eval(document.documentElement.scrollTop+150))}
* html #container_pay{position:absolute;left:expression(eval((document.body.offsetWidth-430)/2));top:expression(eval(document.documentElement.scrollTop+190))}

.opa{
	display: none;
	position:absolute; 
	left:0; 
	top:0; 
	z-index:147; 
	width: 100%; 
	background:#000; 
	filter:progid:DXImageTransform.Microsoft.Alpha(opacity=70); 
	filter: Alpha(opacity=70); 
	-moz-opacity:.7; 
	opacity:0.7;
}
.opa_iframe{
	display: none;
	position:absolute; 
	left:0; 
	top:0; 
	z-index:146; 
	width: 100%; 
	background:#000; 
	filter:progid:DXImageTransform.Microsoft.Alpha(opacity=70); 
	filter: Alpha(opacity=70); 
	-moz-opacity:.7; 
	opacity:0.7;
}
.opa iframe{
	display:none;/*sorry for IE5*/
	_display: block;
	position:absolute;/*must have*/
	top:0;/*must have*/
	left:0;/*must have*/
	z-index:148;/*must have*/
	filter:mask();/*must have*/
	width:1000px;/*must have for any big value*/
	height:1000px/*must have for any big value*/;
}

/*单个添加受益人*/
#jhbeneIframe{

}
#jhbene{
	margin:0 auto;
	width:800px;
	border-top:1px #a2a2aa solid;
}
#jhbene table{
	width:800px;
	margin-top:10px;
	line-height:24px;
}
#jhbene table th{
	text-align:center;
	font-weight:bold;
}
#jhbene table #bene2{
	text-align:center;
}

/*单个修改受益人*/

#jhbene02{
	margin:0 auto;
	width:800px;
	border-top:1px #a2a2aa solid;
}
#jhbene02 table{
	width:800px;
	margin-top:10px;
	line-height:24px;
}
#jhbene02 table th{
	text-align:center;
	font-weight:bold;
}
#jhbene02 table #bene202{
	text-align:center;

}

.bn input {
	font-size: 12px;
	padding-top: 0px !important;
	padding-top: 2px;
}

input.add_btn1 {
	text-align: center;
	cursor: pointer;
}
input.insure_btn, .addr_dele {
	background: #F1F1F1;
	border: 1px solid #bbb;
	color: #333;
	padding: 5px;
	width: 100px;
	*width: 100px;
	*height: 28px;
	text-align: center;
	text-shadow: 0 1px 0 #fff;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #f6f6f6;
	box-shadow: inset 0 0 1px 1px #f6f6f6;
	cursor: pointer;
}
input.insure_btn:hover {
	background: #d9d9d9;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #eaeaea;
	box-shadow: inset 0 0 1px 1px #eaeaea;
	color: #222;
	
}
input.insure_btn:active {
	background: #d0d0d0;
	-webkit-box-shadow: inset 0 0 1px 1px #e3e3e3;
	box-shadow: inset 0 0 1px 1px #e3e3e3;
	color: #000;
}
input.insure_btn3, .addr_dele {
	background: #010165;
	border: 1px solid #010165;
	color: white;
	padding: 5px;
	width: 100px;
	*width: 100px;
	*height: 28px;
	text-align: center;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #f6f6f6;
	box-shadow: inset 0 0 1px 1px #f6f6f6;
	cursor: pointer;
}
input.insure_btn3:hover {
	background: #27408B;
	border-radius: 3px;
	-webkit-box-shadow: inset 0 0 1px 1px #eaeaea;
	box-shadow: inset 0 0 1px 1px #eaeaea;
	color: white;
	
}
input.insure_btn3:active {
	background: #d0d0d0;
	-webkit-box-shadow: inset 0 0 1px 1px #e3e3e3;
	box-shadow: inset 0 0 1px 1px #e3e3e3;
	color: #000;
}
.addr_dele:hover {
	background: #d9d9d9;
	-webkit-box-shadow: inset 0 0 1px 1px #eaeaea;
	box-shadow: inset 0 0 1px 1px #eaeaea;
	color: #222;
}
.addr_dele:active {
	background: #d0d0d0;
	-webkit-box-shadow: inset 0 0 1px 1px #e3e3e3;
	box-shadow: inset 0 0 1px 1px #e3e3e3;
	color: #000;
}
.add_focus {
	width: 70px;
	text-align: center;
}
.add_del {
	text-align: right;
}
.confirm_btn {
	width: 100px;
	margin-right: 10px;
}
input.confirm_btn {
	float: right;
	color: white;
	font: 500 14px SimSun,Arial,sans-serif;
	border: none;
	background: url("../imgs/insure/downbtn_bg.png") no-repeat scroll left top transparent;
	width: 132px;
	height: 30px;
	padding: 0 0 2px 30px;
	cursor: pointer;
}
input.confirm_btn:hover {
	background: url("../imgs/insure/downbtn_bg1.png") no-repeat scroll left top transparent;
}
input.confirm_btn:active {
	background: #58ACFA;
	-webkit-box-shadow: inset 0 0 1px 1px #2E9AFE;
	box-shadow: inset 0 0 1px 1px #2E9AFE;
	color: #000;
}
input.submit_btn{
	float: right;
	color: white;
	font: 500 14px SimSun,Arial,sans-serif;
	border: none;
	background: url("../imgs/insure/nextbtn_bg1.png") no-repeat scroll left top transparent;
	width: 132px;
	height: 30px;
	padding: 0 0 2px 30px;
	cursor: pointer;
}
input.submit_btn:hover{
	background: url("../imgs/insure/nextbtn_bg.png") no-repeat scroll left top transparent;
}
input.back_btn{
	float: left;
	color: #001D73;
	font: 500 13px SimSun,Arial,sans-serif;
	border: none;
	width: 105px;
	height: 21px;
	text-align: center;
	cursor: pointer;
	float: left;
}


/*支付页面*/
.but {
	position: relative;
	overflow: hidden;
	margin: 10px 0;
}
#butleft {
    text-align: left;
}
#pay_content {
    background: none repeat scroll 0 0 white;
    position: relative;
}
.table-list input{
    margin: 5px 0 5px 20px;
}
.table-list img{
	margin: 5px 0 5px 5px;
}
.table-list span{
	margin: 5px 0 5px 5px;
}
.obank input{
	margin-right: 5px;
}
.but table{
	border-collapse: separate;
    border-spacing: 5px;
}
.but td{
	padding: 0;
}
.table-normal img{
	width: 80px;
	height: 40px;
}

/*facebox CSS_start*/
#facebox {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  text-align: left;
}
#facebox .popup{
  position:relative;
  border:3px solid rgba(0,0,0,0);
  -webkit-border-radius:5px;
  -moz-border-radius:5px;
  border-radius:5px;
  -webkit-box-shadow:0 0 18px rgba(0,0,0,0.4);
  -moz-box-shadow:0 0 18px rgba(0,0,0,0.4);
  box-shadow:0 0 18px rgba(0,0,0,0.4);
}
#facebox .content {
  display:table;
  width: 370px;
  
  padding: 10px;
  background: #fff;
  -webkit-border-radius:4px;
  -moz-border-radius:4px;
  border-radius:4px;
}
#facebox .content > p:first-child{
  margin-top:0;
}
#facebox .content > p:last-child{
  margin-bottom:0;
}
#facebox .close{
  position:absolute;
  top:5px;
  right:5px;
  padding:2px;
  background:#fff;
}
#facebox .close img{
  opacity:0.3;
}
#facebox .close:hover img{
  opacity:1.0;
}
#facebox .loading {
  text-align: center;
}
#facebox .image {
  text-align: center;
}
#facebox img {
  border: 0;
  margin: 0;
}
#facebox_overlay {
  position: fixed;
  top: 0px;
  left: 0px;
  height:100%;
  width:100%;
}
.facebox_hide {
  z-index:-100;
}
.facebox_overlayBG {
  background-color: #000;
  z-index: 99;
}
#facebox .bolder {
  background: red;
  color: white;
  font-size: 20px;
  padding: 20px;
  width: 500px;
  height:600px;
}
/*facebox CSS_end*/

/*jdt_box CSS_begin*/
#jdt_box{
float:left;
width:555px;
height:71px;
/*background:url(../img/bak2.png) no-repeat;*/
z-index:1100;
position:absolute;
display:none;
}
#loading{
float:left;
width:458px;
height:34px;
margin-top:20px;
margin-left:50px;
/*background:url(../img/bak.png) no-repeat;*/
}
#loading .pro-left{
width:6px;
height:30px;
background:url(../img/pro-left.png) no-repeat;
float:left;
display:none;
}
#loading .pro-right{
width:6px;
height:30px;
background:url(../img/pro-right.png) no-repeat;
float:left;
display:none;
}
#loading .pro-mid{
width:6px;
height:30px;
background:url(../img/pro-mid.png) repeat-x;
color:#fff;
text-align:center;
font-family:Tahoma;
font-size:18px;
float:left;
}
/*jdt_box CSS_end*/

#div1 {
    background-color: #359AFF;
    width: 450px;
    height: 35px;
    margin: 0 auto;
    block: none;
    position: absolute;
    top: 28px;
    left: 80px;
    z-index: 10;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
}

#div1 ul {
    list-style: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
}

/*使得文字处于中间位置*/
#div1 h3 {
    width: 120px;
    line-height: 25px;
    text-align: center;
    font-style: normal;
    margin-right: 5px;
    cursor: pointer;
}

#div1 li {
    float: left;
}

#div1_1 {
    background-color: #fff;
    width: 450px;
    height: 35px;
    margin: 0 auto;
    float: left;
    border-bottom: 2px solid #B0B0B0;
}

.second_search_line {
    background-color: #fff;
    width: 450px;
    height: 35px;
    margin: 0 auto;
    float: left;
    list-style: none;
}

#china_result {
    border-bottom: 2px solid black;
}

#china_result li {
    margin-top: 10px;
}

#china_result_li_clear h3 {
    width: 45px;
    color: red;
}

#countries_result_li {
    margin-left: 105px;
}

#countries_result li {
    margin-top: 10px;
}

/*            最后一集地域搜索框设置        */
#third_search_line {
    background-color: #fff;
    width: 430px;
    height: 250px;
    margin: 0 auto;
    position: absolute;
    top: 35px;
    left: 0px;
    z-index: 10px;
    padding: 10px;
    margin-left: 0px;
    overflow: auto;
    display: none;

    border-top: 2px solid #6C6C6C;
    box-shadow: 0 5px 15px rgba(0, 0, 0, .5);
}

/*            最后一级目录每个子目录的大小        */
#third_search_line li {
    width: 120px;
    height: 25px;
    text-align: center;
    margin-top: 3px;
    line-height: 25px;
}

/*当鼠标经过最后一级搜索框的时候文字和背景以及鼠标发生变化*/
#third_search_line li:hover {
    color: #000000;
    text-decoration: underline;
    cursor: pointer;
}

.provinceCityDistrictShow {
    width: 174px;
    cursor: pointer;
}

/*设置选中背景*/
.selected {
    background-color: #359AFF;
    color: white;
}

#third_search_line .selected3 {
    color: #0080ff;
}
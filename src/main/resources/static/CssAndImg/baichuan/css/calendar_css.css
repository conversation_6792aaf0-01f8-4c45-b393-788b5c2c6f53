@charset "utf-8";

/*投保页面共有信息*/
a,input,img{outline:none;}
address, caption, cite, code, dfn, th, var {font-style: normal;font-weight: normal;}
button {padding:2px;margin:2px;height:22px;line-height:22px;font-size:12px;text-align:center;}
#img_closed {position:absolute;z-index:100;padding:10px 40px;text-align:center;background-color:#fff;border:1px solid #aaa}
#img_closed span{margin-right: 10px;float: left; height: 24px; line-height: 26px;display: block;}
.content a {color: #0080FF;outline-style: none;text-decoration: none;}
.content input[type='text']{height: 23px;*height: 20px;}
.tabpack td,th{padding: 2px 0;vertical-align:middle;}
.tabpack input[type='text']{padding: 0px;margin: 5px 0;*padding:0px 2px;*margin:0px;width:170px;}
#listpa input{width:auto;}
/*.tabpack .item_title{text-align:justify;}*/
.tabpack .item_title{height:30px;}
.content select{padding: 0px 2px 2px;}
.tabpack select{height: 24px;width: 250px;_width:auto;}
#plancoderadio{min-width: 200px;_width:200px;}
#addpa{width:316px;height:24px;margin-bottom:0px;}
#listpa{float:left;width:380px;height:120px;overflow:hidden;overflow-y:auto;background-color:#fff;border-top:1px solid #ccc;padding-left:10px;}
#addpadiv{width:390px;position:absolute;background-color:#fff;border:1px solid #ccc;padding:0px;margin:0px;}
#showpa{width:100%;float:left;}
.content{color: #000066;font: 100 12px;padding-left: 147px;}
#pri{color:red;}
/*.info_title{background-color:#f5f5f5;margin-left:-20px;padding:3px 0px 3px 20px;margin-bottom: 5px;}*/
.assured{padding-right:20px;}
.tbrname,.phname{width:174px;}
.tbrmessage,.tbrInfoTitle{border-bottom:1px solid #ccc;}
.tbrmessage .phiddtype,.add_rate_tbr .tbrtype{width:98px;}
.tbrmessage .phiddtype option,.add_rate_tbr .tbrtype option{width:57px;}
.tbrmessage input[type=text],.tbrmessage select{height:22px;line-height:22px;}
.tbrnumber,.phiddnum{width:160px;}
.tbrbirth,.birth{width:90px;}
.hidden_birth,.hidden_phname,.hidden_phtel{position: absolute; width:100px;font-size: 12px; line-height: 22px; display: inline;color:#999;}
.insurant thead th ,.policy_holder thead th{background: none repeat scroll 0 0 #F4F8FD;border-bottom:1px solid #d2d2d2;text-align:left;}
.benehead tr th,.tbrhead tr th{text-align:center;}
.insurant th ,.policy_holder th{background: none repeat scroll 0 0 #f4f8fd;height: 25px;line-height: 25px;padding: 0px;text-align:center;}
.insurant td,.policy_holder td,.tbrInfoTitle th{text-align:left;border:1px solid #ccc;border-left-width:1px;border-right-width:1px;}
.insurant .seTd{border-width:0px;overflow:hidden;}
.gendTd{width:60px;}
.idTypeTd,.insurant .phgend{width:80px;}
.relationTd{width:120px;}
.tbrInfoTitle th{border-top:1px solid #d2d2d2;text-align:center;}
.insurant .caozuo{border-right:1px solid #d2d2d2;}
.insurant input[type=button]{line-height:18px; }
.serial{width:30px;}
.insurant,.policy_holder{border:1px solid #d2d2d2;margin-bottom:10px;border-spacing:0px;padding-bottom:10px;}
.policy_holder{width: 954px;}
.insurant select,.policy_holder select{width:140px;}
.insurant input,.insurant select,.policy_holder input,.policy_holder select{border:none;margin: 1px 0px;}
.insurant input:hover,.insurant select:hover,.policy_holder input:hover,.policy_holder select:hover{border:none;}
.insurant input:focus,.insurant select:focus,.policy_holder input:focus,.policy_holder select:focus{border:none;}
.applicant{width:100px;}
.insurant .phsyr{width:80px;}
.applicant1{width:80px;}
#delalone{margin:0px 40px;}
.add_rate_bene{height:21px;overflow:hidden;}
.jhrate input,.jhrate select ,.jhtoubao input,.jhtoubao select{padding:0px;margin:0px}
.jhrate input[type="button"],.jhrate select ,.jhtoubao input[type="button"],.jhtoubao select{padding:3px 8px;}
.jhrate{margin-top: 0px; margin-left: -400px;background: none repeat scroll 0 0 #fff;border: 1px solid #d2d2d2;display: none;position: absolute;z-index: 100;}
.tip_til {background-color: #e7e7e7;background-image: linear-gradient(to bottom, #f2f2f2 0px, #e0e0e0 100%);border-bottom: 1px solid #c2c2c2;overflow: hidden;padding: 3px 10px;}
.bx_rate, .bx_rate2{margin:5px;}

.b_til span, .tip_til span {float: left;font-size: 14px;font-weight: 700;}
.b_til a, .tip_til a {float: right;text-decoration: none;}
.jhtoubao {background: none repeat scroll 0 0 #FFFFFF;border: 1px solid #f4f8fd;display: none;position: absolute;z-index: 100;margin-top: 0px;}
.benedel {width: 46px;}
.bene2 input[type=text],.bene2 select{border:none;width:99%;}
#manyUpdate_editor,.upexcel,.delall{padding: 2px 5px;*padding:0px;}

/*批量粘贴样式*/
#close_secod,#three_close_secod{font-size: 25px;color: #333333;font-weight: bold;}
#beginpaste option{width:inherit;}
#beginpaste table,#beginpaste tr,#beginpaste td{border-collapse:collapse;}
#beginpaste .insure_btn3,#beginpaste .insure_btn,#beginpaste .addr_dele {background: none repeat scroll 0 0 #F1F1F1; border: 1px solid #BBBBBB; border-radius: 3px; box-shadow: 0 0 1px 1px #F6F6F6 inset;color: #333333; cursor: pointer;padding: 5px;text-align: center;text-shadow: 0 1px 0 #FFFFFF;width: 100px;}
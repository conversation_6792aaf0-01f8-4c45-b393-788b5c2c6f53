var arigin = "query";//修改保单成功后靠此识别opner是否为查询页面，是就进行刷新
var isAjaxPolicy = false;
$(function () {
    //取出缓存内关于本页面的查询条件
    try{
        var conditions = JSON.parse(localStorage.getItem(storageConditionName));
        for(var i = 0; i < conditions.length; i++){
            var cdtn = $("#isparents").find("[name='" + conditions[i]["name"] + "']");
            if(!(cdtn.is("[nocover]") && StringUtils.isNotBlank(cdtn.val()))){
                cdtn.val(conditions[i]["value"]);
            }
        }
    }catch (e) {}
    policyTableVue.viewType = ($("#viewType").val() || policyTableVue.viewType);
    $(".operatingArea").find(".viewTypeButton[viewtype='" + policyTableVue.viewType + "']").addClass("selected");
    doQuery();
});

/*
   展开或者关闭保单的详情
   @param trigger:触发展开或关闭的元素
* */
function ajaxGetListDetail(trigger) {
    var policyContainer = trigger.closest(".policyRow");
    var index = policyContainer.attr("index");
    var policy = policyTableVue.policies[index];
    if (policyContainer.find(".fa-chevron-down").hasClass("close")) {
        if (isAjaxPolicy){return;}
        isAjaxPolicy = true;
        //关闭其他展开的信息
        //policyContainer.find(".destination").html(policy.destination);

        policyContainer.find(".fa-chevron-down").removeClass("close").addClass("open rotate");
        //隐去投保人信息
        policyContainer.find(".applicantEmail").animate({opacity: 0}, 1000);
        if(policy["insuredList"] && policy["insuredList"].length > 0){
            policyContainer.find(".toggle_info").slideDown(1000);
            isAjaxPolicy = false;
        }else{
            $.ajax({
                url: window.baseDir + "/policyCenter/public/ajaxPolicyDetails",
                type: "post",
                dataType: "json",
                data: {"policyList": JSON.stringify([policy])},
                error: function () {
                    alertMsg("网络超时，操作中断！");
                },
                // async:false,
                success: function (data) {
                    if (data.success != "1") {
                        alertMsg(data.message);
                        return;
                    }
                    // $("#sendResultMsg_" + policyID).val(data.lastSendResultMessage);
                    var policy = data["value"][0];
                    if(policy && policy["logList"]){
                        policy["logList"].reverse();
                    }
                    policyTableVue.policies.splice(index, 1, policy);
                    setTimeout(function () {
                        policyContainer.find(".toggle_info").slideDown(1000);
                    }, 50);
                },complete:function () {
                    setTimeout(function () {
                        isAjaxPolicy = false;
                    }, 1000);
                }
            });
        }
    } else {
        if (isAjaxPolicy) return;
        policyContainer.find(".toggle_info").slideUp(1000);
        policyContainer.find(".fa-chevron-down").removeClass("open rotate").addClass("close");
        //显示投保人信息
        policyContainer.find(".applicantEmail").animate({opacity: 1}, 1000);

        //显示全部目的地
        if(policy.destination && policy.destination.split(",").length > 1){
            //policyContainer.find(".destination").html(policy.destination.split(",")[0] + ",...");
        }
    }
}



function showContents(obj) {
    $("#contentDetailBox #content_textarea").html($(obj).html());
    $("#contentDetailBox").show().css("z-index",1000);
    $("#opa").show();
}

function doQuery(remain) {
    if(!remain){
        $("[name='pageNumber']").val("1");
    }
    $("#viewType").val(policyTableVue.viewType);
	var arr = $("form[name='formname']").serializeArray();
    localStorage.setItem(storageConditionName, JSON.stringify(arr));
    loadingNow("加载中...");
    if(!remain){
        policyTableVue.policies = [];
    }
    jQuery.ajax({
        "url": window.baseDir + "/policyCenter/public/doQuery",
        "type": "post",
        "dataType": "json",
        "data": $("form[name='formname']").serialize() + "&responseType=json",
        "success": function (data) {
            loadingComplete();
            if(data["success"] == 1){
                lastPage = data["page"]["lastPage"];
                for(var i = data["policies"].length - 1; i>= 0; i--){
                    if(!data["policies"][i]["leafNode"]){
                        data["policies"].splice(i, 1);
                    }

                }
                policyTableVue.policies = policyTableVue.policies.concat(data["policies"]);
                policyTableVue.count = data["page"]["rowCount"];
                $(".countingArea .count").html(data["page"]["rowCount"] + " results");
            }
        },"error": function () {
            loadingComplete();
        },"complete":function () {
            isAjax = false;
        }
    })
}

var isAjax = false;
var lastPage = 0;
function ajaxList(){
    if(isAjax) return;
    isAjax = true;
    var nextPage = (parseInt($("input[name='pageNumber']").val()) || 0) + 1;
    if(nextPage > lastPage){
        isAjax = false;
        return;
    }
    $("input[name='pageNumber']").val(nextPage);
    doQuery("remain");
}

//下载保单文件
//@param policy:保单对象
//@param isEndorse:true:下载批单,false:下载保单
//@param param:红章目的地等信息
function downPDF(policy, isEndorse, param){
    var href = (window.baseDir || "") + "/policyCenter/public/doPrintPdf?policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"] + "&isEndorse=" + !!isEndorse + (param || "");
    if (jQuery("input#notPrintPH:checked").size() > 0) {
        href += "&notPrintPH=1";
    }
    window.open(href, "policyPDF");
}

function downAll() {
    var length = jQuery("input.policy_checked:checked").size();
    var policyIDList = "";
    if (length == 0) {
        alertMsg("请选择保单");
        return false;
    } else {
        for (i = 0; i < length; i++) {
            var _policyID = jQuery("input.policy_checked:checked:eq(" + i + ")").closest("tr").attr("name");
            if (policyIDList != "") {
                policyIDList += "-" + _policyID;
            } else {
                policyIDList += _policyID;
            }
        }
        var href = "printAllPdf?policyIDList=" + policyIDList;
        if (jQuery("input#notPrintPH:checked").size() > 0) {
            href += "&notPrintPH=1";
        }
        if (confirm("确认下载？共选中 " + length + " 份保单"))
            window.open(href, "_self");

    }
}
$(".result_list").on("click", ".viewTypeButton", function () {
    //切换保单的显示视图，是被保人视图，还是保单视图
    if($(this).is(".selected")){return;};//已选中视图，就不要再次选中了
    $(".viewTypeButton").removeClass("selected");
    $(this).addClass("selected");
    policyTableVue.policies = [];
    policyTableVue.viewType = $(this).attr("viewType");
    doQuery();
})
$("#policiesTable").on("click", ".sendmail,.offermail", function () {
    //发送要约邮件或者给客户发送普通邮件
    var index = $(this).closest(".policyRow").attr("index");
    var policy = policyTableVue.policies[index];
    //给用户发送邮件，如果是有效保单，那么就是发送保单邮件
    //如果是未支付的direct的投保单，那么就给客户发送要约邮件
    var url = window.baseDir + "/smsCenter/public/showEmailSendPage?emailSource=policy&policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"];
    if($(this).is(".offermail")){
        url += "&orderID=" + policy["orderID"] + "&emailType=offer";
    }
    showOverlayIframe(url);
}).on("click", ".expandPolicy", function (e) {
    //展开保单详情，包括投被保人参与人旅行信息
    ajaxGetListDetail($(this));
})/*.on("click", ".belong", function () {
    //保单归属
    var policyID = $(this).closest("td").find(".belong_policyId").val();
    var msg = prompt("电话号码：", "");
    if (msg == null) {
        return;
    }
    msg = msg.replace(/ /g, "");
    if (msg) {
        $.ajax({
            url: "ajaxBelongUser",
            type: "post",
            dataType: "json",
            data: "tel=" + msg + "&policyID=" + policyID,
            error: function () {
                alertMsg("网络超时，操作中断！");
            },
            success: function (data) {
                if (data.result == "1") {
                    alertMsg("保存成功");
                } else {
                    alertMsg("保存失败，" + data.message);
                }
            }
        })
    } else {
        alertMsg("电话号码不能为空！");
    }
})*/.on("click", ".listContainerBorder", function () {
    //点击单行保单，有选中或者去选中效果
    $("#mainlist .listContainerBorder.selected").removeClass("selected");
    $(this).addClass("selected");
}).on("click", ".policyRow.first_tr", function () {
    //点击单行保险，有选中或者去选中效果
    $("#mainlist .first_tr.selected").removeClass("selected");
    $(this).addClass("selected");
}).on("click", ".sExpand", function () {
    //保单详细信息里，展开被保人的受益人信息
    if ($(this).find(".fa").hasClass("close")) {
        $(this).closest(".puserInfo").next('tr').find(".beneficiaryInfo").show(1000);
        $(this).find(".fa").removeClass("close");
        $(this).find(".fa").addClass("open");
        $(this).find(".fa").addClass("rotate");
    } else {
        $(this).closest(".puserInfo").next('tr').find(".beneficiaryInfo").hide(1000);
        $(this).find(".fa").removeClass("open");
        $(this).find(".fa").addClass("close");
        $(this).find(".fa").removeClass("rotate");
    }
});

/*发送短信部分*/
var messagePolicy;//正在发送短信的policy对象
function msgLoad(trigger) {//发送短信 -- 载入短信图层
    $("#msg_inf").css({"height": "auto", "overflow-y": "hidden", "overflow-x": "hidden"});
    $("#send_msg_box form")[0].reset();
    scren_point($("#send_msg_box"));
    var index = $(trigger).closest(".policyRow").attr("index");
    var policy = policyTableVue.policies[index];
    var insuredRows = $(trigger).closest(".policyRow").find(".rowSelect:checked");
    messagePolicy = policy;
    var product_name = policy["planName"];
    var td_exprDat = $.trim(policy["expiryDate"]);
    var issue_time = $.trim(policy["issueTime"]);
    $("#defaultSMSChannel").html("<option value=''>请选择</option>");
    $.ajax({
        url: window.baseDir + "/policyCenter/public/ajaxGetSms",
        type: "post",
        data: {"policyID": policy["policyID"], "securityPolicyID" : policy["securityPolicyID"]},
        dataType: "json",
        error: function () {
            alertMsg("网络超时，操作中断！");
        },
        success: function (data) {
            if (data["success"]) {
                var mainData = data["value"];
                var expire_alert = "您好，您于" + issue_time + "在百川保险投保的" + product_name + "将于" + td_exprDat + "失效，特此提醒您。如有续保需要，随时恭候您的来电。24小时客服电话400-996-0007";
                var issue_success = mainData.message;
                if (StringUtils.isNotBlank(mainData["mes4participant"])) {
                    $("#success_participant").val(mainData["mes4participant"]).show();
                }else{
                    $("#success_participant").hide();
                }
                $("#expire_alert").val(expire_alert);
                $("#issue_success").val(issue_success);
                for (var i = 0; i < mainData.channel.length; i++) {
                    if (mainData.channel.length == 1) {
                        $("#defaultSMSChannel").append("<option selected value='" + mainData.channel[i].smsChnlCode + "'>" + mainData.channel[i].smsChnlName + "</option>");
                    } else {
                        $("#defaultSMSChannel").append("<option value='" + mainData.channel[i].smsChnlCode + "'>" + mainData.channel[i].smsChnlName + "</option>");
                    }
                }
                if($("#select_content").is(":hidden")){
                    $("#select_content #issue_success").prop("selected", true);
                    $("#select_content").change();
                }
            } else {
                alertMsg("获取短信模板或发送通道出错，" + data["errorMessage"]);
            }
        }
    })
    document.getElementById("defaultcontent").selected = true;

    $("#msg_send").prop("disabled", true).css("color", '#BBBBBB');
    var duplicatePhone = {};
    var phoneRows = [];
    for(var insuredIndex = 0; insuredIndex < insuredRows.length; insuredIndex++){
        var index = insuredRows.eq(insuredIndex).closest("tr").attr("insuredIndex");
        var phone = policy["insuredList"][index]["phone"];
        if(!duplicatePhone[phone]){
            duplicatePhone[phone] = true;
            phoneRows.push("<div><span></span>发送号码" + (insuredIndex + 1) + "：<input type='text' class='msg_telephone' value=" + phone + " /><div>");
        }
    }
    $("#msg_inf").html(phoneRows.join(""));
    if(phoneRows.length > 0){
        $("#msg_send").prop("disabled", false).css("color", '#222222');
    }
    if (phoneRows.length > 7) {
        $("#msg_inf").css({"height": "155px", "overflow-y": "auto", "overflow-x": "hidden"});
    } else {
        $("#msg_inf").css({"height": "auto", "overflow-y": "hidden", "overflow-x": "hidden"});
    }
}

$("#send_msg_close,#send_msg_cancel").on("click", function () {//关闭发送短信
    hidePopup($("#send_msg_box,#opa"));//关闭弹出层后判断主页面和菜单的覆盖关系
})
$("#select_content").change(function () {//发送短信 -- 选择内容
    $("#msg_text").val($("#select_content").val());
});

//发送短信 -- 添加号码
function msgAdd() {
    if ($("#add_telephone").val() == "" || $("#add_telephone").val().replace(/\d/gi, "") != "") {
        alertMsg("电话号码有误");
    } else {
        var tel = $("#add_telephone").val();
        //验证重复号码
        var i11 = 0;
        for (var i1 = 0; i1 < j; i1++) {
            if (tel == $("[name='msg_telephone1']:eq(" + i1 + ")").val()) {
                i11 = 1;
                break;
            }
        }
        if (i11 == 1) {
            return false;
        }

        $("<div>发送号码" + ($("#msg_inf .msg_telephone").length + 1) + "：<input fmt='tel' type='text' class='msg_telephone' value='" + tel + "' /></div>").appendTo("#msg_inf");
        $("#msg_send").prop("disabled", false).css("color", '#222222');
    }
    $("#add_telephone").val("");
    if ($("#msg_inf .msg_telephone").length > 7) {
        $("#msg_inf").css({"height": "155px", "overflow-y": "auto", "overflow-x": "hidden"});
    } else {
        $("#msg_inf").css({"height": "auto", "overflow-y": "hidden", "overflow-x": "hidden"});
    }
}

function sendMessage() {//发送短信 -- 发送短信
    var jsonMessage = {
        "msg": $("#msg_text").val(),
        "tels": $("#msg_inf .msg_telephone").map(function () {
            if(StringUtils.isNotBlank($(this).val())){
                return $(this).val();
            }
        }).get().join(";"),
        "defaultChannel": $("#defaultSMSChannel").val(),
        "policyID": messagePolicy["policyID"],
        "securityPolicyID": messagePolicy["securityPolicyID"]
    };
    jsonMessage = JSON.stringify(jsonMessage);
    $.ajax({
        url: window.baseDir + "/policyCenter/public/ajaxSendSms",
        type: "post",
        dataType: "json",
        data: {"sms": jsonMessage},
        async: false,
        error: function () {
            alertMsg("网络超时，操作中断！", function () {
                hidePopup($("#send_msg_box,#opa"));
            });
        },
        success: function (data) {
            if (data["success"]){
                alertMsg("发送成功！", function () {
                    hidePopup($("#send_msg_box,#opa"));
                });
            }else{
                alertMsg("发送失败，" + data["errorMessage"], function () {
                    hidePopup($("#send_msg_box,#opa"));
                });
            }
            $("#select_content").val(0);
            $("#msg_text").val("");
        }
    });
}

function tocancel(policy) {
	showOverlayIframe(window.baseDir + "/policyCenter/public/toCancel?policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"]);
}

function resend(policy) {
    //拒保状态的保单需要二次确认
    if (policy["actionStatus"] == "4" && !confirm("该保单发送状态异常，应该先于核心系统沟通后再处理，确认要重新发送吗？")) {
        return;
    }
    window.open(window.baseDir +  '/policyCenter/public/doResend?policyID=' + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"], '_blank');
}
//打开编辑旅行信息和保单页面
//policyID： policyID；
//type: 将要进行的操作：edit:修改,add:追加,renewal:续保
//borc：类型是pageB还是pageC
function editPolicyApplication(policy, type){
    var policyUrl = baseDir + "/policyCenter/public/toFillInTheInsuranceApplicationForm?policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"] + "&actionCode=" + type;
    var travelUrl = baseDir + "/policyCenter/public/travel?policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"] + "&actionCode=" + type;
    //复制追加修改前，先清除掉以前的缓存信息
    clearPagePolicyApplication();
    /*if(type == "add"){
        alertMsg({
            "title": "附加保单",
            "message": "<div class='fieldContainer'><input type='text' required class='orderID field' value='" + policy["orderID"] + "'/><div class='placeholder'>订单号</div><div class='underline'></div></div>",
            "menus": [{
                "text": "取消"
            }, {
                "text": "确定",
                "callback": function (area) {
                    var orderID = area.find(".orderID").val();
                    policyUrl += "&orderID=" + orderID;
                    window.open(travelUrl + "&insureLink=" + encodeURIComponent(policyUrl), "_blank");
                }
            }]
        })
    }else{*/
        if(policy["deviceType"]["code"] == "API"){
            //对接过来的保单，不用完善tavelinfo
            newPageMethod(policyUrl + "&comment=EXTERNAL");
        }else{
            newPageMethod(travelUrl, "_blank");
        }
    //}
}

//打开保单操作菜单弹出层
//计算哪些菜单可以展开，哪些菜单必须合上
var policyMenu = "";
$("body").on("click", ".showPolicyMenus", function () {
    policyMenu = $(this).closest(".bottomInfo");
    var show = $("#policyMenus");
    show.find(".listSubMenuBox").removeAttr("need").hide();
    policyMenu.find(".onmenu").each(function () {
        show.find(".listSubMenuBox[menu='" + $(this).attr("menu") + "']").attr("need", "true").show();
    })
    show.find(".listMenuContainer").each(function () {
        $(this).toggle($(this).find(".listSubMenuBox[need='true']").length > 0);
    })
    scren_point(show);
    $("#opa").hide();
    $(".policyMenuOpa").show();
});
$(".policyMenuOpa").on("click", function () {
    //当点击菜单外面的空白区域时，关闭当前的菜单
    hidePopup("#policyMenus");
    $(".policyMenuOpa").hide();
});
$("#policyMenus").on("click", ".listSubMenuBox", function () {
    var orderID = policyMenu.attr("orderID");
    var policyID = policyMenu.attr("policyID");
    if($(this).is("[origin]")){//能调用菜单元素有的方法，就调用那个方法
        hidePopup("#policyMenus");
        $(".policyMenuOpa").hide();
        policyMenu.find(".onmenu[menu='" + $(this).attr("menu") + "']").click();
    }else if($(this).is("[menu='download']")){
        //保单下载在新的页面打开
        hidePopup("#policyMenus");
        $(".policyMenuOpa").hide();
        showOverlayIframe(baseDir + "/policyCenter/policy/showPrintPdf?orderID=" + orderID + "&policyID=" + policyID);
    }
})


function showBkdtCfmExplanation(policy) {
    //alertMsg($("#cfmExpl_" + policyID).val());
}

function showUnderOwritingperator(param) {
    $(param).siblings(".editInfoUl").show();
    $(param).siblings(".editInfoUl").css("margin-left", $(param).offset().left - $(param).parent().offset().left - 3);
}

$(".edit_tr").on("mouseleave", ".editInfoUl", function () {
    $(this).hide();
})

/*
* 修改/批改/注销重出保单的判断与赋值
* policy:policy对象
* backdateAllowed:是否有倒签权限
* endorseAllowed:是否有批改权限
* */

function editId(policy, backdateAllowed) {
    var action =  analizeModifyPolicyAction(policy, backdateAllowed);
    if(!action){
        alertMsg("该保单不能进行修改操作。");
        return;
    }
    $.when().then(function () {
        var deffered = $.Deferred();
        var message = "";
        if(window.currentAgencyId != policy["agencyid"]){
            message = "你操作的保单是" + policy["agencyAbName"] + "保单，";
        }
        if("c2nc" == action["code"]){
            message += "对已签发的保单修改会立即注销现有保单后签发新的保单，确认继续注销重出么？";
        }else{
            message += "确认" + action["description"] + "该保单吗？";
        }
        confirmMsg(message, "", "确认", "取消", function () {
            if("c2nc" == action["code"]){
                loadingNow("注销现有保单");
                $.ajax({
                    "url": window.baseDir + "/policyCenter/public/doCancel",
                    "type:": "post",
                    "dataType": "json",
                    "data":{"actionType":"modify", "actionCode":"c2nc", "policyID": policy["policyID"],"securityPolicyID": policy["securityPolicyID"], "poundage": 0, "remarks": "注销重出"},
                    "success": function (data) {
                        loadingComplete();
                        if(data["success"]){
                            alertMsg({
                                "title":"注销成功",
                                "message": "保单" + policy["policyID"] + "注销成功, 点击下一步进入录单流程",
                                "menus":[{
                                    "text": "下一步",
                                    "callback": function () {
                                        editPolicyApplication(data["value"], 'c2nn');
                                        doQuery();
                                    }
                                }]
                            });
                            //deffered.resolve();
                        }else{
                            alertMsg(data["errorMessage"]);
                        }
                    },
                    "error": function () {
                        loadingComplete();
                        loadingNow("注销失败");
                    }
                });
            }else{
                deffered.resolve();
            }
        });
        return deffered;
    }).then(function () {
        editPolicyApplication(policy, action["code"]);
    });
}

/*
* 判断对当前保单能进行哪种修改操作
* policy:policy对象
* backdateAllowed:是否有倒签权限
* */
function analizeModifyPolicyAction(policy, backdateAllowed) {
    return PolicyUtils.getModifyAction(policy, backdateAllowed);
}


function toPay(policy) {
    showOverlayIframe(window.baseDir + "/paymentCenter/index?policyID=" + policy["policyID"] + "&securityPolicyID=" + policy["securityPolicyID"]);
}

function downLoadPdfWithOrderID(orderID) {
    var href = "printAllPdfOfOrder?orderID=" + orderID;
    if (jQuery("input#notPrintPH:checked").size() > 0) {
        href += "&notPrintPH=1";
    }
    window.open(href, "_self");
}

function dowanLoadEDPdfWithOrderID(orderID,destination){
    var href="printAllEDPdfOfOrder?orderID="+orderID+"&destination="+destination;
    window.open(href,"_self");
}
function batchDownOption(){
    $(".orderInfo.orderLabel").on("click",".orderLabel",function (){
        $(this).parent().find(".batchDownloadOption").show(800);
    })
}

function ajaxFraudPolicyApplication(policyID){
    if(confirm("确定要将该保单/投保单下的所有投被保人标记为欺诈客户吗？")){
        $.ajax({
            url: "../../../customer/ajaxSaveFraudPolicyApplication",
            type: "post",
            dataType: "json",
            data: "policyID=" + policyID,
            error: function () {
                alertMsg("网络超时，操作中断！");
            },
            success: function (data) {
                if (data.result == "1") {
                    alertMsg("保存成功");
                } else {
                    alertMsg(data.message);
                }
            }
        });
    }
}

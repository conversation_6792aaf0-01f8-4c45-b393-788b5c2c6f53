/*
* 保单，自然人，证件类型，国籍等通用的辅助判定方法
* */
var PolicyUtils = {
	STATUS: {
		COMPLETE: 11
	},
	policyActions: {
		"new": {
			code: "new",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "新增",
			description: "注销重出",
		},
		"repurchase": {
			code: "repurchase",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "再购",
			description: "再购",
		},
		"renewal": {
			code: "renewal",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "续保",
			description: "续保",
		},
		"add": {
			code: "add",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "追加",
			description: "追加",
		},
		"makeUp": {
			code: "makeUp",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "补录",
			description: "补录",
		},
		"edit": {
			code: "edit",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "修改投保单",
			description: "修改",
		},
		"c2nc": {
			code: "c2nc",
			classificationCode: "cancel",
			classificationCNName: "退保",
			cnName: "批改注销",
			description: "注销重出",
		},
		"c2nn": {
			code: "c2nn",
			classificationCode: "new",
			classificationCNName: "新增",
			cnName: "批改新增",
			description: "重出",
		},
		"endorse": {
			code: "endorse",
			classificationCode: "endorse",
			classificationCNName: "批改",
			cnName: "批改",
			description: "批改",
		},
		"cancel": {
			code: "cancel",
			classificationCode: "cancel",
			classificationCNName: "退保",
			cnName: "批退",
			description: "批退",
		},
		"surrender": {
			code: "surrender",
			classificationCode: "cancel",
			classificationCNName: "退保",
			cnName: "退保",
			description: "退保",
		}
	},
	getModifyAction: function (policy, backdateAllowed) {
		/*
		* 判断当前保单能用哪种修改方式
		* policy:policy对象
		* backdateAllowed:是否有倒签权限
		* action的classification是new且actionStatus!=11的（未生效的新单）都是eidt
		* action的classification是new且actionStatus==11的且未到起保日期或者有倒签权限的，是c2nc(先注销/退保)
		* action的code是c2nc,actionStatus==11是c2nn(不注销/退保，直接进入投保页面)
		* action的code是c2nc,actionStatus!=11是c2nc(先注销/退保)
		* */
		var endorseAllowed = policy["endorseAllowed"];//不是在policy获取，是在insurer或者product获取
		var modifyActionCode = "";
		var actionCode = policy["action"]["code"];
		var classification = policy["action"]["classification"]["code"];
		var actionStatus = policy["actionStatus"];
		if(actionCode == "c2nc" && actionStatus == 11){
			modifyActionCode = "c2nn";
		}else if((classification == "new" &&  actionStatus == 11 ||
			actionCode == "c2nc" && actionStatus != 11) && (new Date(policy["effectiveDate"]) - new Date() > 0 || backdateAllowed)){
			if(endorseAllowed && actionCode != "c2nc"){
				modifyActionCode = "endorse";
			}else {
				modifyActionCode = "c2nc";
			}
		}else if(classification == "new" &&  actionStatus != 11){
			modifyActionCode = "edit";
		}
		return this.policyActions[modifyActionCode];
	},
	isNewComplete: function (policy) {
		//保单是否处在新增完成状态，大部分判断保单是否有效都是用这个条件作为判断依据的
		if(this.getClassificationCode(policy["action"]) == "new" && policy["actionStatus"] == this.STATUS.COMPLETE){
			return true;
		}
		return false;
	},
	getActionCode: function (action) {
		//获取action的code
		var code = "";
		if(action){
			if(action["code"]){
				code = action["code"];
			}else if(this.policyActions[action]){
				code = this.policyActions[action]["code"];
			}
		}
		return code;
	},
	getClassificationCode: function (action) {
		//获取action的classificationCode的code
		var code = "";
		if(action){
			if(this.policyActions[action]){
				code = this.policyActions[action]["classificationCode"];
			}else if(action["classification"] && action["classification"]["code"]){
				code = action["classification"]["code"];
			}
		}
		return code;
	},
	getClassificationCNName: function (action) {
		//获取action的classification的cnName
		var cnName = "";
		if(action){
			if(this.policyActions[action]){
				cnName = this.policyActions[action]["classificationCNName"];
			}else if(action["classification"] && action["classification"]["code"]){
				cnName = action["classification"]["code"];
			}
		}
		return cnName;
	},
	getCNName: function (action) {
		//获取当前action的cnName
		var cnName = "";
		if(action){
			if(this.policyActions[action]){
				cnName = this.policyActions[action]["cnName"];
			}else if(action["cnName"]){
				cnName = action["cnName"];
			}
		}
		return cnName;
	}
};

/*
* add by zoupeng on 2021-10-12 证件类型相关的信息与处理
* */
var IDTypeUtils = {
	"IDTypes": {
		"NID": {code: "NID", cnName: "身份证", classificationCode: "ID"},
		"MPT": {code: "MPT", cnName: "台胞证", classificationCode: "ID"},
		"MPHK": {code: "MPHK", cnName: "回乡证", classificationCode: "ID"},
		"BC": {code: "BC", cnName: "出生证", classificationCode: "ID"},
		"MID": {code: "MID", cnName: "军官证", classificationCode: "ID"},
		"OID": {code: "OID", cnName: "其它证", classificationCode: "ID"},
		"PP": {code: "PP", cnName: "护照", classificationCode: "PP"},
		"TPM": {code: "TPM", cnName: "台湾通行证", classificationCode: "PP"},
		"HKPM": {code: "HKPM", cnName: "港澳通行证", classificationCode: "PP"}
	},
	getCode: function (IDType) {
		//获取IDType的code
		var code = "";
		if(IDType){
			if(IDType["code"]){
				code = IDType["code"];
			}else if(this.IDTypes[IDType]){
				code = this.IDTypes[IDType]["code"];
			}
		}
		return code;
	},
	getClassificationCode: function (IDType) {
		//获取IDType的classificationCode的code
		var code = "";
		if(IDType){
			if(this.IDTypes[IDType]){
				code = this.IDTypes[IDType]["classificationCode"];
			}else if(IDType["classification"] && IDType["classification"]["code"]){
				code = IDType["classification"]["code"];
			}
		}
		return code;
	},
	getCNName: function (IDType) {
		//获取当前IDType的cnName
		var cnName = "";
		if(IDType){
			if(this.IDTypes[IDType]){
				cnName = this.IDTypes[IDType]["cnName"];
			}else if(IDType["cnName"]){
				cnName = IDType["cnName"];
			}
		}
		return cnName;
	}
};
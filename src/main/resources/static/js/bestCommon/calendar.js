var c = {};
c.L = ["",new Date()];//日期可选范围
c.E = [$("#insure_startdate"),$("#insure_enddate"),"",""];//起始日期，结束日期，比较的起始日期，比较的结束日期
c.N = 6;//可现实的月份，暂定为6个月
function buildCalendar(){//创建整个日历框架
	var m = "<td><div id='calendarBg'><div id='datePre'><div></div></div><div id='dateNex'><div></div></div></div></td>";
	for(var i = 0 ; i < c.N ; i++){
		m += "<td style='padding:0px;'><table class='calendarTable'><thead><tr><td colspan='7'><div class='calendarTl' id='monthWhole_"+i+"'></div></td></tr><tr><th>日</th><th>一</th><th>二</th><th>三</th><th>四</th><th>五</th><th>六</th></tr></thead><tbody>";
		for(var j = 0 ; j < 6 ; j++){
			m += "<tr><td id='month_"+i+"_"+(j*7)+"'></td><td id='month_"+i+"_"+(j*7+1)+"'></td><td id='month_"+i+"_"+(j*7+2)+"'></td><td id='month_"+i+"_"+(j*7+3)+"'></td><td id='month_"+i+"_"+(j*7+4)+"'></td><td id='month_"+i+"_"+(j*7+5)+"'></td><td id='month_"+i+"_"+(j*7+6)+"'></td>";
		}
		m +="</tbody></table></td>";
	}
	var calendar = "<div class='calendarBox' style='display:none;'><table class='eleLeft dateEditTable'><tr>"+m+"</tr></table>"+
				 "<div class='eleLeft dateEditMain'><div id='calendarClassify'></div>"+
				 "<div><span style='font-weight:800;'>日期范围：</span><select id='dateControl'>"+
				 "<option value=''>自定义</option>"+
				 "<option value='1'>今天</option>"+
				 "<option value='2'>昨天</option>"+
				 "<option value='3'>上周</option>"+
				 "<option value='4'>上月</option>"+
				 "<option value='5'>过去7天</option>"+
				 "<option value='6'>过去30天</option>"+
				 "</select></div>"+
				 "<div class='dateEditBox'><input type='text' id='calendarClassifyPre' readonly/><span>-</span><input loc='end' type='text' id='calendarClassifyEnd' readonly/></div>"+
				 "<div class='cDateEditBox'><span style='font-weight:800;'><input type='checkbox' id='dateCompareChecked' /><label for='dateCompareChecked'>比较日期范围：</label></span><select id='dateCompare'>"+
				 "<option value=''>自定义</option>"+
				 "<option value='1'>上一段时间</option>"+
				 "<option value='2'>去年</option>"+
				 "</select></div>"+
				 "<div class='dateEditBox cDateEditBox'><input type='text' id='dateComparePre' readonly/><span>-</span><input loc='end' type='text' id='dateCompareEnd' readonly/></div>"+
				 "<div class='datebtnbox'><input id='submitDate' type='button' value='应 用' /><a style='margin-left:10px;' id='clearDate' href='javascript:void(0)'>清空</a><a style='margin-left:10px;' id='closeDate' href='javascript:void(0)'>取消</a></div>"+
				 "</div>"+
				 "</div>";
	jQuery(calendar).appendTo("body");
}
$(function(){buildCalendar();});
function setMonthVal(){//填充日期，判断每个日期的显示规则
	var _s = fDate($(".dateEditBox input").eq(0).val());//起始日期
	var _e = fDate($(".dateEditBox input").eq(1).val());//结束日期
	var _cs = fDate($(".dateEditBox input").eq(2).val());//比较的起始日期
	var _ce = fDate($(".dateEditBox input").eq(3).val());//比较的结束日期
	var _ls = isNaN(fDate(c.L[0])) ? 0 : fDate(c.L[0]);//限制起始日期，没有就是最小值
	var _le = isNaN(fDate(c.L[1])) ? Infinity : fDate(c.L[1]);//限制结束日期，没有就是无穷大
	if($(".focusDateInput").attr("loc") == "end"){
		var index = $(".dateEditBox input").index($(".focusDateInput"));
		var _d = fDate($(".dateEditBox input").eq(index-1).val());
		_ls = _ls >= (isNaN(_d) ? 0 : _d) ? _ls : _d;
	}
	for(var i = 0 ; i < c.N ; i++){
		var st = -(new Date(c.D[0],c.D[1]+i+1-c.N,1).getDay())%7+1;//此月第一天是周几
		var et = new Date(c.D[0],c.D[1]+i+2-c.N,0).getDate();//此月最后一天是多少号
		var y = new Date(c.D[0],c.D[1]+i+1-c.N,1).getFullYear();//此月的年份
		var m = new Date(c.D[0],c.D[1]+i+1-c.N,1).getMonth()+1;//此月的月份
		m > 9 ? "" : m = "0" + m;
		$("#monthWhole_"+i).html(y+"年"+m+"月");
		for(var j = 0 ; j <= 41 ; j++,st++){
			var _td = $("#month_"+i+"_"+j).removeClass();
			if(st >0 && st <= et){
				var ststr = st > 9 ? st : "0" + st;
				var _today = fDate(y+","+m+","+ststr);
				_td.html(ststr);
				if(_today < _ls || _today > _le){
					_td.addClass("disabledDate");
				}else{
					_td.addClass("activeDate");
				}
				if(_today == _s || _today == _e || (_today > _s && _today < _e)){
					_td.addClass("selectedDate");
				}
				if(_today == _cs || _today == _ce || (_today > _cs && _today < _ce)){
					_td.addClass("compareSelectedDate");
				}
			}else{
				$("#month_"+i+"_"+j).html("&nbsp;");
			}
		}
	}
	
}

$(document).on("click",".calendarBox .activeDate",function(){//在日历上点选日期
	var i = this.id.split("_")[1];
	var index = $(".dateEditBox input").index($(".focusDateInput"));
	$(".focusDateInput").val($("#monthWhole_"+i).html()+this.innerHTML+"日");
	if($(".focusDateInput").attr("loc") != "end" && fDate($(".dateEditBox input").eq(index).val()) > fDate($(".dateEditBox input").eq(index+1).val())){
		$(".dateEditBox input").eq(index+1).val($(".dateEditBox input").eq(index).val());
	}
	if($("#dateCompareChecked").is(":checked") && $("#dateCompare").val()==""){
		var n = 4;
	}else{
		var n = 2;
	}
	$(".focusDateInput").removeClass();
	$(".dateEditBox input").eq((index+1)%n).addClass("focusDateInput");
	setCD();
	setMonthVal();
})
$(document).on("click",".calendarTl",function(){//直接点击月份时选取
	if($(".focusDateInput").attr("loc")=="end"){
		var _i = $(".dateEditBox input").index($(".focusDateInput"));
		$(".focusDateInput").removeClass();
		$(".dateEditBox input").eq(_i - 1).addClass("focusDateInput");
	}
	var _td = $(this).closest(".calendarTable").find(".activeDate");
	if(_td.length > 0){
		var _i = $(".dateEditBox input").index($(".focusDateInput"));
		var ym = this.innerHTML;
		var s = _td.eq(0).html();
		var e = _td.eq(_td.length - 1).html();
		$(".dateEditBox input").eq(_i).val(ym+s+"日");
		$(".dateEditBox input").eq(_i+1).val(ym+e+"日");
		setCD();
		setMonthVal();
	}
})
$(document).on("focus",".dateEditBox input",function(e){//聚焦日期输入框
	var _this = $(e.target);
	$(".focusDateInput").removeClass();
	_this.addClass("focusDateInput");
	_this.closest(".dateEditBox").prev("div").find("select").val("");
	setMonthVal();
}).on("keydown",".dateEditBox input",function(e){
	e.stopPropagation();
})


$(document).on("change","#dateControl",function(){//选择日期的范围
	var v = this.value;
	var t = typeof(today)=="undefined" ? new Date() : today;
	switch(v){
		case "1"://今天
			var t1 = new Date(t);
			var t2 = new Date(t);
			break;
		case "2"://昨天
			var t1 = new Date(t - 86400000);
			var t2 = new Date(t - 86400000);
			break;
		case "3"://上周
			var tw = t.getDay() == 0 ? 7 : t.getDay();
			var t1 = new Date(t - 86400000*(tw+6));
			var t2 = new Date(t - 86400000*tw);
			break;
		case "4"://上月
			var ty = t.getFullYear();
			var tm = t.getMonth();
			var t1 = new Date(ty , tm - 1 , 1);
			var t2 = new Date(ty , tm  , 0);
			break;
		case "5"://过去7天
			var t1 = new Date(t - 86400000*7);
			var t2 = new Date(t - 86400000*1);
			break;
		case "6"://过去30天
			var t1 = new Date(t - 86400000*30);
			var t2 = new Date(t - 86400000*1);
			break;
		default:return;
	}
	var m1 = (t1.getMonth()+1) > 9 ? (t1.getMonth()+1) : "0" + (t1.getMonth()+1);
	var d1 = t1.getDate() > 9 ? t1.getDate() : "0" + t1.getDate();
	var m2 = (t2.getMonth()+1) > 9 ? (t2.getMonth()+1) : "0" + (t2.getMonth()+1);
	var d2 = t2.getDate() > 9 ? t2.getDate() : "0" + t2.getDate();
	$(".dateEditBox input").eq(0).val(t1.getFullYear()+"年"+m1+"月"+d1+"日");
	$(".dateEditBox input").eq(1).val(t2.getFullYear()+"年"+m2+"月"+d2+"日");
	c.D = [t2.getFullYear(),t2.getMonth()];
	setCD();
	setMonthVal();
})
$(document).on("click","#dateCompareChecked",function(){//点选是否比较日期范围
	if($(this).is(":checked")){
		if($(".dateEditBox input").eq(0).val()=="" || $(".dateEditBox input").eq(1).val()==""){
			$(this).prop("checked",false);
		}else{
			$("#dateCompare").prop("disabled",false);
			$(".dateEditBox").eq(1).show();
			$("#dateCompare").val(1);
		}
	}else{
		$("#dateCompare").prop("disabled",true);
		$(".dateEditBox").eq(1).hide();
		$("#dateCompare").val(1);
		$(".dateEditBox input:gt(1)").val("");
	}
	setCD();
	setMonthVal();
}).on("change","#dateCompare",function(){//切换日期比较范围类型
	$(".focusDateInput").removeClass();
	$(".dateEditBox input").eq(0).addClass("focusDateInput");
	setCD();
	setMonthVal();
})
$(document).on("click","#dateOption p",function(){//选择月份和年份函数
	$(this).siblings(".firstSelectedDate").removeClass();
	$(this).addClass("firstSelectedDate");
	var iniYear = $("#yearOption .firstSelectedDate")[0].id.split("_")[1];
	var iniMonth = $("#monthOption .firstSelectedDate")[0].id.split("_")[1];
	for(var i = 0 ; i < c.monthNum ; i++){
		if(iniMonth*1 + i > 12){
			c.monthArea[i] = iniYear*1 + 1 + "/0" + (iniMonth*1 + i - 12);
		}else{
			var zr = (iniMonth*1 + i) > 9 ? "" : "0";
			c.monthArea[i] = iniYear  + "/" + zr + (iniMonth*1 + i);
		}
	}
	setMonthVal();
})
$(document).on("click","#datePre",function(){//往前一个月
	var y = c.D[0];
	var m = c.D[1]-1;
	c.D = [y,m];
	setMonthVal();
})

$(document).on("click","#dateNex",function(){//往后一个月
	var y = c.D[0];
	var m = c.D[1]+1;
	c.D = [y,m];
	setMonthVal();
})


$(document).on("click","#submitDate",function(){//确认应用选择的日期
	var t = [];
	for(var i = 0 ; i < c.E.length ; i++){
		var dt = $(".dateEditBox input").eq(i).val().replace(/年|月/gi,"-").replace(/日/gi,"");
		c.E[i] && c.E[i].val(dt);
		t.push(dt);
	}
	var _s = t[0].split("-");
	var _e = t[1].split("-");
	var _d = [_s[0],_s[1],_s[2],_e[0],_e[1],_e[2]];
	var _ele = c.O.find("input");
	for(var i = 0 ; i < _d.length ; i++){
		_ele.eq(i).val(_d[i]);
	}
	try{
		c.callBack(t);
	}catch (e) {}
	$(".calendarBox").hide();
})
$(document).on("click","#closeDate",function(){//取消并且关闭选择的日期
	for(var i in c.acDateName){
		c.acDate[i][0] = $(c.acDateName[i][0]).val();
		c.acDate[i][1] = $(c.acDateName[i][1]).val();
	}
	$(".calendarBox").hide();
}).on("click",function(e){
	if($(e.target).closest(".calendarBox").length==0 && $(e.target).closest(c.O).length==0){
		for(var i in c.acDateName){
			c.acDate[i][0] = $(c.acDateName[i][0]).val();
			c.acDate[i][1] = $(c.acDateName[i][1]).val();
		}
		$(".calendarBox").hide();
	}
})
$(document).on("click","#clearDate",function(){//清空所有值
	$(".dateEditBox input").val("");
	setMonthVal();
})

function fDate(){//formatDate格式化时间为毫秒数
	var a = arguments;
	if(a.length == 3){
		return Date.parse(a[0]+"/"+a[1]+"/"+"a2");
	}else if(typeof(a[0]) == "object"){
		return Date.parse(a[0]);
	}else if(typeof(a[0]) == "string"){
		var b = a[0].replace(/年|月|\/|-|,/gi,"/").replace(/日/gi,"");
		return Date.parse(b);
	}else{
		return 0;
	}
}

function setCD(){//设置被比较日期的值
	var d1 = $(".dateEditBox input").eq(0).val().replace(/年|月|\/|-/gi,",").replace(/日/gi,"");
	var d2 = $(".dateEditBox input").eq(1).val().replace(/年|月|\/|-/gi,",").replace(/日/gi,"");
	if($("#dateCompareChecked").is(":checked") && $("#dateCompare").val() == 1){
		var t1 = new Date(fDate(d1)-(fDate(d2)-fDate(d1))-86400000);
		var t2 = new Date(fDate(d1)-86400000);
		$(".dateEditBox input").eq(2).val(t1.getFullYear()+"年"+(t1.getMonth()+1)+"月"+t1.getDate()+"日");
		$(".dateEditBox input").eq(3).val(t2.getFullYear()+"年"+(t2.getMonth()+1)+"月"+t2.getDate()+"日");
	}else if($("#dateCompareChecked").is(":checked") && $("#dateCompare").val() == 2){
		var t1 = new Date(fDate((d1.split(",")[0]-1)+","+d1.split(",")[1]+","+d1.split(",")[2]));
		var t2 = new Date(fDate((d2.split(",")[0]-1)+","+d2.split(",")[1]+","+d2.split(",")[2]));
		if(t1.getDate()!=d1.split(",")[2]){
			t1 = new Date(fDate((d1.split(",")[0]-1)+","+d1.split(",")[1]+","+(d1.split(",")[2]-t1.getDate())));
		}
		if(t2.getDate()!=d2.split(",")[2]){
			t2 = new Date(fDate((d2.split(",")[0]-1)+","+d2.split(",")[1]+","+(d2.split(",")[2]-t2.getDate())));
		}
		$(".dateEditBox input").eq(2).val(t1.getFullYear()+"年"+(t1.getMonth()+1)+"月"+t1.getDate()+"日");
		$(".dateEditBox input").eq(3).val(t2.getFullYear()+"年"+(t2.getMonth()+1)+"月"+t2.getDate()+"日");
	}
}
function showNewCalendar(o){//点击出现日历
	c.O = $(o);
	for(var i = 0 ; i < 4 ; i++){//给对应的日期赋值
		var d = c.E[i] ? c.E[i].val().split(/,|-|\//gi) : [];
		if(d.length == 3){
			$(".dateEditBox input").eq(i).val(d[0]+"年"+d[1]+"月"+d[2]+"日");
		}else{
			$(".dateEditBox input").eq(i).val("");
		}
	}
	if(!isNaN(new Date(c.E[1].val()).getFullYear()+new Date(c.E[1].val()).getMonth())){//确认起始月份
		c.D = [new Date(c.E[1].val()).getFullYear(),new Date(c.E[1].val()).getMonth()];
	}else if(!isNaN(new Date(c.E[0].val()).getFullYear()+new Date(c.E[0].val()).getMonth())){
		c.D = [new Date(c.E[0].val()).getFullYear(),new Date(c.E[0].val()).getMonth()];
	}else{
		c.D = [new Date().getFullYear(),new Date().getMonth()];
	}
	if(c.E[3]){//确认比较是否出现
		$(".cDateEditBox").show();
	}else{
		$(".cDateEditBox").hide();
	}
	if(c.E[3] && c.E[3].val() != ""){//确认比较是否出现
		$("#dateCompareChecked").prop("checked",true);
		$("#dateCompare").prop("disabled",false);
		$(".dateEditBox").eq(1).show();
	}else{
		$("#dateCompareChecked").prop("checked",false);
		$("#dateCompare").prop("disabled",true);
		$(".dateEditBox").eq(1).hide();
	}
	$(".focusDateInput").removeClass();
	$(".dateEditBox input").eq(0).addClass("focusDateInput");
	setMonthVal();
	var topSet = $(o).offset().top+$(o).outerHeight();
	var leftSet = $(o).offset().left+$(o).outerWidth()-$(".calendarBox").outerWidth();
	$(".calendarBox").css({top:topSet,left:leftSet}).show();
	$("#calendarBg").css("width",parseInt($(".eleLeft").css("width"))+29+"px");
}

c.callBack = function(t){};

function Calendar(param) {
	//构建外部环境调用calendar方法的方法
	if(param){
		if(param["start"] && param["end"]){
			this.bindElement(param["start"], param["end"])
		}
		if(param["callback"]){
			this.callback(param["callback"]);
		}
	}
	this.bindElement = function (start, end) {
		var satrtValue = start.val() == "" ? ["","",""] : start.val().split(/\/|-|,/g);
		var endValue = end.val() == "" ? ["","",""] : end.val().split(/\/|-|,/g);
		var dateArray = satrtValue.concat(endValue);
		for(var i = 0 ; i < 6 ; i++){
			$(".editDate").find("input").eq(i).val(dateArray[i]);
		}
		c.E = [start, end];
		return this;
	};
	this.callback = function (callback) {
		c.callBack = callback;
		return this;
	}
}





function inputDate(o,ev){//显示出来的日历的输入值
	var e = ev || window.event;
	var ele = e.target || e.srcElement;
	if((e.keyCode>95 && e.keyCode<106) || (e.keyCode>47 && e.keyCode<58)){//数字键
		setTimeout(function(){
			if($(o).find("input").index(ele)==0 || $(o).find("input").index(ele)==3){
				if($(ele).val().length >= 4){
					$(o).find("input").eq($(o).find("input").index(ele)+1).focus();
				}
			}else if($(o).find("input").index(ele)==1 || $(o).find("input").index(ele)==4){
				if($(ele).val()>=2 || $(ele).val().length >= 2){
					$(o).find("input").eq($(o).find("input").index(ele)+1).focus();
				}
			}else if($(o).find("input").index(ele)==2 || $(o).find("input").index(ele)==5){
				if($(ele).val().length >= 2){
					$(o).find("input").eq(($(o).find("input").index(ele)+1)%6).focus();
				}
			}
		},10);
	}else{
		if(!(e.keyCode == 8 || e.keyCode == 9 || e.keyCode == 13 || (e.keyCode>36 && e.keyCode < 41))){//排除删除，tab，enter，方向键
			e.preventDefault();
		}
	}
}
// JavaScript Document
var del;
$("a[id='delinfo']").click(function(){
	del=$("#trbs").find("tr").index(this.parentNode.parentNode);
	$("#msg_til").text("注意信息");						  
	$("#msg_info").text("您确定删除所有保险计划信息吗？");	
	$("#msg").css({top:$("body").scrollTop()+200,left:$(window).width()/2-235});
	$("#msg").fadeIn("normal");	
	$("#msg").easydrag();
	$("#msg").setHandler("msg_ban");
	$("#msg_btn").click(function(){
		$("#trbs").find("tr").eq(del).remove();	
		$("#trbs").find("tr:odd").css({background:"#f1f4f9"});
		$("#trbs").find("tr:even").css({background:"#ffffff"});
		$("#msg").fadeOut("normal");
	});
});

$("a[id='clos_msg']").click(function(){
	$("#msg").fadeOut("normal");			
});

$("#trbs").find("tr:odd").css({background:"#f1f4f9"});

$("#trbs").find("tr").hover(
	function(){
		$(this).css({background:"#cfe4f9"});
	},
	function(){
		$("#trbs").find("tr:even").css({background:"#ffffff"});
		$("#trbs").find("tr:odd").css({background:"#f1f4f9"});
	}
	);

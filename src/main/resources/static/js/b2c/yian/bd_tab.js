


webpackJsonp([2],{

/***/ 34:
/***/ (function(module, exports) {
        var itemRightsInfoList=[]
        for (var i=0;i<plans.length;i++){
            itemRightsInfoList.push({"insurAmountDesc":plans[i].planname,"productid":plans[i].productid,"planid":plans[i].planid,"strtmon":"","plancode":"'"+plans[i].plancode+"'"})
        }
module.exports = {"itemRightsInfoList":itemRightsInfoList};
/***/ }),

/***/ 35:
/***/ (function(module, exports) {

/***/ }),

/***/ 36:
/***/ (function(module, exports) {

module.exports = "<div class=\"tabs-wrapper\">\n    <div class=\"nav\">\n        <div class=\"nav-tabs\">\n            <div class=\"tabs\">\n                {{~it.itemRightsInfoList : item}}\n                <div\n                    class=\"tab text-truncate\"\n                    onclick=\"changePlan({{=item.productid}},{{=item.planid}},{{=item.plancode}},{{=0}})\"\n                    data-tab-label-index\n                    style=\"width: {{= it.itemRightsInfoList.length <= 3 ? 100.0 / it.itemRightsInfoList.length + '%' : 'auto'}};\"\n                >\n                    <div class=\"text\">\n                        <div>\n                         <div class=\"top\">\n                                <span class=\"number\">{{=item.strtmon}}</span>\n                                <span></span>\n                            </div>\n                            <div planCode=\"{{=item.plancode}}\"\n productid=\"{{=item.productid}}\"\n planid=\"{{=item.planid}}\"\n class=\"bottom\">{{=item.insurAmountDesc}}</div>\n                        </div>\n                    </div>\n                </div>\n                {{~}}\n            </div>\n        </div>\n    </div>\n\n   </div>\n";

/***/ }),

/***/ 37:
/***/ (function(module, exports) {

module.exports = "<div class='tabs-wrapper'>\n    <div class=\"nav\">\n        <div class=\"nav-tabs\">\n            <div class=\"tabs\">\n                {{~it.itemRightsInfoList : item}}\n                <div\n                    class=\"tab text-truncate\"\n                    data-tab-label-index=\"{{=item.rightsId}}\"\n                    style=\"width: {{= it.itemRightsInfoList.length <= 3 ? 100 / it.itemRightsInfoList.length + '%' : 'auto'}};\"\n                >\n                    {{=item.insurAmountDesc}}\n                </div>\n                {{~}}\n            </div>\n        </div>\n    </div>\n\n    <div class=\"tab-content\">\n        {{~it.itemRightsInfoList : item : index}}\n        <div class=\"tab-pane\" data-tab-item-index>\n            <div>{{=index + 1}}</div>\n        </div>\n        {{~}}\n    </div>\n</div>\n";

/***/ }),

/***/ 38:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(59);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(4)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js??ref--1-1!../../../node_modules/postcss-loader/lib/index.js??postcss!../../../node_modules/sass-loader/lib/loader.js!./tab.scss", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js??ref--1-1!../../../node_modules/postcss-loader/lib/index.js??postcss!../../../node_modules/sass-loader/lib/loader.js!./tab.scss");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ }),

/***/ 43:
/***/ (function(module, exports, __webpack_require__) {

/**
 * 选项卡
 * <AUTHOR>
 * @update <EMAIL>
 */

__webpack_require__(80);
var $ = __webpack_require__(0);
var View = __webpack_require__(5);
var util = __webpack_require__(28);

var TabView = View.extend({
    currentTabIndex: undefined,
    _labels: {},
    _items: {},
    currentStyle: 'current',

    // 默认主题风格, 可选'gray'
    theme: 'white',

    events: {
        'click [data-tab-label-index]': function (e) {
            var index = $(e.currentTarget).data('tab-label-index');
            var id = $(e.currentTarget).data('second-id');
            this.selectTab(index, this.getTab(index), id);
        }
    },

    initialize: function () {
        // 调用父类view的初始化方法
        View.prototype.initialize.apply(this, arguments);
        this.$el.addClass(this.theme);
        this.findTabs();
        this.on('select', $.proxy(this.onSelect, this));
    },

    findTabs: function () {
        var that = this;
        var $labels = this.$el.find('[data-tab-label-index]');
        var $items = this.$el.find('[data-tab-item-index]');
        $items.hide();
        this.removeTab();
        $labels.each(function (i) {
            var $label = $labels.eq(i);
            var $item = $items.eq(i).length === 0 ? $items.eq($items.length - 1) : $items.eq(i);
            that.addTab($label, $item);
        });

        if ($labels.length <= 3) {
            this.$el.addClass('fit');
            $labels.css({'width': 100.0 / $labels.length + '%'});
        }
    },

    getTab: function (index) {
        return {
            $label: this._labels[index],
            $item: this._items[index]
        };
    },

    addTab: function (label, item) {
        var index = label.data('tab-label-index');
        if (!index) {
            index = this._labels.length;
            label.data('tab-label-index', index);
            item.data('tab-item-index', index);
        }

        this._labels[index] = label;
        this._items[index] = item;
    },

    removeTab: function (index) {
        if (index === undefined) {
            this._labels = [];
            this._items = [];
        }
        else {
            delete this._labels[index];
            delete this._items[index];
        }
    },

    reset: function () {
        this.removeItem();
        this.currentTabIndex = undefined;
    },

    selectTab: function (index, tab, id) {
        if (this.currentTabIndex !== undefined) {
            this._labels[this.currentTabIndex].removeClass(this.currentStyle);
            this._items[this.currentTabIndex].hide();
        }

        if (!this._labels[index]) {
            index = Object.keys(this._labels)[0];
        }
        if(index!=null) {
            this._labels[index].addClass(this.currentStyle);
            this._items[index].show();
            this.currentTabIndex = index;
            this.trigger('select', index, tab, id);
        }
    },

    onSelect: util.noop
});

module.exports = TabView;


/***/ }),

/***/ 55:
/***/ (function(module, exports, __webpack_require__) {

__webpack_require__(38);
var $ = __webpack_require__(0);
var doT = __webpack_require__(6);
var TabView = __webpack_require__(43);
var viewGray = doT.template(__webpack_require__(36));
var viewWhite = doT.template(__webpack_require__(37));

var dataMore = __webpack_require__(35);
var dataLess = __webpack_require__(34);

$('.tabs-gray-more').html(viewGray(dataMore));
$('.tabs-gray-less').html(viewGray(dataLess));
$('.tabs-white-more').html(viewWhite(dataMore));
$('.tabs-white-less').html(viewWhite(dataLess));

this.grayMoreTab = new TabView({
    el: $('.tabs-wrapper')[0],
    theme: 'gray'
});
this.grayMoreTab.selectTab(0);

this.grayLessTab = new TabView({
    el: $('.tabs-wrapper')[1],
    theme: 'gray'
});
this.grayLessTab.selectTab(0);

this.whiteMoreTab = new TabView({
    el: $('.tabs-wrapper')[2],
    theme: 'white'
});
this.whiteMoreTab.selectTab(0);

this.whiteLessTab = new TabView({
    el: $('.tabs-wrapper')[3],
    theme: 'white'
});
this.whiteLessTab.selectTab(0);

$('ul li').click(function () {
    var className = $(this).attr('class');
    if (!$(this).hasClass('active')) {
        $(this).addClass('active');
        $(this).siblings().removeClass('active');
        $('.demo').hide();
        $('.demo.' + className).show();
    }
});


/***/ }),

/***/ 59:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(3)(undefined);
// imports


// module
exports.push([module.i, ".arrow-right, .arrow-right-black {\n  content: \"\";\n  height: 0;\n  width: 0;\n  overflow: hidden; }\n\n.arrow-right {\n  display: inline-block;\n  border-left: 0.075rem solid #fa9128;\n  border-top: 0.075rem dashed transparent;\n  border-bottom: 0.075rem dashed transparent;\n  vertical-align: 0.02rem; }\n\n.arrow-right-black {\n  display: inline-block;\n  border-left: 0.075rem solid black;\n  border-top: 0.075rem dashed transparent;\n  border-bottom: 0.075rem dashed transparent;\n  vertical-align: 0.02rem; }\n\nul li.active {\n  color: white; }\n\nh3 {\n  padding: 0 0.4rem; }\n\n.demo {}\n\n.tabs-wrapper {\n  margin: 0rem 0; }\n", ""]);

// exports


/***/ }),

/***/ 65:
/***/ (function(module, exports, __webpack_require__) {

exports = module.exports = __webpack_require__(3)(undefined);
// imports


// module
exports.push([module.i, ".tabs-wrapper .nav {\n  position: relative; }\n  .tabs-wrapper .nav:after {\n    content: \"\";\n    position: absolute;\n    bottom: -0.1rem;\n    left: 0;\n    right: 0;\n    border-bottom: 0.01rem solid #e1e4eb; }\n\n.tabs-wrapper .nav-tabs {\n  overflow-x: scroll;\n  overflow-y: hidden;\n  padding-left: 0.4rem; }\n\n.tabs-wrapper .tabs {\n  min-width: 100%;\n  display: inline-block;\n  white-space: nowrap;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box; }\n  .tabs-wrapper .tabs:before, .tabs-wrapper .tabs:after {\n    display: table;\n    content: \"\"; }\n  .tabs-wrapper .tabs:after {\n    clear: both; }\n\n.tabs-wrapper .tab {\n  display: inline-block;\n  min-width: 1rem;\n  width: auto;\n  height: 1rem;\n  line-height: 1.1rem;\n  padding-right: 0.3rem;\n  font-family: PingFangSC-Medium,SourceHanSansCN-Medium;\n  color: #394259;\n  font-size: 0.32rem;\n  position: relative;\n  text-align: center;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  vertical-align: super; }\n  .tabs-wrapper .tab.current {\n    color: #fa9128; }\n  .tabs-wrapper .tab.current:after {\n    content: \"\";\n    position: absolute;\n    border-top: 0.2rem solid #fa9128;\n    bottom: 0.01rem;\n    width: 0.5rem;\n    left: 50%;\n    margin-left: -0.4rem; }\n\n.tabs-wrapper .tab-content {\n  margin-top: 0.4rem;\n  padding: 0 0.4rem; }\n\n.tabs-wrapper .tab-pane {\n  font-size: 0.28rem; }\n\n.tabs-wrapper.fit {\n  overflow: inherit; }\n  .tabs-wrapper.fit .nav:after {\n    bottom: -0.05rem; }\n  .tabs-wrapper.fit .tabs {\n    min-width: inherit;\n    display: block;\n    white-space: inherit; }\n  .tabs-wrapper.fit .tab {\n    float: left;\n    margin-right: 0;\n    padding-right: 0.4rem; }\n  .tabs-wrapper.fit .tab.current:after {\n    bottom: 0.05rem;\n    margin-left: -0.45rem; }\n  .tabs-wrapper.fit .tab + .tab {\n    margin-left: 0; }\n\n.tabs-wrapper.gray .nav {\n  z-index: 2; }\n  .tabs-wrapper.gray .nav:after {\n    content: none; }\n\n.tabs-wrapper.gray .nav-tabs {\n  overflow-x: scroll;\n  overflow-y: hidden;\n  height: 4.375rem;\n  padding: 0.1rem 0.56rem 0 0.38rem;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  background: -webkit-gradient(linear, left top, left bottom, from(white), to(#f5f5f5));\n  background: linear-gradient(to bottom, white, #f5f5f5); }\n\n.tabs-wrapper.gray .tabs {\n  height: 100%; }\n\n.tabs-wrapper.gray .tab {\n  min-width: 6rem;\n  height: 4.4rem;\n  padding-top: 0.4rem;\n  font-size: 1rem;\n  line-height: 0.75rem;\n  padding-right: 0;\n  margin-right: -0.4rem; }\n  .tabs-wrapper.gray .tab > div {\n    width: 95%;\n    margin: auto;\n    height: 100%;\n    color: #394259;\n    background-color: #f7f8fa;\n    -webkit-box-sizing: border-box;\n            box-sizing: border-box;\n    display: table; }\n    .tabs-wrapper.gray .tab > div > div {\n      display: table-cell;\n      vertical-align: middle; }\n    .tabs-wrapper.gray .tab > div div + div {\n      margin-top: 0.5rem; }\n  .tabs-wrapper.gray .tab .number {\n    font-family: PingFangSC-Medium;\n    font-size: 1.25rem;\n    line-height: 1.25em; }\n\n.tabs-wrapper.gray .tab.current > div {\n  position: relative;\n  color: #fa9128;\n  background-color: white;\n  -webkit-box-shadow: 0 -0.02rem 0.1rem rgba(160, 160, 160, 0.5);\n          box-shadow: 0 -0.02rem 0.1rem rgba(160, 160, 160, 0.5); }\n\n.tabs-wrapper.gray .tab.current > div:before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 0.19rem;\n  background-color: #fa9128; }\n\n.tabs-wrapper.gray .tab.current:after {\n  content: none; }\n\n.tabs-wrapper.gray .tab-content {\n  position: relative;\n  margin-top: -0.02rem; }\n  .tabs-wrapper.gray .tab-content:before {\n    content: \"\";\n    position: absolute;\n    top: 0rem;\n    left: 0rem;\n    width: 100%;}\n\n.tabs-wrapper.gray .tab-pane {\n  padding-top: 0.4rem; }\n\n.tabs-wrapper.gray.fit .nav-tabs {\n  padding: 0rem 1.25rem 0; }\n\n.tabs-wrapper.gray.fit .tab {\n  margin-right: 0; }\n", ""]);

// exports


/***/ }),

/***/ 80:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(65);
if(typeof content === 'string') content = [[module.i, content, '']];
// Prepare cssTransformation
var transform;

var options = {}
options.transform = transform
// add the styles to the DOM
var update = __webpack_require__(4)(content, options);
if(content.locals) module.exports = content.locals;
// Hot Module Replacement
if(false) {
	// When the styles change, update the <style> tags
	if(!content.locals) {
		module.hot.accept("!!../../../node_modules/css-loader/index.js??ref--1-1!../../../node_modules/postcss-loader/lib/index.js??postcss!../../../node_modules/sass-loader/lib/loader.js!./tab.scss", function() {
			var newContent = require("!!../../../node_modules/css-loader/index.js??ref--1-1!../../../node_modules/postcss-loader/lib/index.js??postcss!../../../node_modules/sass-loader/lib/loader.js!./tab.scss");
			if(typeof newContent === 'string') newContent = [[module.id, newContent, '']];
			update(newContent);
		});
	}
	// When the module is disposed, remove the <style> tags
	module.hot.dispose(function() { update(); });
}

/***/ })

},[55]);
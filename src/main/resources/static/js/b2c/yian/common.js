if(!window.commonjs){
window.commonjs = true;
var browserInfo=(function(){
    var browser = {}, userAgent = navigator.userAgent.toLowerCase(), s;
    (s = userAgent.match(/msie ([\d.]+)/)) ? browser.ie = s[1] : (s = userAgent.match(/([\d.]+) xiaomi\/miuibrowser/)) ? browser.xiaomi = s[1] : 
	(s = userAgent.match(/micromessenger/)) ? browser.weixin = s[1] : (s = userAgent.match(/firefox\/([\d.]+)/)) ? browser.firefox = s[1] : 
	(s = userAgent.match(/chrome\/([\d.]+)/)) ? browser.chrome = s[1] : (s = userAgent.match(/opera.([\d.]+)/)) ? browser.opera = s[1] : 
	(s = userAgent.match(/version\/([\d.]+).*safari/)) ? browser.safari = s[1] : 0;
	browser.chrome = browser.chrome && window.chrome && window.chrome.loadTimes && window.chrome.csi;
	var p = navigator.platform; 
	browser.win = p.indexOf("Win") == 0; 
	browser.mac = p.indexOf("Mac") == 0; 
	browser.x11 = (p == "X11") || (p.indexOf("Linux") == 0); 
	browser.ipad = (userAgent.match(/ipad/i) != null)?true:false;
	browser.android = userAgent.indexOf('android') > -1 || userAgent.indexOf('linux') > -1;
	browser.ios = !!userAgent.match(/\(i[^;]+;( u;)? cpu.+max os x/);
	return browser;
})()
//弹出层的处理，层级间的覆盖关系-----------------------------------------------------------------------------------------
function scren_point(param_object){//设置弹出层位置
	//jQuery("body").css("overflow","hidden");
	var html_width = jQuery(window).width();
	var html_height = jQuery(window).height();
	var user_width = param_object.css("width");
	var user_height = param_object.css("height");
	var scroTop = jQuery(document).scrollTop();
	var scroLeft = jQuery(document).scrollLeft();
	var user_left = (parseInt(html_width) - parseInt(user_width))/2+scroLeft;
	var user_top = (parseInt(html_height) - parseInt(user_height))/2+scroTop;
	user_top = user_top < 0 ? 0 : user_top;
	if(parent.document != document){
		user_left = user_left-parent.l/2;
	}
	user_left < 0 ? user_left = 0 : "";
	//if(parseInt(html_height) - parseInt(user_height) < 100){
		//param_object.find(".info_edit_body").css({"overflow":"auto","height":parseInt(html_height)-100})
	//}
	param_object.css("top",user_top);
	param_object.css("left",user_left);
	param_object.show();
	jQuery("#opa").show();
	if(parent.document != document){
		jQuery("#opaTop,#opaLeft",parent.document).show()
	}
	jQuery("body").css("overflow","hidden");
	//jQuery("#iframeBox iframe",parent.document).css("z-index",2);
}

function hidePopup(){//关闭设置弹出层位置
	var ar = arguments,len = ar.length,i;
	if(len > 0){
		for(i = 0;i < len ;i++){
			if(typeof ar[i] != "undefined"){
				if(ar[i] instanceof jQuery){
					ar[i].hide();
				}else{
					try{jQuery(ar[i]).hide();}catch(e){}
				}
			}
		}
	}
	if(parent.document != document){
		jQuery("#opaTop,#opaLeft",parent.document).hide()
	}
	jQuery("#opa").hide();
	//jQuery("#iframeBox iframe",parent.document).css("z-index",0);
	jQuery("body").css("overflow","auto");
}
//弹出层的处理，层级间的覆盖关系-----------------------------------------------------------------------------------------

//加载后根据菜单自动调节框架内内容的宽高比例-----------------------------------------------------------------------------
if(parent.document != document){//如果是iframe页面就会进行宽度调整
	/* jQuery("#iframeBox iframe",parent.document).css("z-index",0);
	var p = parent.document;
	if(jQuery("#leftmenu",p).is(":hidden")){
		var l = parseInt(jQuery("#tgMenuBg",p).css("width"));
	}else{
		var l = parseInt(jQuery("#leftmenu",p).css("width"));

	}
	var t = parseInt(jQuery(".pageHeader",p).css("height"));
	var b = parseInt(jQuery("#pageFoot",p).css("height"));
	jQuery(".col_main").css({"left":l,"top":t,"right":0,"bottom":b,"position":"absolute","overflow":"auto"}); */
	jQuery("body").append("<div id='clickBox'></div>");//添加父页面点击事件传递到子页面时的接收点
	jQuery("#clickBox").click();//初始化某些需要点击才会初始化的插件
}
//加载后根据菜单自动调节框架内内容的宽高比例-----------------------------------------------------------------------------


//判断是否需要加载新框架-----------------------------------------------------------------------------
jQuery(document).on("click","a",function(e){
	if(jQuery(this).prop("target") == "navTab"){
		window.parent.editMenuState(jQuery(this).prop("href"),jQuery(this).prop("rel"),jQuery(this).prop("title"),e);
	}
})
jQuery(document).on("click",function(e){
	if(parent.document != document && e.target.id != "clickBox"){//iframe内部点击会影响主页面的某些操作，传递点击动作
		jQuery(parent.document).find("#clickBox").click();
	}
})

jQuery(document).on("keydown",function(e){
	if(e.keyCode == 116){
		window.location = location.href;
		e.preventDefault();
	}
})

//翻页函数-------------------------------------------------------------

if(parseInt($("#curpage").html())=="1"){
	$(".page_pre").hide();
	$(".firstpage").hide();
}else{
	$(".page_pre").show();
	$(".firstpage").show();
}
if(parseInt($("#pagenum").html())==parseInt($("#curpage").html())){
	$(".page_nex").hide()
	$(".lastpage").hide();
}else{
	$(".page_nex").show();
	$(".lastpage").show();
}
$("#curpage").html()=="1"&&$("#pagenum").html()=="1"?$(".pagego").hide():$(".pagego").show();
$(".page_nex").click(function(){
	$("input[name='pageNumber']").val(parseInt($("#curpage").html())+1);
	var p = $("input[name='pageNumber']").closest("form");
	if(p.length == 0){
		$("input[name='page.pageNumber']").val(parseInt($("#curpage").html())+1);
		var p = $("input[name='page.pageNumber']").closest("form");
	}
	var daction = p.attr("daction") || p.prop("action") || "";
	p[0].action = daction;
	loadingNow("加载中...");
	p.submit();
});
$(".page_pre").click(function(){
	$("input[name='pageNumber']").val(parseInt($("#curpage").html())-1);
	var p = $("input[name='pageNumber']").closest("form");
	if(p.length == 0){
		$("input[name='page.pageNumber']").val(parseInt($("#curpage").html())-1);
		var p = $("input[name='page.pageNumber']").closest("form");
	}
	var daction = p.attr("daction") || p.prop("action") || "";
	p[0].action = daction;
	loadingNow("加载中...");
	p.submit();
});
$(".lastpage").click(function(){
	$("input[name='pageNumber']").val(parseInt($("#pagenum").html()));
	var p = $("input[name='pageNumber']").closest("form");
	if(p.length == 0){
		$("input[name='page.pageNumber']").val(parseInt($("#pagenum").html()));
		var p = $("input[name='page.pageNumber']").closest("form");
	}
	var daction = p.attr("daction") || p.prop("action") || "";
	p[0].action = daction;
	loadingNow("加载中...");
	p.submit();
});
$(".firstpage").click(function(){
	$("input[name='pageNumber']").val("1");
	var p = $("input[name='pageNumber']").closest("form");
	if(p.length == 0){
		$("input[name='page.pageNumber']").val("1");
		var p = $("input[name='page.pageNumber']").closest("form");
	}
	var daction = p.attr("daction") || p.prop("action") || "";
	p[0].action = daction;
	loadingNow("加载中...");
	p.submit();
})
$("#goPage").click(function(){
	if(parseInt($(".tex").val())<1||parseInt($(".tex").val())>parseInt($("#pagenum").html())){
		alertMsg("跳转页码不在范围内！");
		return;
	}
	$("input[name='pageNumber']").val(parseInt($(".tex").val()));
	var p = $("input[name='pageNumber']").closest("form");
	if(p.length == 0){
		$("input[name='page.pageNumber']").val(parseInt($(".tex").val()));
		var p = $("input[name='page.pageNumber']").closest("form");
	}
	var daction = p.attr("daction") || p.prop("action") || "";
	p[0].action = daction;
	loadingNow("加载中...");
	p.submit();
});
//翻页函数结束-------------------------------------------------------------

//翻写的alert弹出窗口
// function alertMsg(msg,fun){
// 	if(parent.document != document && parent.alertMsg){
// 		parent.alertMsg(msg,fun);
// 	}else{
// 		$("#dialog_content").html(msg);
// 		var bl = belongIframe();
// 		var zidx = $(bl).css("z-index");
// 		var html_width = jQuery(window).width();
// 		var html_height = jQuery(window).height();
// 		var user_width = $("#dialog").css("width");
// 		var user_height = $("#dialog").css("height");
// 		var scroTop = jQuery(document).scrollTop();
// 		var scroLeft = jQuery(document).scrollLeft();
// 		var user_left = (parseInt(html_width) - parseInt(user_width))/2+scroLeft;
// 		var user_top = (parseInt(html_height) - parseInt(user_height))/2+scroTop;
// 		user_top = user_top < 0 ? 0 : user_top;
// 		if(parent.document != document){
// 			user_left = user_left-parent.l/2;
// 		}
// 		user_left < 0 ? user_left = 0 : "";
// 		$("#dialog").css("top",user_top);
// 		$("#dialog").css("left",user_left);
// 		$(bl).css("z-index",999);
// 		$("#dialog,#dialogOpa").show();
// 		$("#dialog").data({"callback":fun,"bl":bl,"zidx":zidx});
// 	}
// }

	//弹出的对话框start-----------------------------------------------------------------
	/*用法1：alertMsg("message", "title", "callback");title和callback可以互换
	 *用法2: alertMsg({
	  message:"message",
	  title:"title",
	  menus:[{
	  	text:"确定",
       	callback:funciton(){}
	  }]
	  })
	 */
	function alertMsg() {
		if(window.top == window || !window.top.alertMsg){
			var message, title,width, menus = [], topcallback;
			if(typeof arguments[0] == "object"){
				message = arguments[0]["message"];
				title = arguments[0]["title"];
				menus = arguments[0]["menus"];
				width = arguments[0]["width"];
				topcallback = arguments[0]["callback"];
			}else{
				message = arguments[0];
				for(var i = 1; i < 3; i++){
					if(typeof arguments[i] == "function"){
						menus.push({
							"text":"确定",
							"callback": arguments[i]
						});
					}else if(arguments[i]){
						title = arguments[i];
					}
				}
			}
			var marginLeft = 0;
			if(!width){
				if(this.document){
					var iframes = $("iframe", parent.document);
					for(var i = 0; i < iframes.length; i++){
						if(iframes[i].contentWindow == this){
							marginLeft = parseFloat(iframes.eq(i).closest(".overlayContent").css("left"));
							if(!isNaN(marginLeft)){
								marginLeft = marginLeft / 2;
							}
							break;
						}
					}
					getCurrentIframe()
					width = $(this.document).find(".matCardGroup").width() / 2;
				}else{
					width = $(".matCardGroup").width() / 2;
				}
			}
			if(!menus || menus instanceof Array && menus.length == 0){
				menus = [{"text":"确定"}];
			}
			if($("#alertMessageStyle").length == 0){
				$("<style type='text/css' id='alertMessageStyle'>" +
					".alertMessageBox .messageButton{text-align: center;font-size: 14px;color:#b8b8b8; padding: 0px 8px;min-width: 64px;height: 32px;line-height: 32px;border:none;cursor: pointer;display: inline-block;transition: all .2s;background-color: rgba(0,0,0,0);}" +
					".alertMessageBox .messageButton:hover{background-color: rgba(0,0,0,.04);}" +
					"</style>").appendTo("body");
			}
			var messagegBox = $("<div class='alertMessageBox' style='position: fixed;left: 0;right: 0;top: 0;bottom: 0;background-color: rgba(0,0,0,0.6);z-index:9999;opacity: 0;'>" +
				"<div style='color:#5f6368; position: absolute;left: 50%;top: 50%;min-width: 300px;width:" + width + ";margin-left:" + marginLeft + "px;'>" +
				"	<div class='messageWrapper' style='position: relative;left: -50%;background-color: #fff;border-radius: 4px;transform:scale(0);transition: transform " + 300 + "ms;'>" +
				"		<div style='font-size: 16px;line-height: 55px;height: 55px;border-bottom: 1px solid #dadce0;padding: 0 24px;white-space: nowrap;overflow: hidden;'>" + (title ? title : "提示信息") + "</div>" +
				"		<div style='padding:24px 48px;line-height: 26px;max-height: " + ($(window).height() * 0.8 - 100) + "px;overflow: auto;word-break: break-all;'> " + message + "</div>" +
				"		<div style='padding: 8px 0;height: 52px;box-sizing: border-box;text-align: right;'>" +
				(function () {
					var menuText = "";
					for(var i  = 0; i < menus.length; i++){
						var style = menus[i]["style"] ? ("style='" + menus[i]["style"] + "'") : "";
						menuText += "<div " + style + " class='messageButton dialogConfirm animationRipple' type='button'>" + menus[i]["text"] + "</div>";
					}
					return menuText;
				})()+
				"		</div>" +
				"	</div>" +
				"</div>" +
				"</div>").appendTo("body");
			messagegBox.find(".messageWrapper").each(function () {
				$(this).css("top", "-" + ($(this).height() / 2) + "px");
				$(this).css("transform", "scale(1)");
			});
			messagegBox.animate({"opacity": 1}, 300);
			messagegBox.on("click",".dialogConfirm,.dialogClose",function(){
				var index = $(this).index();
				if(!menus[index]["remain"]){
					messagegBox.find(".messageWrapper").css("transform", "scale(0)");
					messagegBox.animate({"opacity": 0}, 300);
				}
				setTimeout(function () {
					if(menus[index]["callback"]){
						menus[index]["callback"](messagegBox);
					}
					if(!menus[index]["remain"]){
						messagegBox.remove();
					}
				}, 300);

			}).on("click", ".needcallback", function () {
				if(topcallback){
					var callbackParam = $(this).attr("callbackParam");
					var remain = $(this).attr("remain");
					if(!remain) {
						messagegBox.find(".messageWrapper").css("transform", "scale(0)");
						messagegBox.animate({"opacity": 0}, 300);
					}
					setTimeout(function () {
						topcallback(callbackParam);
						if(!remain){
							messagegBox.remove();
						}
					}, 300);
				}
			});
			return messagegBox;
		}else{
			return window.top.alertMsg.apply(window, arguments);
		}
	}

	function getCurrentIframe() {
		var iframes = $("iframe", parent.document);
		for(var i = 0; i < iframes.length; i++){
			if(iframes[i].contentWindow == window){
				return iframes.eq(i);
			}
		}
	}

//关闭翻写的alert弹出窗口
$("#dialog").on("click",".info_edit_cancel,.info_edit_close",function(){
	$("#dialog,#dialogOpa").hide();
	try{
		$($("#dialog").data("bl")).css("z-index",$("#dialog").data("zidx"));
	}catch(e){}
	try{
		$("#dialog").data("callback")();
	}catch(e){}
	$("#dialog").removeData();
})


function loadingNow(){//加载时出现的函数loadingBox
	if(parent.document == document){
		if(arguments.length > 0){
			$("#loadingBox").html(arguments[0]);
		}else{
			$("#loadingBox").html("保存信息...");
		}
		var bl = belongIframe();
		var zidx = $(bl).css("z-index");
		var html_width = jQuery(window).width();
		var html_height = jQuery(window).height();
		var user_width = $("#loadingBox").css("width");
		var user_height = $("#loadingBox").css("height");
		var scroTop = jQuery(document).scrollTop();
		var scroLeft = jQuery(document).scrollLeft();
		var user_left = (parseInt(html_width) - parseInt(user_width))/2+scroLeft;
		var user_top = (parseInt(html_height) - parseInt(user_height))/2+scroTop;
		user_top = user_top < 0 ? 0 : user_top;
		if(parent.document != document){
			user_left = user_left-parent.l/2;
		}
		user_left < 0 ? user_left = 0 : "";
		$("#loadingBox").css("top",user_top);
		$("#loadingBox").css("left",user_left);
		$(bl).css("z-index",999);
		$("#loadingBox,#loadingOpa").show();
		$("#loadingBox").data({"bl":bl,"zidx":zidx});
	}else{
		jQuery(parent.document).find("#closeOpaC").html(arguments[0] || "保存信息...");
		var w = jQuery(parent.window).width();
		var uw = jQuery(parent.document).find("#closeOpaC").css("width");
		var ul = (parseInt(w) - parseInt(uw))/2;
		jQuery(parent.document).find("#closeOpaC").css({"left":ul,"top":0}).show();
		jQuery(parent.document).find("#closeOpa,#opaTop,#opaLeft").show();
	}
}
function loadingComplete(){//加载完成时出的函数
	if(parent.document == document){
		try{
			$($("#loadingBox").data("bl")).css("z-index",$("#loadingBox").data("zidx"));
		}catch(e){}
		$("#loadingBox,#loadingOpa").hide();
		$("#loadingBox").removeData();
	}else{
		jQuery(parent.document).find("#closeOpa,#closeOpaC,#opaTop,#opaLeft").hide();
	}
}

function belongIframe(){//查找自身所属iframe
	var i,ifr = parent.document.getElementsByTagName("iframe"),len = ifr.length;
	for(i = 0 ; i < len ; i++){
		if(ifr[i].contentWindow.document == document){
			return ifr[i];
		}
	}
}
function blink(o){//单元格闪烁
	var _time , i = 0;
	var color = o.css("background-color");
	o.focus();
	_time = setInterval(function(){
		if(i == 7){
			clearInterval(_time);
		}
		i++;
		if(i%2==1){
			o.addClass("errbackground");
		}else{
			o.removeClass("errbackground");
		}
	},80)
}

//多项选择的下拉框开始-----------------------------------------------------------------
var multipleSelectSrc = $();//触发多选下拉框的元素
var multipleSelectShow = true;//是否能事件触发关闭多选下拉框
/* param = {
	list:["亚洲":[{partition: "亚洲", name: "阿富汗", cid: 4098, supported: 1}]],//备选项目
	deviationX:10,//水平位置偏移
	deviationY:10,//垂直位置偏移，对计算好的位置进行微调
	css:{"width":600},//多选下拉框最外层的css设置
	max:10,//最多能选多少个
	titleSelectable:true,//是否能选择标题后把所属标题的国家全选
	valueSrc:$()//隐藏值的元素

}
调用方法：
$(triggerElement).multipleSelect({"valueSrc":$(valueElement),"list":["亚洲":[{partition: "亚洲", name: "阿富汗", cid: 4098, supported: 1}]]})
或者
$(triggerElement).multipleSelect().data({"valueSrc":$(valueElement),"list":["亚洲":[{partition: "亚洲", name: "阿富汗", cid: 4098, supported: 1}]]})
或者
<input type="text" valuesrc="#destinationCode" multipleselect />
<input type="hidden" id="destinationCode" />
*/
jQuery.fn.multipleSelect = function(param){//触发多选下拉框的元素及其参数
	if($(this).length > 0){
		multipleSelectSrc = multipleSelectSrc.add($(this).data(param));
	}
	return $(this);
}
$(document).on("click focus",function(e){//触发多选国家，包括构建国家，填充国家
	var _this = $(e.target);
	var sort = {"亚洲":[],"欧洲":[],"非洲":[],"北美洲":[],"南美洲":[],"大洋洲":[],"南极洲":[]};
	if(_this.closest(multipleSelectSrc.add("[multipleSelect]")).length > 0){
		if(_this.data("list")){
			for(var i in _this.data("list")){
				sort[i] = _this.data("list")[i];
			}
			_this.data("list",sort);
			buildMultipleSelect();
			var title = "<div class='titleMenuWrap clearfix'>", selectable = "", unselectable = "", width, selected, selectedStr;
			if(_this.data("titleSelectable")){
				$(".multipleSelectTitle").addClass("titleSelectable")
			}else{
				$(".multipleSelectTitle").removeClass("titleSelectable")
			}
			for(var i in _this.data("list")){
				if(_this.data("list")[i].length > 0){
					var titleSelectable = "",searchList = "";
					if(_this.data("titleSelectable")){
						titleSelectable = "<input type='checkbox' class='titleSelectable' kinds='"+i+"'/>";
					}
					title += "<div class='titleMenu' kinds='"+i+"'>"+titleSelectable+i+"</div>";
					for(var j = 0; j < _this.data("list")[i].length; j++){
						width = Math.ceil((_this.data("list")[i][j]["name"].length + 2) / 11);
						width > 3 &&  (width = 3);
						if(_this.data("list")[i][j]["supported"] == 1 || typeof _this.data("list")[i][j]["supported"] == "undefined"){
							selectable += "<div class='optionBox selectable width"+width+"' kinds='"+i+"' supported='1' show='"+_this.data("list")[i][j]["name"]+"' pinyin='"+(""+_this.data("list")[i][j]["pinyin"]).toUpperCase()+"' pinyinAbbr='"+(""+_this.data("list")[i][j]["pinyinAbbr"]).toUpperCase()+"'><label><span><input val='"+_this.data("list")[i][j]["cid"]+"' kinds='"+i+"' show='"+_this.data("list")[i][j]["name"]+"' class='option' type='checkbox'><span></span></span><div class='nationalFlag'><img src='data:image/png;base64,"+countryIconList[_this.data("list")[i][j]["code"]]+"'></div><span class='optionStr'>"+_this.data("list")[i][j]["name"]+"</span></label></div>";
						}else{
							unselectable += "<div class='optionBox unselectable width"+width+"' kinds='"+i+"' supported='0' searchList='"+searchList+"' show='"+_this.data("list")[i][j]["name"]+"' pinyin='"+(""+_this.data("list")[i][j]["pinyin"]).toUpperCase()+"' pinyinAbbr='"+(""+_this.data("list")[i][j]["pinyinAbbr"]).toUpperCase()+"'><label><span><input val='"+_this.data("list")[i][j]["cid"]+"' kinds='"+i+"' show='"+_this.data("list")[i][j]["name"]+"' class='option' disabled type='checkbox'><span></span></span><div class='nationalFlag'><img src='data:image/png;base64,"+countryIconList[_this.data("list")[i][j]["code"]]+"'></div><span class='optionStr'>"+_this.data("list")[i][j]["name"]+"</span></label></div>";
						}
					}
				}
			}
			title += "<div class='titleMenu isSelected' kinds='已选择'>已选择</div></div><div class='searchTitle'><div class='searchTitleBox'><input placeholder='在此输入快速检索' type='text' class='searchBox'></div></div><p class='multipleSelectClose'>×</p>";
			$(".multipleSelectBox .multipleSelectTitle").html(title);
			$(".multipleSelectBox .mainListBox").html(selectable + unselectable);
			//$(".multipleSelectBox .mainListBox .unselectable").html(unselectable);
			if(_this.data("valueSrc")){
				selected = $(_this.data("valueSrc")).val();
			}else if(_this.attr("valueSrc")){
				selected = $(_this.attr("valueSrc")).val();
			}else{
				selectedStr = _this.val();
			}
			if(selected && selected.split(",").length > 0){
				for(var i = 0; i < selected.split(",").length; i++){
					$(".multipleSelectBox .mainListBox .selectable").find(".option[val='"+selected.split(",")[i]+"']").prop("checked",true);
				}
			}else if(selectedStr && selectedStr.split(",").length > 0){
				for(var i = 0; i < selectedStr.split(",").length; i++){
					$(".multipleSelectBox .mainListBox .selectable").filter(".optionBox[show='"+selectedStr.split(",")[i]+"']").find(".option").prop("checked",true);
				}
			}
			$(".multipleSelectBox .multipleSelectTitle").find(".titleMenu:eq(0)").click();
			if(_this.data("titleSelectable")){
				$(".titleSelectable").each(function(){
					var kind = $(this).attr("kinds");
					var kinds = $(".multipleSelectBox .mainListBox .selectable").find(".option[kinds='"+kind+"']");
					$(this).prop("checked",kinds.length == kinds.filter(":checked").length);
				})
			}
			$("body").animate({"scrollTop":_this.offset().top},500);
			$(".multipleSelectBox").data("src",_this).show();
			$(".multipleSelectBox .searchBox").width($(".multipleSelectBox").width() - $(".multipleSelectBox .titleMenuWrap").width() - 50);
			locationMultipleSelect();
		}
	}
}).on("click",".multipleSelectTitle .titleMenu",function(){//点击分组标题
	$(".multipleSelectBox .searchBox").val("");
	$(".multipleSelectBox .isSelected").prop("checked",false);
	var kinds = $(this).attr("kinds");
	$(".multipleSelectTitle .titleMenu").removeClass("selected");
	$(this).addClass("selected");
	$(".multipleSelectBox .mainListBox").find(".optionBox").hide().filter("[kinds='"+kinds+"']").show();
	if($(this).closest(".isSelected").length > 0){
		$(".multipleSelectBox .mainListBox .optionBox").hide().filter(function(){return $(this).find(".option").is(":checked")}).show();
	}
}).on("click",".multipleSelectTitle .titleSelectable",function(){//点击按组全选按钮
	if($(this).is(":checked")){
		$(".multipleSelectBox .mainListBox .selectable").filter(".optionBox[kinds='"+$(this).attr("kinds")+"']").find(".option").prop("checked",true).change();
	}else{
		$(".multipleSelectBox .mainListBox .selectable").filter(".optionBox[kinds='"+$(this).attr("kinds")+"']").find(".option").prop("checked",false).change();
	}
}).on("click",".multipleSelectBox .unselectable label",function(){//点击不可选国家的提示
	if($(this).closest(".optionBox").attr("supported") ==0){
		multipleSelectShow = false;
		alertMsg("本计划不承保"+$(this).find(".optionStr").html(),function(){setTimeout(function(){multipleSelectShow = true;},1)});
	}
}).on("change",".multipleSelectBox .multipleSelectBody .option",function(){//选择某个国家的处理
	var _this = $(".multipleSelectBox").data("src"), valueSrc = _this.data("valueSrc") || _this.attr("valueSrc");
	var kind = $(this).attr("kinds");
	var kinds = $(".multipleSelectBox .mainListBox .selectable").find(".option[kinds='"+kind+"']");
	var selected = $(".multipleSelectBox .mainListBox .option:checked");
	if(_this.data("max") && selected.length > _this.data("max") && _this.data("max") > 1){
		multipleSelectShow = false;
		alertMsg("本计划最多只能选择"+_this.data("max")+"个国家",function(){setTimeout(function(){multipleSelectShow = true;},1)});
		$(this).prop("checked",false);
	}else{
		if(_this.data("max") && _this.data("max") == 1 && $(this).is(":checked")){
			$(valueSrc).val($(this).attr("val"));
			$(_this).val($(this).attr("show")).change();
			$(".multipleSelectBox").hide();
		}else{
			$(valueSrc).val(selected.map(function(){return $(this).attr("val")}).get().join(","));
			$(_this).val(selected.map(function(){return $(this).attr("show")}).get().join(",")).change();
		}
	}
	$(".multipleSelectTitle .titleSelectable[kinds='"+kind+"']").prop("checked",kinds.length == kinds.filter(":checked").length);
}).on("focus click",function(e){//触发多选国家的隐藏
	if(!$(".multipleSelectBox").is(":hidden") && multipleSelectShow){
		if($(e.target).closest(multipleSelectSrc.add("[multipleSelect],.multipleSelectBox,.multipleSelectConfirm")).length == 0){
			$(".multipleSelectBox").hide();
		}
		if($(e.target).closest(".multipleSelectClose").length > 0){
			$(".multipleSelectBox").hide();
		}
	}
}).on("mousewheel",".multipleSelectBody",function(e){//监控鼠标滚轮，防止国家控件滚动完毕后页面会接着滚动
	/*var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) ||  // chrome & ie
                (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1));
	if(delta > 0 && $(".multipleSelectBody").scrollTop() == 0){//向上滚
		e.preventDefault();
	}else if(delta < 0 && $(".multipleSelectBody")[0].scrollTop + $(".multipleSelectBody")[0].clientHeight == $(".multipleSelectBody")[0].scrollHeight){//向下滚
		e.preventDefault();
	}*/
}).on("keydown keyup click",".multipleSelectBox .searchBox",function(){//给搜索国家输入框填充内容
	var _this = $(this);
	$(".multipleSelectBox .isSelected").prop("checked",false);
	setTimeout(function(){
		if(_this.val() == ""){
			$(".titleMenu.selected").click();
			//$(".multipleSelectBox .mainListBox .optionBox").hide();
		}else{
			var _all = $(".multipleSelectBox .mainListBox").find(".optionBox").hide();
			var _ele = _all.filter("[show*='"+_this.val().toUpperCase()+"']");
				_ele = _ele.add(_all.filter("[pinyin^='"+_this.val().toUpperCase()+"']"));
				_ele = _ele.add(_all.filter("[pinyinAbbr^='"+_this.val().toUpperCase()+"']"));
			_ele.show();
		}

	},10)
}).on("change",".multipleSelectBox .isSelected",function(){//点击仅显示已有国家
	$(".multipleSelectBox .searchBox").val("");
	if($(this).is(":checked")){
		$(".multipleSelectBox .mainListBox .optionBox").hide().filter(function(){return $(this).find(".option").is(":checked")}).show();
	}else{
		$(".titleMenu.selected").click();
		//$(".multipleSelectBox .mainListBox .optionBox").hide();
	}
}).on("mouseover",".multipleSelectBox .optionBox",function(e){//鼠标在备选项的地方背景色变色
	$(".multipleSelectBox .hoverBackgroundList").removeClass("ishover")
	if($(".multipleSelectBox .optionBox").not(":hidden").length > 0){
		var _height = Math.floor($(this).position().top/$(".multipleSelectBox .hoverBackgroundList").height());
		$(".multipleSelectBox .hoverBackgroundList").eq(_height).addClass("ishover");
	}
}).on("mouseover",".multipleSelectBox .hoverBackgroundList",function(e){//鼠标在有背景但是没有备选项的地方
	$(".multipleSelectBox .hoverBackgroundList").removeClass("ishover")
	if($(".multipleSelectBox .optionBox").not(":hidden").length > 0){
		if($(this).position().top < $(".multipleSelectBox .optionBox").not(":hidden").filter(":last").position().top){
			$(this).addClass("ishover")
		}
	}
}).on("mouseleave",".multipleSelectBox .multipleSelectBody",function(){
	$(".multipleSelectBox .hoverBackgroundList").removeClass("ishover")
})
var locationMultipleSelectTime;
function locationMultipleSelect(){//给多选国家控件定位;当触发元素位置变动时，多选下拉框位置紧跟着调整
	clearInterval(locationMultipleSelectTime);
	var _this = $(".multipleSelectBox").data("src");
	var _top = $(_this).offset().top + $(_this).outerHeight(),
		_left = $(_this).offset().left;
		parseInt($(_this).data("deviationX")) && (_left += parseInt($(_this).data("deviationX")));
		parseInt($(_this).data("deviationY")) && (_top += parseInt($(_this).data("deviationY")));
	$(".multipleSelectBox").css({"top":_top,"left":_left});
	locationMultipleSelectTime = setInterval(function(){
		if($(".multipleSelectBox").is(":hidden")){
			clearInterval(locationMultipleSelectTime);
		}else{
			_top = $(_this).offset().top + $(_this).outerHeight(),
			_left = $(_this).offset().left;
			parseInt($(_this).data("deviationX")) && (_left += parseInt($(_this).data("deviationX")));
			parseInt($(_this).data("deviationY")) && (_top += parseInt($(_this).data("deviationY")));
			$(".multipleSelectBox").css({"top":_top,"left":_left});
		}
	},1)
}
function buildMultipleSelect(){//构建多选国家框架，创建多加国家的css
	if($(".multipleSelectBox").length ==0){
		$("<div class='multipleSelectBox'><div class='multipleSelectTitle clearfix'></div><div class='multipleSelectBody'><div class='hoverBackground'></div><div class='mainListBox clearfix'></div></div>").appendTo("body");
		var hoverBackgroundList = "";
		for(var i = 0; i < 16; i++){
			hoverBackgroundList += "<div class='hoverBackgroundList'></div>"
		}
		$(".multipleSelectBox .hoverBackground").html(hoverBackgroundList);
		//$("<style type='text/css'>.multipleSelectBox{width:630px;box-shadow:0 2px 7px rgba(0,0,0,0.8);background-color:#fff;position:absolute;top:100px;left:0;color:#666;}.multipleSelectTitle{height:25px;border-bottom:1px solid #ccc;padding:10px 10px 0;position:relative;}.multipleSelectBox .searchTitle{position:absolute;top:0;left:0;right:0;bottom:35px;overflow:hidden;padding-top:6px;background-color:#fff;}.searchTitleBox{height:29px;border-bottom:1px solid #ccc;padding-left:10px;}.searchTitle label{margin-top:5px;}.selectedTitle{margin-left:20px;}.multipleSelectBox .isSelected{vertical-align:-2px;}.returnButton{top:10px;right:10px;position:absolute;}.titleMenu,.searchButton,.returnButton{border-bottom:3px solid transparent;height:23px;line-height:25px;font-size:12px;margin:0 10px;text-align:center;float:left;cursor:pointer;user-select:none;-ms-user-select:none;-moz-user-select:none;-webkit-user-select:none;}.searchButton{float:right;}.titleMenu:hover,.searchButton:hover{border-bottom-color:rgba(53,154,255,0.1);}.titleMenu.allSelected{border-bottom:2px solid #ffb408;}.titleMenu.selected,.searchButton.selected,.returnButton{color:#3b7bea;border-bottom-color:#3b7bea;background-color:#fff;font-weight:800;}.multipleSelectBody{padding-top:10px;padding-left:10px;height:530px;overflow-x:hidden;overflow-y:auto;position:relative;}.multipleSelectBody .nationalFlag{display:inline-block;width:20px;margin:0 2px;}.multipleSelectBody .hoverBackground{position: absolute;left: 0;right: 0;}.multipleSelectBody  .hoverBackgroundList{position: relative;height: 32px;width: 100%;z-index: 1;}.multipleSelectBody  .hoverBackgroundList.ishover{z-index: 0;background-color:#eee;}.multipleSelectBody .nationalFlag img{max-height:15px;max-width:20px;vertical-align:middle;}.multipleSelectBody .optionBox label{line-height:20px;position:relative;}.multipleSelectBody .optionBox .option{vertical-align:-3px;}.multipleSelectBody .unselectable label{text-decoration:line-through;position:relative;}.multipleSelectBody .unselectable label:before{content:' ';position:absolute;width:10px;height:10px;border-radius:2px;border:1px solid #ccc;background-color:#eee;margin-top:5px;}.multipleSelectBody .unselectable .option{visibility:hidden;}.multipleSelectBody .optionBox{float:left;width:150px;height:16px;padding:8px 0;}.multipleSelectBody .optionBox.width2{width:300px;}.multipleSelectBody .optionBox.width3{width:450px;}.multipleSelectBox .unselectable{color:#aaa;}.multipleSelectFoot{height:15px;padding:0 0 10px;font-size:12px;}.multipleSelectConfirm{color:#3b7bea;cursor:pointer;float:right;margin-right:20px;height:15px;line-height:15px;}.multipleSelectConfirm:hover{font-weight:800;}</style>").appendTo("body");
	}
}
//多项选择的下拉框结束-----------------------------------------------------------------
function zerofill(s) {
	var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
	s = isNaN(s) ? 0 : s;
	return (s < 10 ? '0' : '') + s.toString();
}
//格式化证件号码(idcard)，电话(tel)和银行卡号(bank)，在需要的input上加上fmt="idcard"的属性进行判定
//并重写jquery的val方法
jQuery(function(){
	if(browserInfo.win){//电脑用版本
		jQuery("body").on("focus","input[type='text'],input[type='password']",function(){
			var _this = jQuery(this);
			if(!(_this.innerWidth() < 50 || _this.hasClass("noClear") || _this.is("[noClear]") || _this.prop("readonly") || _this.prop("disabled"))){
				_this.addClass("clearFormat");
			}
		}).on("mouseenter","input[type='text'],input[type='password']",function(e){
			var _this = jQuery(this);
			if(!(_this.innerWidth() < 50 || _this.hasClass("noClear") || _this.is("[noClear]") || _this.prop("readonly") || _this.prop("disabled")) && _this.val() != ""){
				_this.addClass("clearFormat");
			}
		}).on("mouseleave",".clearFormat",function(){
			if(jQuery(this).is(":focus")){
				jQuery(this).removeClass("clearFormatHover");
			}else{
				jQuery(this).removeClass("clearFormat").removeClass("clearFormatHover");
			}
		}).on("mousedown","input.clearFormatHover",function(e){
			if(jQuery(this).css("cursor") == "pointer"){
				jQuery(this).val("");
			}
		}).on("mousemove","input.clearFormat",function(e){
			var w = jQuery(this).innerWidth() - e.offsetX;
			if(w < 20){
				jQuery(this).addClass("clearFormatHover");
			}else{
				jQuery(this).removeClass("clearFormatHover");
			}
		}).on("blur","input.clearFormat",function(){
			jQuery(this).removeClass("clearFormat").removeClass("clearFormatHover");
		})
	}else{//手机用版本
		jQuery("body").on("focus","input[type='text'],input[type='password'],input[type='number']",function(){
			var _this = jQuery(this);
			if(!(_this.innerWidth() < 50 || _this.hasClass("noClear") || _this.is("[noClear]") || _this.prop("readonly"))){
				_this.addClass("clearFormat");
			}
		}).on("touchstart","input.clearFormat",function(e){
			var _this = this;
			//元素相对body位置+元素宽度-touchpoint相对body所在位置
			var w = $(this).offset().left + jQuery(this).innerWidth() - e.originalEvent.targetTouches[0].pageX;
			if(w < 40){
				jQuery(this).val("");
				//应对苹果浏览器下拼音清空后不能输入的问题
				$(_this).blur();
				setTimeout(function(){
					$(_this).focus();
				},100)
			}
		}).on("blur","input.clearFormat",function(){
			jQuery(this).removeClass("clearFormat");
		})
	}
	jQuery("html").on("keydown","input[dateTime]",function(e){
		var _this = this;
		if(e.keyCode == 16 || e.keyCode == 17 || e.keyCode == 18){return;};//alt,ctrl,sheft键位正常使用
		if(e.keyCode == 67 && e.ctrlKey){return;}//复制动作后文本不作处理
		if(!e.ctrlKey && !(e.keyCode == 9 || e.keyCode == 39 || e.keyCode == 37)){
			e.preventDefault();
		}
	}).on("keyup","input[dateTime]",function(e){
		var _this = this;
		var rel = setCalendar1({
			"_fmt":$(_this).attr("dateTime"),
			"_p":getCursortPosition(this),
			"_v":$(this).val1(),
			"_e":e
		})
		setTimeout(function(){//中文输入法下的特殊字符在触发函数前就录入了键盘值
		$(_this).val1(rel[1])
		setCaretPosition(_this,rel[0]);
		},0)
	}).on("blur","input[dateTime]",function(e){
		if($(this).attr("dateTime") != "HH:mm:ss"){
			if(!(new Date($(this).val1().replace(/-/gi,"/")) * 1)){
				$(this).val1("");
			}
		}
	})
	jQuery("html").on("paste","input:text:not([fmt])",function(e){//去掉所有copy过来的前后面空格
		var _this = $(this);
		setTimeout(function(){jQuery(_this).val(jQuery.trim(jQuery(_this).val1()));})
	})
	jQuery("html").on("keydown paste","input[fmt]",function(e){//使电话，证件，银行卡格式化
		var _this = this;
		var fmt = jQuery(this).attr("fmt");
		if(e.keyCode == 16 || e.keyCode == 17 || e.keyCode == 18){return;};//alt,ctrl,sheft键位正常使用
		if(e.keyCode == 67 && e.ctrlKey){return;}//复制动作后文本不作处理
		if(e.target.getAttribute("fmt") == "birth"){
			if(e.type == "paste"){
				e.preventDefault();
				var pastedText;
	        	if (window.clipboardData && window.clipboardData.getData) { // IE
					pastedText = window.clipboardData.getData('Text');
				}else {
	            	pastedText = e.originalEvent.clipboardData.getData('Text');//e.clipboardData.getData('text/plain');
				}
				var aB = analyzeBirth(pastedText);
				if(aB){
					jQuery(_this).val(aB);
				}else{
					alertMsg("粘贴的内容<span style='color:red'>"+pastedText+"</span>格式不正确！");
				}
			}else{
				if(!e.ctrlKey && !(e.keyCode == 9 || e.keyCode == 39 || e.keyCode == 37)){
					if(!((browserInfo.ios || browserInfo.android) && (browserInfo.chrome || browserInfo.xiaomi))){
						e.preventDefault();
					}else{
						var p = getCursortPosition(this);
						$(this).attr({"p0":p[0],"p1":p[1]});
						if(e.keyCode == 8 || e.keyCode == 46){
							e.preventDefault();
						}
					}
				}
			}
		}else if(fmt == "money"){
			if(e.type == "paste"){
				e.preventDefault();
				var pastedText;
	        	if (window.clipboardData && window.clipboardData.getData) { // IE
					pastedText = window.clipboardData.getData('Text');
				}else {
	            	pastedText = e.originalEvent.clipboardData.getData('Text');//e.clipboardData.getData('text/plain');
				}
				if(!isNaN("" + pastedText.replace(/\,/g,""))){
					var aB = formatMoney(parseFloat(pastedText.replace(/\,/g,"")));
					if(aB){
						jQuery(_this).val(aB);
					}
				}else{
					alertMsg("粘贴的内容<span style='color:red'>"+pastedText+"</span>格式不正确！");
				}
			}else{
				if(!e.ctrlKey && !(e.keyCode == 9 || e.keyCode == 39 || e.keyCode == 37)){
					e.preventDefault();
				}
			}
		}else if(e.target.getAttribute("fmt") == "ct"){
		}else{
			if(e.type == "paste"){
				setTimeout(function(){jQuery(_this).val(jQuery.trim(jQuery(_this).val1()));})
			}else{
				if(e.target.getAttribute("fmt") != "idcard"){
					setTimeout(function(){jQuery(_this).val(jQuery(_this).val1());})
				}
			}
		}
	}).on("mousedown","input[fmt='birth']",function(e){
		var _this = this;
		if(_this.value == ""){
			e.preventDefault();
			$(this).focus();
		}
	}).on("focus","input[fmt='birth']",function(){
		var _this = this;
		if(_this.value == ""){
			if(!((browserInfo.ios || browserInfo.android) && (browserInfo.chrome || browserInfo.xiaomi))){
				$(_this).prop("maxlength",0).val("____-__-__");
			}else{
				$(_this).prop("maxlength",100).val("____-__-__");
			}
			
			setTimeout(function(){setCaretPosition(_this,0)},0);
		}
	}).on("blur","input[fmt='birth']",function(){
		var _this = this;
		var v = $(_this).val();
		var d = new Date($(_this).val().replace(/-/gi,"/"));
		var t = v.split("-");
		if(v == "____-__-__" || v == ""){
			$(_this).val("");
		}else if((v > -1 || d.getFullYear() != t[0]*1 || (d.getMonth()+1) != t[1]*1 || d.getDate() != t[2]*1) && (!$(this).hasClass("phExpiry")) && (!$(this).hasClass("phExpiry"))){
			alertMsg("错误的出生日期！",function(){
				$(_this).focus();
			})
		}
	}).on("input","input[fmt='birth']",function(e){
		if((browserInfo.ios || browserInfo.android) && (browserInfo.chrome || browserInfo.xiaomi)){
			var _this = this, val = $(this).val(), p = [$(this).attr("p0")*1,$(this).attr("p1")*1];//p0和p1需要在keydown时确定好
			e.key = val.substr(p[0],1);
			if(!isNaN(e.key) && /^\d$/.test(e.key)){
				e.keyCode = e.key * 1 + 48;
			}
			var len = val.length;
			val = val.substr(0,p[0])+"____-__-__".substr(p[0],p[1]-p[0])+val.substr(len - (10-p[1]));
			p[1] = p[0];
			var rel = setCalendar1({
				"_fmt":"yyyy-MM-dd",
				"_p":p,
				"_v":val,
				"_e":e
			})
			$(_this).val(rel[1]);
			if(browserInfo.chrome){
				setTimeout(function(){setCaretPosition(_this,rel[0]);},0);
			}else{
				setCaretPosition(_this,rel[0]);
			}
			
		}
	}).on("keyup","input[fmt='birth']",function(e){
		if(!((browserInfo.ios || browserInfo.android) && (browserInfo.chrome || browserInfo.xiaomi)) || e.keyCode == 8 || e.keyCode == 46){
			var _this = this;
			var rel = setCalendar1({
				"_fmt":"yyyy-MM-dd",
				"_p":getCursortPosition(this),
				"_v":$(this).val(),
				"_e":e
			})
			setTimeout(function(){//中文输入法下的特殊字符在触发函数前就录入了键盘值
			$(_this).val(rel[1])
			setCaretPosition(_this,rel[0]);
			},0)
		}
	}).on("focus","input[fmt='money']",function(){
		$(this).css({"ime-mode":"disabled"}).prop("maxlength",0);
	}).on("blur","input[fmt='money']",function(){
		$(this).val($(this).val()).change();
	}).on("keyup","input[fmt='money']",function(e){
		var _this = this;
		var rel = setMoney({
			"_fmt":"yyyy-MM-dd",
			"_p":getCursortPosition(this),
			"_v":$(this).val1(),
			"_e":e
		})
		setTimeout(function(){//中文输入法下的特殊字符在触发函数前就录入了键盘值
		$(_this).val1(rel[1])
		setCaretPosition(_this,rel[0]);
		},0)
	})
	if(browserInfo.win && window.chrome && chrome.webstore && chrome.runtime){
		$(document).on("focus","input[fmt='idcard']",function(e){
			var _this = this;
			$(this).prop("readonly",true);
			var idcardTips = $("<div class='idcardTips' style='position:absolute;display:inline-block;width:0;overflow:hidden;border-left:1px solid red;'><div style='white-space:nowrap;position:absolute;'></div></div>");
			idcardTips.css({"height":parseFloat($(this).css("font-size")),"margin-top":parseFloat($(this).css("margin-top")) + (parseFloat($(this).css("line-height"))-parseFloat($(this).css("font-size")))/2,"border-color":$(this).css("color")});
			idcardTips.find("div").css({"font-family":$(this).css("font-family"),"font-size":$(this).css("font-size"),"font-weight":$(this).css("font-weight")});
			$(this).before(idcardTips);
			var setIntervalNum = 0,_thisw = $(this).width();
			var _mleft = parseInt($(this).css("margin-left"))+parseInt($(this).css("padding-left"))+parseInt($(this).css("text-indent"))+parseInt($(this).css("border-left"));
			$(this).data("time",setInterval(function(){
				setIntervalNum = (setIntervalNum + 1)%200;
				var _p = getCursortPosition(_this);
				var _width = idcardTips.css("display","inline-block").find("div").html($(_this).val1().substr(0,_p[0]).replace(/ /g,"&nbsp;")).width()+_mleft;
				_width > _thisw && (_width = _thisw);
				idcardTips.css("margin-left",_width);
				if(setIntervalNum < 100 || _p[0] != _p[1]){
					idcardTips.hide();
				}
			},1))
			$(this).one("blur",function(e){
				$(this).prop("readonly",false).change();
				clearInterval($(this).data("time"));
				$(".idcardTips").remove();
			})
		}).on("keydown","input[fmt='idcard']",function(e){
			var _this = this,_p = getCursortPosition(this), val = $(this).val1();
			if(e.ctrlKey){
				$(this).prop("readonly",false);
				return;
			}
			if(e.which >= 48 && e.which <= 109){
				if($(_this).val().length < 20){
					var _ch = e.key;
					$(_this).val(""+val.slice(0,_p[0])+_ch+val.slice(_p[1]));
					setCaretPosition(_this,getCursortPosition(_this)[0]+1);
				}
			}else if(e.which == 37){
				if(_p[0] > 0){
					setCaretPosition(_this,getCursortPosition(_this)[0] - 1);
				}else{
					setCaretPosition(_this,0);
				}
			}else if(e.which == 39){
				setCaretPosition(_this,getCursortPosition(_this)[0] + 1);
			}else if(_p[0] != _p[1] && (e.which == 8 || e.which == 46)){
				$(_this).val(""+val.slice(0,_p[0])+val.slice(_p[1]));
			}else if(e.which == 8 && _p[0] > 0){
				if(val.slice(_p[0] - 1,_p[0]) == " "){
					$(_this).val(""+val.slice(0,_p[0] - 2)+val.slice(_p[1]));
					if(val.slice(_p[1]).length > 1){
						setCaretPosition(_this,getCursortPosition(_this)[0] - 3);
					}else{
						setCaretPosition(_this,getCursortPosition(_this)[0] - 1);
					}
				}else{
					$(_this).val(""+val.slice(0,_p[0] - 1)+val.slice(_p[1]));
					if(val.slice(_p[0],_p[0] + 1) != " " && val.slice(_p[0],_p[0] + 1) != ""){
						setCaretPosition(_this,getCursortPosition(_this)[0] - 1);
					}
				}
			}else if(e.which == 46){
				if(val.slice(_p[0],_p[0] + 1) == " "){
					$(_this).val(""+val.slice(0,_p[0])+val.slice(_p[1] + 2));
				}else{
					$(_this).val(""+val.slice(0,_p[0])+val.slice(_p[1] + 1));
				}
			}
		}).on("keyup","input[fmt='idcard']",function(e){
			$(this).prop("readonly",true);
		})
	}else if(browserInfo.win){
		$(document).on("focus","input[fmt='idcard']",function(e){
			$(this).css("ime-mode","disabled");
		}).on("keydown","input[fmt='idcard']",function(e){
			$(this).val($(this).val());
		})
	}else{
		$(document).on("blur","input[fmt='idcard']",function(e){
			jQuery(this).val(jQuery(this).val());
		})
		/*$(document).on("focus keyup blur","input[fmt='idcard']",function(e){
			$(this).prop("readonly",false);
		}).on("keydown","input[fmt='idcard']",function(e){
			var _this = this,_p = getCursortPosition(this), val = $(this).val1();
			if(!$(_this).prop("readonly")){
				if(e.which >= 65 && e.which <= 90){
					$(_this).prop("readonly",true);
					var _ch = e.shiftKey ? String.fromCharCode(e.which).toUpperCase() : String.fromCharCode(e.which).toLowerCase();
					$(_this).val(""+val.slice(0,_p[0])+_ch+val.slice(_p[1]));
					setTimeout(function(){setCaretPosition(_this,getCursortPosition(_this)[0]+1)},20);
				}
			}
			setTimeout(function(){$(_this).val($(_this).val());},20);
		})*/
	}
	
	jQuery(document).on("submit","form",function(){
		jQuery(this).find("[fmt]").each(function(){
			jQuery(this).val1(jQuery(this).val());
		})
	})
	jQuery("input[fmt]").each(function(){//加载数据时格式化数据
		if(jQuery(this).attr("fmt") == "ct"){
			var c = jQuery("<div class='calendarTime'></div>");
			var only = jQuery(this).attr("ctparam") || "";
			var cc = jQuery("<span class='calendarTrigger'></span><input type='text' " + only + " maxlength=0 noClear dateTime='HH:mm:ss' value='00:00:00' class='times ideMode' style='background: none;border: none;text-align: right;'>");
			$(this).prop({"maxlength":0,"autocomplete":"off"}).attr({"noClear":"","dateTime":"yyyy-MM-dd"});
			$(this).wrap(c);
			$(this).after(cc);
			$(this).width(75);
		}
		jQuery(this).val(jQuery(this).val());
	})
})

function setCalendar(param){
	var timeDetail = {"y":[4,9999,"_"],"M":[2,12,"_"],"d":[2,31,"_"],"H":[2,23,"0"],"m":[2,59,"0"],"s":[2,59,"0"]};//时间里的长度,最大值,默认值
	var _fmt = param["_fmt"], _p = param["_p"] * 1, _v = param["_v"], _e = param["_e"];//参数分别代表待处理元素，默认格式，光标位置，现有值，输入值
	var fmt = _fmt;
	for(var i in timeDetail){
		fmt = fmt.replace(new RegExp(i,"g"),timeDetail[i][2]);
		timeDetail[i][3] = _fmt.indexOf(i);
	};//每个具体时间的开始位置
	_v = _v.concat(fmt.substr(_v.length)).split("");
	var p = 0;//光标的偏移
	var _k = _e.keyCode - 96 >= 0 ? _e.keyCode - 96 : _e.keyCode - 48;
	if(_e.keyCode == 229 && _e.key && !isNaN(_e.key)){
		_k = _e.key * 1;
	}else if(_e.keyCode == 229 && _e.originalEvent.code){
		if(_e.originalEvent.code.replace(/\D/g,"") != ""){
			_k = _e.originalEvent.code.replace(/\D/g,"") * 1;
		}
	}
	if(_k >= 0 && _k <= 9){
		var bl = timeDetail[_fmt.split("")[_p]];
		if(!bl){bl = timeDetail[_fmt.split("")[_p + 1]];p++;};
		if(bl){
			var detailValue = 0;
			_v[_p + p] = _k;
			for(var i = 0; i < bl[0]; i++){
				detailValue += (_v[bl[3] + i] * 1 || 0) * Math.pow(10,bl[0] - 1 - i);
			}
			if(detailValue > bl[1]){
				for(var i = 0; i < bl[0]; i++){
					_v[bl[3] + i] = 0;
				}
				_v[bl[3] + bl[0] - 1] = _k;
				p = bl[3] + bl[0] + 1 - _p;
			}else{
				if(!timeDetail[_fmt.substr((++p) + _p,1)]){
					p++;
				}
			}
		}
	}else{
		if(_e.keyCode == 8){
			if(timeDetail[_fmt.split("")[_p - 1]]){
				_v[_p - 1] = fmt.substr(_p - 1,1);
				p--;
			}else{
				_v[_p - 2] = fmt.substr(_p - 2,1);
				p = p - 2;
			}
		}
		if(_e.keyCode == 46){
			if(timeDetail[_fmt.split("")[_p]]){
				_v[_p] = fmt.substr(_p,1);
			}else{
				_v[_p + 1] = fmt.substr(_p + 1,1);
				p = p + 1;
			}
		}
	}
	return [_p + p,_v.join("")];
}


function setCalendar1(param){
	var timeDetail = {"y":[4,3000,"_"],"M":[2,12,"_"],"d":[2,31,"_"],"H":[2,23,"0"],"m":[2,59,"0"],"s":[2,59,"0"]};//时间里的长度,最大值,默认值
	var _fmt = param["_fmt"], _p = param["_p"][0] * 1,_lp = param["_p"][1] * 1, _v = param["_v"], _e = param["_e"];//参数分别代表待处理元素，默认格式，光标位置起始，光标位置结束，现有值，输入值
	var fmt = _fmt;
	for(var i in timeDetail){
		fmt = fmt.replace(new RegExp(i,"g"),timeDetail[i][2]);
		timeDetail[i][3] = _fmt.indexOf(i);
	};//每个具体时间的开始位置
	_v = _v.concat(fmt.substr(_v.length)).split("");
	var allowableValue;//输入了一个允许输入的值
	var p = 0;//光标的偏移
	var _k = _e.keyCode - 96 >= 0 ? _e.keyCode - 96 : _e.keyCode - 48;
	if(_e.keyCode == 229 && _e.key && !isNaN(_e.key)){
		_k = _e.key * 1;
	}else if(_e.keyCode == 229 && _e.originalEvent.code){
		if(_e.originalEvent.code.replace(/\D/g,"") != ""){
			_k = _e.originalEvent.code.replace(/\D/g,"") * 1;
		}
	}
	if(_k >= 0 && _k <= 9){
		if((_v[_p + p] == "_" && _v.join("").indexOf("_") == _p + p) || _fmt.substr(_p + p , 1) == "H" || _fmt.substr(_p + p , 1) == "m"  ||  _fmt.substr(_p + p , 1) == "s"){
		var bl = timeDetail[_fmt.split("")[_p]];
		if(!bl){bl = timeDetail[_fmt.split("")[_p + 1]];p++;};
		if(bl){
			var detailValue = 0;
			var originalValue = _v[_p + p];
			_v[_p + p] = _k;
			allowableValue = true;
			for(var i = 0; i < bl[0]; i++){//计算当前位置代表意义的值，"_"用1替代
				detailValue += (isNaN(_v[bl[3] + i] * 1) ? 1 : _v[bl[3] + i] * 1) * Math.pow(10,bl[0] - 1 - i);
			}
			if(detailValue > bl[1] || (detailValue == 0 && bl[1] != "59" && bl[1] != "23")){//当前位置大于最大值则不采用输入的数值
				_v[_p + p] = originalValue;
				allowableValue = false;
			}else{
				if(!timeDetail[_fmt.substr((++p) + _p,1)]){
					p++;
				}
			}
		}
		if(allowableValue && _lp > _p){//选定了范围并且输入了有效值需要清空选定范围的值
			_v = _v.join("").substr(0,_p + 1) + fmt.substring(_p + 1,_lp) + _v.join("").substr(_lp);
			_v = _v.split("");
			}
		}
	}else{
		if(_lp > _p && (_e.keyCode == 8 || _e.keyCode == 46)){
			_v = _v.join("").substr(0,_p) + fmt.substring(_p,_lp) + _v.join("").substr(_lp);
			_v = _v.split("");
		}else{
			if(_e.keyCode == 8){
				if(timeDetail[_fmt.split("")[_p - 1]]){
					_v[_p - 1] = fmt.substr(_p - 1,1);
					p--;
				}else{
					_v[_p - 2] = fmt.substr(_p - 2,1);
					p = p - 2;
				}
			}
			if(_e.keyCode == 46){
				if(timeDetail[_fmt.split("")[_p]]){
					_v[_p] = fmt.substr(_p,1);
				}else{
					_v[_p + 1] = fmt.substr(_p + 1,1);
					p = p + 1;
				}
			}
		}
	}
	if(_p + p < 0){p = -_p};
	return [_p + p,_v.join("")];
}

function setMoney(param){
	var _p = param["_p"][0] * 1,_lp = param["_p"][1] * 1, _v = param["_v"],_e = param["_e"];//光标起始位置，光标结束位置，现有值，e
	var _k = _e.keyCode - 96 >= 0 ? _e.keyCode - 96 : _e.keyCode - 48;
	var p = _v.length - _lp;
	var val = _v;
	var _v1 = _v.substring(0,_p).replace(/,/g,"");//光标前面的值
	var _v2 = _v.substring(_lp).replace(/,/g,"");//光标后面的值
	//处理
	if(_e.keyCode == 229 && _e.key && !isNaN(_e.key)){
		_k = _e.key * 1;
	}else if(_e.keyCode == 229 && _e.originalEvent.code){
		if(_e.originalEvent.code.replace(/\D/g,"") != ""){
			_k = _e.originalEvent.code.replace(/\D/g,"") * 1;
		}
	}
	if(_k >= 0 && _k <= 9){
		val = formatMoney(_v1 + "" + _k + "" + _v2);
	}else if(_e.keyCode == 190 || _e.keyCode == 110 ){
		if(/\./g.test(_v)){
			val = _v;
		}else{
			val = formatMoney(_v1 + "." + _v2);
		}
	}else{
		if(_lp > _p && (_e.keyCode == 8 || _e.keyCode == 46)){
			val = formatMoney(_v1 + "" + _v2);
		}else{
			if(_e.keyCode == 8){
				if(_v.substring(_p - 1,_p) == ","){
					p++;
					val =  formatMoney(_v.substring(0,_p - 2).replace(/,/g,"") + "" + _v2);
				}else{
					val =  formatMoney(_v.substring(0,_p - 1).replace(/,/g,"") + "" + _v2);
				}
			}
			if(_e.keyCode == 46){
				if(_v.substring(_p,_p + 1) == ","){
					val =  formatMoney(_v1 + "" + _v.substring(_lp + 2).replace(/,/g,""));
				}else{
					val =  formatMoney(_v1 + "" + _v.substring(_lp + 1).replace(/,/g,""));
				}
				p--;
			}
		}
	}
	return [val.length - p,val];
}
function formatMoney(num){
    if(!/^(\d+)(\.\d*)?$/.test(num)){return num;}
    var a = RegExp.$1, b = RegExp.$2;
    var re = new RegExp("(\\d)(\\d{3})(,|$)");
    if(a){while(re.test(a)){a = a.replace(re, "$1,$2$3");}}
    return a +""+ b.substr(0,3);
}


function analyzeBirth(birth){
	var yy,mm,dd,y,m,d,rel,fd;
	if(birth.split(/[\/\-\,]/).length == 3){
		yy = birth.split(/[\/\-\,]/)[0]*1;
		mm = birth.split(/[\/\-\,]/)[1]*1;
		dd = birth.split(/[\/\-\,]/)[2]*1;
		if(yy < 1900){
			return false;
		}
	}else if(!/[^\d]/.test(birth)){
		if(birth.length == 8){
			yy = birth.substr(0,4)*1;
			mm = birth.substr(4,2)*1;
			dd = birth.substr(6,2)*1;
		}else if(birth.length == 7){
			yy = birth.substr(0,4)*1;
			mm = birth.substr(4,2)*1;
			dd = birth.substr(6,1)*1;
		}else if(birth.length == 6){
			yy = birth.substr(0,4)*1;
			mm = birth.substr(4,1)*1;
			dd = birth.substr(5,1)*1;
		}else{
			return false;
		}
	}else{
		return false;
	}
	fd = new Date(yy,mm - 1,dd);
	y = fd.getFullYear();
	m = fd.getMonth() + 1;
	d = fd.getDate();
	if(y == yy && m == mm && d == dd){
		rel = yy + "-" + zerofill(mm) + "-" + zerofill(dd);
	}else if(birth.length == 7){
		rel = analyzeBirth(birth.substr(0,4) + "0" + birth.substr(4,3));
	}else{
		rel = false;
	}
	return rel;
}
function zerofill(s) {
	var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
	s = isNaN(s) ? 0 : s;
	return (s < 10 ? '0' : '') + s.toString();
}

function getCursortPosition (ctrl) {
    var CaretPosStart = 0, CaretPosEnd = 0;   // IE Support
    if (document.selection) {
        var Sel = document.selection.createRange ();
		CaretPosStart = Sel.text.length;
        Sel.moveStart ('character', -ctrl.value.length);
        CaretPosEnd = Sel.text.length;
		CaretPosStart = CaretPosEnd - CaretPosStart;
    }else if (ctrl.selectionStart || ctrl.selectionStart == '0'){
		CaretPosStart = ctrl.selectionStart;
		CaretPosEnd = ctrl.selectionEnd;
	}
    return [CaretPosStart,CaretPosEnd];
}
function setCaretPosition(ctrl, pos){
    if(ctrl.setSelectionRange)
    {
        ctrl.setSelectionRange(pos,pos);
    }
    else if (ctrl.createTextRange) {
        var range = ctrl.createTextRange();
        range.collapse(true);
        range.moveEnd('character', pos);
        range.moveStart('character', pos);
        range.select();
    }
}


function getCursorChange(l,o,t){//获取改变后字符串的光标位置，参数分别为初始位置，源字符串，处理后的字符串
	var ll = l[0], original = o.split(""), target = t.split(""), offset = 0, idx = 0, str = "";
	original[ll+offset] == " " && offset--;
	str = original[ll+offset];
	for(var i = 0; i <= ll+offset; i++){original[i] == str && idx++;}
	for(var i = 0; i < target.length; i++){
		target[i] == str && idx--;
		if(idx == 0){
			return i - offset;
		}
	}
	return -1;
}

if(!jQuery.fn.val1){
	jQuery.fn.val1 = jQuery.fn.val;
	jQuery.fn.val = function(){
		var val = "";
		if(arguments.length == 0){
			if(this.is("[fmt]")){
			if(this.attr("fmt") == "ct"){
				if(this.val1() == ""){
					return "";
				}else if(this.siblings(".times").length > 0){
					return this.val1() + " " + this.siblings(".times").val1();
				}else{
					return this.val1();
				}
			}else if(this.attr("fmt") == "birth"){
					return this.val1().replace(/^____-__-__$/g,"");
				}else if(this.attr("fmt") == "money"){
					val = isNaN(parseFloat(this.val1().replace(/\,/g,"")));
					return (val ? "" : "" + parseFloat(this.val1().replace(/\,/g,"")));
				}
				return this.val1().replace(/ /g,"");
			}else{
				return this.val1();
			}
		}else{
			for(var i = 0 ; i < this.length ; i++){
				if(this.eq(i).is("[fmt]")){
					var fmt = this.eq(i).attr("fmt");
					if(this.eq(i).is(":focus")){
						var cursor = getCursortPosition(this[i]);//获取光标位置
					}else{
						cursor = [-1,-1];
					}
					var cc = -1;
					if(fmt == "tel"){
						cc = getCursorChange(cursor,(""+arguments[0]),(""+arguments[0]).replace(/ /g,"").replace(/(^.{3}|.{4})/g,"$1 ").replace(/ $/,""));
						this.eq(i).val1((""+arguments[0]).replace(/ /g,"").replace(/(^.{3}|.{4})/g,"$1 ").replace(/ $/,""));
					}else if(fmt == "idcard"){
						cc = getCursorChange(cursor,(""+arguments[0]),(""+arguments[0]).replace(/[^\u0000-\u0127]| /g,"").replace(/(^.{2}|.{4})/g,"$1 ").replace(/ $/,""));
						this.eq(i).val1((""+arguments[0]).replace(/[^\u0000-\u0127]| /g,"").replace(/(^.{2}|.{4})/g,"$1 ").replace(/ $/,""));
					}else if(fmt == "bank"){
						cc = getCursorChange(cursor,(""+arguments[0]),(""+arguments[0]).replace(/ /g,"").replace(/(.{4})/g,"$1 ").replace(/ $/,""));
						this.eq(i).val1((""+arguments[0]).replace(/ /g,"").replace(/(.{4})/g,"$1 ").replace(/ $/,""));
				}else if(fmt == "ct"){
					var a1 = (""+arguments[0]).split(" ")[0];
					var a2 = (""+arguments[0]).split(" ")[1];
					var a3 = [];
					this.eq(i).val1((""+arguments[0]).split(" ")[0]);
					a3[0] = (a2 && a2.split(":")[0] && a2.split(":")[0] < 24) ? zerofill(a2.split(":")[0]) : "00";
					a3[1] = (a2 && a2.split(":")[1] && a2.split(":")[1] < 60) ? zerofill(a2.split(":")[1]) : "00";
					a3[2] = (a2 && a2.split(":")[2] && a2.split(":")[2] < 60) ? zerofill(a2.split(":")[2]) : "00";
					this.eq(i).siblings(".times").val1(a3.join(":"))
					}else if(fmt == "money"){
						val = isNaN(parseFloat(("" + arguments[0]).replace(/\,/g,"")));
						this.eq(i).val1(""+(val ? "" : "" + formatMoney(parseFloat(("" + arguments[0]).replace(/\,/g,"")))));
					}else{
						this.eq(i).val1(arguments[0]);
					}
					cursor[0] > -1 && cc > -1 && setCaretPosition(this[i],cc)
				}else{
					this.eq(i).val1(arguments[0]);
				}
			}
			return this;
		}
	}
}


//渠道管理，产品管理，系统设置中的小Tab页
jQuery(".categoryTagsMenu").on("click",".MenuTab",function(){
	var tag = $(this).attr("tag");
	$(".categoryTagsMenu .MenuTab.selected").removeClass("selected");
	$(".categoryTagsMain.selected").removeClass("selected");
	$(this).addClass("selected");
	$(".categoryTagsMain[tag='"+tag+"']").addClass("selected");
})


function isBlank(val) {
    return (typeof val == "string") ? $.trim(val) == "" : (val === null || val === undefined);
}

//Div拖动
//$(head).dragDiv(box);//head为触发拖动的元素，box为需要移动的元素
(function($) {
	$.fn.dragDiv = function(divWrap) {
		return this.each(function() {
			var $divMove = $(this);//鼠标可拖拽区域
			var $divWrap = divWrap ? $divMove.parents(divWrap) : $divMove;//整个移动区域
			var mX = 0, mY = 0;//定义鼠标X轴Y轴
			var dX = 0, dY = 0;//定义div左、上位置
			var isDown = false;//mousedown标记
			if($divWrap.length == 0){return;}
			if(document.attachEvent) {//ie的事件监听，拖拽div时禁止选中内容，firefox与chrome已在css中设置过-moz-user-select: none; -webkit-user-select: none;
				$divMove[0].attachEvent('onselectstart', function() {
					return false;
				});
			}
			$divMove.mousedown(function(event) {
				var event = event || window.event;
				mX = event.clientX;
				mY = event.clientY;
				dX = $divWrap.offset().left;
				dY = $divWrap.offset().top;
				isDown = true;//鼠标拖拽启动
			});
			$(document).mousemove(function(event) {
				var event = event || window.event;
				var x = event.clientX;//鼠标滑动时的X轴
				var y = event.clientY;//鼠标滑动时的Y轴
				if(isDown) {
					$divWrap.css({"left": x - mX + dX, "top": y - mY + dY});//div动态位置赋值
				}
			});
			$divMove.mouseup(function() {
				isDown = false;//鼠标拖拽结束
			});
		});
	};
})(jQuery);
}
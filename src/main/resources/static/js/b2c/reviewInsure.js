var discount=$("#isdiscount").val();
var uid = $("#uid").val();
var domain=window.location.host;
var isInMainSite = (partnerId == "1") ? true : false;
var partnerCode = $("#partnerCode").val();
var VATrate=0.000000;

var allCount=new Array(),allBirth=new Array(),allBirth1=new Array(),allPri=new Array();
$(function(){

	var queryData = {"planId": jQuery("#planId").val(), "days": jQuery("#sts").val()};

	showAheadTime();
	jQuery.ajax({//获取价格
		url:"../insure/ajaxPrices.action",
		type:"post",
		dataType:"json",
		contentType: 'application/x-www-form-urlencoded',
		data:queryData,
		error:function(){
			alert("通信失败！");
		},
		success: function(data){
			if(data.success!="1"){
				alert("获取保费价格失败，错误："+data.message+"，请联系客服!");
				return false;
			}

			if (data.jsFormula != undefined && data.jsFormula != ""){
				var priceJson=[];
				eval("getPrice = function(json){if(typeof(json) == 'string'){json = eval('('+json+')')};"+data.jsFormula+";}");//定义取价格函数，从实际价格转换为价格公式
				priceJson.days = queryData.days;//计费参数的天数，年龄到具体再赋予

				//正则提取出费率中的年龄.....
				var jsFormulaAges =data.jsFormula.match(/json.age\s*(<|>)=\s*\d+/g);
				for (var i=0;i<jsFormulaAges.length;i++){
					jsFormulaAges[i] = parseInt(jsFormulaAges[i].substring(jsFormulaAges[i].indexOf('=')+1));
				}
				jsFormulaAges.sort();
				var agesNum = 0;
				for (var i = 0;i <= (jsFormulaAges.length/2)-1; i++){
					allBirth[i]=jsFormulaAges[2*i];
					allBirth1[i]=jsFormulaAges[2*i+1];
					priceJson.age = (jsFormulaAges[2*i]+jsFormulaAges[2*i+1])/2;
					allPri[i]=getPrice(priceJson);
					agesNum ++ ;
				}
				for(var i=0;i<allBirth.length;i++){
					allCount[i] = 0;
				}
				for(var i=0;i<birthday.length;i++){//统计各年龄区间人数
					var getAgeResult=getAge(birthday[i],calAgeRule);
					for(var j=0;j<agesNum;j++){
						if(allBirth[j] <= getAgeResult && allBirth1[j]>=getAgeResult){
							allCount[j] = (isNaN(allCount[j])?0:allCount[j])+1;
						}
					}
				}
			}else {
				var agesNum = data.message.length;//保费年龄区间数
				for(var i=0;i<agesNum;i++){
					allBirth[i]=data.message[i].minAge;
					allBirth1[i]=data.message[i].maxAge;
					allPri[i]=data.message[i].price;
				}
				for(var i=0;i<allBirth.length;i++){
					allCount[i] = 0;
				}
				for(var i=0;i<birthday.length;i++){//统计各年龄区间人数
					var getAgeResult=getAge(birthday[i],calAgeRule);
					for(var j=0;j<agesNum;j++){
						if(allBirth[j] <= getAgeResult && allBirth1[j]>=getAgeResult){
							allCount[j] = (isNaN(allCount[j])?0:allCount[j])+1;
						}
					}
				}
			}
		}
	}).done(function(){//先取得价格再获取积分信息
		updateExtendData();//获取产品的扩展信息
		$("#promotionCredit").remove();
		$("#promotionDiscount").remove();
		$("#promotionDiscountCard").remove();
		$("#promotionExpress").remove();
		$("#promotionNo").prop("checked",true).trigger("click");//用代码触发click事件，会先触发click事件再反转状态

	})
});


var calAgeRule;//按投保日期还是生效日期算生日
var cvrgInfo;//保障项目各项基本信息
var cvrgIds;//保障项目Id的数组
var isLastProDefaultBene = false;//上一个产品是否为有默认受益人的产品,初始化为false
var isCurrentProDefaultBene;//当前选择产品是否为有默认受益人的产品
//改变保单扩展信息显示（与产品相关）
function updateExtendData() {
	var productInfo = {};
	var  productId = $("#ipcid").val();
	var partnerId = $("#partnerId").val();
	$.ajax({
		url: "/policyCenter/insure/ajaxProducts.action",
		type: "post",
		dataType: "json",
		data: "productId="+productId+"&partnerId="+partnerId,
		async:false,
		error:function(){
			alert("通信失败！");
		},
		success: function(data){
			if(data.success!="1"){
				return;
			}
			for(var i=0;i<data.message.length;i++){
				if(productId == data.message[i].id){
					productInfo = data.message[i];
					break;
				}
			}
		}
	})
	calAgeRule = productInfo.calAgeRule;
    isLastProDefaultBene = isCurrentProDefaultBene;
    isCurrentProDefaultBene = productInfo.haveDefaultBnfcry == 1;
	$("#policyExtendInfoBlock>div").hide();

	$("div#cvrgInfo,.insuredPrice").hide();
	$("#plancoderadio").closest("tr").show();

}

function setCvrgAmounts() {
    var cvrgAmountsStr = "";
    $(".cvrgAmount").each(function () {
        if ($(this).val() && parseFloat($(this).val()) > 0) {
            cvrgAmountsStr += "{\"cvrgId\":\"" + $(this).siblings(".cvrgId").val() + "\",\"cvrgAmount\":\"" + parseFloat($(this).val()).toFixed(2) + "\"},";
        }
    });
    if (cvrgAmountsStr) {
        cvrgAmountsStr = cvrgAmountsStr.slice(0, -1);
    }
$("#cvrgAmounts").val("[" + cvrgAmountsStr + "]");
}
function loginCallback(){//异步登录后重新处理优惠方式的关系

}

//显示需提前投保时间
function showAheadTime(){
	var startTime=new Date(jQuery("#activeDat").val().replace(/-/gi,"/"));
	var insureAheadTime=jQuery("#insureAheadTime").val();
	var aheadDay=Math.floor(insureAheadTime/10000);
	var aheadHour=Math.floor(insureAheadTime%10000/100);
	var aheadMinute=Math.floor(insureAheadTime%100);
	var aheadTime=new Date(startTime.getTime()-aheadDay*86400000-aheadHour*3600000-aheadMinute*60000);
	if(insureAheadTime!=0 && insureAheadTime%10000==0){ //提前投保时间为整数天
		aheadTime=new Date(startTime.getTime()-(aheadDay-1)*86400000-aheadHour*3600000-aheadMinute*60000);
	}
	var now=new Date();
	if(aheadTime<now){
		jQuery("#aheadTime").html("保险生效时间过早，请返回修改！");
		return ;
	}
	aheadTime=aheadTime.getFullYear() + "-" + zerofill(aheadTime.getMonth() + 1) + "-" + zerofill(aheadTime.getDate()) + ' ' + zerofill(aheadTime.getHours()) + ':' + zerofill(aheadTime.getMinutes()) + ":" + zerofill(aheadTime.getSeconds());
	jQuery("#aheadTime").html("该产品需至少提前"+jQuery("#insureAheadTimeShow").val()+"天投保，为确保出单成功请在"+aheadTime+"前完成支付！");
}
function zerofill(s) {
	var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
	s = isNaN(s) ? 0 : s;
	return (s < 10 ? '0' : '') + s.toString();
}

//计算年龄函数
function getAge(bir,calAgeRule){
	var getAgeStartTime;
	if(calAgeRule==1){
		getAgeStartTime=activeDat;
	}else{
		getAgeStartTime=nowDate;
	}
	try{
		var _date = new Date(bir.split(" ")[0].replace(/-/gi,"/"));
		var _y = _date.getFullYear();
		var _m = _date.getMonth()+1;
		var _d = _date.getDate();
		var _bir = bir.split(" ")[0].split("-"),_now = getAgeStartTime.split(" ")[0].split("-");
		var _birth = _now[0]-_bir[0];
		if(_y == _bir[0]*1 &&_m == _bir[1]*1 &&_d == _bir[2]*1){
			if(_now[1]*100+_now[2]*1-_bir[1]*100-_bir[2]*1 <= 0){//月系数为100，日系数为1，两者相减，大于等于0岁数为_birth，否则为_birth-1
				_birth = _birth - 1;
			}
		}else{
			_birth = -1;
		}
	}catch(e){
		var _birth = -1;
	}
	_birth = isNaN(_birth)?-1:_birth;
	return _birth;
}




//积分信息--------------------------------------------------------------------------------------------
var credit = "noCredit",caseToPointsExchangeCaseRate = 1;


//优惠方式选择
$("[name='promotion.preferentialType']").on("click",function(){
	$("#cardNo").closest("tr").hide();
	$("#discountSet").closest("tr").hide();
	if($("#promotionDiscountCard").prop("checked")){
		$("#cardNo").closest("tr").show();
	}
	countTotalPremium();
})
//计算总保费，显示费用
var totalPremium;//总保费
var totalFee;//总费用
var oldtotalFee;//之前的实收现金
var totalEach=[];//各年龄段的总保费
var selectedDiscount;//选择的折扣数额
var selectedCreditFee;//选择的积分数
var selectedExpressFee;//（是否）选择快递优惠的快递费用
var limitCredit;//最大可输入积分数额
var usingCredit;//以前使用积分支付时，修改或批改时前保单抵用的积分金额
var creditRatio;//积分抵现比例
var usedtotalFee;//修改或批改时以前支付的总费用
function countTotalPremium(){
	var hasPromotion = false;
	totalPremium = 0;
	usedtotalFee = 0;
	usingCredit = 0;
	$(".listFeeRow").remove();
	totalEach=[];
	for(var i=0;i<allCount.length;i++){

		totalEach[i]=allCount[i]*allPri[i];

		var newRow="<tr align='right' class='listFeeRow'";
		if(!allCount[i]){
			newRow+=" style='display:none;'";
		}
		newRow+="><td>（"+allBirth[i]+"岁~"+allBirth1[i]+"岁）保费</td><td>"+allPri[i].toFixed(2)+"</td><td>×</td><td>"+allCount[i]+"</td><td>=</td><td>"+totalEach[i].toFixed(2)+"</td><td>"+totalEach[i].toFixed(2)+"</td></tr>";

		if (parseFloat($("#paidin").val())!=0&&$("#paidin").val()!=""){
			$("#useFeeInfoRow").show();
		}


		if($("#fee_list tbody").find(".listFeeRow").length>0){
			$("#fee_list tbody").find(".listFeeRow:last").after(newRow);
		}else{
			$("#fee_list tbody").prepend(newRow);
		}
		totalPremium+=totalEach[i];
	}
	var promotionType = $("#orderPreferentialType").val();

	//修改时折扣、积分、快递优惠类型的显隐
	if ($("#actType").val() == "edit" && promotionType && promotionType != "0") {//修改保单，绑定已选的优惠方式
		$(":radio[name='promotion.preferentialType']").closest("label").hide();
		$(":radio[name='promotion.preferentialType']").each(function () {
			if ($(this).attr("id") != "promotionDiscountCard" && $(this).val() == promotionType && !$(this).prop("checked")) {
				$(this).prop("checked", true).trigger("click");//用代码触发click事件，会先触发click事件再反转状态
			}
		});
		$(":radio[name='promotion.preferentialType']:checked").closest("label").show();
	} else {
		if (uid == "") {
			$("#promotionNo").closest("label").show();
			/* if(!isInMainSite){
			 $("#promotionDiscountCard").closest("label").show();
			 } */
			$("#promotionDiscount").closest("label").hide();
			$("#promotionCredit").closest("label").hide();

				$("#promotionNo").closest("label").show();
				$("#promotionExpress").closest("label").hide();

		} else {
			$("#promotionNo").closest("label").hide();
			$("#promotionDiscountCard").closest("label").hide();


			if (discount != "" && discount != 1) {//折扣用户
				hasPromotion = true;
				$("#promotionNo").prop("checked", false);
				$("#promotionDiscount").closest("label").show();
				$("#discountSet").closest("tr").hide();
			}
			//积分用户
			if (credit != "noCredit") {
				hasPromotion = true;
				$("#promotionNo").prop("checked", false);
				$("#promotionCredit").closest("label").show();
			}

			$("#promotionExpress").closest("label").hide();


			!hasPromotion && $("#promotionNo").closest("label").show();
		}
	}

	selectedDiscount=1;
	selectedCreditFee=0;
	selectedExpressFee=0;
	$("#promotionValue").val("");

	$("#expressFeeInfoRow").hide();


	$("#expressFeeInfoRow").find("td:last").text("15.00");

	$("#creditFeeInfoRow").hide();
	$("#bc_out_credits").closest("td").hide();
	$("#get_credits").closest("div").hide();

	$("#promotionType").val($("[name='promotion.preferentialType']:checked").val());
	if ($("#paidin").val()!=""){
		oldtotalFee = parseFloat($("#paidin").val());
		usedtotalFee = oldtotalFee;
	} else {
		usedtotalFee = 0;
	}
	//$("#promotiontypeSelect").find("label:hidden").remove();
	totalFee = totalPremium * selectedDiscount + totalPremium * VATrate + selectedExpressFee - selectedCreditFee-usedtotalFee;
	$("#totalFee").html("合计支付金额：<b style='font-size:21px;'>¥"+totalFee.toFixed(2)+"</b>");
}

//检查输入的积分
function checkUsedPoints(o){
	var num = o.value;
	usingCredit = 0;
	if(num!=""){
		if(num.replace(/\d/gi,"")=="" && num<limitCredit && num>=0){

		}else{
			o.value = limitCredit;
		}
	}else{
		o.value = 0;
	}
	if ($("#paidin").val()!=""){
		if ($("#payCredit").val()!=0) {
			usingCredit = parseInt($("#payCredit").val());
			oldtotalFee = parseFloat($("#paidin").val());
			usedtotalFee = oldtotalFee+usingCredit*creditRatio;
		} else {
			oldtotalFee = parseFloat($("#paidin").val());
			usedtotalFee = oldtotalFee;
		}
	} else {
		usedtotalFee = 0;
	}
	$("#creditFeeInfoRow").find("td:eq(5),td:eq(7)").text("-"+(o.value*creditRatio).toFixed(2));
	totalFee = totalPremium * selectedDiscount + selectedExpressFee - o.value * creditRatio -usedtotalFee;
	$("#totalFee").html("合计支付金额：<b style='font-size:21px;'>¥"+totalFee.toFixed(2)+"</b>");
	$("#promotionValue").val(o.value);
	$("#bc_out_credits").text(credit-o.value).closest("td").show();
	$("#get_credits").text(parseInt((totalPremium-o.value*creditRatio-usingCredit*creditRatio)*caseToPointsExchangeCaseRate)).closest("div").show();
}






jQuery("#next_insure1").click(function(){//提交信息
	jQuery("#next_insure1").prop("disabled",true);
	setTimeout(function(){jQuery("#next_insure1").prop("disabled",false);},5000);
	$("#promotionType").attr("name","promotion.preferentialType");
	if(!$("#promotionNo").is(":checked")){
		if($("input[name='promotion.preferentialType']").length > 1 && ($("#promotionType").val() == "0" || $("#promotionType").val() == "")){
			alert("请选择一项优惠方式！");
			return;
		}
	}
	
	if($("#promotionDiscountCard").prop("checked")){
		if($("#cardNo").val()=="" || $("#cardKey").val()==""){
			alert("打折卡信息不完整！");
			return false;
		}
	}
	// if(jQuery("#agycardnum").length!=0 && jQuery("#agycardnum").val()!=""){
	// 	alert("已输入卡号，但是没有点击使用!");
	// 	jQuery("#agycardbtn").focus();
	// 	return;
	// }
	// jQuery("#promotionType").val(promotionStatus);
	// if(promotionStatus==2){//积分优惠
	// 	if(!isNaN($("#bc_credits").val()) && $("#bc_credits").val()!=0){//积分是数字就加上
	// 		jQuery("#credits").val($("#bc_credits").val());
	// 		jQuery("#creditsFee").val($("#bc_credits").val()*creditRatio*1);
	// 	}
	// 	jQuery("#expsFee").val(expressPrice);//有积分快递不打折
	// }else if(promotionStatus == 1){//快递优惠
	// 	jQuery("#expsFee").val(expressFee);
	// }else if(promotionStatus == ""){
	// 	jQuery("#expsFee").val(expressPrice);
	// 	jQuery("#promotionType").val("-1");
	// }

	jQuery.ajax({
		url: "/policyCenter/insure/blacklistUser.action",
		type: "post",
		dataType: "json",
		data: {"jsonStr":jQuery("[name=pusers]").val()}
	}).fail(function(){
		alert("通信失败！");
	}).done(function(data){
		if(data.success != "1"){
			alert(data.message);
		}else{
			try{//谷歌分析
				Transaction["revenue"] = totalFee || 0;
				Transaction["shipping"] = selectedExpressFee || 0;
				ga('ecommerce:addTransaction', Transaction);
				for(var i=0;i<allCount.length;i++){
					addItem["quantity"] += allCount[i];
				}
				addItem["price"] = parseFloat(totalPremium/addItem["quantity"]).toFixed(2);
				ga('ecommerce:addItem',addItem);
				ga('ecommerce:send');
			}catch(e){}

			jQuery("#postPolicyInfo").attr("action","saveInsure");
			jQuery("#postPolicyInfo").submit();
		}
	});
});


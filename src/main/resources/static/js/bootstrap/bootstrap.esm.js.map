{"version": 3, "file": "bootstrap.esm.js", "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(object)\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getElementFromSelector,\n  getjQuery,\n  getNextActiveElement,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        event.delegateTarget = target\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.originalHandler === handler && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFunction : handler\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFunction\n    delegationFunction = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFunction) {\n      delegationFunction = wrapFunction(delegationFunction)\n    } else {\n      handler = wrapFunction(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFunction) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const handlerKey of Object.keys(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    for (const keyHandlers of Object.keys(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = new Event(event, { bubbles, cancelable: true })\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      for (const key of Object.keys(args)) {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      }\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index'\nimport Manipulator from '../dom/manipulator'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const property of Object.keys(configTypes)) {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport { executeAfterTransition, getElement } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.2.0-beta1'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\n/**\n * Constants\n */\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config'\nimport EventHandler from '../dom/event-handler'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  leftCallback: null,\n  rightCallback: null,\n  endCallback: null\n}\n\nconst DefaultType = {\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)',\n  endCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport Swipe from './util/swipe'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  ride: '(boolean|string)',\n  pause: '(string|boolean)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getSelectorFromElement,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  for (const element of selectorElements) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    this._menu = SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    const getToggleButton = SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode)\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow } from './index'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, isRTL, isVisible, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (event.target !== event.currentTarget) { // click is inside modal-dialog\n        return\n      }\n\n      if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n        return\n      }\n\n      if (this._config.backdrop) {\n        this.hide()\n      }\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { getElement, isElement } from '../util/index'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  extraClass: '',\n  template: '<div></div>',\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist\n}\n\nconst DefaultType = {\n  extraClass: '(string|function)',\n  template: 'string',\n  content: 'object',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object'\n}\n\nconst DefaultContentType = {\n  selector: '(string|element)',\n  entry: '(string|element|function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg(this) : arg\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = false\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter()\n      } else {\n        context._leave()\n      }\n\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._createPopper(tip)\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      const previousHoverState = this._isHovered\n\n      this._isHovered = false\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (previousHoverState) {\n        this._leave()\n      }\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = false\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        tip.remove()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n\n      this._disposePopper()\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    let isShown = false\n    if (this.tip) {\n      isShown = this._isShown()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    this._disposePopper()\n    this.tip = this._createTipElement(content)\n\n    if (isShown) {\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._config.title\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg.call(this._element) : arg\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._config.originalTitle\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    config.originalTitle = this._element.getAttribute('title') || ''\n    config.title = this._resolvePossibleFunction(config.title) || config.originalTitle\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: [0.1, 0.5, 1],\n      rootMargin: this._getRootMargin()\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n  _getRootMargin() {\n    return this._config.offset ? `${this._config.offset}px 0px -30%` : this._config.rootMargin\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, getNextActiveElement, isDisabled } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst SELECTOR_DROPDOWN_ITEM = '.dropdown-item'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo: maybe is redundant\n        element.classList.add(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.focus()\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo maybe is redundant\n        element.classList.remove(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    toggle(SELECTOR_DROPDOWN_ITEM, CLASS_NAME_ACTIVE)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["MAX_UID", "MILLISECONDS_MULTIPLIER", "TRANSITION_END", "toType", "object", "undefined", "Object", "prototype", "toString", "call", "match", "toLowerCase", "getUID", "prefix", "Math", "floor", "random", "document", "getElementById", "getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "querySelector", "getElementFromSelector", "getTransitionDurationFromElement", "transitionDuration", "transitionDelay", "window", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "isVisible", "getClientRects", "elementIsVisible", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "isRTL", "dir", "defineJQueryPlugin", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "bootstrapHandler", "event", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "values", "find", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParameters", "originalTypeEvent", "delegationFunction", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "wrapFunction", "relatedTarget", "handlers", "previousFunction", "replace", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "keys", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "value", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "VERSION", "BaseComponent", "_element", "_config", "Data", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "getInstance", "getOrCreateInstance", "eventName", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "EVENT_CLOSE", "EVENT_CLOSED", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "<PERSON><PERSON>", "close", "closeEvent", "_destroyElement", "each", "data", "DATA_API_KEY", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_TOGGLE", "EVENT_CLICK_DATA_API", "<PERSON><PERSON>", "toggle", "button", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "POINTER_TYPE_TOUCH", "POINTER_TYPE_PEN", "CLASS_NAME_POINTER_EVENT", "SWIPE_THRESHOLD", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "touches", "clientX", "_eventIsPointerPenTouch", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "TOUCHEVENT_COMPAT_WAIT", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_SLIDE", "CLASS_NAME_END", "CLASS_NAME_START", "CLASS_NAME_NEXT", "CLASS_NAME_PREV", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "SELECTOR_ITEM_IMG", "SELECTOR_INDICATORS", "SELECTOR_DATA_SLIDE", "SELECTOR_DATA_RIDE", "KEY_TO_DIRECTION", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "endCallBack", "clearTimeout", "swipeConfig", "_directionToOrder", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "_orderToDirection", "slideEvent", "isCycling", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "clearInterval", "carousel", "slideIndex", "carousels", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "CLASS_NAME_HORIZONTAL", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "startEvent", "activeInstance", "dimension", "_getDimension", "style", "complete", "capitalizedDimension", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "selectorElements", "ESCAPE_KEY", "TAB_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "RIGHT_MOUSE_BUTTON", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "CLASS_NAME_DROPUP", "CLASS_NAME_DROPEND", "CLASS_NAME_DROPSTART", "CLASS_NAME_DROPUP_CENTER", "CLASS_NAME_DROPDOWN_CENTER", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "SELECTOR_NAVBAR", "SELECTOR_NAVBAR_NAV", "SELECTOR_VISIBLE_ITEMS", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "PLACEMENT_TOPCENTER", "PLACEMENT_BOTTOMCENTER", "offset", "boundary", "reference", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "showEvent", "_createPopper", "focus", "_completeHide", "destroy", "update", "hideEvent", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "clearMenus", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "manipulationCallBack", "setProperty", "_applyManipulationCallback", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "rootElement", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_NAV_FORWARD", "TAB_NAV_BACKWARD", "trapElement", "autofocus", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "EVENT_HIDE_PREVENTED", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_KEYDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "OPEN_SELECTOR", "SELECTOR_DIALOG", "SELECTOR_MODAL_BODY", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "scrollTop", "modalBody", "transitionComplete", "_triggerBackdropTransition", "currentTarget", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "CLASS_NAME_BACKDROP", "scroll", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "position", "uriAttributes", "ARIA_ATTRIBUTE_PATTERN", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "sanitizeHtml", "unsafeHtml", "allowList", "sanitizeFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createdDocument", "parseFromString", "elementName", "attributeList", "allowedAttributes", "innerHTML", "extraClass", "template", "content", "html", "sanitize", "sanitizeFn", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "_maybeSanitize", "text", "entries", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_MODAL", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "TRIGGER_MANUAL", "EVENT_INSERTED", "EVENT_CLICK", "EVENT_FOCUSOUT", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "title", "delay", "container", "fallbackPlacements", "customClass", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "_hideModalHandler", "_disposePopper", "_isWithContent", "shadowRoot", "isInTheDom", "ownerDocument", "_getTipElement", "previousHoverState", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "<PERSON><PERSON><PERSON><PERSON>", "isShown", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitle", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "EVENT_ACTIVATE", "CLASS_NAME_DROPDOWN_ITEM", "SELECTOR_DATA_SPY", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_LINK_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "rootMargin", "smoothScroll", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "threshold", "_getR<PERSON><PERSON><PERSON><PERSON>", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "id", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "CLASS_DROPDOWN", "SELECTOR_DROPDOWN_MENU", "SELECTOR_DROPDOWN_ITEM", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_TAB_PANEL", "SELECTOR_OUTER", "SELECTOR_INNER", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMA,OAAO,GAAG,OAAhB,CAAA;AACA,MAAMC,uBAAuB,GAAG,IAAhC,CAAA;AACA,MAAMC,cAAc,GAAG,eAAvB;;AAGA,MAAMC,MAAM,GAAGC,MAAM,IAAI;AACvB,EAAA,IAAIA,MAAM,KAAK,IAAX,IAAmBA,MAAM,KAAKC,SAAlC,EAA6C;IAC3C,OAAQ,CAAA,EAAED,MAAO,CAAjB,CAAA,CAAA;AACD,GAAA;;AAED,EAAA,OAAOE,MAAM,CAACC,SAAP,CAAiBC,QAAjB,CAA0BC,IAA1B,CAA+BL,MAA/B,CAAA,CAAuCM,KAAvC,CAA6C,aAA7C,EAA4D,CAA5D,CAAA,CAA+DC,WAA/D,EAAP,CAAA;AACD,CAND,CAAA;AAQA;AACA;AACA;;;AAEA,MAAMC,MAAM,GAAGC,MAAM,IAAI;EACvB,GAAG;IACDA,MAAM,IAAIC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,EAAgBhB,GAAAA,OAA3B,CAAV,CAAA;AACD,GAFD,QAESiB,QAAQ,CAACC,cAAT,CAAwBL,MAAxB,CAFT,EAAA;;AAIA,EAAA,OAAOA,MAAP,CAAA;AACD,CAND,CAAA;;AAQA,MAAMM,WAAW,GAAGC,OAAO,IAAI;AAC7B,EAAA,IAAIC,QAAQ,GAAGD,OAAO,CAACE,YAAR,CAAqB,gBAArB,CAAf,CAAA;;AAEA,EAAA,IAAI,CAACD,QAAD,IAAaA,QAAQ,KAAK,GAA9B,EAAmC;IACjC,IAAIE,aAAa,GAAGH,OAAO,CAACE,YAAR,CAAqB,MAArB,CAApB,CADiC;AAIjC;AACA;AACA;;AACA,IAAA,IAAI,CAACC,aAAD,IAAmB,CAACA,aAAa,CAACC,QAAd,CAAuB,GAAvB,CAAD,IAAgC,CAACD,aAAa,CAACE,UAAd,CAAyB,GAAzB,CAAxD,EAAwF;AACtF,MAAA,OAAO,IAAP,CAAA;AACD,KATgC;;;AAYjC,IAAA,IAAIF,aAAa,CAACC,QAAd,CAAuB,GAAvB,CAAA,IAA+B,CAACD,aAAa,CAACE,UAAd,CAAyB,GAAzB,CAApC,EAAmE;MACjEF,aAAa,GAAI,CAAGA,CAAAA,EAAAA,aAAa,CAACG,KAAd,CAAoB,GAApB,CAAA,CAAyB,CAAzB,CAA4B,CAAhD,CAAA,CAAA;AACD,KAAA;;AAEDL,IAAAA,QAAQ,GAAGE,aAAa,IAAIA,aAAa,KAAK,GAAnC,GAAyCA,aAAa,CAACI,IAAd,EAAzC,GAAgE,IAA3E,CAAA;AACD,GAAA;;AAED,EAAA,OAAON,QAAP,CAAA;AACD,CAvBD,CAAA;;AAyBA,MAAMO,sBAAsB,GAAGR,OAAO,IAAI;AACxC,EAAA,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B,CAAA;;AAEA,EAAA,IAAIC,QAAJ,EAAc;IACZ,OAAOJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,CAAmCA,GAAAA,QAAnC,GAA8C,IAArD,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CARD,CAAA;;AAUA,MAAMS,sBAAsB,GAAGV,OAAO,IAAI;AACxC,EAAA,MAAMC,QAAQ,GAAGF,WAAW,CAACC,OAAD,CAA5B,CAAA;EAEA,OAAOC,QAAQ,GAAGJ,QAAQ,CAACY,aAAT,CAAuBR,QAAvB,CAAH,GAAsC,IAArD,CAAA;AACD,CAJD,CAAA;;AAMA,MAAMU,gCAAgC,GAAGX,OAAO,IAAI;EAClD,IAAI,CAACA,OAAL,EAAc;AACZ,IAAA,OAAO,CAAP,CAAA;AACD,GAHiD;;;EAMlD,IAAI;IAAEY,kBAAF;AAAsBC,IAAAA,eAAAA;AAAtB,GAAA,GAA0CC,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAA9C,CAAA;AAEA,EAAA,MAAMgB,uBAAuB,GAAGC,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAhC,CAAA;EACA,MAAMO,oBAAoB,GAAGF,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAA7B,CATkD;;AAYlD,EAAA,IAAI,CAACG,uBAAD,IAA4B,CAACG,oBAAjC,EAAuD;AACrD,IAAA,OAAO,CAAP,CAAA;AACD,GAdiD;;;EAiBlDP,kBAAkB,GAAGA,kBAAkB,CAACN,KAAnB,CAAyB,GAAzB,CAAA,CAA8B,CAA9B,CAArB,CAAA;EACAO,eAAe,GAAGA,eAAe,CAACP,KAAhB,CAAsB,GAAtB,CAAA,CAA2B,CAA3B,CAAlB,CAAA;AAEA,EAAA,OAAO,CAACW,MAAM,CAACC,UAAP,CAAkBN,kBAAlB,CAAA,GAAwCK,MAAM,CAACC,UAAP,CAAkBL,eAAlB,CAAzC,IAA+EhC,uBAAtF,CAAA;AACD,CArBD,CAAA;;AAuBA,MAAMuC,oBAAoB,GAAGpB,OAAO,IAAI;AACtCA,EAAAA,OAAO,CAACqB,aAAR,CAAsB,IAAIC,KAAJ,CAAUxC,cAAV,CAAtB,CAAA,CAAA;AACD,CAFD,CAAA;;AAIA,MAAMyC,SAAS,GAAGvC,MAAM,IAAI;AAC1B,EAAA,IAAI,CAACA,MAAD,IAAW,OAAOA,MAAP,KAAkB,QAAjC,EAA2C;AACzC,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOA,MAAM,CAACwC,MAAd,KAAyB,WAA7B,EAA0C;AACxCxC,IAAAA,MAAM,GAAGA,MAAM,CAAC,CAAD,CAAf,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,OAAOA,MAAM,CAACyC,QAAd,KAA2B,WAAlC,CAAA;AACD,CAVD,CAAA;;AAYA,MAAMC,UAAU,GAAG1C,MAAM,IAAI;AAC3B;AACA,EAAA,IAAIuC,SAAS,CAACvC,MAAD,CAAb,EAAuB;IACrB,OAAOA,MAAM,CAACwC,MAAP,GAAgBxC,MAAM,CAAC,CAAD,CAAtB,GAA4BA,MAAnC,CAAA;AACD,GAAA;;EAED,IAAI,OAAOA,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,CAAC2C,MAAP,GAAgB,CAAlD,EAAqD;AACnD,IAAA,OAAO9B,QAAQ,CAACY,aAAT,CAAuBzB,MAAvB,CAAP,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CAXD,CAAA;;AAaA,MAAM4C,SAAS,GAAG5B,OAAO,IAAI;AAC3B,EAAA,IAAI,CAACuB,SAAS,CAACvB,OAAD,CAAV,IAAuBA,OAAO,CAAC6B,cAAR,EAAA,CAAyBF,MAAzB,KAAoC,CAA/D,EAAkE;AAChE,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;AAED,EAAA,MAAMG,gBAAgB,GAAGf,gBAAgB,CAACf,OAAD,CAAhB,CAA0B+B,gBAA1B,CAA2C,YAA3C,CAA6D,KAAA,SAAtF,CAL2B;;AAO3B,EAAA,MAAMC,aAAa,GAAGhC,OAAO,CAACiC,OAAR,CAAgB,qBAAhB,CAAtB,CAAA;;EAEA,IAAI,CAACD,aAAL,EAAoB;AAClB,IAAA,OAAOF,gBAAP,CAAA;AACD,GAAA;;EAED,IAAIE,aAAa,KAAKhC,OAAtB,EAA+B;AAC7B,IAAA,MAAMkC,OAAO,GAAGlC,OAAO,CAACiC,OAAR,CAAgB,SAAhB,CAAhB,CAAA;;AACA,IAAA,IAAIC,OAAO,IAAIA,OAAO,CAACC,UAAR,KAAuBH,aAAtC,EAAqD;AACnD,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;;IAED,IAAIE,OAAO,KAAK,IAAhB,EAAsB;AACpB,MAAA,OAAO,KAAP,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,OAAOJ,gBAAP,CAAA;AACD,CAzBD,CAAA;;AA2BA,MAAMM,UAAU,GAAGpC,OAAO,IAAI;EAC5B,IAAI,CAACA,OAAD,IAAYA,OAAO,CAACyB,QAAR,KAAqBY,IAAI,CAACC,YAA1C,EAAwD;AACtD,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAED,IAAItC,OAAO,CAACuC,SAAR,CAAkBC,QAAlB,CAA2B,UAA3B,CAAJ,EAA4C;AAC1C,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOxC,OAAO,CAACyC,QAAf,KAA4B,WAAhC,EAA6C;IAC3C,OAAOzC,OAAO,CAACyC,QAAf,CAAA;AACD,GAAA;;AAED,EAAA,OAAOzC,OAAO,CAAC0C,YAAR,CAAqB,UAArB,CAAA,IAAoC1C,OAAO,CAACE,YAAR,CAAqB,UAArB,CAAA,KAAqC,OAAhF,CAAA;AACD,CAdD,CAAA;;AAgBA,MAAMyC,cAAc,GAAG3C,OAAO,IAAI;AAChC,EAAA,IAAI,CAACH,QAAQ,CAAC+C,eAAT,CAAyBC,YAA9B,EAA4C;AAC1C,IAAA,OAAO,IAAP,CAAA;AACD,GAH+B;;;AAMhC,EAAA,IAAI,OAAO7C,OAAO,CAAC8C,WAAf,KAA+B,UAAnC,EAA+C;AAC7C,IAAA,MAAMC,IAAI,GAAG/C,OAAO,CAAC8C,WAAR,EAAb,CAAA;AACA,IAAA,OAAOC,IAAI,YAAYC,UAAhB,GAA6BD,IAA7B,GAAoC,IAA3C,CAAA;AACD,GAAA;;EAED,IAAI/C,OAAO,YAAYgD,UAAvB,EAAmC;AACjC,IAAA,OAAOhD,OAAP,CAAA;AACD,GAb+B;;;AAgBhC,EAAA,IAAI,CAACA,OAAO,CAACmC,UAAb,EAAyB;AACvB,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,OAAOQ,cAAc,CAAC3C,OAAO,CAACmC,UAAT,CAArB,CAAA;AACD,CArBD,CAAA;;AAuBA,MAAMc,IAAI,GAAG,MAAM,EAAnB,CAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMC,MAAM,GAAGlD,OAAO,IAAI;EACxBA,OAAO,CAACmD,YAAR,CADwB;AAEzB,CAFD,CAAA;;AAIA,MAAMC,SAAS,GAAG,MAAM;AACtB,EAAA,IAAItC,MAAM,CAACuC,MAAP,IAAiB,CAACxD,QAAQ,CAACyD,IAAT,CAAcZ,YAAd,CAA2B,mBAA3B,CAAtB,EAAuE;IACrE,OAAO5B,MAAM,CAACuC,MAAd,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,IAAP,CAAA;AACD,CAND,CAAA;;AAQA,MAAME,yBAAyB,GAAG,EAAlC,CAAA;;AAEA,MAAMC,kBAAkB,GAAGC,QAAQ,IAAI;AACrC,EAAA,IAAI5D,QAAQ,CAAC6D,UAAT,KAAwB,SAA5B,EAAuC;AACrC;AACA,IAAA,IAAI,CAACH,yBAAyB,CAAC5B,MAA/B,EAAuC;AACrC9B,MAAAA,QAAQ,CAAC8D,gBAAT,CAA0B,kBAA1B,EAA8C,MAAM;AAClD,QAAA,KAAK,MAAMF,QAAX,IAAuBF,yBAAvB,EAAkD;UAChDE,QAAQ,EAAA,CAAA;AACT,SAAA;OAHH,CAAA,CAAA;AAKD,KAAA;;IAEDF,yBAAyB,CAACK,IAA1B,CAA+BH,QAA/B,CAAA,CAAA;AACD,GAXD,MAWO;IACLA,QAAQ,EAAA,CAAA;AACT,GAAA;AACF,CAfD,CAAA;;AAiBA,MAAMI,KAAK,GAAG,MAAMhE,QAAQ,CAAC+C,eAAT,CAAyBkB,GAAzB,KAAiC,KAArD,CAAA;;AAEA,MAAMC,kBAAkB,GAAGC,MAAM,IAAI;AACnCR,EAAAA,kBAAkB,CAAC,MAAM;IACvB,MAAMS,CAAC,GAAGb,SAAS,EAAnB,CAAA;AACA;;AACA,IAAA,IAAIa,CAAJ,EAAO;AACL,MAAA,MAAMC,IAAI,GAAGF,MAAM,CAACG,IAApB,CAAA;AACA,MAAA,MAAMC,kBAAkB,GAAGH,CAAC,CAACI,EAAF,CAAKH,IAAL,CAA3B,CAAA;AACAD,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,CAAaF,GAAAA,MAAM,CAACM,eAApB,CAAA;AACAL,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,CAAWK,CAAAA,WAAX,GAAyBP,MAAzB,CAAA;;AACAC,MAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,CAAWM,CAAAA,UAAX,GAAwB,MAAM;AAC5BP,QAAAA,CAAC,CAACI,EAAF,CAAKH,IAAL,IAAaE,kBAAb,CAAA;QACA,OAAOJ,MAAM,CAACM,eAAd,CAAA;OAFF,CAAA;AAID,KAAA;AACF,GAbiB,CAAlB,CAAA;AAcD,CAfD,CAAA;;AAiBA,MAAMG,OAAO,GAAGhB,QAAQ,IAAI;AAC1B,EAAA,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;IAClCA,QAAQ,EAAA,CAAA;AACT,GAAA;AACF,CAJD,CAAA;;AAMA,MAAMiB,sBAAsB,GAAG,CAACjB,QAAD,EAAWkB,iBAAX,EAA8BC,iBAAiB,GAAG,IAAlD,KAA2D;EACxF,IAAI,CAACA,iBAAL,EAAwB;IACtBH,OAAO,CAAChB,QAAD,CAAP,CAAA;AACA,IAAA,OAAA;AACD,GAAA;;EAED,MAAMoB,eAAe,GAAG,CAAxB,CAAA;AACA,EAAA,MAAMC,gBAAgB,GAAGnE,gCAAgC,CAACgE,iBAAD,CAAhC,GAAsDE,eAA/E,CAAA;EAEA,IAAIE,MAAM,GAAG,KAAb,CAAA;;EAEA,MAAMC,OAAO,GAAG,CAAC;AAAEC,IAAAA,MAAAA;AAAF,GAAD,KAAgB;IAC9B,IAAIA,MAAM,KAAKN,iBAAf,EAAkC;AAChC,MAAA,OAAA;AACD,KAAA;;AAEDI,IAAAA,MAAM,GAAG,IAAT,CAAA;AACAJ,IAAAA,iBAAiB,CAACO,mBAAlB,CAAsCpG,cAAtC,EAAsDkG,OAAtD,CAAA,CAAA;IACAP,OAAO,CAAChB,QAAD,CAAP,CAAA;GAPF,CAAA;;AAUAkB,EAAAA,iBAAiB,CAAChB,gBAAlB,CAAmC7E,cAAnC,EAAmDkG,OAAnD,CAAA,CAAA;AACAG,EAAAA,UAAU,CAAC,MAAM;IACf,IAAI,CAACJ,MAAL,EAAa;MACX3D,oBAAoB,CAACuD,iBAAD,CAApB,CAAA;AACD,KAAA;GAHO,EAIPG,gBAJO,CAAV,CAAA;AAKD,CA3BD,CAAA;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,MAAMM,oBAAoB,GAAG,CAACC,IAAD,EAAOC,aAAP,EAAsBC,aAAtB,EAAqCC,cAArC,KAAwD;AACnF,EAAA,MAAMC,UAAU,GAAGJ,IAAI,CAAC1D,MAAxB,CAAA;EACA,IAAI+D,KAAK,GAAGL,IAAI,CAACM,OAAL,CAAaL,aAAb,CAAZ,CAFmF;AAKnF;;AACA,EAAA,IAAII,KAAK,KAAK,CAAC,CAAf,EAAkB;AAChB,IAAA,OAAO,CAACH,aAAD,IAAkBC,cAAlB,GAAmCH,IAAI,CAACI,UAAU,GAAG,CAAd,CAAvC,GAA0DJ,IAAI,CAAC,CAAD,CAArE,CAAA;AACD,GAAA;;AAEDK,EAAAA,KAAK,IAAIH,aAAa,GAAG,CAAH,GAAO,CAAC,CAA9B,CAAA;;AAEA,EAAA,IAAIC,cAAJ,EAAoB;AAClBE,IAAAA,KAAK,GAAG,CAACA,KAAK,GAAGD,UAAT,IAAuBA,UAA/B,CAAA;AACD,GAAA;;AAED,EAAA,OAAOJ,IAAI,CAAC3F,IAAI,CAACkG,GAAL,CAAS,CAAT,EAAYlG,IAAI,CAACmG,GAAL,CAASH,KAAT,EAAgBD,UAAU,GAAG,CAA7B,CAAZ,CAAD,CAAX,CAAA;AACD,CAjBD;;ACvSA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;AAEA,MAAMK,cAAc,GAAG,oBAAvB,CAAA;AACA,MAAMC,cAAc,GAAG,MAAvB,CAAA;AACA,MAAMC,aAAa,GAAG,QAAtB,CAAA;AACA,MAAMC,aAAa,GAAG,EAAtB;;AACA,IAAIC,QAAQ,GAAG,CAAf,CAAA;AACA,MAAMC,YAAY,GAAG;AACnBC,EAAAA,UAAU,EAAE,WADO;AAEnBC,EAAAA,UAAU,EAAE,UAAA;AAFO,CAArB,CAAA;AAKA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,CAAQ,CAC3B,OAD2B,EAE3B,UAF2B,EAG3B,SAH2B,EAI3B,WAJ2B,EAK3B,aAL2B,EAM3B,YAN2B,EAO3B,gBAP2B,EAQ3B,WAR2B,EAS3B,UAT2B,EAU3B,WAV2B,EAW3B,aAX2B,EAY3B,WAZ2B,EAa3B,SAb2B,EAc3B,UAd2B,EAe3B,OAf2B,EAgB3B,mBAhB2B,EAiB3B,YAjB2B,EAkB3B,WAlB2B,EAmB3B,UAnB2B,EAoB3B,aApB2B,EAqB3B,aArB2B,EAsB3B,aAtB2B,EAuB3B,WAvB2B,EAwB3B,cAxB2B,EAyB3B,eAzB2B,EA0B3B,cA1B2B,EA2B3B,eA3B2B,EA4B3B,YA5B2B,EA6B3B,OA7B2B,EA8B3B,MA9B2B,EA+B3B,QA/B2B,EAgC3B,OAhC2B,EAiC3B,QAjC2B,EAkC3B,QAlC2B,EAmC3B,SAnC2B,EAoC3B,UApC2B,EAqC3B,MArC2B,EAsC3B,QAtC2B,EAuC3B,cAvC2B,EAwC3B,QAxC2B,EAyC3B,MAzC2B,EA0C3B,kBA1C2B,EA2C3B,kBA3C2B,EA4C3B,OA5C2B,EA6C3B,OA7C2B,EA8C3B,QA9C2B,CAAR,CAArB,CAAA;AAiDA;AACA;AACA;;AAEA,SAASC,WAAT,CAAqBxG,OAArB,EAA8ByG,GAA9B,EAAmC;AACjC,EAAA,OAAQA,GAAG,IAAK,CAAEA,EAAAA,GAAI,KAAIP,QAAQ,EAAG,CAA9B,CAAA,IAAoClG,OAAO,CAACkG,QAA5C,IAAwDA,QAAQ,EAAvE,CAAA;AACD,CAAA;;AAED,SAASQ,QAAT,CAAkB1G,OAAlB,EAA2B;AACzB,EAAA,MAAMyG,GAAG,GAAGD,WAAW,CAACxG,OAAD,CAAvB,CAAA;EAEAA,OAAO,CAACkG,QAAR,GAAmBO,GAAnB,CAAA;EACAR,aAAa,CAACQ,GAAD,CAAb,GAAqBR,aAAa,CAACQ,GAAD,CAAb,IAAsB,EAA3C,CAAA;EAEA,OAAOR,aAAa,CAACQ,GAAD,CAApB,CAAA;AACD,CAAA;;AAED,SAASE,gBAAT,CAA0B3G,OAA1B,EAAmCqE,EAAnC,EAAuC;AACrC,EAAA,OAAO,SAASW,OAAT,CAAiB4B,KAAjB,EAAwB;IAC7BA,KAAK,CAACC,cAAN,GAAuB7G,OAAvB,CAAA;;IAEA,IAAIgF,OAAO,CAAC8B,MAAZ,EAAoB;MAClBC,YAAY,CAACC,GAAb,CAAiBhH,OAAjB,EAA0B4G,KAAK,CAACK,IAAhC,EAAsC5C,EAAtC,CAAA,CAAA;AACD,KAAA;;IAED,OAAOA,EAAE,CAAC6C,KAAH,CAASlH,OAAT,EAAkB,CAAC4G,KAAD,CAAlB,CAAP,CAAA;GAPF,CAAA;AASD,CAAA;;AAED,SAASO,0BAAT,CAAoCnH,OAApC,EAA6CC,QAA7C,EAAuDoE,EAAvD,EAA2D;AACzD,EAAA,OAAO,SAASW,OAAT,CAAiB4B,KAAjB,EAAwB;AAC7B,IAAA,MAAMQ,WAAW,GAAGpH,OAAO,CAACqH,gBAAR,CAAyBpH,QAAzB,CAApB,CAAA;;AAEA,IAAA,KAAK,IAAI;AAAEgF,MAAAA,MAAAA;AAAF,KAAA,GAAa2B,KAAtB,EAA6B3B,MAAM,IAAIA,MAAM,KAAK,IAAlD,EAAwDA,MAAM,GAAGA,MAAM,CAAC9C,UAAxE,EAAoF;AAClF,MAAA,KAAK,MAAMmF,UAAX,IAAyBF,WAAzB,EAAsC;QACpC,IAAIE,UAAU,KAAKrC,MAAnB,EAA2B;AACzB,UAAA,SAAA;AACD,SAAA;;QAED2B,KAAK,CAACC,cAAN,GAAuB5B,MAAvB,CAAA;;QAEA,IAAID,OAAO,CAAC8B,MAAZ,EAAoB;UAClBC,YAAY,CAACC,GAAb,CAAiBhH,OAAjB,EAA0B4G,KAAK,CAACK,IAAhC,EAAsChH,QAAtC,EAAgDoE,EAAhD,CAAA,CAAA;AACD,SAAA;;QAED,OAAOA,EAAE,CAAC6C,KAAH,CAASjC,MAAT,EAAiB,CAAC2B,KAAD,CAAjB,CAAP,CAAA;AACD,OAAA;AACF,KAAA;GAjBH,CAAA;AAmBD,CAAA;;AAED,SAASW,WAAT,CAAqBC,MAArB,EAA6BxC,OAA7B,EAAsCyC,kBAAkB,GAAG,IAA3D,EAAiE;EAC/D,OAAOvI,MAAM,CAACwI,MAAP,CAAcF,MAAd,CACJG,CAAAA,IADI,CACCf,KAAK,IAAIA,KAAK,CAACgB,eAAN,KAA0B5C,OAA1B,IAAqC4B,KAAK,CAACa,kBAAN,KAA6BA,kBAD5E,CAAP,CAAA;AAED,CAAA;;AAED,SAASI,mBAAT,CAA6BC,iBAA7B,EAAgD9C,OAAhD,EAAyD+C,kBAAzD,EAA6E;AAC3E,EAAA,MAAMC,UAAU,GAAG,OAAOhD,OAAP,KAAmB,QAAtC,CAAA;AACA,EAAA,MAAM4C,eAAe,GAAGI,UAAU,GAAGD,kBAAH,GAAwB/C,OAA1D,CAAA;AACA,EAAA,IAAIiD,SAAS,GAAGC,YAAY,CAACJ,iBAAD,CAA5B,CAAA;;AAEA,EAAA,IAAI,CAACxB,YAAY,CAAC6B,GAAb,CAAiBF,SAAjB,CAAL,EAAkC;AAChCA,IAAAA,SAAS,GAAGH,iBAAZ,CAAA;AACD,GAAA;;AAED,EAAA,OAAO,CAACE,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAP,CAAA;AACD,CAAA;;AAED,SAASG,UAAT,CAAoBpI,OAApB,EAA6B8H,iBAA7B,EAAgD9C,OAAhD,EAAyD+C,kBAAzD,EAA6EjB,MAA7E,EAAqF;AACnF,EAAA,IAAI,OAAOgB,iBAAP,KAA6B,QAA7B,IAAyC,CAAC9H,OAA9C,EAAuD;AACrD,IAAA,OAAA;AACD,GAAA;;EAED,IAAI,CAACgF,OAAL,EAAc;AACZA,IAAAA,OAAO,GAAG+C,kBAAV,CAAA;AACAA,IAAAA,kBAAkB,GAAG,IAArB,CAAA;AACD,GARkF;AAWnF;;;EACA,IAAID,iBAAiB,IAAI3B,YAAzB,EAAuC;IACrC,MAAMkC,YAAY,GAAGhE,EAAE,IAAI;MACzB,OAAO,UAAUuC,KAAV,EAAiB;QACtB,IAAI,CAACA,KAAK,CAAC0B,aAAP,IAAyB1B,KAAK,CAAC0B,aAAN,KAAwB1B,KAAK,CAACC,cAA9B,IAAgD,CAACD,KAAK,CAACC,cAAN,CAAqBrE,QAArB,CAA8BoE,KAAK,CAAC0B,aAApC,CAA9E,EAAmI;AACjI,UAAA,OAAOjE,EAAE,CAAChF,IAAH,CAAQ,IAAR,EAAcuH,KAAd,CAAP,CAAA;AACD,SAAA;OAHH,CAAA;KADF,CAAA;;AAQA,IAAA,IAAImB,kBAAJ,EAAwB;AACtBA,MAAAA,kBAAkB,GAAGM,YAAY,CAACN,kBAAD,CAAjC,CAAA;AACD,KAFD,MAEO;AACL/C,MAAAA,OAAO,GAAGqD,YAAY,CAACrD,OAAD,CAAtB,CAAA;AACD,KAAA;AACF,GAAA;;AAED,EAAA,MAAM,CAACgD,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAA,GAA2CJ,mBAAmB,CAACC,iBAAD,EAAoB9C,OAApB,EAA6B+C,kBAA7B,CAApE,CAAA;AACA,EAAA,MAAMP,MAAM,GAAGd,QAAQ,CAAC1G,OAAD,CAAvB,CAAA;AACA,EAAA,MAAMuI,QAAQ,GAAGf,MAAM,CAACS,SAAD,CAAN,KAAsBT,MAAM,CAACS,SAAD,CAAN,GAAoB,EAA1C,CAAjB,CAAA;AACA,EAAA,MAAMO,gBAAgB,GAAGjB,WAAW,CAACgB,QAAD,EAAWX,eAAX,EAA4BI,UAAU,GAAGhD,OAAH,GAAa,IAAnD,CAApC,CAAA;;AAEA,EAAA,IAAIwD,gBAAJ,EAAsB;AACpBA,IAAAA,gBAAgB,CAAC1B,MAAjB,GAA0B0B,gBAAgB,CAAC1B,MAAjB,IAA2BA,MAArD,CAAA;AAEA,IAAA,OAAA;AACD,GAAA;;AAED,EAAA,MAAML,GAAG,GAAGD,WAAW,CAACoB,eAAD,EAAkBE,iBAAiB,CAACW,OAAlB,CAA0B3C,cAA1B,EAA0C,EAA1C,CAAlB,CAAvB,CAAA;AACA,EAAA,MAAMzB,EAAE,GAAG2D,UAAU,GACnBb,0BAA0B,CAACnH,OAAD,EAAUgF,OAAV,EAAmB+C,kBAAnB,CADP,GAEnBpB,gBAAgB,CAAC3G,OAAD,EAAUgF,OAAV,CAFlB,CAAA;AAIAX,EAAAA,EAAE,CAACoD,kBAAH,GAAwBO,UAAU,GAAGhD,OAAH,GAAa,IAA/C,CAAA;EACAX,EAAE,CAACuD,eAAH,GAAqBA,eAArB,CAAA;EACAvD,EAAE,CAACyC,MAAH,GAAYA,MAAZ,CAAA;EACAzC,EAAE,CAAC6B,QAAH,GAAcO,GAAd,CAAA;AACA8B,EAAAA,QAAQ,CAAC9B,GAAD,CAAR,GAAgBpC,EAAhB,CAAA;AAEArE,EAAAA,OAAO,CAAC2D,gBAAR,CAAyBsE,SAAzB,EAAoC5D,EAApC,EAAwC2D,UAAxC,CAAA,CAAA;AACD,CAAA;;AAED,SAASU,aAAT,CAAuB1I,OAAvB,EAAgCwH,MAAhC,EAAwCS,SAAxC,EAAmDjD,OAAnD,EAA4DyC,kBAA5D,EAAgF;AAC9E,EAAA,MAAMpD,EAAE,GAAGkD,WAAW,CAACC,MAAM,CAACS,SAAD,CAAP,EAAoBjD,OAApB,EAA6ByC,kBAA7B,CAAtB,CAAA;;EAEA,IAAI,CAACpD,EAAL,EAAS;AACP,IAAA,OAAA;AACD,GAAA;;EAEDrE,OAAO,CAACkF,mBAAR,CAA4B+C,SAA5B,EAAuC5D,EAAvC,EAA2CsE,OAAO,CAAClB,kBAAD,CAAlD,CAAA,CAAA;EACA,OAAOD,MAAM,CAACS,SAAD,CAAN,CAAkB5D,EAAE,CAAC6B,QAArB,CAAP,CAAA;AACD,CAAA;;AAED,SAAS0C,wBAAT,CAAkC5I,OAAlC,EAA2CwH,MAA3C,EAAmDS,SAAnD,EAA8DY,SAA9D,EAAyE;AACvE,EAAA,MAAMC,iBAAiB,GAAGtB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C,CAAA;;EAEA,KAAK,MAAMc,UAAX,IAAyB7J,MAAM,CAAC8J,IAAP,CAAYF,iBAAZ,CAAzB,EAAyD;AACvD,IAAA,IAAIC,UAAU,CAAC3I,QAAX,CAAoByI,SAApB,CAAJ,EAAoC;AAClC,MAAA,MAAMjC,KAAK,GAAGkC,iBAAiB,CAACC,UAAD,CAA/B,CAAA;AACAL,MAAAA,aAAa,CAAC1I,OAAD,EAAUwH,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb,CAAA;AACD,KAAA;AACF,GAAA;AACF,CAAA;;AAED,SAASS,YAAT,CAAsBtB,KAAtB,EAA6B;AAC3B;EACAA,KAAK,GAAGA,KAAK,CAAC6B,OAAN,CAAc1C,cAAd,EAA8B,EAA9B,CAAR,CAAA;AACA,EAAA,OAAOI,YAAY,CAACS,KAAD,CAAZ,IAAuBA,KAA9B,CAAA;AACD,CAAA;;AAED,MAAMG,YAAY,GAAG;EACnBkC,EAAE,CAACjJ,OAAD,EAAU4G,KAAV,EAAiB5B,OAAjB,EAA0B+C,kBAA1B,EAA8C;IAC9CK,UAAU,CAACpI,OAAD,EAAU4G,KAAV,EAAiB5B,OAAjB,EAA0B+C,kBAA1B,EAA8C,KAA9C,CAAV,CAAA;GAFiB;;EAKnBmB,GAAG,CAAClJ,OAAD,EAAU4G,KAAV,EAAiB5B,OAAjB,EAA0B+C,kBAA1B,EAA8C;IAC/CK,UAAU,CAACpI,OAAD,EAAU4G,KAAV,EAAiB5B,OAAjB,EAA0B+C,kBAA1B,EAA8C,IAA9C,CAAV,CAAA;GANiB;;EASnBf,GAAG,CAAChH,OAAD,EAAU8H,iBAAV,EAA6B9C,OAA7B,EAAsC+C,kBAAtC,EAA0D;AAC3D,IAAA,IAAI,OAAOD,iBAAP,KAA6B,QAA7B,IAAyC,CAAC9H,OAA9C,EAAuD;AACrD,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM,CAACgI,UAAD,EAAaJ,eAAb,EAA8BK,SAA9B,CAAA,GAA2CJ,mBAAmB,CAACC,iBAAD,EAAoB9C,OAApB,EAA6B+C,kBAA7B,CAApE,CAAA;AACA,IAAA,MAAMoB,WAAW,GAAGlB,SAAS,KAAKH,iBAAlC,CAAA;AACA,IAAA,MAAMN,MAAM,GAAGd,QAAQ,CAAC1G,OAAD,CAAvB,CAAA;AACA,IAAA,MAAMoJ,WAAW,GAAGtB,iBAAiB,CAACzH,UAAlB,CAA6B,GAA7B,CAApB,CAAA;;AAEA,IAAA,IAAI,OAAOuH,eAAP,KAA2B,WAA/B,EAA4C;AAC1C;MACA,IAAI,CAACJ,MAAD,IAAW,CAACA,MAAM,CAACS,SAAD,CAAtB,EAAmC;AACjC,QAAA,OAAA;AACD,OAAA;;AAEDS,MAAAA,aAAa,CAAC1I,OAAD,EAAUwH,MAAV,EAAkBS,SAAlB,EAA6BL,eAA7B,EAA8CI,UAAU,GAAGhD,OAAH,GAAa,IAArE,CAAb,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIoE,WAAJ,EAAiB;MACf,KAAK,MAAMC,YAAX,IAA2BnK,MAAM,CAAC8J,IAAP,CAAYxB,MAAZ,CAA3B,EAAgD;AAC9CoB,QAAAA,wBAAwB,CAAC5I,OAAD,EAAUwH,MAAV,EAAkB6B,YAAlB,EAAgCvB,iBAAiB,CAACwB,KAAlB,CAAwB,CAAxB,CAAhC,CAAxB,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,MAAMR,iBAAiB,GAAGtB,MAAM,CAACS,SAAD,CAAN,IAAqB,EAA/C,CAAA;;IACA,KAAK,MAAMsB,WAAX,IAA0BrK,MAAM,CAAC8J,IAAP,CAAYF,iBAAZ,CAA1B,EAA0D;MACxD,MAAMC,UAAU,GAAGQ,WAAW,CAACd,OAAZ,CAAoBzC,aAApB,EAAmC,EAAnC,CAAnB,CAAA;;MAEA,IAAI,CAACmD,WAAD,IAAgBrB,iBAAiB,CAAC1H,QAAlB,CAA2B2I,UAA3B,CAApB,EAA4D;AAC1D,QAAA,MAAMnC,KAAK,GAAGkC,iBAAiB,CAACS,WAAD,CAA/B,CAAA;AACAb,QAAAA,aAAa,CAAC1I,OAAD,EAAUwH,MAAV,EAAkBS,SAAlB,EAA6BrB,KAAK,CAACgB,eAAnC,EAAoDhB,KAAK,CAACa,kBAA1D,CAAb,CAAA;AACD,OAAA;AACF,KAAA;GA3CgB;;AA8CnB+B,EAAAA,OAAO,CAACxJ,OAAD,EAAU4G,KAAV,EAAiB6C,IAAjB,EAAuB;AAC5B,IAAA,IAAI,OAAO7C,KAAP,KAAiB,QAAjB,IAA6B,CAAC5G,OAAlC,EAA2C;AACzC,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;IAED,MAAMiE,CAAC,GAAGb,SAAS,EAAnB,CAAA;AACA,IAAA,MAAM6E,SAAS,GAAGC,YAAY,CAACtB,KAAD,CAA9B,CAAA;AACA,IAAA,MAAMuC,WAAW,GAAGvC,KAAK,KAAKqB,SAA9B,CAAA;IAEA,IAAIyB,WAAW,GAAG,IAAlB,CAAA;IACA,IAAIC,OAAO,GAAG,IAAd,CAAA;IACA,IAAIC,cAAc,GAAG,IAArB,CAAA;IACA,IAAIC,gBAAgB,GAAG,KAAvB,CAAA;;IAEA,IAAIV,WAAW,IAAIlF,CAAnB,EAAsB;MACpByF,WAAW,GAAGzF,CAAC,CAAC3C,KAAF,CAAQsF,KAAR,EAAe6C,IAAf,CAAd,CAAA;AAEAxF,MAAAA,CAAC,CAACjE,OAAD,CAAD,CAAWwJ,OAAX,CAAmBE,WAAnB,CAAA,CAAA;AACAC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACI,oBAAZ,EAAX,CAAA;AACAF,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACK,6BAAZ,EAAlB,CAAA;AACAF,MAAAA,gBAAgB,GAAGH,WAAW,CAACM,kBAAZ,EAAnB,CAAA;AACD,KAAA;;AAED,IAAA,MAAMC,GAAG,GAAG,IAAI3I,KAAJ,CAAUsF,KAAV,EAAiB;MAAE+C,OAAF;AAAWO,MAAAA,UAAU,EAAE,IAAA;KAAxC,CAAZ,CAvB4B;;AA0B5B,IAAA,IAAI,OAAOT,IAAP,KAAgB,WAApB,EAAiC;MAC/B,KAAK,MAAMU,GAAX,IAAkBjL,MAAM,CAAC8J,IAAP,CAAYS,IAAZ,CAAlB,EAAqC;AACnCvK,QAAAA,MAAM,CAACkL,cAAP,CAAsBH,GAAtB,EAA2BE,GAA3B,EAAgC;AAC9BE,UAAAA,GAAG,GAAG;YACJ,OAAOZ,IAAI,CAACU,GAAD,CAAX,CAAA;AACD,WAAA;;SAHH,CAAA,CAAA;AAKD,OAAA;AACF,KAAA;;AAED,IAAA,IAAIN,gBAAJ,EAAsB;AACpBI,MAAAA,GAAG,CAACK,cAAJ,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAIV,cAAJ,EAAoB;MAClB5J,OAAO,CAACqB,aAAR,CAAsB4I,GAAtB,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAIA,GAAG,CAACJ,gBAAJ,IAAwBH,WAA5B,EAAyC;AACvCA,MAAAA,WAAW,CAACY,cAAZ,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,OAAOL,GAAP,CAAA;AACD,GAAA;;AA/FkB,CAArB;;AC7NA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA,MAAMM,UAAU,GAAG,IAAIC,GAAJ,EAAnB,CAAA;AAEA,aAAe;AACbC,EAAAA,GAAG,CAACzK,OAAD,EAAUmK,GAAV,EAAeO,QAAf,EAAyB;AAC1B,IAAA,IAAI,CAACH,UAAU,CAACpC,GAAX,CAAenI,OAAf,CAAL,EAA8B;AAC5BuK,MAAAA,UAAU,CAACE,GAAX,CAAezK,OAAf,EAAwB,IAAIwK,GAAJ,EAAxB,CAAA,CAAA;AACD,KAAA;;IAED,MAAMG,WAAW,GAAGJ,UAAU,CAACF,GAAX,CAAerK,OAAf,CAApB,CAL0B;AAQ1B;;AACA,IAAA,IAAI,CAAC2K,WAAW,CAACxC,GAAZ,CAAgBgC,GAAhB,CAAD,IAAyBQ,WAAW,CAACC,IAAZ,KAAqB,CAAlD,EAAqD;AACnD;AACAC,MAAAA,OAAO,CAACC,KAAR,CAAe,CAAA,4EAAA,EAA8EC,KAAK,CAACC,IAAN,CAAWL,WAAW,CAAC3B,IAAZ,EAAX,CAA+B,CAAA,CAA/B,CAAkC,CAA/H,CAAA,CAAA,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED2B,IAAAA,WAAW,CAACF,GAAZ,CAAgBN,GAAhB,EAAqBO,QAArB,CAAA,CAAA;GAhBW;;AAmBbL,EAAAA,GAAG,CAACrK,OAAD,EAAUmK,GAAV,EAAe;AAChB,IAAA,IAAII,UAAU,CAACpC,GAAX,CAAenI,OAAf,CAAJ,EAA6B;MAC3B,OAAOuK,UAAU,CAACF,GAAX,CAAerK,OAAf,EAAwBqK,GAAxB,CAA4BF,GAA5B,CAAA,IAAoC,IAA3C,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,IAAP,CAAA;GAxBW;;AA2Bbc,EAAAA,MAAM,CAACjL,OAAD,EAAUmK,GAAV,EAAe;AACnB,IAAA,IAAI,CAACI,UAAU,CAACpC,GAAX,CAAenI,OAAf,CAAL,EAA8B;AAC5B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM2K,WAAW,GAAGJ,UAAU,CAACF,GAAX,CAAerK,OAAf,CAApB,CAAA;AAEA2K,IAAAA,WAAW,CAACO,MAAZ,CAAmBf,GAAnB,EAPmB;;AAUnB,IAAA,IAAIQ,WAAW,CAACC,IAAZ,KAAqB,CAAzB,EAA4B;MAC1BL,UAAU,CAACW,MAAX,CAAkBlL,OAAlB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAxCY,CAAf;;ACbA;AACA;AACA;AACA;AACA;AACA;AAEA,SAASmL,aAAT,CAAuBC,KAAvB,EAA8B;EAC5B,IAAIA,KAAK,KAAK,MAAd,EAAsB;AACpB,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;EAED,IAAIA,KAAK,KAAK,OAAd,EAAuB;AACrB,IAAA,OAAO,KAAP,CAAA;AACD,GAAA;;EAED,IAAIA,KAAK,KAAKnK,MAAM,CAACmK,KAAD,CAAN,CAAchM,QAAd,EAAd,EAAwC;IACtC,OAAO6B,MAAM,CAACmK,KAAD,CAAb,CAAA;AACD,GAAA;;AAED,EAAA,IAAIA,KAAK,KAAK,EAAV,IAAgBA,KAAK,KAAK,MAA9B,EAAsC;AACpC,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B;AAC7B,IAAA,OAAOA,KAAP,CAAA;AACD,GAAA;;EAED,IAAI;IACF,OAAOC,IAAI,CAACC,KAAL,CAAWC,kBAAkB,CAACH,KAAD,CAA7B,CAAP,CAAA;AACD,GAFD,CAEE,OAAM,OAAA,EAAA;AACN,IAAA,OAAOA,KAAP,CAAA;AACD,GAAA;AACF,CAAA;;AAED,SAASI,gBAAT,CAA0BrB,GAA1B,EAA+B;AAC7B,EAAA,OAAOA,GAAG,CAAC1B,OAAJ,CAAY,QAAZ,EAAsBgD,GAAG,IAAK,CAAA,CAAA,EAAGA,GAAG,CAAClM,WAAJ,EAAkB,EAAnD,CAAP,CAAA;AACD,CAAA;;AAED,MAAMmM,WAAW,GAAG;AAClBC,EAAAA,gBAAgB,CAAC3L,OAAD,EAAUmK,GAAV,EAAeiB,KAAf,EAAsB;IACpCpL,OAAO,CAAC4L,YAAR,CAAsB,CAAUJ,QAAAA,EAAAA,gBAAgB,CAACrB,GAAD,CAAM,CAAtD,CAAA,EAAyDiB,KAAzD,CAAA,CAAA;GAFgB;;AAKlBS,EAAAA,mBAAmB,CAAC7L,OAAD,EAAUmK,GAAV,EAAe;IAChCnK,OAAO,CAAC8L,eAAR,CAAyB,CAAA,QAAA,EAAUN,gBAAgB,CAACrB,GAAD,CAAM,CAAzD,CAAA,CAAA,CAAA;GANgB;;EASlB4B,iBAAiB,CAAC/L,OAAD,EAAU;IACzB,IAAI,CAACA,OAAL,EAAc;AACZ,MAAA,OAAO,EAAP,CAAA;AACD,KAAA;;IAED,MAAMgM,UAAU,GAAG,EAAnB,CAAA;IACA,MAAMC,MAAM,GAAG/M,MAAM,CAAC8J,IAAP,CAAYhJ,OAAO,CAACkM,OAApB,CAA6BC,CAAAA,MAA7B,CAAoChC,GAAG,IAAIA,GAAG,CAAC9J,UAAJ,CAAe,IAAf,CAAwB,IAAA,CAAC8J,GAAG,CAAC9J,UAAJ,CAAe,UAAf,CAApE,CAAf,CAAA;;AAEA,IAAA,KAAK,MAAM8J,GAAX,IAAkB8B,MAAlB,EAA0B;MACxB,IAAIG,OAAO,GAAGjC,GAAG,CAAC1B,OAAJ,CAAY,KAAZ,EAAmB,EAAnB,CAAd,CAAA;AACA2D,MAAAA,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,CAAf,EAAkB9M,WAAlB,EAAA,GAAkC6M,OAAO,CAAC9C,KAAR,CAAc,CAAd,EAAiB8C,OAAO,CAACzK,MAAzB,CAA5C,CAAA;AACAqK,MAAAA,UAAU,CAACI,OAAD,CAAV,GAAsBjB,aAAa,CAACnL,OAAO,CAACkM,OAAR,CAAgB/B,GAAhB,CAAD,CAAnC,CAAA;AACD,KAAA;;AAED,IAAA,OAAO6B,UAAP,CAAA;GAvBgB;;AA0BlBM,EAAAA,gBAAgB,CAACtM,OAAD,EAAUmK,GAAV,EAAe;AAC7B,IAAA,OAAOgB,aAAa,CAACnL,OAAO,CAACE,YAAR,CAAsB,CAAUsL,QAAAA,EAAAA,gBAAgB,CAACrB,GAAD,CAAM,CAAA,CAAtD,CAAD,CAApB,CAAA;AACD,GAAA;;AA5BiB,CAApB;;ACvCA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;;AAEA,MAAMoC,MAAN,CAAa;AACX;AACkB,EAAA,WAAPC,OAAO,GAAG;AACnB,IAAA,OAAO,EAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAO,EAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,MAAM,IAAIuI,KAAJ,CAAU,qEAAV,CAAN,CAAA;AACD,GAAA;;EAEDC,UAAU,CAACC,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,IAAA,CAAKC,eAAL,CAAqBD,MAArB,CAAT,CAAA;AACAA,IAAAA,MAAM,GAAG,IAAA,CAAKE,iBAAL,CAAuBF,MAAvB,CAAT,CAAA;;IACA,IAAKG,CAAAA,gBAAL,CAAsBH,MAAtB,CAAA,CAAA;;AACA,IAAA,OAAOA,MAAP,CAAA;AACD,GAAA;;EAEDE,iBAAiB,CAACF,MAAD,EAAS;AACxB,IAAA,OAAOA,MAAP,CAAA;AACD,GAAA;;AAEDC,EAAAA,eAAe,CAACD,MAAD,EAAS5M,OAAT,EAAkB;AAC/B,IAAA,MAAMgN,UAAU,GAAGzL,SAAS,CAACvB,OAAD,CAAT,GAAqB0L,WAAW,CAACY,gBAAZ,CAA6BtM,OAA7B,EAAsC,QAAtC,CAArB,GAAuE,EAA1F,CAD+B;;AAG/B,IAAA,OAAO,EACL,GAAG,IAAKiN,CAAAA,WAAL,CAAiBT,OADf;MAEL,IAAI,OAAOQ,UAAP,KAAsB,QAAtB,GAAiCA,UAAjC,GAA8C,EAAlD,CAFK;AAGL,MAAA,IAAIzL,SAAS,CAACvB,OAAD,CAAT,GAAqB0L,WAAW,CAACK,iBAAZ,CAA8B/L,OAA9B,CAArB,GAA8D,EAAlE,CAHK;AAIL,MAAA,IAAI,OAAO4M,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,EAA1C,CAAA;KAJF,CAAA;AAMD,GAAA;;EAEDG,gBAAgB,CAACH,MAAD,EAASM,WAAW,GAAG,IAAKD,CAAAA,WAAL,CAAiBR,WAAxC,EAAqD;IACnE,KAAK,MAAMU,QAAX,IAAuBjO,MAAM,CAAC8J,IAAP,CAAYkE,WAAZ,CAAvB,EAAiD;AAC/C,MAAA,MAAME,aAAa,GAAGF,WAAW,CAACC,QAAD,CAAjC,CAAA;AACA,MAAA,MAAM/B,KAAK,GAAGwB,MAAM,CAACO,QAAD,CAApB,CAAA;AACA,MAAA,MAAME,SAAS,GAAG9L,SAAS,CAAC6J,KAAD,CAAT,GAAmB,SAAnB,GAA+BrM,MAAM,CAACqM,KAAD,CAAvD,CAAA;;MAEA,IAAI,CAAC,IAAIkC,MAAJ,CAAWF,aAAX,EAA0BG,IAA1B,CAA+BF,SAA/B,CAAL,EAAgD;AAC9C,QAAA,MAAM,IAAIG,SAAJ,CACH,GAAE,IAAKP,CAAAA,WAAL,CAAiB9I,IAAjB,CAAsBsJ,WAAtB,EAAoC,aAAYN,QAAS,CAAA,iBAAA,EAAmBE,SAAU,CAAuBD,qBAAAA,EAAAA,aAAc,IAD1H,CAAN,CAAA;AAGD,OAAA;AACF,KAAA;AACF,GAAA;;AAhDU;;ACdb;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMM,OAAO,GAAG,aAAhB,CAAA;AAEA;AACA;AACA;;AAEA,MAAMC,aAAN,SAA4BpB,MAA5B,CAAmC;AACjCU,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;AAC3B,IAAA,KAAA,EAAA,CAAA;AAEA5M,IAAAA,OAAO,GAAG0B,UAAU,CAAC1B,OAAD,CAApB,CAAA;;IACA,IAAI,CAACA,OAAL,EAAc;AACZ,MAAA,OAAA;AACD,KAAA;;IAED,IAAK4N,CAAAA,QAAL,GAAgB5N,OAAhB,CAAA;AACA,IAAA,IAAA,CAAK6N,OAAL,GAAe,IAAA,CAAKlB,UAAL,CAAgBC,MAAhB,CAAf,CAAA;IAEAkB,IAAI,CAACrD,GAAL,CAAS,IAAKmD,CAAAA,QAAd,EAAwB,IAAA,CAAKX,WAAL,CAAiBc,QAAzC,EAAmD,IAAnD,CAAA,CAAA;AACD,GAbgC;;;AAgBjCC,EAAAA,OAAO,GAAG;IACRF,IAAI,CAAC7C,MAAL,CAAY,IAAA,CAAK2C,QAAjB,EAA2B,IAAA,CAAKX,WAAL,CAAiBc,QAA5C,CAAA,CAAA;IACAhH,YAAY,CAACC,GAAb,CAAiB,IAAA,CAAK4G,QAAtB,EAAgC,IAAA,CAAKX,WAAL,CAAiBgB,SAAjD,CAAA,CAAA;;IAEA,KAAK,MAAMC,YAAX,IAA2BhP,MAAM,CAACiP,mBAAP,CAA2B,IAA3B,CAA3B,EAA6D;MAC3D,IAAKD,CAAAA,YAAL,IAAqB,IAArB,CAAA;AACD,KAAA;AACF,GAAA;;EAEDE,cAAc,CAAC3K,QAAD,EAAWzD,OAAX,EAAoBqO,UAAU,GAAG,IAAjC,EAAuC;AACnD3J,IAAAA,sBAAsB,CAACjB,QAAD,EAAWzD,OAAX,EAAoBqO,UAApB,CAAtB,CAAA;AACD,GAAA;;EAED1B,UAAU,CAACC,MAAD,EAAS;IACjBA,MAAM,GAAG,KAAKC,eAAL,CAAqBD,MAArB,EAA6B,IAAA,CAAKgB,QAAlC,CAAT,CAAA;AACAhB,IAAAA,MAAM,GAAG,IAAA,CAAKE,iBAAL,CAAuBF,MAAvB,CAAT,CAAA;;IACA,IAAKG,CAAAA,gBAAL,CAAsBH,MAAtB,CAAA,CAAA;;AACA,IAAA,OAAOA,MAAP,CAAA;AACD,GAlCgC;;;EAqCf,OAAX0B,WAAW,CAACtO,OAAD,EAAU;IAC1B,OAAO8N,IAAI,CAACzD,GAAL,CAAS3I,UAAU,CAAC1B,OAAD,CAAnB,EAA8B,IAAK+N,CAAAA,QAAnC,CAAP,CAAA;AACD,GAAA;;AAEyB,EAAA,OAAnBQ,mBAAmB,CAACvO,OAAD,EAAU4M,MAAM,GAAG,EAAnB,EAAuB;AAC/C,IAAA,OAAO,KAAK0B,WAAL,CAAiBtO,OAAjB,CAA6B,IAAA,IAAI,IAAJ,CAASA,OAAT,EAAkB,OAAO4M,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAxD,CAApC,CAAA;AACD,GAAA;;AAEiB,EAAA,WAAPc,OAAO,GAAG;AACnB,IAAA,OAAOA,OAAP,CAAA;AACD,GAAA;;AAEkB,EAAA,WAARK,QAAQ,GAAG;IACpB,OAAQ,CAAA,GAAA,EAAK,IAAK5J,CAAAA,IAAK,CAAvB,CAAA,CAAA;AACD,GAAA;;AAEmB,EAAA,WAAT8J,SAAS,GAAG;IACrB,OAAQ,CAAA,CAAA,EAAG,IAAKF,CAAAA,QAAS,CAAzB,CAAA,CAAA;AACD,GAAA;;EAEe,OAATS,SAAS,CAACtK,IAAD,EAAO;AACrB,IAAA,OAAQ,CAAEA,EAAAA,IAAK,CAAE,EAAA,IAAA,CAAK+J,SAAU,CAAhC,CAAA,CAAA;AACD,GAAA;;AA3DgC;;ACtBnC;AACA;AACA;AACA;AACA;AACA;;AAKA,MAAMQ,oBAAoB,GAAG,CAACC,SAAD,EAAYC,MAAM,GAAG,MAArB,KAAgC;AAC3D,EAAA,MAAMC,UAAU,GAAI,CAAA,aAAA,EAAeF,SAAS,CAACT,SAAU,CAAvD,CAAA,CAAA;AACA,EAAA,MAAM/J,IAAI,GAAGwK,SAAS,CAACvK,IAAvB,CAAA;AAEA4C,EAAAA,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B+O,UAA1B,EAAuC,CAAA,kBAAA,EAAoB1K,IAAK,CAAA,EAAA,CAAhE,EAAqE,UAAU0C,KAAV,EAAiB;IACpF,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcxG,QAAd,CAAuB,IAAA,CAAKyO,OAA5B,CAAJ,EAA0C;AACxCjI,MAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAIlI,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM6C,MAAM,GAAGvE,sBAAsB,CAAC,IAAD,CAAtB,IAAgC,IAAA,CAAKuB,OAAL,CAAc,CAAGiC,CAAAA,EAAAA,IAAK,EAAtB,CAA/C,CAAA;IACA,MAAMwG,QAAQ,GAAGgE,SAAS,CAACH,mBAAV,CAA8BtJ,MAA9B,CAAjB,CAVoF;;IAapFyF,QAAQ,CAACiE,MAAD,CAAR,EAAA,CAAA;GAbF,CAAA,CAAA;AAeD,CAnBD;;ACVA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMxK,MAAI,GAAG,OAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,UAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AAEA,MAAMe,WAAW,GAAI,CAAOb,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAMc,YAAY,GAAI,CAAQd,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAMe,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMC,iBAAe,GAAG,MAAxB,CAAA;AAEA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBvB,aAApB,CAAkC;AAChC;AACe,EAAA,WAAJxJ,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAJ+B;;;AAOhCgL,EAAAA,KAAK,GAAG;IACN,MAAMC,UAAU,GAAGrI,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoCkB,WAApC,CAAnB,CAAA;;IAEA,IAAIM,UAAU,CAACvF,gBAAf,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK+D,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BgE,iBAA/B,CAAA,CAAA;;IAEA,MAAMZ,UAAU,GAAG,IAAA,CAAKT,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCwM,iBAAjC,CAAnB,CAAA;;IACA,IAAKZ,CAAAA,cAAL,CAAoB,MAAM,IAAKiB,CAAAA,eAAL,EAA1B,EAAkD,IAAA,CAAKzB,QAAvD,EAAiES,UAAjE,CAAA,CAAA;AACD,GAlB+B;;;AAqBhCgB,EAAAA,eAAe,GAAG;IAChB,IAAKzB,CAAAA,QAAL,CAAc3C,MAAd,EAAA,CAAA;;AACAlE,IAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoCmB,YAApC,CAAA,CAAA;AACA,IAAA,IAAA,CAAKf,OAAL,EAAA,CAAA;AACD,GAzB+B;;;EA4BV,OAAf1J,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;AAC3B,MAAA,MAAMC,IAAI,GAAGL,KAAK,CAACX,mBAAN,CAA0B,IAA1B,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAO3B,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI2C,IAAI,CAAC3C,MAAD,CAAJ,KAAiB3N,SAAjB,IAA8B2N,MAAM,CAACvM,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuM,MAAM,KAAK,aAAvE,EAAsF;AACpF,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;AAED2C,MAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AA1C+B,CAAA;AA6ClC;AACA;AACA;;;AAEA6B,oBAAoB,CAACS,KAAD,EAAQ,OAAR,CAApB,CAAA;AAEA;AACA;AACA;;AAEAnL,kBAAkB,CAACmL,KAAD,CAAlB;;ACpFA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;AAEA,MAAM/K,MAAI,GAAG,QAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,WAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AAEA,MAAMC,mBAAiB,GAAG,QAA1B,CAAA;AACA,MAAMC,sBAAoB,GAAG,2BAA7B,CAAA;AACA,MAAMC,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AAEA;AACA;AACA;;AAEA,MAAMI,MAAN,SAAqBjC,aAArB,CAAmC;AACjC;AACe,EAAA,WAAJxJ,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAJgC;;;AAOjC0L,EAAAA,MAAM,GAAG;AACP;AACA,IAAA,IAAA,CAAKjC,QAAL,CAAchC,YAAd,CAA2B,cAA3B,EAA2C,IAAA,CAAKgC,QAAL,CAAcrL,SAAd,CAAwBsN,MAAxB,CAA+BJ,mBAA/B,CAA3C,CAAA,CAAA;AACD,GAVgC;;;EAaX,OAAfnL,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;AAC3B,MAAA,MAAMC,IAAI,GAAGK,MAAM,CAACrB,mBAAP,CAA2B,IAA3B,CAAb,CAAA;;MAEA,IAAI3B,MAAM,KAAK,QAAf,EAAyB;QACvB2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,OAAA;AACF,KANM,CAAP,CAAA;AAOD,GAAA;;AArBgC,CAAA;AAwBnC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDD,sBAAhD,EAAsE9I,KAAK,IAAI;AAC7EA,EAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;EAEA,MAAMwF,MAAM,GAAGlJ,KAAK,CAAC3B,MAAN,CAAahD,OAAb,CAAqByN,sBAArB,CAAf,CAAA;AACA,EAAA,MAAMH,IAAI,GAAGK,MAAM,CAACrB,mBAAP,CAA2BuB,MAA3B,CAAb,CAAA;AAEAP,EAAAA,IAAI,CAACM,MAAL,EAAA,CAAA;AACD,CAPD,CAAA,CAAA;AASA;AACA;AACA;;AAEA9L,kBAAkB,CAAC6L,MAAD,CAAlB;;ACrEA;AACA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;;AAEA,MAAMG,cAAc,GAAG;EACrBpI,IAAI,CAAC1H,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAAC+C,eAA9B,EAA+C;AACjD,IAAA,OAAO,GAAGoN,MAAH,CAAU,GAAGC,OAAO,CAAC9Q,SAAR,CAAkBkI,gBAAlB,CAAmChI,IAAnC,CAAwCW,OAAxC,EAAiDC,QAAjD,CAAb,CAAP,CAAA;GAFmB;;EAKrBiQ,OAAO,CAACjQ,QAAD,EAAWD,OAAO,GAAGH,QAAQ,CAAC+C,eAA9B,EAA+C;IACpD,OAAOqN,OAAO,CAAC9Q,SAAR,CAAkBsB,aAAlB,CAAgCpB,IAAhC,CAAqCW,OAArC,EAA8CC,QAA9C,CAAP,CAAA;GANmB;;AASrBkQ,EAAAA,QAAQ,CAACnQ,OAAD,EAAUC,QAAV,EAAoB;AAC1B,IAAA,OAAO,GAAG+P,MAAH,CAAU,GAAGhQ,OAAO,CAACmQ,QAArB,CAA+BhE,CAAAA,MAA/B,CAAsCiE,KAAK,IAAIA,KAAK,CAACC,OAAN,CAAcpQ,QAAd,CAA/C,CAAP,CAAA;GAVmB;;AAarBqQ,EAAAA,OAAO,CAACtQ,OAAD,EAAUC,QAAV,EAAoB;IACzB,MAAMqQ,OAAO,GAAG,EAAhB,CAAA;IACA,IAAIC,QAAQ,GAAGvQ,OAAO,CAACmC,UAAR,CAAmBF,OAAnB,CAA2BhC,QAA3B,CAAf,CAAA;;AAEA,IAAA,OAAOsQ,QAAP,EAAiB;MACfD,OAAO,CAAC1M,IAAR,CAAa2M,QAAb,CAAA,CAAA;MACAA,QAAQ,GAAGA,QAAQ,CAACpO,UAAT,CAAoBF,OAApB,CAA4BhC,QAA5B,CAAX,CAAA;AACD,KAAA;;AAED,IAAA,OAAOqQ,OAAP,CAAA;GAtBmB;;AAyBrBE,EAAAA,IAAI,CAACxQ,OAAD,EAAUC,QAAV,EAAoB;AACtB,IAAA,IAAIwQ,QAAQ,GAAGzQ,OAAO,CAAC0Q,sBAAvB,CAAA;;AAEA,IAAA,OAAOD,QAAP,EAAiB;AACf,MAAA,IAAIA,QAAQ,CAACJ,OAAT,CAAiBpQ,QAAjB,CAAJ,EAAgC;QAC9B,OAAO,CAACwQ,QAAD,CAAP,CAAA;AACD,OAAA;;MAEDA,QAAQ,GAAGA,QAAQ,CAACC,sBAApB,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,EAAP,CAAA;GApCmB;;AAsCrB;AACAC,EAAAA,IAAI,CAAC3Q,OAAD,EAAUC,QAAV,EAAoB;AACtB,IAAA,IAAI0Q,IAAI,GAAG3Q,OAAO,CAAC4Q,kBAAnB,CAAA;;AAEA,IAAA,OAAOD,IAAP,EAAa;AACX,MAAA,IAAIA,IAAI,CAACN,OAAL,CAAapQ,QAAb,CAAJ,EAA4B;QAC1B,OAAO,CAAC0Q,IAAD,CAAP,CAAA;AACD,OAAA;;MAEDA,IAAI,GAAGA,IAAI,CAACC,kBAAZ,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,EAAP,CAAA;GAlDmB;;EAqDrBC,iBAAiB,CAAC7Q,OAAD,EAAU;AACzB,IAAA,MAAM8Q,UAAU,GAAG,CACjB,GADiB,EAEjB,QAFiB,EAGjB,OAHiB,EAIjB,UAJiB,EAKjB,QALiB,EAMjB,SANiB,EAOjB,YAPiB,EAQjB,0BARiB,CAAA,CASjBC,GATiB,CASb9Q,QAAQ,IAAK,CAAEA,EAAAA,QAAS,CATX,qBAAA,CAAA,CAAA,CASmC+Q,IATnC,CASwC,GATxC,CAAnB,CAAA;IAWA,OAAO,IAAA,CAAKrJ,IAAL,CAAUmJ,UAAV,EAAsB9Q,OAAtB,CAAA,CAA+BmM,MAA/B,CAAsC8E,EAAE,IAAI,CAAC7O,UAAU,CAAC6O,EAAD,CAAX,IAAmBrP,SAAS,CAACqP,EAAD,CAAxE,CAAP,CAAA;AACD,GAAA;;AAlEoB,CAAvB;;ACbA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;AAEA,MAAM9M,MAAI,GAAG,OAAb,CAAA;AACA,MAAM8J,WAAS,GAAG,WAAlB,CAAA;AACA,MAAMiD,gBAAgB,GAAI,CAAYjD,UAAAA,EAAAA,WAAU,CAAhD,CAAA,CAAA;AACA,MAAMkD,eAAe,GAAI,CAAWlD,SAAAA,EAAAA,WAAU,CAA9C,CAAA,CAAA;AACA,MAAMmD,cAAc,GAAI,CAAUnD,QAAAA,EAAAA,WAAU,CAA5C,CAAA,CAAA;AACA,MAAMoD,iBAAiB,GAAI,CAAapD,WAAAA,EAAAA,WAAU,CAAlD,CAAA,CAAA;AACA,MAAMqD,eAAe,GAAI,CAAWrD,SAAAA,EAAAA,WAAU,CAA9C,CAAA,CAAA;AACA,MAAMsD,kBAAkB,GAAG,OAA3B,CAAA;AACA,MAAMC,gBAAgB,GAAG,KAAzB,CAAA;AACA,MAAMC,wBAAwB,GAAG,eAAjC,CAAA;AACA,MAAMC,eAAe,GAAG,EAAxB,CAAA;AAEA,MAAMlF,SAAO,GAAG;AACdmF,EAAAA,YAAY,EAAE,IADA;AAEdC,EAAAA,aAAa,EAAE,IAFD;AAGdC,EAAAA,WAAW,EAAE,IAAA;AAHC,CAAhB,CAAA;AAMA,MAAMpF,aAAW,GAAG;AAClBkF,EAAAA,YAAY,EAAE,iBADI;AAElBC,EAAAA,aAAa,EAAE,iBAFG;AAGlBC,EAAAA,WAAW,EAAE,iBAAA;AAHK,CAApB,CAAA;AAMA;AACA;AACA;;AAEA,MAAMC,KAAN,SAAoBvF,MAApB,CAA2B;AACzBU,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;AAC3B,IAAA,KAAA,EAAA,CAAA;IACA,IAAKgB,CAAAA,QAAL,GAAgB5N,OAAhB,CAAA;;IAEA,IAAI,CAACA,OAAD,IAAY,CAAC8R,KAAK,CAACC,WAAN,EAAjB,EAAsC;AACpC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKlE,OAAL,GAAe,IAAA,CAAKlB,UAAL,CAAgBC,MAAhB,CAAf,CAAA;IACA,IAAKoF,CAAAA,OAAL,GAAe,CAAf,CAAA;AACA,IAAA,IAAA,CAAKC,qBAAL,GAA6BtJ,OAAO,CAAC7H,MAAM,CAACoR,YAAR,CAApC,CAAA;;AACA,IAAA,IAAA,CAAKC,WAAL,EAAA,CAAA;AACD,GAbwB;;;AAgBP,EAAA,WAAP3F,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GA1BwB;;;AA6BzB6J,EAAAA,OAAO,GAAG;AACRjH,IAAAA,YAAY,CAACC,GAAb,CAAiB,IAAK4G,CAAAA,QAAtB,EAAgCK,WAAhC,CAAA,CAAA;AACD,GA/BwB;;;EAkCzBmE,MAAM,CAACxL,KAAD,EAAQ;IACZ,IAAI,CAAC,IAAKqL,CAAAA,qBAAV,EAAiC;MAC/B,IAAKD,CAAAA,OAAL,GAAepL,KAAK,CAACyL,OAAN,CAAc,CAAd,EAAiBC,OAAhC,CAAA;AAEA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKC,CAAAA,uBAAL,CAA6B3L,KAA7B,CAAJ,EAAyC;AACvC,MAAA,IAAA,CAAKoL,OAAL,GAAepL,KAAK,CAAC0L,OAArB,CAAA;AACD,KAAA;AACF,GAAA;;EAEDE,IAAI,CAAC5L,KAAD,EAAQ;AACV,IAAA,IAAI,IAAK2L,CAAAA,uBAAL,CAA6B3L,KAA7B,CAAJ,EAAyC;AACvC,MAAA,IAAA,CAAKoL,OAAL,GAAepL,KAAK,CAAC0L,OAAN,GAAgB,KAAKN,OAApC,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKS,YAAL,EAAA,CAAA;;AACAhO,IAAAA,OAAO,CAAC,IAAA,CAAKoJ,OAAL,CAAagE,WAAd,CAAP,CAAA;AACD,GAAA;;EAEDa,KAAK,CAAC9L,KAAD,EAAQ;IACX,IAAKoL,CAAAA,OAAL,GAAepL,KAAK,CAACyL,OAAN,IAAiBzL,KAAK,CAACyL,OAAN,CAAc1Q,MAAd,GAAuB,CAAxC,GACb,CADa,GAEbiF,KAAK,CAACyL,OAAN,CAAc,CAAd,CAAiBC,CAAAA,OAAjB,GAA2B,IAAA,CAAKN,OAFlC,CAAA;AAGD,GAAA;;AAEDS,EAAAA,YAAY,GAAG;IACb,MAAME,SAAS,GAAGjT,IAAI,CAACkT,GAAL,CAAS,IAAA,CAAKZ,OAAd,CAAlB,CAAA;;IAEA,IAAIW,SAAS,IAAIjB,eAAjB,EAAkC;AAChC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMmB,SAAS,GAAGF,SAAS,GAAG,KAAKX,OAAnC,CAAA;IAEA,IAAKA,CAAAA,OAAL,GAAe,CAAf,CAAA;;IAEA,IAAI,CAACa,SAAL,EAAgB;AACd,MAAA,OAAA;AACD,KAAA;;AAEDpO,IAAAA,OAAO,CAACoO,SAAS,GAAG,CAAZ,GAAgB,IAAKhF,CAAAA,OAAL,CAAa+D,aAA7B,GAA6C,IAAA,CAAK/D,OAAL,CAAa8D,YAA3D,CAAP,CAAA;AACD,GAAA;;AAEDQ,EAAAA,WAAW,GAAG;IACZ,IAAI,IAAA,CAAKF,qBAAT,EAAgC;AAC9BlL,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+ByD,iBAA/B,EAAkDzK,KAAK,IAAI,IAAA,CAAKwL,MAAL,CAAYxL,KAAZ,CAA3D,CAAA,CAAA;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+B0D,eAA/B,EAAgD1K,KAAK,IAAI,IAAA,CAAK4L,IAAL,CAAU5L,KAAV,CAAzD,CAAA,CAAA;;AAEA,MAAA,IAAA,CAAKgH,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BrB,wBAA5B,CAAA,CAAA;AACD,KALD,MAKO;AACL1K,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BsD,gBAA/B,EAAiDtK,KAAK,IAAI,IAAA,CAAKwL,MAAL,CAAYxL,KAAZ,CAA1D,CAAA,CAAA;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BuD,eAA/B,EAAgDvK,KAAK,IAAI,IAAA,CAAK8L,KAAL,CAAW9L,KAAX,CAAzD,CAAA,CAAA;AACAG,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BwD,cAA/B,EAA+CxK,KAAK,IAAI,IAAA,CAAK4L,IAAL,CAAU5L,KAAV,CAAxD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAED2L,uBAAuB,CAAC3L,KAAD,EAAQ;AAC7B,IAAA,OAAO,IAAKqL,CAAAA,qBAAL,KAA+BrL,KAAK,CAACmM,WAAN,KAAsBvB,gBAAtB,IAA0C5K,KAAK,CAACmM,WAAN,KAAsBxB,kBAA/F,CAAP,CAAA;AACD,GA9FwB;;;AAiGP,EAAA,OAAXQ,WAAW,GAAG;IACnB,OAAO,cAAA,IAAkBlS,QAAQ,CAAC+C,eAA3B,IAA8CoQ,SAAS,CAACC,cAAV,GAA2B,CAAhF,CAAA;AACD,GAAA;;AAnGwB;;AC3C3B;AACA;AACA;AACA;AACA;AACA;AAiBA;AACA;AACA;;AAEA,MAAM9O,MAAI,GAAG,UAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,aAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AAEA,MAAM0D,gBAAc,GAAG,WAAvB,CAAA;AACA,MAAMC,iBAAe,GAAG,YAAxB,CAAA;AACA,MAAMC,sBAAsB,GAAG,GAA/B;;AAEA,MAAMC,UAAU,GAAG,MAAnB,CAAA;AACA,MAAMC,UAAU,GAAG,MAAnB,CAAA;AACA,MAAMC,cAAc,GAAG,MAAvB,CAAA;AACA,MAAMC,eAAe,GAAG,OAAxB,CAAA;AAEA,MAAMC,WAAW,GAAI,CAAOxF,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAMyF,UAAU,GAAI,CAAMzF,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAM0F,eAAa,GAAI,CAAS1F,OAAAA,EAAAA,WAAU,CAA1C,CAAA,CAAA;AACA,MAAM2F,kBAAgB,GAAI,CAAY3F,UAAAA,EAAAA,WAAU,CAAhD,CAAA,CAAA;AACA,MAAM4F,kBAAgB,GAAI,CAAY5F,UAAAA,EAAAA,WAAU,CAAhD,CAAA,CAAA;AACA,MAAM6F,gBAAgB,GAAI,CAAW7F,SAAAA,EAAAA,WAAU,CAA/C,CAAA,CAAA;AACA,MAAM8F,qBAAmB,GAAI,CAAA,IAAA,EAAM9F,WAAU,CAAA,EAAEuB,cAAa,CAA5D,CAAA,CAAA;AACA,MAAMG,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AAEA,MAAMwE,mBAAmB,GAAG,UAA5B,CAAA;AACA,MAAMvE,mBAAiB,GAAG,QAA1B,CAAA;AACA,MAAMwE,gBAAgB,GAAG,OAAzB,CAAA;AACA,MAAMC,cAAc,GAAG,mBAAvB,CAAA;AACA,MAAMC,gBAAgB,GAAG,qBAAzB,CAAA;AACA,MAAMC,eAAe,GAAG,oBAAxB,CAAA;AACA,MAAMC,eAAe,GAAG,oBAAxB,CAAA;AAEA,MAAMC,eAAe,GAAG,SAAxB,CAAA;AACA,MAAMC,aAAa,GAAG,gBAAtB,CAAA;AACA,MAAMC,oBAAoB,GAAGF,eAAe,GAAGC,aAA/C,CAAA;AACA,MAAME,iBAAiB,GAAG,oBAA1B,CAAA;AACA,MAAMC,mBAAmB,GAAG,sBAA5B,CAAA;AACA,MAAMC,mBAAmB,GAAG,qCAA5B,CAAA;AACA,MAAMC,kBAAkB,GAAG,2BAA3B,CAAA;AAEA,MAAMC,gBAAgB,GAAG;EACvB,CAAC3B,gBAAD,GAAkBM,eADK;AAEvB,EAAA,CAACL,iBAAD,GAAmBI,cAAAA;AAFI,CAAzB,CAAA;AAKA,MAAM/G,SAAO,GAAG;AACdsI,EAAAA,QAAQ,EAAE,IADI;AAEdC,EAAAA,QAAQ,EAAE,IAFI;AAGdC,EAAAA,KAAK,EAAE,OAHO;AAIdC,EAAAA,IAAI,EAAE,KAJQ;AAKdC,EAAAA,KAAK,EAAE,IALO;AAMdC,EAAAA,IAAI,EAAE,IAAA;AANQ,CAAhB,CAAA;AASA,MAAM1I,aAAW,GAAG;AAClBqI,EAAAA,QAAQ,EAAE,kBADQ;AAElBC,EAAAA,QAAQ,EAAE,SAFQ;AAGlBE,EAAAA,IAAI,EAAE,kBAHY;AAIlBD,EAAAA,KAAK,EAAE,kBAJW;AAKlBE,EAAAA,KAAK,EAAE,SALW;AAMlBC,EAAAA,IAAI,EAAE,SAAA;AANY,CAApB,CAAA;AASA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuBzH,aAAvB,CAAqC;AACnCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKyI,CAAAA,SAAL,GAAiB,IAAjB,CAAA;IACA,IAAKC,CAAAA,cAAL,GAAsB,IAAtB,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;IACA,IAAKC,CAAAA,YAAL,GAAoB,IAApB,CAAA;IAEA,IAAKC,CAAAA,kBAAL,GAA0B3F,cAAc,CAACG,OAAf,CAAuBwE,mBAAvB,EAA4C,IAAK9G,CAAAA,QAAjD,CAA1B,CAAA;;AACA,IAAA,IAAA,CAAK+H,kBAAL,EAAA,CAAA;;AAEA,IAAA,IAAI,KAAK9H,OAAL,CAAaoH,IAAb,KAAsBjB,mBAA1B,EAA+C;AAC7C,MAAA,IAAA,CAAK4B,KAAL,EAAA,CAAA;AACD,KAAA;AACF,GAhBkC;;;AAmBjB,EAAA,WAAPpJ,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GA7BkC;;;AAgCnCwM,EAAAA,IAAI,GAAG;IACL,IAAKkF,CAAAA,MAAL,CAAYxC,UAAZ,CAAA,CAAA;AACD,GAAA;;AAEDyC,EAAAA,eAAe,GAAG;AAChB;AACA;AACA;IACA,IAAI,CAACjW,QAAQ,CAACkW,MAAV,IAAoBnU,SAAS,CAAC,IAAA,CAAKgM,QAAN,CAAjC,EAAkD;AAChD,MAAA,IAAA,CAAK+C,IAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDH,EAAAA,IAAI,GAAG;IACL,IAAKqF,CAAAA,MAAL,CAAYvC,UAAZ,CAAA,CAAA;AACD,GAAA;;AAED0B,EAAAA,KAAK,GAAG;IACN,IAAI,IAAA,CAAKO,UAAT,EAAqB;MACnBnU,oBAAoB,CAAC,IAAKwM,CAAAA,QAAN,CAApB,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKoI,cAAL,EAAA,CAAA;AACD,GAAA;;AAEDJ,EAAAA,KAAK,GAAG;AACN,IAAA,IAAA,CAAKI,cAAL,EAAA,CAAA;;AACA,IAAA,IAAA,CAAKC,eAAL,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAKZ,SAAL,GAAiBa,WAAW,CAAC,MAAM,IAAA,CAAKJ,eAAL,EAAP,EAA+B,IAAA,CAAKjI,OAAL,CAAaiH,QAA5C,CAA5B,CAAA;AACD,GAAA;;AAEDqB,EAAAA,iBAAiB,GAAG;AAClB,IAAA,IAAI,CAAC,IAAA,CAAKtI,OAAL,CAAaoH,IAAlB,EAAwB;AACtB,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKM,UAAT,EAAqB;MACnBxO,YAAY,CAACmC,GAAb,CAAiB,IAAK0E,CAAAA,QAAtB,EAAgC8F,UAAhC,EAA4C,MAAM,IAAKkC,CAAAA,KAAL,EAAlD,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKA,KAAL,EAAA,CAAA;AACD,GAAA;;EAEDQ,EAAE,CAAC1Q,KAAD,EAAQ;AACR,IAAA,MAAM2Q,KAAK,GAAG,IAAKC,CAAAA,SAAL,EAAd,CAAA;;IACA,IAAI5Q,KAAK,GAAG2Q,KAAK,CAAC1U,MAAN,GAAe,CAAvB,IAA4B+D,KAAK,GAAG,CAAxC,EAA2C;AACzC,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK6P,UAAT,EAAqB;AACnBxO,MAAAA,YAAY,CAACmC,GAAb,CAAiB,IAAA,CAAK0E,QAAtB,EAAgC8F,UAAhC,EAA4C,MAAM,IAAA,CAAK0C,EAAL,CAAQ1Q,KAAR,CAAlD,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED,MAAM6Q,WAAW,GAAG,IAAKC,CAAAA,aAAL,CAAmB,IAAKC,CAAAA,UAAL,EAAnB,CAApB,CAAA;;IACA,IAAIF,WAAW,KAAK7Q,KAApB,EAA2B;AACzB,MAAA,OAAA;AACD,KAAA;;IAED,MAAMgR,KAAK,GAAGhR,KAAK,GAAG6Q,WAAR,GAAsBlD,UAAtB,GAAmCC,UAAjD,CAAA;;AAEA,IAAA,IAAA,CAAKuC,MAAL,CAAYa,KAAZ,EAAmBL,KAAK,CAAC3Q,KAAD,CAAxB,CAAA,CAAA;AACD,GAAA;;AAEDsI,EAAAA,OAAO,GAAG;IACR,IAAI,IAAA,CAAKyH,YAAT,EAAuB;MACrB,IAAKA,CAAAA,YAAL,CAAkBzH,OAAlB,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,KAAA,CAAMA,OAAN,EAAA,CAAA;AACD,GAxGkC;;;EA2GnClB,iBAAiB,CAACF,MAAD,EAAS;AACxBA,IAAAA,MAAM,CAAC+J,eAAP,GAAyB/J,MAAM,CAACkI,QAAhC,CAAA;AACA,IAAA,OAAOlI,MAAP,CAAA;AACD,GAAA;;AAED+I,EAAAA,kBAAkB,GAAG;AACnB,IAAA,IAAI,IAAK9H,CAAAA,OAAL,CAAakH,QAAjB,EAA2B;AACzBhO,MAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+B+F,eAA/B,EAA8C/M,KAAK,IAAI,IAAA,CAAKgQ,QAAL,CAAchQ,KAAd,CAAvD,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,KAAKiH,OAAL,CAAamH,KAAb,KAAuB,OAA3B,EAAoC;MAClCjO,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+BgG,kBAA/B,EAAiD,MAAM,IAAKoB,CAAAA,KAAL,EAAvD,CAAA,CAAA;MACAjO,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+BiG,kBAA/B,EAAiD,MAAM,IAAKsC,CAAAA,iBAAL,EAAvD,CAAA,CAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAKtI,OAAL,CAAaqH,KAAb,IAAsBpD,KAAK,CAACC,WAAN,EAA1B,EAA+C;AAC7C,MAAA,IAAA,CAAK8E,uBAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,uBAAuB,GAAG;AACxB,IAAA,KAAK,MAAMC,GAAX,IAAkB/G,cAAc,CAACpI,IAAf,CAAoB8M,iBAApB,EAAuC,IAAA,CAAK7G,QAA5C,CAAlB,EAAyE;AACvE7G,MAAAA,YAAY,CAACkC,EAAb,CAAgB6N,GAAhB,EAAqBhD,gBAArB,EAAuClN,KAAK,IAAIA,KAAK,CAAC0D,cAAN,EAAhD,CAAA,CAAA;AACD,KAAA;;IAED,MAAMyM,WAAW,GAAG,MAAM;AACxB,MAAA,IAAI,KAAKlJ,OAAL,CAAamH,KAAb,KAAuB,OAA3B,EAAoC;AAClC,QAAA,OAAA;AACD,OAHuB;AAMxB;AACA;AACA;AACA;AACA;AACA;;;AAEA,MAAA,IAAA,CAAKA,KAAL,EAAA,CAAA;;MACA,IAAI,IAAA,CAAKQ,YAAT,EAAuB;QACrBwB,YAAY,CAAC,IAAKxB,CAAAA,YAAN,CAAZ,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKA,YAAL,GAAoBrQ,UAAU,CAAC,MAAM,IAAKgR,CAAAA,iBAAL,EAAP,EAAiC/C,sBAAsB,GAAG,IAAA,CAAKvF,OAAL,CAAaiH,QAAvE,CAA9B,CAAA;KAlBF,CAAA;;AAqBA,IAAA,MAAMmC,WAAW,GAAG;MAClBtF,YAAY,EAAE,MAAM,IAAA,CAAKkE,MAAL,CAAY,KAAKqB,iBAAL,CAAuB3D,cAAvB,CAAZ,CADF;MAElB3B,aAAa,EAAE,MAAM,IAAA,CAAKiE,MAAL,CAAY,KAAKqB,iBAAL,CAAuB1D,eAAvB,CAAZ,CAFH;AAGlB3B,MAAAA,WAAW,EAAEkF,WAAAA;KAHf,CAAA;IAMA,IAAKtB,CAAAA,YAAL,GAAoB,IAAI3D,KAAJ,CAAU,IAAKlE,CAAAA,QAAf,EAAyBqJ,WAAzB,CAApB,CAAA;AACD,GAAA;;EAEDL,QAAQ,CAAChQ,KAAD,EAAQ;IACd,IAAI,iBAAA,CAAkB2G,IAAlB,CAAuB3G,KAAK,CAAC3B,MAAN,CAAa4J,OAApC,CAAJ,EAAkD;AAChD,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMgE,SAAS,GAAGgC,gBAAgB,CAACjO,KAAK,CAACuD,GAAP,CAAlC,CAAA;;AACA,IAAA,IAAI0I,SAAJ,EAAe;AACbjM,MAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;;AACA,MAAA,IAAA,CAAKuL,MAAL,CAAY,IAAA,CAAKqB,iBAAL,CAAuBrE,SAAvB,CAAZ,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAED2D,aAAa,CAACxW,OAAD,EAAU;AACrB,IAAA,OAAO,KAAKsW,SAAL,EAAA,CAAiB3Q,OAAjB,CAAyB3F,OAAzB,CAAP,CAAA;AACD,GAAA;;EAEDmX,0BAA0B,CAACzR,KAAD,EAAQ;IAChC,IAAI,CAAC,IAAKgQ,CAAAA,kBAAV,EAA8B;AAC5B,MAAA,OAAA;AACD,KAAA;;IAED,MAAM0B,eAAe,GAAGrH,cAAc,CAACG,OAAf,CAAuBoE,eAAvB,EAAwC,IAAKoB,CAAAA,kBAA7C,CAAxB,CAAA;AAEA0B,IAAAA,eAAe,CAAC7U,SAAhB,CAA0B0I,MAA1B,CAAiCwE,mBAAjC,CAAA,CAAA;IACA2H,eAAe,CAACtL,eAAhB,CAAgC,cAAhC,CAAA,CAAA;AAEA,IAAA,MAAMuL,kBAAkB,GAAGtH,cAAc,CAACG,OAAf,CAAwB,CAAqBxK,mBAAAA,EAAAA,KAAM,CAAnD,EAAA,CAAA,EAAwD,IAAKgQ,CAAAA,kBAA7D,CAA3B,CAAA;;AAEA,IAAA,IAAI2B,kBAAJ,EAAwB;AACtBA,MAAAA,kBAAkB,CAAC9U,SAAnB,CAA6BuQ,GAA7B,CAAiCrD,mBAAjC,CAAA,CAAA;AACA4H,MAAAA,kBAAkB,CAACzL,YAAnB,CAAgC,cAAhC,EAAgD,MAAhD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDqK,EAAAA,eAAe,GAAG;AAChB,IAAA,MAAMjW,OAAO,GAAG,IAAA,CAAKsV,cAAL,IAAuB,IAAA,CAAKmB,UAAL,EAAvC,CAAA;;IAEA,IAAI,CAACzW,OAAL,EAAc;AACZ,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMsX,eAAe,GAAGrW,MAAM,CAACsW,QAAP,CAAgBvX,OAAO,CAACE,YAAR,CAAqB,kBAArB,CAAhB,EAA0D,EAA1D,CAAxB,CAAA;IAEA,IAAK2N,CAAAA,OAAL,CAAaiH,QAAb,GAAwBwC,eAAe,IAAI,IAAA,CAAKzJ,OAAL,CAAa8I,eAAxD,CAAA;AACD,GAAA;;AAEDd,EAAAA,MAAM,CAACa,KAAD,EAAQ1W,OAAO,GAAG,IAAlB,EAAwB;IAC5B,IAAI,IAAA,CAAKuV,UAAT,EAAqB;AACnB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMjQ,aAAa,GAAG,IAAKmR,CAAAA,UAAL,EAAtB,CAAA;;AACA,IAAA,MAAMe,MAAM,GAAGd,KAAK,KAAKrD,UAAzB,CAAA;AACA,IAAA,MAAMoE,WAAW,GAAGzX,OAAO,IAAIoF,oBAAoB,CAAC,KAAKkR,SAAL,EAAD,EAAmBhR,aAAnB,EAAkCkS,MAAlC,EAA0C,KAAK3J,OAAL,CAAasH,IAAvD,CAAnD,CAAA;;IAEA,IAAIsC,WAAW,KAAKnS,aAApB,EAAmC;AACjC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMoS,gBAAgB,GAAG,IAAA,CAAKlB,aAAL,CAAmBiB,WAAnB,CAAzB,CAAA;;IAEA,MAAME,YAAY,GAAGnJ,SAAS,IAAI;MAChC,OAAOzH,YAAY,CAACyC,OAAb,CAAqB,KAAKoE,QAA1B,EAAoCY,SAApC,EAA+C;AACpDlG,QAAAA,aAAa,EAAEmP,WADqC;AAEpD5E,QAAAA,SAAS,EAAE,IAAA,CAAK+E,iBAAL,CAAuBlB,KAAvB,CAFyC;AAGpD1L,QAAAA,IAAI,EAAE,IAAA,CAAKwL,aAAL,CAAmBlR,aAAnB,CAH8C;AAIpD8Q,QAAAA,EAAE,EAAEsB,gBAAAA;AAJgD,OAA/C,CAAP,CAAA;KADF,CAAA;;AASA,IAAA,MAAMG,UAAU,GAAGF,YAAY,CAAClE,WAAD,CAA/B,CAAA;;IAEA,IAAIoE,UAAU,CAAChO,gBAAf,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAACvE,aAAD,IAAkB,CAACmS,WAAvB,EAAoC;AAClC;AACA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMK,SAAS,GAAGnP,OAAO,CAAC,IAAA,CAAK0M,SAAN,CAAzB,CAAA;AACA,IAAA,IAAA,CAAKL,KAAL,EAAA,CAAA;IAEA,IAAKO,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;IAEA,IAAK4B,CAAAA,0BAAL,CAAgCO,gBAAhC,CAAA,CAAA;;IACA,IAAKpC,CAAAA,cAAL,GAAsBmC,WAAtB,CAAA;AAEA,IAAA,MAAMM,oBAAoB,GAAGP,MAAM,GAAGrD,gBAAH,GAAsBD,cAAzD,CAAA;AACA,IAAA,MAAM8D,cAAc,GAAGR,MAAM,GAAGpD,eAAH,GAAqBC,eAAlD,CAAA;AAEAoD,IAAAA,WAAW,CAAClV,SAAZ,CAAsBuQ,GAAtB,CAA0BkF,cAA1B,CAAA,CAAA;IAEA9U,MAAM,CAACuU,WAAD,CAAN,CAAA;AAEAnS,IAAAA,aAAa,CAAC/C,SAAd,CAAwBuQ,GAAxB,CAA4BiF,oBAA5B,CAAA,CAAA;AACAN,IAAAA,WAAW,CAAClV,SAAZ,CAAsBuQ,GAAtB,CAA0BiF,oBAA1B,CAAA,CAAA;;IAEA,MAAME,gBAAgB,GAAG,MAAM;AAC7BR,MAAAA,WAAW,CAAClV,SAAZ,CAAsB0I,MAAtB,CAA6B8M,oBAA7B,EAAmDC,cAAnD,CAAA,CAAA;AACAP,MAAAA,WAAW,CAAClV,SAAZ,CAAsBuQ,GAAtB,CAA0BrD,mBAA1B,CAAA,CAAA;MAEAnK,aAAa,CAAC/C,SAAd,CAAwB0I,MAAxB,CAA+BwE,mBAA/B,EAAkDuI,cAAlD,EAAkED,oBAAlE,CAAA,CAAA;MAEA,IAAKxC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;MAEAoC,YAAY,CAACjE,UAAD,CAAZ,CAAA;KARF,CAAA;;IAWA,IAAKtF,CAAAA,cAAL,CAAoB6J,gBAApB,EAAsC3S,aAAtC,EAAqD,IAAA,CAAK4S,WAAL,EAArD,CAAA,CAAA;;AAEA,IAAA,IAAIJ,SAAJ,EAAe;AACb,MAAA,IAAA,CAAKlC,KAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDsC,EAAAA,WAAW,GAAG;IACZ,OAAO,IAAA,CAAKtK,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCyR,gBAAjC,CAAP,CAAA;AACD,GAAA;;AAEDwC,EAAAA,UAAU,GAAG;IACX,OAAO1G,cAAc,CAACG,OAAf,CAAuBsE,oBAAvB,EAA6C,IAAA,CAAK5G,QAAlD,CAAP,CAAA;AACD,GAAA;;AAED0I,EAAAA,SAAS,GAAG;IACV,OAAOvG,cAAc,CAACpI,IAAf,CAAoB4M,aAApB,EAAmC,IAAA,CAAK3G,QAAxC,CAAP,CAAA;AACD,GAAA;;AAEDoI,EAAAA,cAAc,GAAG;IACf,IAAI,IAAA,CAAKX,SAAT,EAAoB;MAClB8C,aAAa,CAAC,IAAK9C,CAAAA,SAAN,CAAb,CAAA;MACA,IAAKA,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACD,KAAA;AACF,GAAA;;EAED6B,iBAAiB,CAACrE,SAAD,EAAY;IAC3B,IAAIhP,KAAK,EAAT,EAAa;AACX,MAAA,OAAOgP,SAAS,KAAKU,cAAd,GAA+BD,UAA/B,GAA4CD,UAAnD,CAAA;AACD,KAAA;;AAED,IAAA,OAAOR,SAAS,KAAKU,cAAd,GAA+BF,UAA/B,GAA4CC,UAAnD,CAAA;AACD,GAAA;;EAEDsE,iBAAiB,CAAClB,KAAD,EAAQ;IACvB,IAAI7S,KAAK,EAAT,EAAa;AACX,MAAA,OAAO6S,KAAK,KAAKpD,UAAV,GAAuBC,cAAvB,GAAwCC,eAA/C,CAAA;AACD,KAAA;;AAED,IAAA,OAAOkD,KAAK,KAAKpD,UAAV,GAAuBE,eAAvB,GAAyCD,cAAhD,CAAA;AACD,GAzTkC;;;EA4Tb,OAAfjP,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAG6F,QAAQ,CAAC7G,mBAAT,CAA6B,IAA7B,EAAmC3B,MAAnC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;QAC9B2C,IAAI,CAAC6G,EAAL,CAAQxJ,MAAR,CAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,IAAI2C,IAAI,CAAC3C,MAAD,CAAJ,KAAiB3N,SAAjB,IAA8B2N,MAAM,CAACvM,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuM,MAAM,KAAK,aAAvE,EAAsF;AACpF,UAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,SAAA;;QAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,OAAA;AACF,KAfM,CAAP,CAAA;AAgBD,GAAA;;AA7UkC,CAAA;AAgVrC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDgF,mBAAhD,EAAqE,UAAU/N,KAAV,EAAiB;AACpF,EAAA,MAAM3B,MAAM,GAAGvE,sBAAsB,CAAC,IAAD,CAArC,CAAA;;AAEA,EAAA,IAAI,CAACuE,MAAD,IAAW,CAACA,MAAM,CAAC1C,SAAP,CAAiBC,QAAjB,CAA0BwR,mBAA1B,CAAhB,EAAgE;AAC9D,IAAA,OAAA;AACD,GAAA;;AAEDpN,EAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AAEA,EAAA,MAAM8N,QAAQ,GAAGhD,QAAQ,CAAC7G,mBAAT,CAA6BtJ,MAA7B,CAAjB,CAAA;AACA,EAAA,MAAMoT,UAAU,GAAG,IAAA,CAAKnY,YAAL,CAAkB,kBAAlB,CAAnB,CAAA;;AAEA,EAAA,IAAImY,UAAJ,EAAgB;IACdD,QAAQ,CAAChC,EAAT,CAAYiC,UAAZ,CAAA,CAAA;;AACAD,IAAAA,QAAQ,CAACjC,iBAAT,EAAA,CAAA;;AACA,IAAA,OAAA;AACD,GAAA;;EAED,IAAIzK,WAAW,CAACY,gBAAZ,CAA6B,IAA7B,EAAmC,OAAnC,CAAgD,KAAA,MAApD,EAA4D;AAC1D8L,IAAAA,QAAQ,CAACzH,IAAT,EAAA,CAAA;;AACAyH,IAAAA,QAAQ,CAACjC,iBAAT,EAAA,CAAA;;AACA,IAAA,OAAA;AACD,GAAA;;AAEDiC,EAAAA,QAAQ,CAAC5H,IAAT,EAAA,CAAA;;AACA4H,EAAAA,QAAQ,CAACjC,iBAAT,EAAA,CAAA;AACD,CA1BD,CAAA,CAAA;AA4BApP,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwBiT,qBAAxB,EAA6C,MAAM;AACjD,EAAA,MAAMuE,SAAS,GAAGvI,cAAc,CAACpI,IAAf,CAAoBiN,kBAApB,CAAlB,CAAA;;AAEA,EAAA,KAAK,MAAMwD,QAAX,IAAuBE,SAAvB,EAAkC;IAChClD,QAAQ,CAAC7G,mBAAT,CAA6B6J,QAA7B,CAAA,CAAA;AACD,GAAA;AACF,CAND,CAAA,CAAA;AAQA;AACA;AACA;;AAEArU,kBAAkB,CAACqR,QAAD,CAAlB;;ACxdA;AACA;AACA;AACA;AACA;AACA;AAaA;AACA;AACA;;AAEA,MAAMjR,MAAI,GAAG,UAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,aAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AAEA,MAAM+I,YAAU,GAAI,CAAMtK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,aAAW,GAAI,CAAOvK,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAMwK,YAAU,GAAI,CAAMxK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMyK,cAAY,GAAI,CAAQzK,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAM0B,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AAEA,MAAMP,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAM0J,mBAAmB,GAAG,UAA5B,CAAA;AACA,MAAMC,qBAAqB,GAAG,YAA9B,CAAA;AACA,MAAMC,oBAAoB,GAAG,WAA7B,CAAA;AACA,MAAMC,0BAA0B,GAAI,CAAA,QAAA,EAAUH,mBAAoB,CAAA,EAAA,EAAIA,mBAAoB,CAA1F,CAAA,CAAA;AACA,MAAMI,qBAAqB,GAAG,qBAA9B,CAAA;AAEA,MAAMC,KAAK,GAAG,OAAd,CAAA;AACA,MAAMC,MAAM,GAAG,QAAf,CAAA;AAEA,MAAMC,gBAAgB,GAAG,sCAAzB,CAAA;AACA,MAAMxJ,sBAAoB,GAAG,6BAA7B,CAAA;AAEA,MAAMlD,SAAO,GAAG;AACdqD,EAAAA,MAAM,EAAE,IADM;AAEdsJ,EAAAA,MAAM,EAAE,IAAA;AAFM,CAAhB,CAAA;AAKA,MAAM1M,aAAW,GAAG;AAClBoD,EAAAA,MAAM,EAAE,SADU;AAElBsJ,EAAAA,MAAM,EAAE,gBAAA;AAFU,CAApB,CAAA;AAKA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuBzL,aAAvB,CAAqC;AACnCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKyM,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;IACA,IAAKC,CAAAA,aAAL,GAAqB,EAArB,CAAA;AAEA,IAAA,MAAMC,UAAU,GAAGxJ,cAAc,CAACpI,IAAf,CAAoB+H,sBAApB,CAAnB,CAAA;;AAEA,IAAA,KAAK,MAAM8J,IAAX,IAAmBD,UAAnB,EAA+B;AAC7B,MAAA,MAAMtZ,QAAQ,GAAGO,sBAAsB,CAACgZ,IAAD,CAAvC,CAAA;AACA,MAAA,MAAMC,aAAa,GAAG1J,cAAc,CAACpI,IAAf,CAAoB1H,QAApB,CAAA,CACnBkM,MADmB,CACZuN,YAAY,IAAIA,YAAY,KAAK,IAAA,CAAK9L,QAD1B,CAAtB,CAAA;;AAGA,MAAA,IAAI3N,QAAQ,KAAK,IAAb,IAAqBwZ,aAAa,CAAC9X,MAAvC,EAA+C;AAC7C,QAAA,IAAA,CAAK2X,aAAL,CAAmB1V,IAAnB,CAAwB4V,IAAxB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAA,CAAKG,mBAAL,EAAA,CAAA;;AAEA,IAAA,IAAI,CAAC,IAAA,CAAK9L,OAAL,CAAasL,MAAlB,EAA0B;AACxB,MAAA,IAAA,CAAKS,yBAAL,CAA+B,IAAA,CAAKN,aAApC,EAAmD,IAAA,CAAKO,QAAL,EAAnD,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKhM,CAAAA,OAAL,CAAagC,MAAjB,EAAyB;AACvB,MAAA,IAAA,CAAKA,MAAL,EAAA,CAAA;AACD,KAAA;AACF,GA5BkC;;;AA+BjB,EAAA,WAAPrD,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAzCkC;;;AA4CnC0L,EAAAA,MAAM,GAAG;IACP,IAAI,IAAA,CAAKgK,QAAL,EAAJ,EAAqB;AACnB,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;AACD,KAFD,MAEO;AACL,MAAA,IAAA,CAAKC,IAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,KAAKV,gBAAL,IAAyB,IAAKQ,CAAAA,QAAL,EAA7B,EAA8C;AAC5C,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIG,cAAc,GAAG,EAArB,CALK;;AAQL,IAAA,IAAI,IAAKnM,CAAAA,OAAL,CAAasL,MAAjB,EAAyB;MACvBa,cAAc,GAAG,IAAKC,CAAAA,sBAAL,CAA4Bf,gBAA5B,EACd/M,MADc,CACPnM,OAAO,IAAIA,OAAO,KAAK,KAAK4N,QADrB,CAAA,CAEdmD,GAFc,CAEV/Q,OAAO,IAAIoZ,QAAQ,CAAC7K,mBAAT,CAA6BvO,OAA7B,EAAsC;AAAE6P,QAAAA,MAAM,EAAE,KAAA;AAAV,OAAtC,CAFD,CAAjB,CAAA;AAGD,KAAA;;IAED,IAAImK,cAAc,CAACrY,MAAf,IAAyBqY,cAAc,CAAC,CAAD,CAAd,CAAkBX,gBAA/C,EAAiE;AAC/D,MAAA,OAAA;AACD,KAAA;;IAED,MAAMa,UAAU,GAAGnT,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC2K,YAApC,CAAnB,CAAA;;IACA,IAAI2B,UAAU,CAACrQ,gBAAf,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,KAAK,MAAMsQ,cAAX,IAA6BH,cAA7B,EAA6C;AAC3CG,MAAAA,cAAc,CAACL,IAAf,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,MAAMM,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;AAEA,IAAA,IAAA,CAAKzM,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B0N,mBAA/B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAK/K,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B8F,qBAA5B,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAKhL,QAAL,CAAc0M,KAAd,CAAoBF,SAApB,IAAiC,CAAjC,CAAA;;AAEA,IAAA,IAAA,CAAKR,yBAAL,CAA+B,IAAKN,CAAAA,aAApC,EAAmD,IAAnD,CAAA,CAAA;;IACA,IAAKD,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;IAEA,MAAMkB,QAAQ,GAAG,MAAM;MACrB,IAAKlB,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;AAEA,MAAA,IAAA,CAAKzL,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B2N,qBAA/B,CAAA,CAAA;;MACA,IAAKhL,CAAAA,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B6F,mBAA5B,EAAiD1J,iBAAjD,CAAA,CAAA;;AAEA,MAAA,IAAA,CAAKrB,QAAL,CAAc0M,KAAd,CAAoBF,SAApB,IAAiC,EAAjC,CAAA;AAEArT,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC4K,aAApC,CAAA,CAAA;KARF,CAAA;;AAWA,IAAA,MAAMgC,oBAAoB,GAAGJ,SAAS,CAAC,CAAD,CAAT,CAAa3M,WAAb,EAAA,GAA6B2M,SAAS,CAAC9Q,KAAV,CAAgB,CAAhB,CAA1D,CAAA;AACA,IAAA,MAAMmR,UAAU,GAAI,CAAQD,MAAAA,EAAAA,oBAAqB,CAAjD,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAKpM,cAAL,CAAoBmM,QAApB,EAA8B,IAAK3M,CAAAA,QAAnC,EAA6C,IAA7C,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKA,QAAL,CAAc0M,KAAd,CAAoBF,SAApB,CAAA,GAAkC,CAAE,EAAA,IAAA,CAAKxM,QAAL,CAAc6M,UAAd,CAA0B,CAA9D,EAAA,CAAA,CAAA;AACD,GAAA;;AAEDX,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,KAAKT,gBAAL,IAAyB,CAAC,IAAKQ,CAAAA,QAAL,EAA9B,EAA+C;AAC7C,MAAA,OAAA;AACD,KAAA;;IAED,MAAMK,UAAU,GAAGnT,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC6K,YAApC,CAAnB,CAAA;;IACA,IAAIyB,UAAU,CAACrQ,gBAAf,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMuQ,SAAS,GAAG,IAAKC,CAAAA,aAAL,EAAlB,CAAA;;AAEA,IAAA,IAAA,CAAKzM,QAAL,CAAc0M,KAAd,CAAoBF,SAApB,CAAkC,GAAA,CAAA,EAAE,IAAKxM,CAAAA,QAAL,CAAc8M,qBAAd,EAAsCN,CAAAA,SAAtC,CAAiD,CAArF,EAAA,CAAA,CAAA;IAEAlX,MAAM,CAAC,IAAK0K,CAAAA,QAAN,CAAN,CAAA;;AAEA,IAAA,IAAA,CAAKA,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B8F,qBAA5B,CAAA,CAAA;;IACA,IAAKhL,CAAAA,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B0N,mBAA/B,EAAoD1J,iBAApD,CAAA,CAAA;;AAEA,IAAA,KAAK,MAAMzF,OAAX,IAAsB,IAAA,CAAK8P,aAA3B,EAA0C;AACxC,MAAA,MAAMtZ,OAAO,GAAGU,sBAAsB,CAAC8I,OAAD,CAAtC,CAAA;;MAEA,IAAIxJ,OAAO,IAAI,CAAC,IAAA,CAAK6Z,QAAL,CAAc7Z,OAAd,CAAhB,EAAwC;AACtC,QAAA,IAAA,CAAK4Z,yBAAL,CAA+B,CAACpQ,OAAD,CAA/B,EAA0C,KAA1C,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAK6P,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;IAEA,MAAMkB,QAAQ,GAAG,MAAM;MACrB,IAAKlB,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;AACA,MAAA,IAAA,CAAKzL,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B2N,qBAA/B,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKhL,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B6F,mBAA5B,CAAA,CAAA;;AACA5R,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC8K,cAApC,CAAA,CAAA;KAJF,CAAA;;AAOA,IAAA,IAAA,CAAK9K,QAAL,CAAc0M,KAAd,CAAoBF,SAApB,IAAiC,EAAjC,CAAA;;AAEA,IAAA,IAAA,CAAKhM,cAAL,CAAoBmM,QAApB,EAA8B,IAAK3M,CAAAA,QAAnC,EAA6C,IAA7C,CAAA,CAAA;AACD,GAAA;;AAEDiM,EAAAA,QAAQ,CAAC7Z,OAAO,GAAG,IAAA,CAAK4N,QAAhB,EAA0B;AAChC,IAAA,OAAO5N,OAAO,CAACuC,SAAR,CAAkBC,QAAlB,CAA2ByM,iBAA3B,CAAP,CAAA;AACD,GAtJkC;;;EAyJnCnC,iBAAiB,CAACF,MAAD,EAAS;IACxBA,MAAM,CAACiD,MAAP,GAAgBlH,OAAO,CAACiE,MAAM,CAACiD,MAAR,CAAvB,CADwB;;IAExBjD,MAAM,CAACuM,MAAP,GAAgBzX,UAAU,CAACkL,MAAM,CAACuM,MAAR,CAA1B,CAAA;AACA,IAAA,OAAOvM,MAAP,CAAA;AACD,GAAA;;AAEDyN,EAAAA,aAAa,GAAG;IACd,OAAO,IAAA,CAAKzM,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCuW,qBAAjC,CAAA,GAA0DC,KAA1D,GAAkEC,MAAzE,CAAA;AACD,GAAA;;AAEDU,EAAAA,mBAAmB,GAAG;AACpB,IAAA,IAAI,CAAC,IAAA,CAAK9L,OAAL,CAAasL,MAAlB,EAA0B;AACxB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMhJ,QAAQ,GAAG,IAAA,CAAK8J,sBAAL,CAA4BvK,sBAA5B,CAAjB,CAAA;;AAEA,IAAA,KAAK,MAAM1P,OAAX,IAAsBmQ,QAAtB,EAAgC;AAC9B,MAAA,MAAMwK,QAAQ,GAAGja,sBAAsB,CAACV,OAAD,CAAvC,CAAA;;AAEA,MAAA,IAAI2a,QAAJ,EAAc;QACZ,IAAKf,CAAAA,yBAAL,CAA+B,CAAC5Z,OAAD,CAA/B,EAA0C,IAAK6Z,CAAAA,QAAL,CAAcc,QAAd,CAA1C,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EAEDV,sBAAsB,CAACha,QAAD,EAAW;AAC/B,IAAA,MAAMkQ,QAAQ,GAAGJ,cAAc,CAACpI,IAAf,CAAoBmR,0BAApB,EAAgD,IAAA,CAAKjL,OAAL,CAAasL,MAA7D,CAAjB,CAD+B;;IAG/B,OAAOpJ,cAAc,CAACpI,IAAf,CAAoB1H,QAApB,EAA8B,IAAA,CAAK4N,OAAL,CAAasL,MAA3C,CAAA,CAAmDhN,MAAnD,CAA0DnM,OAAO,IAAI,CAACmQ,QAAQ,CAAC/P,QAAT,CAAkBJ,OAAlB,CAAtE,CAAP,CAAA;AACD,GAAA;;AAED4Z,EAAAA,yBAAyB,CAACgB,YAAD,EAAeC,MAAf,EAAuB;AAC9C,IAAA,IAAI,CAACD,YAAY,CAACjZ,MAAlB,EAA0B;AACxB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,KAAK,MAAM3B,OAAX,IAAsB4a,YAAtB,EAAoC;MAClC5a,OAAO,CAACuC,SAAR,CAAkBsN,MAAlB,CAAyBgJ,oBAAzB,EAA+C,CAACgC,MAAhD,CAAA,CAAA;AACA7a,MAAAA,OAAO,CAAC4L,YAAR,CAAqB,eAArB,EAAsCiP,MAAtC,CAAA,CAAA;AACD,KAAA;AACF,GAlMkC;;;EAqMb,OAAfvW,eAAe,CAACsI,MAAD,EAAS;IAC7B,MAAMiB,OAAO,GAAG,EAAhB,CAAA;;IACA,IAAI,OAAOjB,MAAP,KAAkB,QAAlB,IAA8B,YAAYW,IAAZ,CAAiBX,MAAjB,CAAlC,EAA4D;MAC1DiB,OAAO,CAACgC,MAAR,GAAiB,KAAjB,CAAA;AACD,KAAA;;IAED,OAAO,IAAA,CAAKP,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAG6J,QAAQ,CAAC7K,mBAAT,CAA6B,IAA7B,EAAmCV,OAAnC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOjB,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,UAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,SAAA;;QAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,OAAA;AACF,KAVM,CAAP,CAAA;AAWD,GAAA;;AAtNkC,CAAA;AAyNrC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDD,sBAAhD,EAAsE,UAAU9I,KAAV,EAAiB;AACrF;AACA,EAAA,IAAIA,KAAK,CAAC3B,MAAN,CAAa4J,OAAb,KAAyB,GAAzB,IAAiCjI,KAAK,CAACC,cAAN,IAAwBD,KAAK,CAACC,cAAN,CAAqBgI,OAArB,KAAiC,GAA9F,EAAoG;AAClGjI,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACD,GAAA;;AAED,EAAA,MAAMrK,QAAQ,GAAGO,sBAAsB,CAAC,IAAD,CAAvC,CAAA;AACA,EAAA,MAAMsa,gBAAgB,GAAG/K,cAAc,CAACpI,IAAf,CAAoB1H,QAApB,CAAzB,CAAA;;AAEA,EAAA,KAAK,MAAMD,OAAX,IAAsB8a,gBAAtB,EAAwC;AACtC1B,IAAAA,QAAQ,CAAC7K,mBAAT,CAA6BvO,OAA7B,EAAsC;AAAE6P,MAAAA,MAAM,EAAE,KAAA;AAAV,KAAtC,EAAyDA,MAAzD,EAAA,CAAA;AACD,GAAA;AACF,CAZD,CAAA,CAAA;AAcA;AACA;AACA;;AAEA9L,kBAAkB,CAACqV,QAAD,CAAlB;;AC3SA;AACA;AACA;AACA;AACA;AACA;AAkBA;AACA;AACA;;AAEA,MAAMjV,MAAI,GAAG,UAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,aAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AAEA,MAAMuL,YAAU,GAAG,QAAnB,CAAA;AACA,MAAMC,SAAO,GAAG,KAAhB,CAAA;AACA,MAAMC,cAAY,GAAG,SAArB,CAAA;AACA,MAAMC,gBAAc,GAAG,WAAvB,CAAA;AACA,MAAMC,kBAAkB,GAAG,CAA3B;;AAEA,MAAM1C,YAAU,GAAI,CAAMxK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMyK,cAAY,GAAI,CAAQzK,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAMsK,YAAU,GAAI,CAAMtK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,aAAW,GAAI,CAAOvK,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAM0B,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AACA,MAAM4L,sBAAsB,GAAI,CAAA,OAAA,EAASnN,WAAU,CAAA,EAAEuB,cAAa,CAAlE,CAAA,CAAA;AACA,MAAM6L,oBAAoB,GAAI,CAAA,KAAA,EAAOpN,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AAEA,MAAMP,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMqM,iBAAiB,GAAG,QAA1B,CAAA;AACA,MAAMC,kBAAkB,GAAG,SAA3B,CAAA;AACA,MAAMC,oBAAoB,GAAG,WAA7B,CAAA;AACA,MAAMC,wBAAwB,GAAG,eAAjC,CAAA;AACA,MAAMC,0BAA0B,GAAG,iBAAnC,CAAA;AAEA,MAAMhM,sBAAoB,GAAG,2DAA7B,CAAA;AACA,MAAMiM,0BAA0B,GAAI,CAAA,EAAEjM,sBAAqB,CAAA,CAAA,EAAGT,iBAAgB,CAA9E,CAAA,CAAA;AACA,MAAM2M,aAAa,GAAG,gBAAtB,CAAA;AACA,MAAMC,eAAe,GAAG,SAAxB,CAAA;AACA,MAAMC,mBAAmB,GAAG,aAA5B,CAAA;AACA,MAAMC,sBAAsB,GAAG,6DAA/B,CAAA;AAEA,MAAMC,aAAa,GAAGnY,KAAK,EAAK,GAAA,SAAL,GAAiB,WAA5C,CAAA;AACA,MAAMoY,gBAAgB,GAAGpY,KAAK,EAAK,GAAA,WAAL,GAAmB,SAAjD,CAAA;AACA,MAAMqY,gBAAgB,GAAGrY,KAAK,EAAK,GAAA,YAAL,GAAoB,cAAlD,CAAA;AACA,MAAMsY,mBAAmB,GAAGtY,KAAK,EAAK,GAAA,cAAL,GAAsB,YAAvD,CAAA;AACA,MAAMuY,eAAe,GAAGvY,KAAK,EAAK,GAAA,YAAL,GAAoB,aAAjD,CAAA;AACA,MAAMwY,cAAc,GAAGxY,KAAK,EAAK,GAAA,aAAL,GAAqB,YAAjD,CAAA;AACA,MAAMyY,mBAAmB,GAAG,KAA5B,CAAA;AACA,MAAMC,sBAAsB,GAAG,QAA/B,CAAA;AAEA,MAAM/P,SAAO,GAAG;AACdgQ,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CADM;AAEdC,EAAAA,QAAQ,EAAE,iBAFI;AAGdC,EAAAA,SAAS,EAAE,QAHG;AAIdC,EAAAA,OAAO,EAAE,SAJK;AAKdC,EAAAA,YAAY,EAAE,IALA;AAMdC,EAAAA,SAAS,EAAE,IAAA;AANG,CAAhB,CAAA;AASA,MAAMpQ,aAAW,GAAG;AAClB+P,EAAAA,MAAM,EAAE,yBADU;AAElBC,EAAAA,QAAQ,EAAE,kBAFQ;AAGlBC,EAAAA,SAAS,EAAE,yBAHO;AAIlBC,EAAAA,OAAO,EAAE,QAJS;AAKlBC,EAAAA,YAAY,EAAE,wBALI;AAMlBC,EAAAA,SAAS,EAAE,kBAAA;AANO,CAApB,CAAA;AASA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuBnP,aAAvB,CAAqC;AACnCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKmQ,CAAAA,OAAL,GAAe,IAAf,CAAA;AACA,IAAA,IAAA,CAAKC,OAAL,GAAe,IAAA,CAAKpP,QAAL,CAAczL,UAA7B,CAJ2B;;IAK3B,IAAK8a,CAAAA,KAAL,GAAalN,cAAc,CAACG,OAAf,CAAuB0L,aAAvB,EAAsC,IAAKoB,CAAAA,OAA3C,CAAb,CAAA;AACA,IAAA,IAAA,CAAKE,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;AACD,GARkC;;;AAWjB,EAAA,WAAP3Q,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GArBkC;;;AAwBnC0L,EAAAA,MAAM,GAAG;IACP,OAAO,IAAA,CAAKgK,QAAL,EAAkB,GAAA,IAAA,CAAKC,IAAL,EAAlB,GAAgC,IAAKC,CAAAA,IAAL,EAAvC,CAAA;AACD,GAAA;;AAEDA,EAAAA,IAAI,GAAG;IACL,IAAI3X,UAAU,CAAC,IAAKwL,CAAAA,QAAN,CAAV,IAA6B,IAAA,CAAKiM,QAAL,EAAjC,EAAkD;AAChD,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMvR,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,IAAKsF,CAAAA,QAAAA;KADtB,CAAA;AAIA,IAAA,MAAMwP,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC2K,YAApC,EAAgDjQ,aAAhD,CAAlB,CAAA;;IAEA,IAAI8U,SAAS,CAACvT,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,IAAKwT,CAAAA,aAAL,GAfK;AAkBL;AACA;AACA;;;AACA,IAAA,IAAI,cAAkBxd,IAAAA,QAAQ,CAAC+C,eAA3B,IAA8C,CAAC,IAAKoa,CAAAA,OAAL,CAAa/a,OAAb,CAAqB6Z,mBAArB,CAAnD,EAA8F;AAC5F,MAAA,KAAK,MAAM9b,OAAX,IAAsB,EAAA,CAAGgQ,MAAH,CAAU,GAAGnQ,QAAQ,CAACyD,IAAT,CAAc6M,QAA3B,CAAtB,EAA4D;AAC1DpJ,QAAAA,YAAY,CAACkC,EAAb,CAAgBjJ,OAAhB,EAAyB,WAAzB,EAAsCiD,IAAtC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAK2K,CAAAA,QAAL,CAAc0P,KAAd,EAAA,CAAA;;AACA,IAAA,IAAA,CAAK1P,QAAL,CAAchC,YAAd,CAA2B,eAA3B,EAA4C,IAA5C,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAKqR,KAAL,CAAW1a,SAAX,CAAqBuQ,GAArB,CAAyB7D,iBAAzB,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKrB,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B7D,iBAA5B,CAAA,CAAA;;IACAlI,YAAY,CAACyC,OAAb,CAAqB,IAAA,CAAKoE,QAA1B,EAAoC4K,aAApC,EAAiDlQ,aAAjD,CAAA,CAAA;AACD,GAAA;;AAEDwR,EAAAA,IAAI,GAAG;IACL,IAAI1X,UAAU,CAAC,IAAA,CAAKwL,QAAN,CAAV,IAA6B,CAAC,IAAA,CAAKiM,QAAL,EAAlC,EAAmD;AACjD,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMvR,aAAa,GAAG;AACpBA,MAAAA,aAAa,EAAE,IAAKsF,CAAAA,QAAAA;KADtB,CAAA;;IAIA,IAAK2P,CAAAA,aAAL,CAAmBjV,aAAnB,CAAA,CAAA;AACD,GAAA;;AAED0F,EAAAA,OAAO,GAAG;IACR,IAAI,IAAA,CAAK+O,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaS,OAAb,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,KAAA,CAAMxP,OAAN,EAAA,CAAA;AACD,GAAA;;AAEDyP,EAAAA,MAAM,GAAG;AACP,IAAA,IAAA,CAAKP,SAAL,GAAiB,IAAKC,CAAAA,aAAL,EAAjB,CAAA;;IACA,IAAI,IAAA,CAAKJ,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaU,MAAb,EAAA,CAAA;AACD,KAAA;AACF,GAxFkC;;;EA2FnCF,aAAa,CAACjV,aAAD,EAAgB;AAC3B,IAAA,MAAMoV,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC6K,YAApC,EAAgDnQ,aAAhD,CAAlB,CAAA;;IACA,IAAIoV,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAJ0B;AAO3B;;;AACA,IAAA,IAAI,cAAkBhK,IAAAA,QAAQ,CAAC+C,eAA/B,EAAgD;AAC9C,MAAA,KAAK,MAAM5C,OAAX,IAAsB,EAAA,CAAGgQ,MAAH,CAAU,GAAGnQ,QAAQ,CAACyD,IAAT,CAAc6M,QAA3B,CAAtB,EAA4D;AAC1DpJ,QAAAA,YAAY,CAACC,GAAb,CAAiBhH,OAAjB,EAA0B,WAA1B,EAAuCiD,IAAvC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;IAED,IAAI,IAAA,CAAK8Z,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaS,OAAb,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKP,KAAL,CAAW1a,SAAX,CAAqB0I,MAArB,CAA4BgE,iBAA5B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKrB,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BgE,iBAA/B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKrB,QAAL,CAAchC,YAAd,CAA2B,eAA3B,EAA4C,OAA5C,CAAA,CAAA;;AACAF,IAAAA,WAAW,CAACG,mBAAZ,CAAgC,IAAKoR,CAAAA,KAArC,EAA4C,QAA5C,CAAA,CAAA;IACAlW,YAAY,CAACyC,OAAb,CAAqB,IAAA,CAAKoE,QAA1B,EAAoC8K,cAApC,EAAkDpQ,aAAlD,CAAA,CAAA;AACD,GAAA;;EAEDqE,UAAU,CAACC,MAAD,EAAS;AACjBA,IAAAA,MAAM,GAAG,KAAA,CAAMD,UAAN,CAAiBC,MAAjB,CAAT,CAAA;;IAEA,IAAI,OAAOA,MAAM,CAAC8P,SAAd,KAA4B,QAA5B,IAAwC,CAACnb,SAAS,CAACqL,MAAM,CAAC8P,SAAR,CAAlD,IACF,OAAO9P,MAAM,CAAC8P,SAAP,CAAiBhC,qBAAxB,KAAkD,UADpD,EAEE;AACA;MACA,MAAM,IAAIlN,SAAJ,CAAe,CAAA,EAAErJ,MAAI,CAACsJ,WAAL,EAAmB,CAAA,8FAAA,CAApC,CAAN,CAAA;AACD,KAAA;;AAED,IAAA,OAAOb,MAAP,CAAA;AACD,GAAA;;AAEDyQ,EAAAA,aAAa,GAAG;AACd,IAAA,IAAI,OAAOM,MAAP,KAAkB,WAAtB,EAAmC;AACjC,MAAA,MAAM,IAAInQ,SAAJ,CAAc,+DAAd,CAAN,CAAA;AACD,KAAA;;IAED,IAAIoQ,gBAAgB,GAAG,IAAA,CAAKhQ,QAA5B,CAAA;;AAEA,IAAA,IAAI,KAAKC,OAAL,CAAa6O,SAAb,KAA2B,QAA/B,EAAyC;MACvCkB,gBAAgB,GAAG,KAAKZ,OAAxB,CAAA;KADF,MAEO,IAAIzb,SAAS,CAAC,KAAKsM,OAAL,CAAa6O,SAAd,CAAb,EAAuC;AAC5CkB,MAAAA,gBAAgB,GAAGlc,UAAU,CAAC,KAAKmM,OAAL,CAAa6O,SAAd,CAA7B,CAAA;KADK,MAEA,IAAI,OAAO,IAAA,CAAK7O,OAAL,CAAa6O,SAApB,KAAkC,QAAtC,EAAgD;AACrDkB,MAAAA,gBAAgB,GAAG,IAAA,CAAK/P,OAAL,CAAa6O,SAAhC,CAAA;AACD,KAAA;;AAED,IAAA,MAAME,YAAY,GAAG,IAAKiB,CAAAA,gBAAL,EAArB,CAAA;;AACA,IAAA,IAAA,CAAKd,OAAL,GAAeY,MAAM,CAACG,YAAP,CAAoBF,gBAApB,EAAsC,IAAKX,CAAAA,KAA3C,EAAkDL,YAAlD,CAAf,CAAA;AACD,GAAA;;AAED/C,EAAAA,QAAQ,GAAG;IACT,OAAO,IAAA,CAAKoD,KAAL,CAAW1a,SAAX,CAAqBC,QAArB,CAA8ByM,iBAA9B,CAAP,CAAA;AACD,GAAA;;AAED8O,EAAAA,aAAa,GAAG;IACd,MAAMC,cAAc,GAAG,IAAA,CAAKhB,OAA5B,CAAA;;IAEA,IAAIgB,cAAc,CAACzb,SAAf,CAAyBC,QAAzB,CAAkC+Y,kBAAlC,CAAJ,EAA2D;AACzD,MAAA,OAAOa,eAAP,CAAA;AACD,KAAA;;IAED,IAAI4B,cAAc,CAACzb,SAAf,CAAyBC,QAAzB,CAAkCgZ,oBAAlC,CAAJ,EAA6D;AAC3D,MAAA,OAAOa,cAAP,CAAA;AACD,KAAA;;IAED,IAAI2B,cAAc,CAACzb,SAAf,CAAyBC,QAAzB,CAAkCiZ,wBAAlC,CAAJ,EAAiE;AAC/D,MAAA,OAAOa,mBAAP,CAAA;AACD,KAAA;;IAED,IAAI0B,cAAc,CAACzb,SAAf,CAAyBC,QAAzB,CAAkCkZ,0BAAlC,CAAJ,EAAmE;AACjE,MAAA,OAAOa,sBAAP,CAAA;AACD,KAjBa;;;AAoBd,IAAA,MAAM0B,KAAK,GAAGld,gBAAgB,CAAC,KAAKkc,KAAN,CAAhB,CAA6Blb,gBAA7B,CAA8C,eAA9C,CAA+DxB,CAAAA,IAA/D,OAA0E,KAAxF,CAAA;;IAEA,IAAIyd,cAAc,CAACzb,SAAf,CAAyBC,QAAzB,CAAkC8Y,iBAAlC,CAAJ,EAA0D;AACxD,MAAA,OAAO2C,KAAK,GAAGhC,gBAAH,GAAsBD,aAAlC,CAAA;AACD,KAAA;;AAED,IAAA,OAAOiC,KAAK,GAAG9B,mBAAH,GAAyBD,gBAArC,CAAA;AACD,GAAA;;AAEDiB,EAAAA,aAAa,GAAG;AACd,IAAA,OAAO,KAAKvP,QAAL,CAAc3L,OAAd,CAAsB4Z,eAAtB,MAA2C,IAAlD,CAAA;AACD,GAAA;;AAEDqC,EAAAA,UAAU,GAAG;IACX,MAAM;AAAE1B,MAAAA,MAAAA;AAAF,KAAA,GAAa,KAAK3O,OAAxB,CAAA;;AAEA,IAAA,IAAI,OAAO2O,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,MAAA,OAAOA,MAAM,CAAClc,KAAP,CAAa,GAAb,CAAA,CAAkByQ,GAAlB,CAAsB3F,KAAK,IAAInK,MAAM,CAACsW,QAAP,CAAgBnM,KAAhB,EAAuB,EAAvB,CAA/B,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOoR,MAAP,KAAkB,UAAtB,EAAkC;MAChC,OAAO2B,UAAU,IAAI3B,MAAM,CAAC2B,UAAD,EAAa,IAAA,CAAKvQ,QAAlB,CAA3B,CAAA;AACD,KAAA;;AAED,IAAA,OAAO4O,MAAP,CAAA;AACD,GAAA;;AAEDqB,EAAAA,gBAAgB,GAAG;AACjB,IAAA,MAAMO,qBAAqB,GAAG;MAC5BC,SAAS,EAAE,IAAKN,CAAAA,aAAL,EADiB;AAE5BO,MAAAA,SAAS,EAAE,CAAC;AACVpa,QAAAA,IAAI,EAAE,iBADI;AAEVqa,QAAAA,OAAO,EAAE;UACP9B,QAAQ,EAAE,IAAK5O,CAAAA,OAAL,CAAa4O,QAAAA;AADhB,SAAA;AAFC,OAAD,EAMX;AACEvY,QAAAA,IAAI,EAAE,QADR;AAEEqa,QAAAA,OAAO,EAAE;UACP/B,MAAM,EAAE,KAAK0B,UAAL,EAAA;AADD,SAAA;OARA,CAAA;AAFiB,KAA9B,CADiB;;IAkBjB,IAAI,IAAA,CAAKhB,SAAL,IAAkB,IAAA,CAAKrP,OAAL,CAAa8O,OAAb,KAAyB,QAA/C,EAAyD;MACvDjR,WAAW,CAACC,gBAAZ,CAA6B,IAAKsR,CAAAA,KAAlC,EAAyC,QAAzC,EAAmD,QAAnD,CAAA,CADuD;;MAEvDmB,qBAAqB,CAACE,SAAtB,GAAkC,CAAC;AACjCpa,QAAAA,IAAI,EAAE,aAD2B;AAEjCsa,QAAAA,OAAO,EAAE,KAAA;AAFwB,OAAD,CAAlC,CAAA;AAID,KAAA;;IAED,OAAO,EACL,GAAGJ,qBADE;AAEL,MAAA,IAAI,OAAO,IAAKvQ,CAAAA,OAAL,CAAa+O,YAApB,KAAqC,UAArC,GAAkD,IAAA,CAAK/O,OAAL,CAAa+O,YAAb,CAA0BwB,qBAA1B,CAAlD,GAAqG,IAAKvQ,CAAAA,OAAL,CAAa+O,YAAtH,CAAA;KAFF,CAAA;AAID,GAAA;;AAED6B,EAAAA,eAAe,CAAC;IAAEtU,GAAF;AAAOlF,IAAAA,MAAAA;AAAP,GAAD,EAAkB;AAC/B,IAAA,MAAMoR,KAAK,GAAGtG,cAAc,CAACpI,IAAf,CAAoBoU,sBAApB,EAA4C,IAAA,CAAKkB,KAAjD,CAAwD9Q,CAAAA,MAAxD,CAA+DnM,OAAO,IAAI4B,SAAS,CAAC5B,OAAD,CAAnF,CAAd,CAAA;;AAEA,IAAA,IAAI,CAACqW,KAAK,CAAC1U,MAAX,EAAmB;AACjB,MAAA,OAAA;AACD,KAL8B;AAQ/B;;;AACAyD,IAAAA,oBAAoB,CAACiR,KAAD,EAAQpR,MAAR,EAAgBkF,GAAG,KAAK+Q,gBAAxB,EAAwC,CAAC7E,KAAK,CAACjW,QAAN,CAAe6E,MAAf,CAAzC,CAApB,CAAqFqY,KAArF,EAAA,CAAA;AACD,GAjPkC;;;EAoPb,OAAfhZ,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAGuN,QAAQ,CAACvO,mBAAT,CAA6B,IAA7B,EAAmC3B,MAAnC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;MAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;EAEgB,OAAV8R,UAAU,CAAC9X,KAAD,EAAQ;AACvB,IAAA,IAAIA,KAAK,CAACkJ,MAAN,KAAiBqL,kBAAjB,IAAwCvU,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAACuD,GAAN,KAAc6Q,SAApF,EAA8F;AAC5F,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM2D,WAAW,GAAG5O,cAAc,CAACpI,IAAf,CAAoBgU,0BAApB,CAApB,CAAA;;AAEA,IAAA,KAAK,MAAM9L,MAAX,IAAqB8O,WAArB,EAAkC;AAChC,MAAA,MAAMC,OAAO,GAAG9B,QAAQ,CAACxO,WAAT,CAAqBuB,MAArB,CAAhB,CAAA;;MACA,IAAI,CAAC+O,OAAD,IAAYA,OAAO,CAAC/Q,OAAR,CAAgBgP,SAAhB,KAA8B,KAA9C,EAAqD;AACnD,QAAA,SAAA;AACD,OAAA;;AAED,MAAA,MAAMgC,YAAY,GAAGjY,KAAK,CAACiY,YAAN,EAArB,CAAA;MACA,MAAMC,YAAY,GAAGD,YAAY,CAACze,QAAb,CAAsBwe,OAAO,CAAC3B,KAA9B,CAArB,CAAA;;AACA,MAAA,IACE4B,YAAY,CAACze,QAAb,CAAsBwe,OAAO,CAAChR,QAA9B,CAAA,IACCgR,OAAO,CAAC/Q,OAAR,CAAgBgP,SAAhB,KAA8B,QAA9B,IAA0C,CAACiC,YAD5C,IAECF,OAAO,CAAC/Q,OAAR,CAAgBgP,SAAhB,KAA8B,SAA9B,IAA2CiC,YAH9C,EAIE;AACA,QAAA,SAAA;AACD,OAd+B;;;AAiBhC,MAAA,IAAIF,OAAO,CAAC3B,KAAR,CAAcza,QAAd,CAAuBoE,KAAK,CAAC3B,MAA7B,CAA0C2B,KAAAA,KAAK,CAACK,IAAN,KAAe,OAAf,IAA0BL,KAAK,CAACuD,GAAN,KAAc6Q,SAAzC,IAAqD,qCAAqCzN,IAArC,CAA0C3G,KAAK,CAAC3B,MAAN,CAAa4J,OAAvD,CAA9F,CAAJ,EAAoK;AAClK,QAAA,SAAA;AACD,OAAA;;AAED,MAAA,MAAMvG,aAAa,GAAG;QAAEA,aAAa,EAAEsW,OAAO,CAAChR,QAAAA;OAA/C,CAAA;;AAEA,MAAA,IAAIhH,KAAK,CAACK,IAAN,KAAe,OAAnB,EAA4B;QAC1BqB,aAAa,CAACsG,UAAd,GAA2BhI,KAA3B,CAAA;AACD,OAAA;;MAEDgY,OAAO,CAACrB,aAAR,CAAsBjV,aAAtB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAE2B,OAArByW,qBAAqB,CAACnY,KAAD,EAAQ;AAClC;AACA;IAEA,MAAMoY,OAAO,GAAG,iBAAA,CAAkBzR,IAAlB,CAAuB3G,KAAK,CAAC3B,MAAN,CAAa4J,OAApC,CAAhB,CAAA;AACA,IAAA,MAAMoQ,aAAa,GAAGrY,KAAK,CAACuD,GAAN,KAAc4Q,YAApC,CAAA;AACA,IAAA,MAAMmE,eAAe,GAAG,CAACjE,cAAD,EAAeC,gBAAf,CAA+B9a,CAAAA,QAA/B,CAAwCwG,KAAK,CAACuD,GAA9C,CAAxB,CAAA;;AAEA,IAAA,IAAI,CAAC+U,eAAD,IAAoB,CAACD,aAAzB,EAAwC;AACtC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAID,OAAO,IAAI,CAACC,aAAhB,EAA+B;AAC7B,MAAA,OAAA;AACD,KAAA;;AAEDrY,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AAEA,IAAA,MAAM6U,eAAe,GAAGpP,cAAc,CAACG,OAAf,CAAuBR,sBAAvB,EAA6C9I,KAAK,CAACC,cAAN,CAAqB1E,UAAlE,CAAxB,CAAA;AACA,IAAA,MAAMuI,QAAQ,GAAGoS,QAAQ,CAACvO,mBAAT,CAA6B4Q,eAA7B,CAAjB,CAAA;;AAEA,IAAA,IAAID,eAAJ,EAAqB;AACnBtY,MAAAA,KAAK,CAACwY,eAAN,EAAA,CAAA;AACA1U,MAAAA,QAAQ,CAACqP,IAAT,EAAA,CAAA;;MACArP,QAAQ,CAAC+T,eAAT,CAAyB7X,KAAzB,CAAA,CAAA;;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI8D,QAAQ,CAACmP,QAAT,EAAJ,EAAyB;AAAE;AACzBjT,MAAAA,KAAK,CAACwY,eAAN,EAAA,CAAA;AACA1U,MAAAA,QAAQ,CAACoP,IAAT,EAAA,CAAA;AACAqF,MAAAA,eAAe,CAAC7B,KAAhB,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AA3UkC,CAAA;AA8UrC;AACA;AACA;;;AAEAvW,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0Bub,sBAA1B,EAAkD1L,sBAAlD,EAAwEoN,QAAQ,CAACiC,qBAAjF,CAAA,CAAA;AACAhY,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0Bub,sBAA1B,EAAkDQ,aAAlD,EAAiEkB,QAAQ,CAACiC,qBAA1E,CAAA,CAAA;AACAhY,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDmN,QAAQ,CAAC4B,UAAzD,CAAA,CAAA;AACA3X,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0Bwb,oBAA1B,EAAgDyB,QAAQ,CAAC4B,UAAzD,CAAA,CAAA;AACA3X,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDD,sBAAhD,EAAsE,UAAU9I,KAAV,EAAiB;AACrFA,EAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACAwS,EAAAA,QAAQ,CAACvO,mBAAT,CAA6B,IAA7B,EAAmCsB,MAAnC,EAAA,CAAA;AACD,CAHD,CAAA,CAAA;AAKA;AACA;AACA;;AAEA9L,kBAAkB,CAAC+Y,QAAD,CAAlB;;AC1bA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;AAEA,MAAMuC,sBAAsB,GAAG,mDAA/B,CAAA;AACA,MAAMC,uBAAuB,GAAG,aAAhC,CAAA;AACA,MAAMC,gBAAgB,GAAG,eAAzB,CAAA;AACA,MAAMC,eAAe,GAAG,cAAxB,CAAA;AAEA;AACA;AACA;;AAEA,MAAMC,eAAN,CAAsB;AACpBxS,EAAAA,WAAW,GAAG;AACZ,IAAA,IAAA,CAAKW,QAAL,GAAgB/N,QAAQ,CAACyD,IAAzB,CAAA;AACD,GAHmB;;;AAMpBoc,EAAAA,QAAQ,GAAG;AACT;AACA,IAAA,MAAMC,aAAa,GAAG9f,QAAQ,CAAC+C,eAAT,CAAyBgd,WAA/C,CAAA;IACA,OAAOlgB,IAAI,CAACkT,GAAL,CAAS9R,MAAM,CAAC+e,UAAP,GAAoBF,aAA7B,CAAP,CAAA;AACD,GAAA;;AAED7F,EAAAA,IAAI,GAAG;AACL,IAAA,MAAMgG,KAAK,GAAG,IAAKJ,CAAAA,QAAL,EAAd,CAAA;;IACA,IAAKK,CAAAA,gBAAL,GAFK;;;AAIL,IAAA,IAAA,CAAKC,qBAAL,CAA2B,IAAKpS,CAAAA,QAAhC,EAA0C2R,gBAA1C,EAA4DU,eAAe,IAAIA,eAAe,GAAGH,KAAjG,EAJK;;;IAML,IAAKE,CAAAA,qBAAL,CAA2BX,sBAA3B,EAAmDE,gBAAnD,EAAqEU,eAAe,IAAIA,eAAe,GAAGH,KAA1G,CAAA,CAAA;;IACA,IAAKE,CAAAA,qBAAL,CAA2BV,uBAA3B,EAAoDE,eAApD,EAAqES,eAAe,IAAIA,eAAe,GAAGH,KAA1G,CAAA,CAAA;AACD,GAAA;;AAEDI,EAAAA,KAAK,GAAG;AACN,IAAA,IAAA,CAAKC,uBAAL,CAA6B,IAAKvS,CAAAA,QAAlC,EAA4C,UAA5C,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKuS,uBAAL,CAA6B,IAAKvS,CAAAA,QAAlC,EAA4C2R,gBAA5C,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKY,uBAAL,CAA6Bd,sBAA7B,EAAqDE,gBAArD,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKY,uBAAL,CAA6Bb,uBAA7B,EAAsDE,eAAtD,CAAA,CAAA;AACD,GAAA;;AAEDY,EAAAA,aAAa,GAAG;IACd,OAAO,IAAA,CAAKV,QAAL,EAAA,GAAkB,CAAzB,CAAA;AACD,GA/BmB;;;AAkCpBK,EAAAA,gBAAgB,GAAG;AACjB,IAAA,IAAA,CAAKM,qBAAL,CAA2B,IAAKzS,CAAAA,QAAhC,EAA0C,UAA1C,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKA,QAAL,CAAc0M,KAAd,CAAoBgG,QAApB,GAA+B,QAA/B,CAAA;AACD,GAAA;;AAEDN,EAAAA,qBAAqB,CAAC/f,QAAD,EAAWsgB,aAAX,EAA0B9c,QAA1B,EAAoC;AACvD,IAAA,MAAM+c,cAAc,GAAG,IAAKd,CAAAA,QAAL,EAAvB,CAAA;;IACA,MAAMe,oBAAoB,GAAGzgB,OAAO,IAAI;AACtC,MAAA,IAAIA,OAAO,KAAK,IAAK4N,CAAAA,QAAjB,IAA6B9M,MAAM,CAAC+e,UAAP,GAAoB7f,OAAO,CAAC4f,WAAR,GAAsBY,cAA3E,EAA2F;AACzF,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKH,qBAAL,CAA2BrgB,OAA3B,EAAoCugB,aAApC,CAAA,CAAA;;MACA,MAAMN,eAAe,GAAGnf,MAAM,CAACC,gBAAP,CAAwBf,OAAxB,CAAiC+B,CAAAA,gBAAjC,CAAkDwe,aAAlD,CAAxB,CAAA;AACAvgB,MAAAA,OAAO,CAACsa,KAAR,CAAcoG,WAAd,CAA0BH,aAA1B,EAA0C,CAAA,EAAE9c,QAAQ,CAACxC,MAAM,CAACC,UAAP,CAAkB+e,eAAlB,CAAD,CAAqC,CAAzF,EAAA,CAAA,CAAA,CAAA;KAPF,CAAA;;AAUA,IAAA,IAAA,CAAKU,0BAAL,CAAgC1gB,QAAhC,EAA0CwgB,oBAA1C,CAAA,CAAA;AACD,GAAA;;AAEDJ,EAAAA,qBAAqB,CAACrgB,OAAD,EAAUugB,aAAV,EAAyB;IAC5C,MAAMK,WAAW,GAAG5gB,OAAO,CAACsa,KAAR,CAAcvY,gBAAd,CAA+Bwe,aAA/B,CAApB,CAAA;;AACA,IAAA,IAAIK,WAAJ,EAAiB;AACflV,MAAAA,WAAW,CAACC,gBAAZ,CAA6B3L,OAA7B,EAAsCugB,aAAtC,EAAqDK,WAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDT,EAAAA,uBAAuB,CAAClgB,QAAD,EAAWsgB,aAAX,EAA0B;IAC/C,MAAME,oBAAoB,GAAGzgB,OAAO,IAAI;MACtC,MAAMoL,KAAK,GAAGM,WAAW,CAACY,gBAAZ,CAA6BtM,OAA7B,EAAsCugB,aAAtC,CAAd,CADsC;;MAGtC,IAAInV,KAAK,KAAK,IAAd,EAAoB;AAClBpL,QAAAA,OAAO,CAACsa,KAAR,CAAcuG,cAAd,CAA6BN,aAA7B,CAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED7U,MAAAA,WAAW,CAACG,mBAAZ,CAAgC7L,OAAhC,EAAyCugB,aAAzC,CAAA,CAAA;AACAvgB,MAAAA,OAAO,CAACsa,KAAR,CAAcoG,WAAd,CAA0BH,aAA1B,EAAyCnV,KAAzC,CAAA,CAAA;KATF,CAAA;;AAYA,IAAA,IAAA,CAAKuV,0BAAL,CAAgC1gB,QAAhC,EAA0CwgB,oBAA1C,CAAA,CAAA;AACD,GAAA;;AAEDE,EAAAA,0BAA0B,CAAC1gB,QAAD,EAAW6gB,QAAX,EAAqB;AAC7C,IAAA,IAAIvf,SAAS,CAACtB,QAAD,CAAb,EAAyB;MACvB6gB,QAAQ,CAAC7gB,QAAD,CAAR,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,KAAK,MAAM8gB,GAAX,IAAkBhR,cAAc,CAACpI,IAAf,CAAoB1H,QAApB,EAA8B,IAAA,CAAK2N,QAAnC,CAAlB,EAAgE;MAC9DkT,QAAQ,CAACC,GAAD,CAAR,CAAA;AACD,KAAA;AACF,GAAA;;AAtFmB;;ACxBtB;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;AAEA,MAAM5c,MAAI,GAAG,UAAb,CAAA;AACA,MAAM6K,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMC,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAM+R,eAAe,GAAI,CAAe7c,aAAAA,EAAAA,MAAK,CAA7C,CAAA,CAAA;AAEA,MAAMqI,SAAO,GAAG;AACdyU,EAAAA,SAAS,EAAE,gBADG;AAEdrf,EAAAA,SAAS,EAAE,IAFG;AAEG;AACjByM,EAAAA,UAAU,EAAE,KAHE;AAId6S,EAAAA,WAAW,EAAE,MAJC;AAIO;AACrBC,EAAAA,aAAa,EAAE,IAAA;AALD,CAAhB,CAAA;AAQA,MAAM1U,aAAW,GAAG;AAClBwU,EAAAA,SAAS,EAAE,QADO;AAElBrf,EAAAA,SAAS,EAAE,SAFO;AAGlByM,EAAAA,UAAU,EAAE,SAHM;AAIlB6S,EAAAA,WAAW,EAAE,kBAJK;AAKlBC,EAAAA,aAAa,EAAE,iBAAA;AALG,CAApB,CAAA;AAQA;AACA;AACA;;AAEA,MAAMC,QAAN,SAAuB7U,MAAvB,CAA8B;EAC5BU,WAAW,CAACL,MAAD,EAAS;AAClB,IAAA,KAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAKiB,OAAL,GAAe,IAAA,CAAKlB,UAAL,CAAgBC,MAAhB,CAAf,CAAA;IACA,IAAKyU,CAAAA,WAAL,GAAmB,KAAnB,CAAA;IACA,IAAKzT,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACD,GAN2B;;;AASV,EAAA,WAAPpB,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAnB2B;;;EAsB5B4V,IAAI,CAACtW,QAAD,EAAW;AACb,IAAA,IAAI,CAAC,IAAA,CAAKoK,OAAL,CAAajM,SAAlB,EAA6B;MAC3B6C,OAAO,CAAChB,QAAD,CAAP,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK6d,OAAL,EAAA,CAAA;;AAEA,IAAA,MAAMthB,OAAO,GAAG,IAAKuhB,CAAAA,WAAL,EAAhB,CAAA;;AACA,IAAA,IAAI,IAAK1T,CAAAA,OAAL,CAAaQ,UAAjB,EAA6B;MAC3BnL,MAAM,CAAClD,OAAD,CAAN,CAAA;AACD,KAAA;;AAEDA,IAAAA,OAAO,CAACuC,SAAR,CAAkBuQ,GAAlB,CAAsB7D,iBAAtB,CAAA,CAAA;;IAEA,IAAKuS,CAAAA,iBAAL,CAAuB,MAAM;MAC3B/c,OAAO,CAAChB,QAAD,CAAP,CAAA;KADF,CAAA,CAAA;AAGD,GAAA;;EAEDqW,IAAI,CAACrW,QAAD,EAAW;AACb,IAAA,IAAI,CAAC,IAAA,CAAKoK,OAAL,CAAajM,SAAlB,EAA6B;MAC3B6C,OAAO,CAAChB,QAAD,CAAP,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK8d,WAAL,EAAmBhf,CAAAA,SAAnB,CAA6B0I,MAA7B,CAAoCgE,iBAApC,CAAA,CAAA;;IAEA,IAAKuS,CAAAA,iBAAL,CAAuB,MAAM;AAC3B,MAAA,IAAA,CAAKxT,OAAL,EAAA,CAAA;MACAvJ,OAAO,CAAChB,QAAD,CAAP,CAAA;KAFF,CAAA,CAAA;AAID,GAAA;;AAEDuK,EAAAA,OAAO,GAAG;IACR,IAAI,CAAC,IAAKqT,CAAAA,WAAV,EAAuB;AACrB,MAAA,OAAA;AACD,KAAA;;AAEDta,IAAAA,YAAY,CAACC,GAAb,CAAiB,IAAK4G,CAAAA,QAAtB,EAAgCoT,eAAhC,CAAA,CAAA;;IAEA,IAAKpT,CAAAA,QAAL,CAAc3C,MAAd,EAAA,CAAA;;IACA,IAAKoW,CAAAA,WAAL,GAAmB,KAAnB,CAAA;AACD,GAjE2B;;;AAoE5BE,EAAAA,WAAW,GAAG;IACZ,IAAI,CAAC,IAAK3T,CAAAA,QAAV,EAAoB;AAClB,MAAA,MAAM6T,QAAQ,GAAG5hB,QAAQ,CAAC6hB,aAAT,CAAuB,KAAvB,CAAjB,CAAA;AACAD,MAAAA,QAAQ,CAACR,SAAT,GAAqB,IAAKpT,CAAAA,OAAL,CAAaoT,SAAlC,CAAA;;AACA,MAAA,IAAI,IAAKpT,CAAAA,OAAL,CAAaQ,UAAjB,EAA6B;AAC3BoT,QAAAA,QAAQ,CAAClf,SAAT,CAAmBuQ,GAAnB,CAAuB9D,iBAAvB,CAAA,CAAA;AACD,OAAA;;MAED,IAAKpB,CAAAA,QAAL,GAAgB6T,QAAhB,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,KAAK7T,QAAZ,CAAA;AACD,GAAA;;EAEDd,iBAAiB,CAACF,MAAD,EAAS;AACxB;IACAA,MAAM,CAACsU,WAAP,GAAqBxf,UAAU,CAACkL,MAAM,CAACsU,WAAR,CAA/B,CAAA;AACA,IAAA,OAAOtU,MAAP,CAAA;AACD,GAAA;;AAED0U,EAAAA,OAAO,GAAG;IACR,IAAI,IAAA,CAAKD,WAAT,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMrhB,OAAO,GAAG,IAAKuhB,CAAAA,WAAL,EAAhB,CAAA;;AACA,IAAA,IAAA,CAAK1T,OAAL,CAAaqT,WAAb,CAAyBS,MAAzB,CAAgC3hB,OAAhC,CAAA,CAAA;;AAEA+G,IAAAA,YAAY,CAACkC,EAAb,CAAgBjJ,OAAhB,EAAyBghB,eAAzB,EAA0C,MAAM;AAC9Cvc,MAAAA,OAAO,CAAC,IAAA,CAAKoJ,OAAL,CAAasT,aAAd,CAAP,CAAA;KADF,CAAA,CAAA;IAIA,IAAKE,CAAAA,WAAL,GAAmB,IAAnB,CAAA;AACD,GAAA;;EAEDG,iBAAiB,CAAC/d,QAAD,EAAW;IAC1BiB,sBAAsB,CAACjB,QAAD,EAAW,IAAK8d,CAAAA,WAAL,EAAX,EAA+B,IAAK1T,CAAAA,OAAL,CAAaQ,UAA5C,CAAtB,CAAA;AACD,GAAA;;AAzG2B;;ACxC9B;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;;AAEA,MAAMlK,MAAI,GAAG,WAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,cAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAM6T,eAAa,GAAI,CAAS3T,OAAAA,EAAAA,WAAU,CAA1C,CAAA,CAAA;AACA,MAAM4T,iBAAiB,GAAI,CAAa5T,WAAAA,EAAAA,WAAU,CAAlD,CAAA,CAAA;AAEA,MAAM+M,OAAO,GAAG,KAAhB,CAAA;AACA,MAAM8G,eAAe,GAAG,SAAxB,CAAA;AACA,MAAMC,gBAAgB,GAAG,UAAzB,CAAA;AAEA,MAAMvV,SAAO,GAAG;AACdwV,EAAAA,WAAW,EAAE,IADC;AACK;AACnBC,EAAAA,SAAS,EAAE,IAAA;AAFG,CAAhB,CAAA;AAKA,MAAMxV,aAAW,GAAG;AAClBuV,EAAAA,WAAW,EAAE,SADK;AAElBC,EAAAA,SAAS,EAAE,SAAA;AAFO,CAApB,CAAA;AAKA;AACA;AACA;;AAEA,MAAMC,SAAN,SAAwB3V,MAAxB,CAA+B;EAC7BU,WAAW,CAACL,MAAD,EAAS;AAClB,IAAA,KAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAKiB,OAAL,GAAe,IAAA,CAAKlB,UAAL,CAAgBC,MAAhB,CAAf,CAAA;IACA,IAAKuV,CAAAA,SAAL,GAAiB,KAAjB,CAAA;IACA,IAAKC,CAAAA,oBAAL,GAA4B,IAA5B,CAAA;AACD,GAN4B;;;AASX,EAAA,WAAP5V,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAnB4B;;;AAsB7Bke,EAAAA,QAAQ,GAAG;IACT,IAAI,IAAA,CAAKF,SAAT,EAAoB;AAClB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKtU,CAAAA,OAAL,CAAaoU,SAAjB,EAA4B;AAC1B,MAAA,IAAA,CAAKpU,OAAL,CAAamU,WAAb,CAAyB1E,KAAzB,EAAA,CAAA;AACD,KAAA;;AAEDvW,IAAAA,YAAY,CAACC,GAAb,CAAiBnH,QAAjB,EAA2BoO,WAA3B,EATS;;AAUTlH,IAAAA,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B+hB,eAA1B,EAAyChb,KAAK,IAAI,IAAA,CAAK0b,cAAL,CAAoB1b,KAApB,CAAlD,CAAA,CAAA;AACAG,IAAAA,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0BgiB,iBAA1B,EAA6Cjb,KAAK,IAAI,IAAA,CAAK2b,cAAL,CAAoB3b,KAApB,CAAtD,CAAA,CAAA;IAEA,IAAKub,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACD,GAAA;;AAEDK,EAAAA,UAAU,GAAG;IACX,IAAI,CAAC,IAAKL,CAAAA,SAAV,EAAqB;AACnB,MAAA,OAAA;AACD,KAAA;;IAED,IAAKA,CAAAA,SAAL,GAAiB,KAAjB,CAAA;AACApb,IAAAA,YAAY,CAACC,GAAb,CAAiBnH,QAAjB,EAA2BoO,WAA3B,CAAA,CAAA;AACD,GA7C4B;;;EAgD7BqU,cAAc,CAAC1b,KAAD,EAAQ;IACpB,MAAM;AAAEob,MAAAA,WAAAA;AAAF,KAAA,GAAkB,KAAKnU,OAA7B,CAAA;;IAEA,IAAIjH,KAAK,CAAC3B,MAAN,KAAiBpF,QAAjB,IAA6B+G,KAAK,CAAC3B,MAAN,KAAiB+c,WAA9C,IAA6DA,WAAW,CAACxf,QAAZ,CAAqBoE,KAAK,CAAC3B,MAA3B,CAAjE,EAAqG;AACnG,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMwd,QAAQ,GAAG1S,cAAc,CAACc,iBAAf,CAAiCmR,WAAjC,CAAjB,CAAA;;AAEA,IAAA,IAAIS,QAAQ,CAAC9gB,MAAT,KAAoB,CAAxB,EAA2B;AACzBqgB,MAAAA,WAAW,CAAC1E,KAAZ,EAAA,CAAA;AACD,KAFD,MAEO,IAAI,IAAA,CAAK8E,oBAAL,KAA8BL,gBAAlC,EAAoD;MACzDU,QAAQ,CAACA,QAAQ,CAAC9gB,MAAT,GAAkB,CAAnB,CAAR,CAA8B2b,KAA9B,EAAA,CAAA;AACD,KAFM,MAEA;AACLmF,MAAAA,QAAQ,CAAC,CAAD,CAAR,CAAYnF,KAAZ,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDiF,cAAc,CAAC3b,KAAD,EAAQ;AACpB,IAAA,IAAIA,KAAK,CAACuD,GAAN,KAAc6Q,OAAlB,EAA2B;AACzB,MAAA,OAAA;AACD,KAAA;;IAED,IAAKoH,CAAAA,oBAAL,GAA4Bxb,KAAK,CAAC8b,QAAN,GAAiBX,gBAAjB,GAAoCD,eAAhE,CAAA;AACD,GAAA;;AAxE4B;;ACvC/B;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;AACA;;AAEA,MAAM3d,MAAI,GAAG,OAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,UAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AACA,MAAMuL,YAAU,GAAG,QAAnB,CAAA;AAEA,MAAMtC,YAAU,GAAI,CAAMxK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAM0U,sBAAoB,GAAI,CAAe1U,aAAAA,EAAAA,WAAU,CAAvD,CAAA,CAAA;AACA,MAAMyK,cAAY,GAAI,CAAQzK,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAMsK,YAAU,GAAI,CAAMtK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,aAAW,GAAI,CAAOvK,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAM2U,cAAY,GAAI,CAAQ3U,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAM4U,mBAAmB,GAAI,CAAe5U,aAAAA,EAAAA,WAAU,CAAtD,CAAA,CAAA;AACA,MAAM6U,uBAAqB,GAAI,CAAiB7U,eAAAA,EAAAA,WAAU,CAA1D,CAAA,CAAA;AACA,MAAM0B,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AAEA,MAAMuT,eAAe,GAAG,YAAxB,CAAA;AACA,MAAM/T,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMC,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAM+T,iBAAiB,GAAG,cAA1B,CAAA;AAEA,MAAMC,eAAa,GAAG,aAAtB,CAAA;AACA,MAAMC,eAAe,GAAG,eAAxB,CAAA;AACA,MAAMC,mBAAmB,GAAG,aAA5B,CAAA;AACA,MAAMzT,sBAAoB,GAAG,0BAA7B,CAAA;AAEA,MAAMlD,SAAO,GAAG;AACdiV,EAAAA,QAAQ,EAAE,IADI;AAEd1M,EAAAA,QAAQ,EAAE,IAFI;AAGduI,EAAAA,KAAK,EAAE,IAAA;AAHO,CAAhB,CAAA;AAMA,MAAM7Q,aAAW,GAAG;AAClBgV,EAAAA,QAAQ,EAAE,kBADQ;AAElB1M,EAAAA,QAAQ,EAAE,SAFQ;AAGlBuI,EAAAA,KAAK,EAAE,SAAA;AAHW,CAApB,CAAA;AAMA;AACA;AACA;;AAEA,MAAM8F,KAAN,SAAoBzV,aAApB,CAAkC;AAChCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKyW,CAAAA,OAAL,GAAetT,cAAc,CAACG,OAAf,CAAuBgT,eAAvB,EAAwC,IAAKtV,CAAAA,QAA7C,CAAf,CAAA;AACA,IAAA,IAAA,CAAK0V,SAAL,GAAiB,IAAKC,CAAAA,mBAAL,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,UAAL,GAAkB,IAAKC,CAAAA,oBAAL,EAAlB,CAAA;IACA,IAAK5J,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKR,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;AACA,IAAA,IAAA,CAAKqK,UAAL,GAAkB,IAAIjE,eAAJ,EAAlB,CAAA;;AAEA,IAAA,IAAA,CAAK9J,kBAAL,EAAA,CAAA;AACD,GAZ+B;;;AAed,EAAA,WAAPnJ,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAzB+B;;;EA4BhC0L,MAAM,CAACvH,aAAD,EAAgB;IACpB,OAAO,IAAA,CAAKuR,QAAL,GAAgB,IAAKC,CAAAA,IAAL,EAAhB,GAA8B,IAAKC,CAAAA,IAAL,CAAUzR,aAAV,CAArC,CAAA;AACD,GAAA;;EAEDyR,IAAI,CAACzR,aAAD,EAAgB;AAClB,IAAA,IAAI,IAAKuR,CAAAA,QAAL,IAAiB,IAAA,CAAKR,gBAA1B,EAA4C;AAC1C,MAAA,OAAA;AACD,KAAA;;IAED,MAAM+D,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC2K,YAApC,EAAgD;AAChEjQ,MAAAA,aAAAA;AADgE,KAAhD,CAAlB,CAAA;;IAIA,IAAI8U,SAAS,CAACvT,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,IAAKgQ,CAAAA,QAAL,GAAgB,IAAhB,CAAA;IACA,IAAKR,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;IAEA,IAAKqK,CAAAA,UAAL,CAAgB5J,IAAhB,EAAA,CAAA;;AAEAja,IAAAA,QAAQ,CAACyD,IAAT,CAAcf,SAAd,CAAwBuQ,GAAxB,CAA4BiQ,eAA5B,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAKY,aAAL,EAAA,CAAA;;IAEA,IAAKL,CAAAA,SAAL,CAAevJ,IAAf,CAAoB,MAAM,IAAK6J,CAAAA,YAAL,CAAkBtb,aAAlB,CAA1B,CAAA,CAAA;AACD,GAAA;;AAEDwR,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,CAAC,IAAKD,CAAAA,QAAN,IAAkB,IAAA,CAAKR,gBAA3B,EAA6C;AAC3C,MAAA,OAAA;AACD,KAAA;;IAED,MAAMqE,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC6K,YAApC,CAAlB,CAAA;;IAEA,IAAIiF,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,IAAKgQ,CAAAA,QAAL,GAAgB,KAAhB,CAAA;IACA,IAAKR,CAAAA,gBAAL,GAAwB,IAAxB,CAAA;;IACA,IAAKmK,CAAAA,UAAL,CAAgBhB,UAAhB,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAK5U,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BgE,iBAA/B,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAKb,cAAL,CAAoB,MAAM,IAAA,CAAKyV,UAAL,EAA1B,EAA6C,IAAA,CAAKjW,QAAlD,EAA4D,IAAKsK,CAAAA,WAAL,EAA5D,CAAA,CAAA;AACD,GAAA;;AAEDlK,EAAAA,OAAO,GAAG;IACR,KAAK,MAAM8V,WAAX,IAA0B,CAAChjB,MAAD,EAAS,IAAA,CAAKuiB,OAAd,CAA1B,EAAkD;AAChDtc,MAAAA,YAAY,CAACC,GAAb,CAAiB8c,WAAjB,EAA8B7V,WAA9B,CAAA,CAAA;AACD,KAAA;;IAED,IAAKqV,CAAAA,SAAL,CAAetV,OAAf,EAAA,CAAA;;IACA,IAAKwV,CAAAA,UAAL,CAAgBhB,UAAhB,EAAA,CAAA;;AACA,IAAA,KAAA,CAAMxU,OAAN,EAAA,CAAA;AACD,GAAA;;AAED+V,EAAAA,YAAY,GAAG;AACb,IAAA,IAAA,CAAKJ,aAAL,EAAA,CAAA;AACD,GAzF+B;;;AA4FhCJ,EAAAA,mBAAmB,GAAG;IACpB,OAAO,IAAInC,QAAJ,CAAa;AAClBxf,MAAAA,SAAS,EAAE+G,OAAO,CAAC,KAAKkF,OAAL,CAAa4T,QAAd,CADA;AACyB;MAC3CpT,UAAU,EAAE,KAAK6J,WAAL,EAAA;AAFM,KAAb,CAAP,CAAA;AAID,GAAA;;AAEDuL,EAAAA,oBAAoB,GAAG;IACrB,OAAO,IAAIvB,SAAJ,CAAc;AACnBF,MAAAA,WAAW,EAAE,IAAKpU,CAAAA,QAAAA;AADC,KAAd,CAAP,CAAA;AAGD,GAAA;;EAEDgW,YAAY,CAACtb,aAAD,EAAgB;AAC1B;IACA,IAAI,CAACzI,QAAQ,CAACyD,IAAT,CAAcd,QAAd,CAAuB,IAAA,CAAKoL,QAA5B,CAAL,EAA4C;AAC1C/N,MAAAA,QAAQ,CAACyD,IAAT,CAAcqe,MAAd,CAAqB,KAAK/T,QAA1B,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKA,QAAL,CAAc0M,KAAd,CAAoBqC,OAApB,GAA8B,OAA9B,CAAA;;AACA,IAAA,IAAA,CAAK/O,QAAL,CAAc9B,eAAd,CAA8B,aAA9B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAK8B,QAAL,CAAchC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKgC,QAAL,CAAchC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKgC,QAAL,CAAcoW,SAAd,GAA0B,CAA1B,CAAA;IAEA,MAAMC,SAAS,GAAGlU,cAAc,CAACG,OAAf,CAAuBiT,mBAAvB,EAA4C,IAAKE,CAAAA,OAAjD,CAAlB,CAAA;;AACA,IAAA,IAAIY,SAAJ,EAAe;MACbA,SAAS,CAACD,SAAV,GAAsB,CAAtB,CAAA;AACD,KAAA;;IAED9gB,MAAM,CAAC,IAAK0K,CAAAA,QAAN,CAAN,CAAA;;AAEA,IAAA,IAAA,CAAKA,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B7D,iBAA5B,CAAA,CAAA;;IAEA,MAAMiV,kBAAkB,GAAG,MAAM;AAC/B,MAAA,IAAI,IAAKrW,CAAAA,OAAL,CAAayP,KAAjB,EAAwB;QACtB,IAAKkG,CAAAA,UAAL,CAAgBnB,QAAhB,EAAA,CAAA;AACD,OAAA;;MAED,IAAKhJ,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;AACAtS,MAAAA,YAAY,CAACyC,OAAb,CAAqB,KAAKoE,QAA1B,EAAoC4K,aAApC,EAAiD;AAC/ClQ,QAAAA,aAAAA;OADF,CAAA,CAAA;KANF,CAAA;;IAWA,IAAK8F,CAAAA,cAAL,CAAoB8V,kBAApB,EAAwC,KAAKb,OAA7C,EAAsD,IAAKnL,CAAAA,WAAL,EAAtD,CAAA,CAAA;AACD,GAAA;;AAEDvC,EAAAA,kBAAkB,GAAG;IACnB5O,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BkV,uBAA/B,EAAsDlc,KAAK,IAAI;AAC7D,MAAA,IAAIA,KAAK,CAACuD,GAAN,KAAc4Q,YAAlB,EAA8B;AAC5B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,IAAKlN,CAAAA,OAAL,CAAakH,QAAjB,EAA2B;AACzBnO,QAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACA,QAAA,IAAA,CAAKwP,IAAL,EAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKqK,0BAAL,EAAA,CAAA;KAXF,CAAA,CAAA;AAcApd,IAAAA,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwB8hB,cAAxB,EAAsC,MAAM;AAC1C,MAAA,IAAI,KAAK/I,QAAL,IAAiB,CAAC,IAAA,CAAKR,gBAA3B,EAA6C;AAC3C,QAAA,IAAA,CAAKsK,aAAL,EAAA,CAAA;AACD,OAAA;KAHH,CAAA,CAAA;IAMA5c,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BiV,mBAA/B,EAAoDjc,KAAK,IAAI;AAC3D,MAAA,IAAIA,KAAK,CAAC3B,MAAN,KAAiB2B,KAAK,CAACwd,aAA3B,EAA0C;AAAE;AAC1C,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,KAAKvW,OAAL,CAAa4T,QAAb,KAA0B,QAA9B,EAAwC;AACtC,QAAA,IAAA,CAAK0C,0BAAL,EAAA,CAAA;;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,IAAKtW,CAAAA,OAAL,CAAa4T,QAAjB,EAA2B;AACzB,QAAA,IAAA,CAAK3H,IAAL,EAAA,CAAA;AACD,OAAA;KAZH,CAAA,CAAA;AAcD,GAAA;;AAED+J,EAAAA,UAAU,GAAG;AACX,IAAA,IAAA,CAAKjW,QAAL,CAAc0M,KAAd,CAAoBqC,OAApB,GAA8B,MAA9B,CAAA;;AACA,IAAA,IAAA,CAAK/O,QAAL,CAAchC,YAAd,CAA2B,aAA3B,EAA0C,IAA1C,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKgC,QAAL,CAAc9B,eAAd,CAA8B,YAA9B,CAAA,CAAA;;AACA,IAAA,IAAA,CAAK8B,QAAL,CAAc9B,eAAd,CAA8B,MAA9B,CAAA,CAAA;;IACA,IAAKuN,CAAAA,gBAAL,GAAwB,KAAxB,CAAA;;AAEA,IAAA,IAAA,CAAKiK,SAAL,CAAexJ,IAAf,CAAoB,MAAM;AACxBja,MAAAA,QAAQ,CAACyD,IAAT,CAAcf,SAAd,CAAwB0I,MAAxB,CAA+B8X,eAA/B,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKsB,iBAAL,EAAA,CAAA;;MACA,IAAKX,CAAAA,UAAL,CAAgBxD,KAAhB,EAAA,CAAA;;AACAnZ,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC8K,cAApC,CAAA,CAAA;KAJF,CAAA,CAAA;AAMD,GAAA;;AAEDR,EAAAA,WAAW,GAAG;IACZ,OAAO,IAAA,CAAKtK,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCwM,iBAAjC,CAAP,CAAA;AACD,GAAA;;AAEDmV,EAAAA,0BAA0B,GAAG;IAC3B,MAAMzG,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC+U,sBAApC,CAAlB,CAAA;;IACA,IAAIjF,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,MAAMya,kBAAkB,GAAG,IAAA,CAAK1W,QAAL,CAAc2W,YAAd,GAA6B1kB,QAAQ,CAAC+C,eAAT,CAAyB4hB,YAAjF,CAAA;IACA,MAAMC,gBAAgB,GAAG,IAAK7W,CAAAA,QAAL,CAAc0M,KAAd,CAAoBoK,SAA7C,CAP2B;;AAS3B,IAAA,IAAID,gBAAgB,KAAK,QAArB,IAAiC,IAAK7W,CAAAA,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCwgB,iBAAjC,CAArC,EAA0F;AACxF,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,CAACsB,kBAAL,EAAyB;AACvB,MAAA,IAAA,CAAK1W,QAAL,CAAc0M,KAAd,CAAoBoK,SAApB,GAAgC,QAAhC,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK9W,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BkQ,iBAA5B,CAAA,CAAA;;IACA,IAAK5U,CAAAA,cAAL,CAAoB,MAAM;AACxB,MAAA,IAAA,CAAKR,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B+X,iBAA/B,CAAA,CAAA;;MACA,IAAK5U,CAAAA,cAAL,CAAoB,MAAM;AACxB,QAAA,IAAA,CAAKR,QAAL,CAAc0M,KAAd,CAAoBoK,SAApB,GAAgCD,gBAAhC,CAAA;OADF,EAEG,KAAKpB,OAFR,CAAA,CAAA;KAFF,EAKG,KAAKA,OALR,CAAA,CAAA;;IAOA,IAAKzV,CAAAA,QAAL,CAAc0P,KAAd,EAAA,CAAA;AACD,GAAA;AAED;AACF;AACA;;;AAEEqG,EAAAA,aAAa,GAAG;IACd,MAAMW,kBAAkB,GAAG,IAAA,CAAK1W,QAAL,CAAc2W,YAAd,GAA6B1kB,QAAQ,CAAC+C,eAAT,CAAyB4hB,YAAjF,CAAA;;AACA,IAAA,MAAMhE,cAAc,GAAG,IAAA,CAAKkD,UAAL,CAAgBhE,QAAhB,EAAvB,CAAA;;AACA,IAAA,MAAMiF,iBAAiB,GAAGnE,cAAc,GAAG,CAA3C,CAAA;;AAEA,IAAA,IAAImE,iBAAiB,IAAI,CAACL,kBAA1B,EAA8C;AAC5C,MAAA,MAAMnX,QAAQ,GAAGtJ,KAAK,EAAK,GAAA,aAAL,GAAqB,cAA3C,CAAA;MACA,IAAK+J,CAAAA,QAAL,CAAc0M,KAAd,CAAoBnN,QAApB,CAAiC,GAAA,CAAA,EAAEqT,cAAe,CAAlD,EAAA,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAACmE,iBAAD,IAAsBL,kBAA1B,EAA8C;AAC5C,MAAA,MAAMnX,QAAQ,GAAGtJ,KAAK,EAAK,GAAA,cAAL,GAAsB,aAA5C,CAAA;MACA,IAAK+J,CAAAA,QAAL,CAAc0M,KAAd,CAAoBnN,QAApB,CAAiC,GAAA,CAAA,EAAEqT,cAAe,CAAlD,EAAA,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAED6D,EAAAA,iBAAiB,GAAG;AAClB,IAAA,IAAA,CAAKzW,QAAL,CAAc0M,KAAd,CAAoBsK,WAApB,GAAkC,EAAlC,CAAA;AACA,IAAA,IAAA,CAAKhX,QAAL,CAAc0M,KAAd,CAAoBuK,YAApB,GAAmC,EAAnC,CAAA;AACD,GAvP+B;;;AA0PV,EAAA,OAAfvgB,eAAe,CAACsI,MAAD,EAAStE,aAAT,EAAwB;IAC5C,OAAO,IAAA,CAAKgH,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAG6T,KAAK,CAAC7U,mBAAN,CAA0B,IAA1B,EAAgC3B,MAAhC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;AAED2C,MAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAatE,aAAb,CAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AAxQ+B,CAAA;AA2QlC;AACA;AACA;;;AAEAvB,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDD,sBAAhD,EAAsE,UAAU9I,KAAV,EAAiB;AACrF,EAAA,MAAM3B,MAAM,GAAGvE,sBAAsB,CAAC,IAAD,CAArC,CAAA;;EAEA,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcN,QAAd,CAAuB,IAAA,CAAKyO,OAA5B,CAAJ,EAA0C;AACxCjI,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACD,GAAA;;EAEDvD,YAAY,CAACmC,GAAb,CAAiBjE,MAAjB,EAAyBsT,YAAzB,EAAqC6E,SAAS,IAAI;IAChD,IAAIA,SAAS,CAACvT,gBAAd,EAAgC;AAC9B;AACA,MAAA,OAAA;AACD,KAAA;;AAED9C,IAAAA,YAAY,CAACmC,GAAb,CAAiBjE,MAAjB,EAAyByT,cAAzB,EAAuC,MAAM;AAC3C,MAAA,IAAI9W,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,QAAA,IAAA,CAAK0b,KAAL,EAAA,CAAA;AACD,OAAA;KAHH,CAAA,CAAA;AAKD,GAXD,EAPqF;;AAqBrF,EAAA,MAAMwH,WAAW,GAAG/U,cAAc,CAACG,OAAf,CAAuB+S,eAAvB,CAApB,CAAA;;AACA,EAAA,IAAI6B,WAAJ,EAAiB;AACf1B,IAAAA,KAAK,CAAC9U,WAAN,CAAkBwW,WAAlB,EAA+BhL,IAA/B,EAAA,CAAA;AACD,GAAA;;AAED,EAAA,MAAMvK,IAAI,GAAG6T,KAAK,CAAC7U,mBAAN,CAA0BtJ,MAA1B,CAAb,CAAA;EAEAsK,IAAI,CAACM,MAAL,CAAY,IAAZ,CAAA,CAAA;AACD,CA7BD,CAAA,CAAA;AA+BApB,oBAAoB,CAAC2U,KAAD,CAApB,CAAA;AAEA;AACA;AACA;;AAEArf,kBAAkB,CAACqf,KAAD,CAAlB;;AClXA;AACA;AACA;AACA;AACA;AACA;AAgBA;AACA;AACA;;AAEA,MAAMjf,MAAI,GAAG,WAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,cAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,cAAY,GAAG,WAArB,CAAA;AACA,MAAMuE,qBAAmB,GAAI,CAAA,IAAA,EAAM9F,WAAU,CAAA,EAAEuB,cAAa,CAA5D,CAAA,CAAA;AACA,MAAMuL,UAAU,GAAG,QAAnB,CAAA;AAEA,MAAM9L,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAM8V,oBAAkB,GAAG,SAA3B,CAAA;AACA,MAAMC,iBAAiB,GAAG,QAA1B,CAAA;AACA,MAAMC,mBAAmB,GAAG,oBAA5B,CAAA;AACA,MAAMhC,aAAa,GAAG,iBAAtB,CAAA;AAEA,MAAM1K,YAAU,GAAI,CAAMtK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,aAAW,GAAI,CAAOvK,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAMwK,YAAU,GAAI,CAAMxK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAM0U,oBAAoB,GAAI,CAAe1U,aAAAA,EAAAA,WAAU,CAAvD,CAAA,CAAA;AACA,MAAMyK,cAAY,GAAI,CAAQzK,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAM2U,YAAY,GAAI,CAAQ3U,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAM0B,sBAAoB,GAAI,CAAA,KAAA,EAAO1B,WAAU,CAAA,EAAEuB,cAAa,CAA9D,CAAA,CAAA;AACA,MAAMsT,qBAAqB,GAAI,CAAiB7U,eAAAA,EAAAA,WAAU,CAA1D,CAAA,CAAA;AAEA,MAAMyB,sBAAoB,GAAG,8BAA7B,CAAA;AAEA,MAAMlD,SAAO,GAAG;AACdiV,EAAAA,QAAQ,EAAE,IADI;AAEd1M,EAAAA,QAAQ,EAAE,IAFI;AAGdmQ,EAAAA,MAAM,EAAE,KAAA;AAHM,CAAhB,CAAA;AAMA,MAAMzY,aAAW,GAAG;AAClBgV,EAAAA,QAAQ,EAAE,kBADQ;AAElB1M,EAAAA,QAAQ,EAAE,SAFQ;AAGlBmQ,EAAAA,MAAM,EAAE,SAAA;AAHU,CAApB,CAAA;AAMA;AACA;AACA;;AAEA,MAAMC,SAAN,SAAwBxX,aAAxB,CAAsC;AACpCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKiN,CAAAA,QAAL,GAAgB,KAAhB,CAAA;AACA,IAAA,IAAA,CAAKyJ,SAAL,GAAiB,IAAKC,CAAAA,mBAAL,EAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,UAAL,GAAkB,IAAKC,CAAAA,oBAAL,EAAlB,CAAA;;AACA,IAAA,IAAA,CAAK9N,kBAAL,EAAA,CAAA;AACD,GARmC;;;AAWlB,EAAA,WAAPnJ,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GArBmC;;;EAwBpC0L,MAAM,CAACvH,aAAD,EAAgB;IACpB,OAAO,IAAA,CAAKuR,QAAL,GAAgB,IAAKC,CAAAA,IAAL,EAAhB,GAA8B,IAAKC,CAAAA,IAAL,CAAUzR,aAAV,CAArC,CAAA;AACD,GAAA;;EAEDyR,IAAI,CAACzR,aAAD,EAAgB;IAClB,IAAI,IAAA,CAAKuR,QAAT,EAAmB;AACjB,MAAA,OAAA;AACD,KAAA;;IAED,MAAMuD,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC2K,YAApC,EAAgD;AAAEjQ,MAAAA,aAAAA;AAAF,KAAhD,CAAlB,CAAA;;IAEA,IAAI8U,SAAS,CAACvT,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,IAAKgQ,CAAAA,QAAL,GAAgB,IAAhB,CAAA;;IACA,IAAKyJ,CAAAA,SAAL,CAAevJ,IAAf,EAAA,CAAA;;AAEA,IAAA,IAAI,CAAC,IAAA,CAAKlM,OAAL,CAAaqX,MAAlB,EAA0B;MACxB,IAAIzF,eAAJ,GAAsB3F,IAAtB,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKlM,QAAL,CAAchC,YAAd,CAA2B,YAA3B,EAAyC,IAAzC,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKgC,QAAL,CAAchC,YAAd,CAA2B,MAA3B,EAAmC,QAAnC,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKgC,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BiS,oBAA5B,CAAA,CAAA;;IAEA,MAAM9M,gBAAgB,GAAG,MAAM;AAC7B,MAAA,IAAI,CAAC,IAAA,CAAKpK,OAAL,CAAaqX,MAAlB,EAA0B;QACxB,IAAK1B,CAAAA,UAAL,CAAgBnB,QAAhB,EAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAKzU,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B7D,iBAA5B,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKrB,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B8Z,oBAA/B,CAAA,CAAA;;AACAhe,MAAAA,YAAY,CAACyC,OAAb,CAAqB,KAAKoE,QAA1B,EAAoC4K,aAApC,EAAiD;AAAElQ,QAAAA,aAAAA;OAAnD,CAAA,CAAA;KAPF,CAAA;;AAUA,IAAA,IAAA,CAAK8F,cAAL,CAAoB6J,gBAApB,EAAsC,IAAKrK,CAAAA,QAA3C,EAAqD,IAArD,CAAA,CAAA;AACD,GAAA;;AAEDkM,EAAAA,IAAI,GAAG;IACL,IAAI,CAAC,IAAKD,CAAAA,QAAV,EAAoB;AAClB,MAAA,OAAA;AACD,KAAA;;IAED,MAAM6D,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC6K,YAApC,CAAlB,CAAA;;IAEA,IAAIiF,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,IAAK2Z,CAAAA,UAAL,CAAgBhB,UAAhB,EAAA,CAAA;;IACA,IAAK5U,CAAAA,QAAL,CAAcwX,IAAd,EAAA,CAAA;;IACA,IAAKvL,CAAAA,QAAL,GAAgB,KAAhB,CAAA;;AACA,IAAA,IAAA,CAAKjM,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BkS,iBAA5B,CAAA,CAAA;;IACA,IAAK1B,CAAAA,SAAL,CAAexJ,IAAf,EAAA,CAAA;;IAEA,MAAMuL,gBAAgB,GAAG,MAAM;MAC7B,IAAKzX,CAAAA,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BgE,iBAA/B,EAAgD+V,iBAAhD,CAAA,CAAA;;AACA,MAAA,IAAA,CAAKpX,QAAL,CAAc9B,eAAd,CAA8B,YAA9B,CAAA,CAAA;;AACA,MAAA,IAAA,CAAK8B,QAAL,CAAc9B,eAAd,CAA8B,MAA9B,CAAA,CAAA;;AAEA,MAAA,IAAI,CAAC,IAAA,CAAK+B,OAAL,CAAaqX,MAAlB,EAA0B;QACxB,IAAIzF,eAAJ,GAAsBS,KAAtB,EAAA,CAAA;AACD,OAAA;;AAEDnZ,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC8K,cAApC,CAAA,CAAA;KATF,CAAA;;AAYA,IAAA,IAAA,CAAKtK,cAAL,CAAoBiX,gBAApB,EAAsC,IAAKzX,CAAAA,QAA3C,EAAqD,IAArD,CAAA,CAAA;AACD,GAAA;;AAEDI,EAAAA,OAAO,GAAG;IACR,IAAKsV,CAAAA,SAAL,CAAetV,OAAf,EAAA,CAAA;;IACA,IAAKwV,CAAAA,UAAL,CAAgBhB,UAAhB,EAAA,CAAA;;AACA,IAAA,KAAA,CAAMxU,OAAN,EAAA,CAAA;AACD,GAnGmC;;;AAsGpCuV,EAAAA,mBAAmB,GAAG;IACpB,MAAMpC,aAAa,GAAG,MAAM;AAC1B,MAAA,IAAI,KAAKtT,OAAL,CAAa4T,QAAb,KAA0B,QAA9B,EAAwC;AACtC1a,QAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC+U,oBAApC,CAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAK7I,IAAL,EAAA,CAAA;AACD,KAPD,CADoB;;;IAWpB,MAAMlY,SAAS,GAAG+G,OAAO,CAAC,KAAKkF,OAAL,CAAa4T,QAAd,CAAzB,CAAA;IAEA,OAAO,IAAIL,QAAJ,CAAa;AAClBH,MAAAA,SAAS,EAAEgE,mBADO;MAElBrjB,SAFkB;AAGlByM,MAAAA,UAAU,EAAE,IAHM;AAIlB6S,MAAAA,WAAW,EAAE,IAAA,CAAKtT,QAAL,CAAczL,UAJT;AAKlBgf,MAAAA,aAAa,EAAEvf,SAAS,GAAGuf,aAAH,GAAmB,IAAA;AALzB,KAAb,CAAP,CAAA;AAOD,GAAA;;AAEDsC,EAAAA,oBAAoB,GAAG;IACrB,OAAO,IAAIvB,SAAJ,CAAc;AACnBF,MAAAA,WAAW,EAAE,IAAKpU,CAAAA,QAAAA;AADC,KAAd,CAAP,CAAA;AAGD,GAAA;;AAED+H,EAAAA,kBAAkB,GAAG;IACnB5O,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BkV,qBAA/B,EAAsDlc,KAAK,IAAI;AAC7D,MAAA,IAAIA,KAAK,CAACuD,GAAN,KAAc4Q,UAAlB,EAA8B;AAC5B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,CAAC,IAAA,CAAKlN,OAAL,CAAakH,QAAlB,EAA4B;AAC1BhO,QAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC+U,oBAApC,CAAA,CAAA;AACA,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAK7I,IAAL,EAAA,CAAA;KAVF,CAAA,CAAA;AAYD,GA/ImC;;;EAkJd,OAAfxV,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAG4V,SAAS,CAAC5W,mBAAV,CAA8B,IAA9B,EAAoC3B,MAApC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI2C,IAAI,CAAC3C,MAAD,CAAJ,KAAiB3N,SAAjB,IAA8B2N,MAAM,CAACvM,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuM,MAAM,KAAK,aAAvE,EAAsF;AACpF,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;AAED2C,MAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AAhKmC,CAAA;AAmKtC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,sBAA1B,EAAgDD,sBAAhD,EAAsE,UAAU9I,KAAV,EAAiB;AACrF,EAAA,MAAM3B,MAAM,GAAGvE,sBAAsB,CAAC,IAAD,CAArC,CAAA;;EAEA,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcN,QAAd,CAAuB,IAAA,CAAKyO,OAA5B,CAAJ,EAA0C;AACxCjI,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACD,GAAA;;AAED,EAAA,IAAIlI,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB,IAAA,OAAA;AACD,GAAA;;AAED2E,EAAAA,YAAY,CAACmC,GAAb,CAAiBjE,MAAjB,EAAyByT,cAAzB,EAAuC,MAAM;AAC3C;AACA,IAAA,IAAI9W,SAAS,CAAC,IAAD,CAAb,EAAqB;AACnB,MAAA,IAAA,CAAK0b,KAAL,EAAA,CAAA;AACD,KAAA;AACF,GALD,EAXqF;;AAmBrF,EAAA,MAAMwH,WAAW,GAAG/U,cAAc,CAACG,OAAf,CAAuB+S,aAAvB,CAApB,CAAA;;AACA,EAAA,IAAI6B,WAAW,IAAIA,WAAW,KAAK7f,MAAnC,EAA2C;AACzCkgB,IAAAA,SAAS,CAAC7W,WAAV,CAAsBwW,WAAtB,EAAmChL,IAAnC,EAAA,CAAA;AACD,GAAA;;AAED,EAAA,MAAMvK,IAAI,GAAG4V,SAAS,CAAC5W,mBAAV,CAA8BtJ,MAA9B,CAAb,CAAA;EACAsK,IAAI,CAACM,MAAL,CAAY,IAAZ,CAAA,CAAA;AACD,CA1BD,CAAA,CAAA;AA4BA9I,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwBiT,qBAAxB,EAA6C,MAAM;EACjD,KAAK,MAAM9T,QAAX,IAAuB8P,cAAc,CAACpI,IAAf,CAAoBsb,aAApB,CAAvB,EAA2D;AACzDkC,IAAAA,SAAS,CAAC5W,mBAAV,CAA8BtO,QAA9B,EAAwC8Z,IAAxC,EAAA,CAAA;AACD,GAAA;AACF,CAJD,CAAA,CAAA;AAMAhT,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwB8hB,YAAxB,EAAsC,MAAM;EAC1C,KAAK,MAAM5iB,OAAX,IAAsB+P,cAAc,CAACpI,IAAf,CAAoB,8CAApB,CAAtB,EAA2F;IACzF,IAAI5G,gBAAgB,CAACf,OAAD,CAAhB,CAA0BslB,QAA1B,KAAuC,OAA3C,EAAoD;AAClDH,MAAAA,SAAS,CAAC5W,mBAAV,CAA8BvO,OAA9B,EAAuC8Z,IAAvC,EAAA,CAAA;AACD,KAAA;AACF,GAAA;AACF,CAND,CAAA,CAAA;AAQArL,oBAAoB,CAAC0W,SAAD,CAApB,CAAA;AAEA;AACA;AACA;;AAEAphB,kBAAkB,CAACohB,SAAD,CAAlB;;ACxRA;AACA;AACA;AACA;AACA;AACA;AAEA,MAAMI,aAAa,GAAG,IAAIhf,GAAJ,CAAQ,CAC5B,YAD4B,EAE5B,MAF4B,EAG5B,MAH4B,EAI5B,UAJ4B,EAK5B,UAL4B,EAM5B,QAN4B,EAO5B,KAP4B,EAQ5B,YAR4B,CAAR,CAAtB,CAAA;AAWA,MAAMif,sBAAsB,GAAG,gBAA/B,CAAA;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,gEAAzB,CAAA;AAEA;AACA;AACA;AACA;AACA;;AACA,MAAMC,gBAAgB,GAAG,oIAAzB,CAAA;;AAEA,MAAMC,gBAAgB,GAAG,CAACC,SAAD,EAAYC,oBAAZ,KAAqC;AAC5D,EAAA,MAAMC,aAAa,GAAGF,SAAS,CAACG,QAAV,CAAmBxmB,WAAnB,EAAtB,CAAA;;AAEA,EAAA,IAAIsmB,oBAAoB,CAACzlB,QAArB,CAA8B0lB,aAA9B,CAAJ,EAAkD;AAChD,IAAA,IAAIP,aAAa,CAACpd,GAAd,CAAkB2d,aAAlB,CAAJ,EAAsC;AACpC,MAAA,OAAOnd,OAAO,CAAC8c,gBAAgB,CAAClY,IAAjB,CAAsBqY,SAAS,CAACI,SAAhC,CAA8CN,IAAAA,gBAAgB,CAACnY,IAAjB,CAAsBqY,SAAS,CAACI,SAAhC,CAA/C,CAAd,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,IAAP,CAAA;AACD,GAT2D;;;EAY5D,OAAOH,oBAAoB,CAAC1Z,MAArB,CAA4B8Z,cAAc,IAAIA,cAAc,YAAY3Y,MAAxE,CAAA,CACJ4Y,IADI,CACCC,KAAK,IAAIA,KAAK,CAAC5Y,IAAN,CAAWuY,aAAX,CADV,CAAP,CAAA;AAED,CAdD,CAAA;;AAgBO,MAAMM,gBAAgB,GAAG;AAC9B;AACA,EAAA,GAAA,EAAK,CAAC,OAAD,EAAU,KAAV,EAAiB,IAAjB,EAAuB,MAAvB,EAA+B,MAA/B,EAAuCZ,sBAAvC,CAFyB;EAG9Ba,CAAC,EAAE,CAAC,QAAD,EAAW,MAAX,EAAmB,OAAnB,EAA4B,KAA5B,CAH2B;AAI9BC,EAAAA,IAAI,EAAE,EAJwB;AAK9BC,EAAAA,CAAC,EAAE,EAL2B;AAM9BC,EAAAA,EAAE,EAAE,EAN0B;AAO9BC,EAAAA,GAAG,EAAE,EAPyB;AAQ9BC,EAAAA,IAAI,EAAE,EARwB;AAS9BC,EAAAA,GAAG,EAAE,EATyB;AAU9BC,EAAAA,EAAE,EAAE,EAV0B;AAW9BC,EAAAA,EAAE,EAAE,EAX0B;AAY9BC,EAAAA,EAAE,EAAE,EAZ0B;AAa9BC,EAAAA,EAAE,EAAE,EAb0B;AAc9BC,EAAAA,EAAE,EAAE,EAd0B;AAe9BC,EAAAA,EAAE,EAAE,EAf0B;AAgB9BC,EAAAA,EAAE,EAAE,EAhB0B;AAiB9BC,EAAAA,EAAE,EAAE,EAjB0B;AAkB9BC,EAAAA,CAAC,EAAE,EAlB2B;AAmB9BtQ,EAAAA,GAAG,EAAE,CAAC,KAAD,EAAQ,QAAR,EAAkB,KAAlB,EAAyB,OAAzB,EAAkC,OAAlC,EAA2C,QAA3C,CAnByB;AAoB9BuQ,EAAAA,EAAE,EAAE,EApB0B;AAqB9BC,EAAAA,EAAE,EAAE,EArB0B;AAsB9BC,EAAAA,CAAC,EAAE,EAtB2B;AAuB9BC,EAAAA,GAAG,EAAE,EAvByB;AAwB9BC,EAAAA,CAAC,EAAE,EAxB2B;AAyB9BC,EAAAA,KAAK,EAAE,EAzBuB;AA0B9BC,EAAAA,IAAI,EAAE,EA1BwB;AA2B9BC,EAAAA,GAAG,EAAE,EA3ByB;AA4B9BC,EAAAA,GAAG,EAAE,EA5ByB;AA6B9BC,EAAAA,MAAM,EAAE,EA7BsB;AA8B9BC,EAAAA,CAAC,EAAE,EA9B2B;AA+B9BC,EAAAA,EAAE,EAAE,EAAA;AA/B0B,CAAzB,CAAA;AAkCA,SAASC,YAAT,CAAsBC,UAAtB,EAAkCC,SAAlC,EAA6CC,gBAA7C,EAA+D;AACpE,EAAA,IAAI,CAACF,UAAU,CAACvmB,MAAhB,EAAwB;AACtB,IAAA,OAAOumB,UAAP,CAAA;AACD,GAAA;;AAED,EAAA,IAAIE,gBAAgB,IAAI,OAAOA,gBAAP,KAA4B,UAApD,EAAgE;IAC9D,OAAOA,gBAAgB,CAACF,UAAD,CAAvB,CAAA;AACD,GAAA;;AAED,EAAA,MAAMG,SAAS,GAAG,IAAIvnB,MAAM,CAACwnB,SAAX,EAAlB,CAAA;EACA,MAAMC,eAAe,GAAGF,SAAS,CAACG,eAAV,CAA0BN,UAA1B,EAAsC,WAAtC,CAAxB,CAAA;AACA,EAAA,MAAMzF,QAAQ,GAAG,EAAGzS,CAAAA,MAAH,CAAU,GAAGuY,eAAe,CAACjlB,IAAhB,CAAqB+D,gBAArB,CAAsC,GAAtC,CAAb,CAAjB,CAAA;;AAEA,EAAA,KAAK,MAAMrH,OAAX,IAAsByiB,QAAtB,EAAgC;AAC9B,IAAA,MAAMgG,WAAW,GAAGzoB,OAAO,CAAC+lB,QAAR,CAAiBxmB,WAAjB,EAApB,CAAA;;IAEA,IAAI,CAACL,MAAM,CAAC8J,IAAP,CAAYmf,SAAZ,CAAA,CAAuB/nB,QAAvB,CAAgCqoB,WAAhC,CAAL,EAAmD;AACjDzoB,MAAAA,OAAO,CAACiL,MAAR,EAAA,CAAA;AAEA,MAAA,SAAA;AACD,KAAA;;IAED,MAAMyd,aAAa,GAAG,EAAG1Y,CAAAA,MAAH,CAAU,GAAGhQ,OAAO,CAACgM,UAArB,CAAtB,CAAA;AACA,IAAA,MAAM2c,iBAAiB,GAAG,EAAA,CAAG3Y,MAAH,CAAUmY,SAAS,CAAC,GAAD,CAAT,IAAkB,EAA5B,EAAgCA,SAAS,CAACM,WAAD,CAAT,IAA0B,EAA1D,CAA1B,CAAA;;AAEA,IAAA,KAAK,MAAM7C,SAAX,IAAwB8C,aAAxB,EAAuC;AACrC,MAAA,IAAI,CAAC/C,gBAAgB,CAACC,SAAD,EAAY+C,iBAAZ,CAArB,EAAqD;AACnD3oB,QAAAA,OAAO,CAAC8L,eAAR,CAAwB8Z,SAAS,CAACG,QAAlC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;AAED,EAAA,OAAOwC,eAAe,CAACjlB,IAAhB,CAAqBslB,SAA5B,CAAA;AACD;;ACrHD;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMzkB,MAAI,GAAG,iBAAb,CAAA;AAEA,MAAMqI,SAAO,GAAG;AACdqc,EAAAA,UAAU,EAAE,EADE;AAEdC,EAAAA,QAAQ,EAAE,aAFI;AAGdC,EAAAA,OAAO,EAAE,EAHK;AAGD;AACbC,EAAAA,IAAI,EAAE,KAJQ;AAKdC,EAAAA,QAAQ,EAAE,IALI;AAMdC,EAAAA,UAAU,EAAE,IANE;AAOdf,EAAAA,SAAS,EAAE/B,gBAAAA;AAPG,CAAhB,CAAA;AAUA,MAAM3Z,aAAW,GAAG;AAClBoc,EAAAA,UAAU,EAAE,mBADM;AAElBC,EAAAA,QAAQ,EAAE,QAFQ;AAGlBC,EAAAA,OAAO,EAAE,QAHS;AAIlBC,EAAAA,IAAI,EAAE,SAJY;AAKlBC,EAAAA,QAAQ,EAAE,SALQ;AAMlBC,EAAAA,UAAU,EAAE,iBANM;AAOlBf,EAAAA,SAAS,EAAE,QAAA;AAPO,CAApB,CAAA;AAUA,MAAMgB,kBAAkB,GAAG;AACzBlpB,EAAAA,QAAQ,EAAE,kBADe;AAEzBmpB,EAAAA,KAAK,EAAE,gCAAA;AAFkB,CAA3B,CAAA;AAKA;AACA;AACA;;AAEA,MAAMC,eAAN,SAA8B9c,MAA9B,CAAqC;EACnCU,WAAW,CAACL,MAAD,EAAS;AAClB,IAAA,KAAA,EAAA,CAAA;AACA,IAAA,IAAA,CAAKiB,OAAL,GAAe,IAAA,CAAKlB,UAAL,CAAgBC,MAAhB,CAAf,CAAA;AACD,GAJkC;;;AAOjB,EAAA,WAAPJ,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAjBkC;;;AAoBnCmlB,EAAAA,UAAU,GAAG;IACX,OAAOpqB,MAAM,CAACwI,MAAP,CAAc,KAAKmG,OAAL,CAAakb,OAA3B,CAAA,CACJhY,GADI,CACAnE,MAAM,IAAI,IAAA,CAAK2c,wBAAL,CAA8B3c,MAA9B,CADV,CAEJT,CAAAA,MAFI,CAEGxD,OAFH,CAAP,CAAA;AAGD,GAAA;;AAED6gB,EAAAA,UAAU,GAAG;AACX,IAAA,OAAO,IAAKF,CAAAA,UAAL,EAAkB3nB,CAAAA,MAAlB,GAA2B,CAAlC,CAAA;AACD,GAAA;;EAED8nB,aAAa,CAACV,OAAD,EAAU;IACrB,IAAKW,CAAAA,aAAL,CAAmBX,OAAnB,CAAA,CAAA;;IACA,IAAKlb,CAAAA,OAAL,CAAakb,OAAb,GAAuB,EAAE,GAAG,IAAA,CAAKlb,OAAL,CAAakb,OAAlB;MAA2B,GAAGA,OAAAA;KAArD,CAAA;AACA,IAAA,OAAO,IAAP,CAAA;AACD,GAAA;;AAEDY,EAAAA,MAAM,GAAG;AACP,IAAA,MAAMC,eAAe,GAAG/pB,QAAQ,CAAC6hB,aAAT,CAAuB,KAAvB,CAAxB,CAAA;IACAkI,eAAe,CAAChB,SAAhB,GAA4B,IAAKiB,CAAAA,cAAL,CAAoB,IAAKhc,CAAAA,OAAL,CAAaib,QAAjC,CAA5B,CAAA;;AAEA,IAAA,KAAK,MAAM,CAAC7oB,QAAD,EAAW6pB,IAAX,CAAX,IAA+B5qB,MAAM,CAAC6qB,OAAP,CAAe,IAAKlc,CAAAA,OAAL,CAAakb,OAA5B,CAA/B,EAAqE;AACnE,MAAA,IAAA,CAAKiB,WAAL,CAAiBJ,eAAjB,EAAkCE,IAAlC,EAAwC7pB,QAAxC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,MAAM6oB,QAAQ,GAAGc,eAAe,CAACzZ,QAAhB,CAAyB,CAAzB,CAAjB,CAAA;;IACA,MAAM0Y,UAAU,GAAG,IAAKU,CAAAA,wBAAL,CAA8B,IAAK1b,CAAAA,OAAL,CAAagb,UAA3C,CAAnB,CAAA;;AAEA,IAAA,IAAIA,UAAJ,EAAgB;MACdC,QAAQ,CAACvmB,SAAT,CAAmBuQ,GAAnB,CAAuB,GAAG+V,UAAU,CAACvoB,KAAX,CAAiB,GAAjB,CAA1B,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,OAAOwoB,QAAP,CAAA;AACD,GApDkC;;;EAuDnC/b,gBAAgB,CAACH,MAAD,EAAS;IACvB,KAAMG,CAAAA,gBAAN,CAAuBH,MAAvB,CAAA,CAAA;;AACA,IAAA,IAAA,CAAK8c,aAAL,CAAmB9c,MAAM,CAACmc,OAA1B,CAAA,CAAA;AACD,GAAA;;EAEDW,aAAa,CAACO,GAAD,EAAM;AACjB,IAAA,KAAK,MAAM,CAAChqB,QAAD,EAAW8oB,OAAX,CAAX,IAAkC7pB,MAAM,CAAC6qB,OAAP,CAAeE,GAAf,CAAlC,EAAuD;AACrD,MAAA,KAAA,CAAMld,gBAAN,CAAuB;QAAE9M,QAAF;AAAYmpB,QAAAA,KAAK,EAAEL,OAAAA;AAAnB,OAAvB,EAAqDI,kBAArD,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDa,EAAAA,WAAW,CAAClB,QAAD,EAAWC,OAAX,EAAoB9oB,QAApB,EAA8B;IACvC,MAAMiqB,eAAe,GAAGna,cAAc,CAACG,OAAf,CAAuBjQ,QAAvB,EAAiC6oB,QAAjC,CAAxB,CAAA;;IAEA,IAAI,CAACoB,eAAL,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAEDnB,IAAAA,OAAO,GAAG,IAAA,CAAKQ,wBAAL,CAA8BR,OAA9B,CAAV,CAAA;;IAEA,IAAI,CAACA,OAAL,EAAc;AACZmB,MAAAA,eAAe,CAACjf,MAAhB,EAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI1J,SAAS,CAACwnB,OAAD,CAAb,EAAwB;AACtB,MAAA,IAAA,CAAKoB,qBAAL,CAA2BzoB,UAAU,CAACqnB,OAAD,CAArC,EAAgDmB,eAAhD,CAAA,CAAA;;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKrc,CAAAA,OAAL,CAAamb,IAAjB,EAAuB;AACrBkB,MAAAA,eAAe,CAACtB,SAAhB,GAA4B,KAAKiB,cAAL,CAAoBd,OAApB,CAA5B,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAEDmB,eAAe,CAACE,WAAhB,GAA8BrB,OAA9B,CAAA;AACD,GAAA;;EAEDc,cAAc,CAACI,GAAD,EAAM;IAClB,OAAO,IAAA,CAAKpc,OAAL,CAAaob,QAAb,GAAwBhB,YAAY,CAACgC,GAAD,EAAM,IAAA,CAAKpc,OAAL,CAAasa,SAAnB,EAA8B,IAAKta,CAAAA,OAAL,CAAaqb,UAA3C,CAApC,GAA6Fe,GAApG,CAAA;AACD,GAAA;;EAEDV,wBAAwB,CAACU,GAAD,EAAM;IAC5B,OAAO,OAAOA,GAAP,KAAe,UAAf,GAA4BA,GAAG,CAAC,IAAD,CAA/B,GAAwCA,GAA/C,CAAA;AACD,GAAA;;AAEDE,EAAAA,qBAAqB,CAACnqB,OAAD,EAAUkqB,eAAV,EAA2B;AAC9C,IAAA,IAAI,IAAKrc,CAAAA,OAAL,CAAamb,IAAjB,EAAuB;MACrBkB,eAAe,CAACtB,SAAhB,GAA4B,EAA5B,CAAA;MACAsB,eAAe,CAACvI,MAAhB,CAAuB3hB,OAAvB,CAAA,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;AAEDkqB,IAAAA,eAAe,CAACE,WAAhB,GAA8BpqB,OAAO,CAACoqB,WAAtC,CAAA;AACD,GAAA;;AA7GkC;;AC/CrC;AACA;AACA;AACA;AACA;AACA;AAUA;AACA;AACA;;AAEA,MAAMjmB,MAAI,GAAG,SAAb,CAAA;AACA,MAAMkmB,qBAAqB,GAAG,IAAI9jB,GAAJ,CAAQ,CAAC,UAAD,EAAa,WAAb,EAA0B,YAA1B,CAAR,CAA9B,CAAA;AAEA,MAAMyI,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMsb,gBAAgB,GAAG,OAAzB,CAAA;AACA,MAAMrb,iBAAe,GAAG,MAAxB,CAAA;AAEA,MAAMsb,sBAAsB,GAAG,gBAA/B,CAAA;AACA,MAAMC,cAAc,GAAI,CAAGF,CAAAA,EAAAA,gBAAiB,CAA5C,CAAA,CAAA;AAEA,MAAMG,gBAAgB,GAAG,eAAzB,CAAA;AAEA,MAAMC,aAAa,GAAG,OAAtB,CAAA;AACA,MAAMC,aAAa,GAAG,OAAtB,CAAA;AACA,MAAMC,aAAa,GAAG,OAAtB,CAAA;AACA,MAAMC,cAAc,GAAG,QAAvB,CAAA;AAEA,MAAMpS,YAAU,GAAG,MAAnB,CAAA;AACA,MAAMC,cAAY,GAAG,QAArB,CAAA;AACA,MAAMH,YAAU,GAAG,MAAnB,CAAA;AACA,MAAMC,aAAW,GAAG,OAApB,CAAA;AACA,MAAMsS,cAAc,GAAG,UAAvB,CAAA;AACA,MAAMC,aAAW,GAAG,OAApB,CAAA;AACA,MAAMnJ,eAAa,GAAG,SAAtB,CAAA;AACA,MAAMoJ,gBAAc,GAAG,UAAvB,CAAA;AACA,MAAMpX,gBAAgB,GAAG,YAAzB,CAAA;AACA,MAAMC,gBAAgB,GAAG,YAAzB,CAAA;AAEA,MAAMoX,aAAa,GAAG;AACpBC,EAAAA,IAAI,EAAE,MADc;AAEpBC,EAAAA,GAAG,EAAE,KAFe;AAGpBC,EAAAA,KAAK,EAAEvnB,KAAK,EAAK,GAAA,MAAL,GAAc,OAHN;AAIpBwnB,EAAAA,MAAM,EAAE,QAJY;AAKpBC,EAAAA,IAAI,EAAEznB,KAAK,EAAK,GAAA,OAAL,GAAe,MAAA;AALN,CAAtB,CAAA;AAQA,MAAM2I,SAAO,GAAG;AACd+e,EAAAA,SAAS,EAAE,IADG;AAEdzC,EAAAA,QAAQ,EAAE,sCACE,GAAA,mCADF,GAEE,mCAFF,GAGA,QALI;AAMdtf,EAAAA,OAAO,EAAE,aANK;AAOdgiB,EAAAA,KAAK,EAAE,EAPO;AAQdC,EAAAA,KAAK,EAAE,CARO;AASdzC,EAAAA,IAAI,EAAE,KATQ;AAUd/oB,EAAAA,QAAQ,EAAE,KAVI;AAWdoe,EAAAA,SAAS,EAAE,KAXG;AAYd7B,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAZM;AAadkP,EAAAA,SAAS,EAAE,KAbG;EAcdC,kBAAkB,EAAE,CAAC,KAAD,EAAQ,OAAR,EAAiB,QAAjB,EAA2B,MAA3B,CAdN;AAedlP,EAAAA,QAAQ,EAAE,iBAfI;AAgBdmP,EAAAA,WAAW,EAAE,EAhBC;AAiBd3C,EAAAA,QAAQ,EAAE,IAjBI;AAkBdC,EAAAA,UAAU,EAAE,IAlBE;AAmBdf,EAAAA,SAAS,EAAE/B,gBAnBG;AAoBdxJ,EAAAA,YAAY,EAAE,IAAA;AApBA,CAAhB,CAAA;AAuBA,MAAMnQ,aAAW,GAAG;AAClB8e,EAAAA,SAAS,EAAE,SADO;AAElBzC,EAAAA,QAAQ,EAAE,QAFQ;AAGlB0C,EAAAA,KAAK,EAAE,2BAHW;AAIlBhiB,EAAAA,OAAO,EAAE,QAJS;AAKlBiiB,EAAAA,KAAK,EAAE,iBALW;AAMlBzC,EAAAA,IAAI,EAAE,SANY;AAOlB/oB,EAAAA,QAAQ,EAAE,kBAPQ;AAQlBoe,EAAAA,SAAS,EAAE,mBARO;AASlB7B,EAAAA,MAAM,EAAE,yBATU;AAUlBkP,EAAAA,SAAS,EAAE,0BAVO;AAWlBC,EAAAA,kBAAkB,EAAE,OAXF;AAYlBlP,EAAAA,QAAQ,EAAE,kBAZQ;AAalBmP,EAAAA,WAAW,EAAE,mBAbK;AAclB3C,EAAAA,QAAQ,EAAE,SAdQ;AAelBC,EAAAA,UAAU,EAAE,iBAfM;AAgBlBf,EAAAA,SAAS,EAAE,QAhBO;AAiBlBvL,EAAAA,YAAY,EAAE,wBAAA;AAjBI,CAApB,CAAA;AAoBA;AACA;AACA;;AAEA,MAAMiP,OAAN,SAAsBle,aAAtB,CAAoC;AAClCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;AAC3B,IAAA,IAAI,OAAO+Q,MAAP,KAAkB,WAAtB,EAAmC;AACjC,MAAA,MAAM,IAAInQ,SAAJ,CAAc,8DAAd,CAAN,CAAA;AACD,KAAA;;AAED,IAAA,KAAA,CAAMxN,OAAN,EAAe4M,MAAf,CAAA,CAL2B;;IAQ3B,IAAKkf,CAAAA,UAAL,GAAkB,IAAlB,CAAA;IACA,IAAKC,CAAAA,QAAL,GAAgB,CAAhB,CAAA;IACA,IAAKC,CAAAA,UAAL,GAAkB,KAAlB,CAAA;IACA,IAAKC,CAAAA,cAAL,GAAsB,EAAtB,CAAA;IACA,IAAKlP,CAAAA,OAAL,GAAe,IAAf,CAAA;AACA,IAAA,IAAA,CAAKmP,gBAAL,GAAwB,IAAxB,CAb2B;;IAgB3B,IAAKC,CAAAA,GAAL,GAAW,IAAX,CAAA;;AAEA,IAAA,IAAA,CAAKC,aAAL,EAAA,CAAA;AACD,GApBiC;;;AAuBhB,EAAA,WAAP5f,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAjCiC;;;AAoClCkoB,EAAAA,MAAM,GAAG;IACP,IAAKP,CAAAA,UAAL,GAAkB,IAAlB,CAAA;AACD,GAAA;;AAEDQ,EAAAA,OAAO,GAAG;IACR,IAAKR,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACD,GAAA;;AAEDS,EAAAA,aAAa,GAAG;AACd,IAAA,IAAA,CAAKT,UAAL,GAAkB,CAAC,IAAA,CAAKA,UAAxB,CAAA;AACD,GAAA;;EAEDjc,MAAM,CAACjJ,KAAD,EAAQ;IACZ,IAAI,CAAC,IAAKklB,CAAAA,UAAV,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAIllB,KAAJ,EAAW;AACT,MAAA,MAAMgY,OAAO,GAAG,IAAA,CAAK4N,4BAAL,CAAkC5lB,KAAlC,CAAhB,CAAA;;MAEAgY,OAAO,CAACqN,cAAR,CAAuBQ,KAAvB,GAA+B,CAAC7N,OAAO,CAACqN,cAAR,CAAuBQ,KAAvD,CAAA;;AAEA,MAAA,IAAI7N,OAAO,CAAC8N,oBAAR,EAAJ,EAAoC;AAClC9N,QAAAA,OAAO,CAAC+N,MAAR,EAAA,CAAA;AACD,OAFD,MAEO;AACL/N,QAAAA,OAAO,CAACgO,MAAR,EAAA,CAAA;AACD,OAAA;;AAED,MAAA,OAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK/S,QAAL,EAAJ,EAAqB;AACnB,MAAA,IAAA,CAAK+S,MAAL,EAAA,CAAA;;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKD,MAAL,EAAA,CAAA;AACD,GAAA;;AAED3e,EAAAA,OAAO,GAAG;IACRgJ,YAAY,CAAC,IAAK+U,CAAAA,QAAN,CAAZ,CAAA;AAEAhlB,IAAAA,YAAY,CAACC,GAAb,CAAiB,IAAA,CAAK4G,QAAL,CAAc3L,OAAd,CAAsBuoB,cAAtB,CAAjB,EAAwDC,gBAAxD,EAA0E,KAAKoC,iBAA/E,CAAA,CAAA;;IAEA,IAAI,IAAA,CAAKV,GAAT,EAAc;MACZ,IAAKA,CAAAA,GAAL,CAASlhB,MAAT,EAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK6hB,cAAL,EAAA,CAAA;;AACA,IAAA,KAAA,CAAM9e,OAAN,EAAA,CAAA;AACD,GAAA;;AAED+L,EAAAA,IAAI,GAAG;IACL,IAAI,IAAA,CAAKnM,QAAL,CAAc0M,KAAd,CAAoBqC,OAApB,KAAgC,MAApC,EAA4C;AAC1C,MAAA,MAAM,IAAIjQ,KAAJ,CAAU,qCAAV,CAAN,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,EAAE,IAAKqgB,CAAAA,cAAL,MAAyB,IAAKjB,CAAAA,UAAhC,CAAJ,EAAiD;AAC/C,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM1O,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC,IAAA,CAAKX,WAAL,CAAiBuB,SAAjB,CAA2B+J,YAA3B,CAApC,CAAlB,CAAA;AACA,IAAA,MAAMyU,UAAU,GAAGrqB,cAAc,CAAC,IAAA,CAAKiL,QAAN,CAAjC,CAAA;;AACA,IAAA,MAAMqf,UAAU,GAAG,CAACD,UAAU,IAAI,KAAKpf,QAAL,CAAcsf,aAAd,CAA4BtqB,eAA3C,EAA4DJ,QAA5D,CAAqE,IAAA,CAAKoL,QAA1E,CAAnB,CAAA;;AAEA,IAAA,IAAIwP,SAAS,CAACvT,gBAAV,IAA8B,CAACojB,UAAnC,EAA+C;AAC7C,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMd,GAAG,GAAG,IAAKgB,CAAAA,cAAL,EAAZ,CAAA;;IAEA,IAAKvf,CAAAA,QAAL,CAAchC,YAAd,CAA2B,kBAA3B,EAA+CugB,GAAG,CAACjsB,YAAJ,CAAiB,IAAjB,CAA/C,CAAA,CAAA;;IAEA,MAAM;AAAEwrB,MAAAA,SAAAA;AAAF,KAAA,GAAgB,KAAK7d,OAA3B,CAAA;;AAEA,IAAA,IAAI,CAAC,IAAA,CAAKD,QAAL,CAAcsf,aAAd,CAA4BtqB,eAA5B,CAA4CJ,QAA5C,CAAqD,IAAK2pB,CAAAA,GAA1D,CAAL,EAAqE;MACnET,SAAS,CAAC/J,MAAV,CAAiBwK,GAAjB,CAAA,CAAA;AACAplB,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAA,CAAKoE,QAA1B,EAAoC,IAAKX,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2Bsc,cAA3B,CAApC,CAAA,CAAA;AACD,KAAA;;IAED,IAAI,IAAA,CAAK/N,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaU,MAAb,EAAA,CAAA;AACD,KAFD,MAEO;MACL,IAAKJ,CAAAA,aAAL,CAAmB8O,GAAnB,CAAA,CAAA;AACD,KAAA;;AAEDA,IAAAA,GAAG,CAAC5pB,SAAJ,CAAcuQ,GAAd,CAAkB7D,iBAAlB,EAlCK;AAqCL;AACA;AACA;;AACA,IAAA,IAAI,cAAkBpP,IAAAA,QAAQ,CAAC+C,eAA/B,EAAgD;AAC9C,MAAA,KAAK,MAAM5C,OAAX,IAAsB,EAAA,CAAGgQ,MAAH,CAAU,GAAGnQ,QAAQ,CAACyD,IAAT,CAAc6M,QAA3B,CAAtB,EAA4D;AAC1DpJ,QAAAA,YAAY,CAACkC,EAAb,CAAgBjJ,OAAhB,EAAyB,WAAzB,EAAsCiD,IAAtC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;IAED,MAAMsX,QAAQ,GAAG,MAAM;MACrB,MAAM6S,kBAAkB,GAAG,IAAA,CAAKpB,UAAhC,CAAA;MAEA,IAAKA,CAAAA,UAAL,GAAkB,KAAlB,CAAA;AACAjlB,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAA,CAAKoE,QAA1B,EAAoC,IAAKX,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2BgK,aAA3B,CAApC,CAAA,CAAA;;AAEA,MAAA,IAAI4U,kBAAJ,EAAwB;AACtB,QAAA,IAAA,CAAKR,MAAL,EAAA,CAAA;AACD,OAAA;KARH,CAAA;;IAWA,IAAKxe,CAAAA,cAAL,CAAoBmM,QAApB,EAA8B,KAAK4R,GAAnC,EAAwC,IAAKjU,CAAAA,WAAL,EAAxC,CAAA,CAAA;AACD,GAAA;;AAED4B,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,CAAC,IAAA,CAAKD,QAAL,EAAL,EAAsB;AACpB,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM6D,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC,IAAA,CAAKX,WAAL,CAAiBuB,SAAjB,CAA2BiK,YAA3B,CAApC,CAAlB,CAAA;;IACA,IAAIiF,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMsiB,GAAG,GAAG,IAAKgB,CAAAA,cAAL,EAAZ,CAAA;;AACAhB,IAAAA,GAAG,CAAC5pB,SAAJ,CAAc0I,MAAd,CAAqBgE,iBAArB,EAXK;AAcL;;AACA,IAAA,IAAI,cAAkBpP,IAAAA,QAAQ,CAAC+C,eAA/B,EAAgD;AAC9C,MAAA,KAAK,MAAM5C,OAAX,IAAsB,EAAA,CAAGgQ,MAAH,CAAU,GAAGnQ,QAAQ,CAACyD,IAAT,CAAc6M,QAA3B,CAAtB,EAA4D;AAC1DpJ,QAAAA,YAAY,CAACC,GAAb,CAAiBhH,OAAjB,EAA0B,WAA1B,EAAuCiD,IAAvC,CAAA,CAAA;AACD,OAAA;AACF,KAAA;;AAED,IAAA,IAAA,CAAKgpB,cAAL,CAAoBrB,aAApB,CAAA,GAAqC,KAArC,CAAA;AACA,IAAA,IAAA,CAAKqB,cAAL,CAAoBtB,aAApB,CAAA,GAAqC,KAArC,CAAA;AACA,IAAA,IAAA,CAAKsB,cAAL,CAAoBvB,aAApB,CAAA,GAAqC,KAArC,CAAA;IACA,IAAKsB,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;IAEA,MAAMzR,QAAQ,GAAG,MAAM;MACrB,IAAI,IAAA,CAAKmS,oBAAL,EAAJ,EAAiC;AAC/B,QAAA,OAAA;AACD,OAAA;;MAED,IAAI,CAAC,IAAKV,CAAAA,UAAV,EAAsB;AACpBG,QAAAA,GAAG,CAAClhB,MAAJ,EAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAA,CAAK2C,QAAL,CAAc9B,eAAd,CAA8B,kBAA9B,CAAA,CAAA;;AACA/E,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAA,CAAKoE,QAA1B,EAAoC,IAAKX,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2BkK,cAA3B,CAApC,CAAA,CAAA;;AAEA,MAAA,IAAA,CAAKoU,cAAL,EAAA,CAAA;KAZF,CAAA;;IAeA,IAAK1e,CAAAA,cAAL,CAAoBmM,QAApB,EAA8B,KAAK4R,GAAnC,EAAwC,IAAKjU,CAAAA,WAAL,EAAxC,CAAA,CAAA;AACD,GAAA;;AAEDuF,EAAAA,MAAM,GAAG;IACP,IAAI,IAAA,CAAKV,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaU,MAAb,EAAA,CAAA;AACD,KAAA;AACF,GApMiC;;;AAuMlCsP,EAAAA,cAAc,GAAG;AACf,IAAA,OAAOpkB,OAAO,CAAC,IAAK0kB,CAAAA,SAAL,EAAD,CAAd,CAAA;AACD,GAAA;;AAEDF,EAAAA,cAAc,GAAG;IACf,IAAI,CAAC,IAAKhB,CAAAA,GAAV,EAAe;MACb,IAAKA,CAAAA,GAAL,GAAW,IAAKmB,CAAAA,iBAAL,CAAuB,IAAKC,CAAAA,sBAAL,EAAvB,CAAX,CAAA;AACD,KAAA;;AAED,IAAA,OAAO,KAAKpB,GAAZ,CAAA;AACD,GAAA;;EAEDmB,iBAAiB,CAACvE,OAAD,EAAU;IACzB,MAAMoD,GAAG,GAAG,IAAA,CAAKqB,mBAAL,CAAyBzE,OAAzB,CAAkCY,CAAAA,MAAlC,EAAZ,CADyB;;;IAIzB,IAAI,CAACwC,GAAL,EAAU;AACR,MAAA,OAAO,IAAP,CAAA;AACD,KAAA;;IAEDA,GAAG,CAAC5pB,SAAJ,CAAc0I,MAAd,CAAqB+D,iBAArB,EAAsCC,iBAAtC,CAAA,CARyB;;IAUzBkd,GAAG,CAAC5pB,SAAJ,CAAcuQ,GAAd,CAAmB,MAAK,IAAK7F,CAAAA,WAAL,CAAiB9I,IAAK,CAA9C,KAAA,CAAA,CAAA,CAAA;IAEA,MAAMspB,KAAK,GAAGjuB,MAAM,CAAC,IAAA,CAAKyN,WAAL,CAAiB9I,IAAlB,CAAN,CAA8B/E,QAA9B,EAAd,CAAA;AAEA+sB,IAAAA,GAAG,CAACvgB,YAAJ,CAAiB,IAAjB,EAAuB6hB,KAAvB,CAAA,CAAA;;IAEA,IAAI,IAAA,CAAKvV,WAAL,EAAJ,EAAwB;AACtBiU,MAAAA,GAAG,CAAC5pB,SAAJ,CAAcuQ,GAAd,CAAkB9D,iBAAlB,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,OAAOmd,GAAP,CAAA;AACD,GAAA;;EAEDuB,UAAU,CAAC3E,OAAD,EAAU;IAClB,IAAI4E,OAAO,GAAG,KAAd,CAAA;;IACA,IAAI,IAAA,CAAKxB,GAAT,EAAc;MACZwB,OAAO,GAAG,IAAK9T,CAAAA,QAAL,EAAV,CAAA;MACA,IAAKsS,CAAAA,GAAL,CAASlhB,MAAT,EAAA,CAAA;MACA,IAAKkhB,CAAAA,GAAL,GAAW,IAAX,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKW,cAAL,EAAA,CAAA;;AACA,IAAA,IAAA,CAAKX,GAAL,GAAW,IAAA,CAAKmB,iBAAL,CAAuBvE,OAAvB,CAAX,CAAA;;AAEA,IAAA,IAAI4E,OAAJ,EAAa;AACX,MAAA,IAAA,CAAK5T,IAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDyT,mBAAmB,CAACzE,OAAD,EAAU;IAC3B,IAAI,IAAA,CAAKmD,gBAAT,EAA2B;AACzB,MAAA,IAAA,CAAKA,gBAAL,CAAsBzC,aAAtB,CAAoCV,OAApC,CAAA,CAAA;AACD,KAFD,MAEO;MACL,IAAKmD,CAAAA,gBAAL,GAAwB,IAAI7C,eAAJ,CAAoB,EAC1C,GAAG,KAAKxb,OADkC;AAE1C;AACA;QACAkb,OAJ0C;AAK1CF,QAAAA,UAAU,EAAE,IAAKU,CAAAA,wBAAL,CAA8B,IAAK1b,CAAAA,OAAL,CAAa+d,WAA3C,CAAA;AAL8B,OAApB,CAAxB,CAAA;AAOD,KAAA;;AAED,IAAA,OAAO,KAAKM,gBAAZ,CAAA;AACD,GAAA;;AAEDqB,EAAAA,sBAAsB,GAAG;IACvB,OAAO;MACL,CAAChD,sBAAD,GAA0B,IAAA,CAAK8C,SAAL,EAAA;KAD5B,CAAA;AAGD,GAAA;;AAEDA,EAAAA,SAAS,GAAG;IACV,OAAO,IAAA,CAAKxf,OAAL,CAAa2d,KAApB,CAAA;AACD,GAlRiC;;;EAqRlCgB,4BAA4B,CAAC5lB,KAAD,EAAQ;AAClC,IAAA,OAAO,IAAKqG,CAAAA,WAAL,CAAiBsB,mBAAjB,CAAqC3H,KAAK,CAACC,cAA3C,EAA2D,IAAA,CAAK+mB,kBAAL,EAA3D,CAAP,CAAA;AACD,GAAA;;AAED1V,EAAAA,WAAW,GAAG;AACZ,IAAA,OAAO,KAAKrK,OAAL,CAAa0d,SAAb,IAA2B,KAAKY,GAAL,IAAY,IAAKA,CAAAA,GAAL,CAAS5pB,SAAT,CAAmBC,QAAnB,CAA4BwM,iBAA5B,CAA9C,CAAA;AACD,GAAA;;AAED6K,EAAAA,QAAQ,GAAG;IACT,OAAO,IAAA,CAAKsS,GAAL,IAAY,IAAKA,CAAAA,GAAL,CAAS5pB,SAAT,CAAmBC,QAAnB,CAA4ByM,iBAA5B,CAAnB,CAAA;AACD,GAAA;;EAEDoO,aAAa,CAAC8O,GAAD,EAAM;AACjB,IAAA,MAAM9N,SAAS,GAAG,OAAO,IAAA,CAAKxQ,OAAL,CAAawQ,SAApB,KAAkC,UAAlC,GAChB,IAAKxQ,CAAAA,OAAL,CAAawQ,SAAb,CAAuBhf,IAAvB,CAA4B,IAA5B,EAAkC8sB,GAAlC,EAAuC,IAAA,CAAKve,QAA5C,CADgB,GAEhB,IAAA,CAAKC,OAAL,CAAawQ,SAFf,CAAA;IAGA,MAAMwP,UAAU,GAAG5C,aAAa,CAAC5M,SAAS,CAAC5Q,WAAV,EAAD,CAAhC,CAAA;AACA,IAAA,IAAA,CAAKsP,OAAL,GAAeY,MAAM,CAACG,YAAP,CAAoB,IAAKlQ,CAAAA,QAAzB,EAAmCue,GAAnC,EAAwC,IAAKtO,CAAAA,gBAAL,CAAsBgQ,UAAtB,CAAxC,CAAf,CAAA;AACD,GAAA;;AAED3P,EAAAA,UAAU,GAAG;IACX,MAAM;AAAE1B,MAAAA,MAAAA;AAAF,KAAA,GAAa,KAAK3O,OAAxB,CAAA;;AAEA,IAAA,IAAI,OAAO2O,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,MAAA,OAAOA,MAAM,CAAClc,KAAP,CAAa,GAAb,CAAA,CAAkByQ,GAAlB,CAAsB3F,KAAK,IAAInK,MAAM,CAACsW,QAAP,CAAgBnM,KAAhB,EAAuB,EAAvB,CAA/B,CAAP,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOoR,MAAP,KAAkB,UAAtB,EAAkC;MAChC,OAAO2B,UAAU,IAAI3B,MAAM,CAAC2B,UAAD,EAAa,IAAA,CAAKvQ,QAAlB,CAA3B,CAAA;AACD,KAAA;;AAED,IAAA,OAAO4O,MAAP,CAAA;AACD,GAAA;;EAED+M,wBAAwB,CAACU,GAAD,EAAM;AAC5B,IAAA,OAAO,OAAOA,GAAP,KAAe,UAAf,GAA4BA,GAAG,CAAC5qB,IAAJ,CAAS,IAAA,CAAKuO,QAAd,CAA5B,GAAsDqc,GAA7D,CAAA;AACD,GAAA;;EAEDpM,gBAAgB,CAACgQ,UAAD,EAAa;AAC3B,IAAA,MAAMzP,qBAAqB,GAAG;AAC5BC,MAAAA,SAAS,EAAEwP,UADiB;AAE5BvP,MAAAA,SAAS,EAAE,CACT;AACEpa,QAAAA,IAAI,EAAE,MADR;AAEEqa,QAAAA,OAAO,EAAE;UACPoN,kBAAkB,EAAE,IAAK9d,CAAAA,OAAL,CAAa8d,kBAAAA;AAD1B,SAAA;AAFX,OADS,EAOT;AACEznB,QAAAA,IAAI,EAAE,QADR;AAEEqa,QAAAA,OAAO,EAAE;UACP/B,MAAM,EAAE,KAAK0B,UAAL,EAAA;AADD,SAAA;AAFX,OAPS,EAaT;AACEha,QAAAA,IAAI,EAAE,iBADR;AAEEqa,QAAAA,OAAO,EAAE;UACP9B,QAAQ,EAAE,IAAK5O,CAAAA,OAAL,CAAa4O,QAAAA;AADhB,SAAA;AAFX,OAbS,EAmBT;AACEvY,QAAAA,IAAI,EAAE,OADR;AAEEqa,QAAAA,OAAO,EAAE;AACPve,UAAAA,OAAO,EAAG,CAAA,CAAA,EAAG,IAAKiN,CAAAA,WAAL,CAAiB9I,IAAK,CAAA,MAAA,CAAA;AAD5B,SAAA;AAFX,OAnBS,EAyBT;AACED,QAAAA,IAAI,EAAE,iBADR;AAEEsa,QAAAA,OAAO,EAAE,IAFX;AAGEsP,QAAAA,KAAK,EAAE,YAHT;QAIEzpB,EAAE,EAAEkL,IAAI,IAAI;AACV;AACA;UACA,IAAK4d,CAAAA,cAAL,EAAsBvhB,CAAAA,YAAtB,CAAmC,uBAAnC,EAA4D2D,IAAI,CAACwe,KAAL,CAAW1P,SAAvE,CAAA,CAAA;AACD,SAAA;OAjCM,CAAA;KAFb,CAAA;IAwCA,OAAO,EACL,GAAGD,qBADE;AAEL,MAAA,IAAI,OAAO,IAAKvQ,CAAAA,OAAL,CAAa+O,YAApB,KAAqC,UAArC,GAAkD,IAAA,CAAK/O,OAAL,CAAa+O,YAAb,CAA0BwB,qBAA1B,CAAlD,GAAqG,IAAKvQ,CAAAA,OAAL,CAAa+O,YAAtH,CAAA;KAFF,CAAA;AAID,GAAA;;AAEDwP,EAAAA,aAAa,GAAG;IACd,MAAM4B,QAAQ,GAAG,IAAA,CAAKngB,OAAL,CAAarE,OAAb,CAAqBlJ,KAArB,CAA2B,GAA3B,CAAjB,CAAA;;AAEA,IAAA,KAAK,MAAMkJ,OAAX,IAAsBwkB,QAAtB,EAAgC;MAC9B,IAAIxkB,OAAO,KAAK,OAAhB,EAAyB;QACvBzC,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+B,IAAKX,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2Buc,aAA3B,CAA/B,EAAwE,IAAA,CAAKld,OAAL,CAAa5N,QAArF,EAA+F2G,KAAK,IAAI,IAAKiJ,CAAAA,MAAL,CAAYjJ,KAAZ,CAAxG,CAAA,CAAA;AACD,OAFD,MAEO,IAAI4C,OAAO,KAAKqhB,cAAhB,EAAgC;QACrC,MAAMoD,OAAO,GAAGzkB,OAAO,KAAKkhB,aAAZ,GACd,IAAA,CAAKzd,WAAL,CAAiBuB,SAAjB,CAA2BoF,gBAA3B,CADc,GAEd,IAAK3G,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2BoT,eAA3B,CAFF,CAAA;QAGA,MAAMsM,QAAQ,GAAG1kB,OAAO,KAAKkhB,aAAZ,GACf,IAAA,CAAKzd,WAAL,CAAiBuB,SAAjB,CAA2BqF,gBAA3B,CADe,GAEf,IAAK5G,CAAAA,WAAL,CAAiBuB,SAAjB,CAA2Bwc,gBAA3B,CAFF,CAAA;AAIAjkB,QAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BqgB,OAA/B,EAAwC,IAAA,CAAKpgB,OAAL,CAAa5N,QAArD,EAA+D2G,KAAK,IAAI;AACtE,UAAA,MAAMgY,OAAO,GAAG,IAAA,CAAK4N,4BAAL,CAAkC5lB,KAAlC,CAAhB,CAAA;;AACAgY,UAAAA,OAAO,CAACqN,cAAR,CAAuBrlB,KAAK,CAACK,IAAN,KAAe,SAAf,GAA2B0jB,aAA3B,GAA2CD,aAAlE,IAAmF,IAAnF,CAAA;;AACA9L,UAAAA,OAAO,CAAC+N,MAAR,EAAA,CAAA;SAHF,CAAA,CAAA;AAKA5lB,QAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+BsgB,QAA/B,EAAyC,IAAA,CAAKrgB,OAAL,CAAa5N,QAAtD,EAAgE2G,KAAK,IAAI;AACvE,UAAA,MAAMgY,OAAO,GAAG,IAAA,CAAK4N,4BAAL,CAAkC5lB,KAAlC,CAAhB,CAAA;;UACAgY,OAAO,CAACqN,cAAR,CAAuBrlB,KAAK,CAACK,IAAN,KAAe,UAAf,GAA4B0jB,aAA5B,GAA4CD,aAAnE,CACE9L,GAAAA,OAAO,CAAChR,QAAR,CAAiBpL,QAAjB,CAA0BoE,KAAK,CAAC0B,aAAhC,CADF,CAAA;;AAGAsW,UAAAA,OAAO,CAACgO,MAAR,EAAA,CAAA;SALF,CAAA,CAAA;AAOD,OAAA;AACF,KAAA;;IAED,IAAKC,CAAAA,iBAAL,GAAyB,MAAM;MAC7B,IAAI,IAAA,CAAKjf,QAAT,EAAmB;AACjB,QAAA,IAAA,CAAKkM,IAAL,EAAA,CAAA;AACD,OAAA;KAHH,CAAA;;AAMA/S,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAAL,CAAc3L,OAAd,CAAsBuoB,cAAtB,CAAhB,EAAuDC,gBAAvD,EAAyE,KAAKoC,iBAA9E,CAAA,CAAA;;AAEA,IAAA,IAAI,IAAKhf,CAAAA,OAAL,CAAa5N,QAAjB,EAA2B;AACzB,MAAA,IAAA,CAAK4N,OAAL,GAAe,EACb,GAAG,KAAKA,OADK;AAEbrE,QAAAA,OAAO,EAAE,QAFI;AAGbvJ,QAAAA,QAAQ,EAAE,EAAA;OAHZ,CAAA;AAKD,KAND,MAMO;AACL,MAAA,IAAA,CAAKkuB,SAAL,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDA,EAAAA,SAAS,GAAG;AACV,IAAA,MAAM3C,KAAK,GAAG,IAAK3d,CAAAA,OAAL,CAAaugB,aAA3B,CAAA;;IAEA,IAAI,CAAC5C,KAAL,EAAY;AACV,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,CAAC,IAAA,CAAK5d,QAAL,CAAc1N,YAAd,CAA2B,YAA3B,CAAD,IAA6C,CAAC,IAAA,CAAK0N,QAAL,CAAcwc,WAAhE,EAA6E;AAC3E,MAAA,IAAA,CAAKxc,QAAL,CAAchC,YAAd,CAA2B,YAA3B,EAAyC4f,KAAzC,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK5d,QAAL,CAAc9B,eAAd,CAA8B,OAA9B,CAAA,CAAA;AACD,GAAA;;AAED6gB,EAAAA,MAAM,GAAG;AACP,IAAA,IAAI,IAAK9S,CAAAA,QAAL,EAAmB,IAAA,IAAA,CAAKmS,UAA5B,EAAwC;MACtC,IAAKA,CAAAA,UAAL,GAAkB,IAAlB,CAAA;AACA,MAAA,OAAA;AACD,KAAA;;IAED,IAAKA,CAAAA,UAAL,GAAkB,IAAlB,CAAA;;IAEA,IAAKqC,CAAAA,WAAL,CAAiB,MAAM;MACrB,IAAI,IAAA,CAAKrC,UAAT,EAAqB;AACnB,QAAA,IAAA,CAAKjS,IAAL,EAAA,CAAA;AACD,OAAA;AACF,KAJD,EAIG,IAAKlM,CAAAA,OAAL,CAAa4d,KAAb,CAAmB1R,IAJtB,CAAA,CAAA;AAKD,GAAA;;AAED6S,EAAAA,MAAM,GAAG;IACP,IAAI,IAAA,CAAKF,oBAAL,EAAJ,EAAiC;AAC/B,MAAA,OAAA;AACD,KAAA;;IAED,IAAKV,CAAAA,UAAL,GAAkB,KAAlB,CAAA;;IAEA,IAAKqC,CAAAA,WAAL,CAAiB,MAAM;MACrB,IAAI,CAAC,IAAKrC,CAAAA,UAAV,EAAsB;AACpB,QAAA,IAAA,CAAKlS,IAAL,EAAA,CAAA;AACD,OAAA;AACF,KAJD,EAIG,IAAKjM,CAAAA,OAAL,CAAa4d,KAAb,CAAmB3R,IAJtB,CAAA,CAAA;AAKD,GAAA;;AAEDuU,EAAAA,WAAW,CAACrpB,OAAD,EAAUspB,OAAV,EAAmB;IAC5BtX,YAAY,CAAC,IAAK+U,CAAAA,QAAN,CAAZ,CAAA;AACA,IAAA,IAAA,CAAKA,QAAL,GAAgB5mB,UAAU,CAACH,OAAD,EAAUspB,OAAV,CAA1B,CAAA;AACD,GAAA;;AAED5B,EAAAA,oBAAoB,GAAG;IACrB,OAAOxtB,MAAM,CAACwI,MAAP,CAAc,IAAA,CAAKukB,cAAnB,CAAmC7rB,CAAAA,QAAnC,CAA4C,IAA5C,CAAP,CAAA;AACD,GAAA;;EAEDuM,UAAU,CAACC,MAAD,EAAS;IACjB,MAAM2hB,cAAc,GAAG7iB,WAAW,CAACK,iBAAZ,CAA8B,IAAA,CAAK6B,QAAnC,CAAvB,CAAA;;IAEA,KAAK,MAAM4gB,aAAX,IAA4BtvB,MAAM,CAAC8J,IAAP,CAAYulB,cAAZ,CAA5B,EAAyD;AACvD,MAAA,IAAIlE,qBAAqB,CAACliB,GAAtB,CAA0BqmB,aAA1B,CAAJ,EAA8C;QAC5C,OAAOD,cAAc,CAACC,aAAD,CAArB,CAAA;AACD,OAAA;AACF,KAAA;;IAED5hB,MAAM,GAAG,EACP,GAAG2hB,cADI;MAEP,IAAI,OAAO3hB,MAAP,KAAkB,QAAlB,IAA8BA,MAA9B,GAAuCA,MAAvC,GAAgD,EAApD,CAAA;KAFF,CAAA;AAIAA,IAAAA,MAAM,GAAG,IAAA,CAAKC,eAAL,CAAqBD,MAArB,CAAT,CAAA;AACAA,IAAAA,MAAM,GAAG,IAAA,CAAKE,iBAAL,CAAuBF,MAAvB,CAAT,CAAA;;IACA,IAAKG,CAAAA,gBAAL,CAAsBH,MAAtB,CAAA,CAAA;;AACA,IAAA,OAAOA,MAAP,CAAA;AACD,GAAA;;EAEDE,iBAAiB,CAACF,MAAD,EAAS;AACxBA,IAAAA,MAAM,CAAC8e,SAAP,GAAmB9e,MAAM,CAAC8e,SAAP,KAAqB,KAArB,GAA6B7rB,QAAQ,CAACyD,IAAtC,GAA6C5B,UAAU,CAACkL,MAAM,CAAC8e,SAAR,CAA1E,CAAA;;AAEA,IAAA,IAAI,OAAO9e,MAAM,CAAC6e,KAAd,KAAwB,QAA5B,EAAsC;MACpC7e,MAAM,CAAC6e,KAAP,GAAe;QACb1R,IAAI,EAAEnN,MAAM,CAAC6e,KADA;QAEb3R,IAAI,EAAElN,MAAM,CAAC6e,KAAAA;OAFf,CAAA;AAID,KAAA;;IAED7e,MAAM,CAACwhB,aAAP,GAAuB,IAAKxgB,CAAAA,QAAL,CAAc1N,YAAd,CAA2B,OAA3B,CAAA,IAAuC,EAA9D,CAAA;AACA0M,IAAAA,MAAM,CAAC4e,KAAP,GAAe,IAAA,CAAKjC,wBAAL,CAA8B3c,MAAM,CAAC4e,KAArC,CAAA,IAA+C5e,MAAM,CAACwhB,aAArE,CAAA;;AACA,IAAA,IAAI,OAAOxhB,MAAM,CAAC4e,KAAd,KAAwB,QAA5B,EAAsC;MACpC5e,MAAM,CAAC4e,KAAP,GAAe5e,MAAM,CAAC4e,KAAP,CAAapsB,QAAb,EAAf,CAAA;AACD,KAAA;;AAED,IAAA,IAAI,OAAOwN,MAAM,CAACmc,OAAd,KAA0B,QAA9B,EAAwC;MACtCnc,MAAM,CAACmc,OAAP,GAAiBnc,MAAM,CAACmc,OAAP,CAAe3pB,QAAf,EAAjB,CAAA;AACD,KAAA;;AAED,IAAA,OAAOwN,MAAP,CAAA;AACD,GAAA;;AAEDghB,EAAAA,kBAAkB,GAAG;IACnB,MAAMhhB,MAAM,GAAG,EAAf,CAAA;;AAEA,IAAA,KAAK,MAAMzC,GAAX,IAAkB,IAAA,CAAK0D,OAAvB,EAAgC;AAC9B,MAAA,IAAI,IAAKZ,CAAAA,WAAL,CAAiBT,OAAjB,CAAyBrC,GAAzB,CAAkC,KAAA,IAAA,CAAK0D,OAAL,CAAa1D,GAAb,CAAtC,EAAyD;QACvDyC,MAAM,CAACzC,GAAD,CAAN,GAAc,KAAK0D,OAAL,CAAa1D,GAAb,CAAd,CAAA;AACD,OAAA;AACF,KAPkB;AAUnB;AACA;;;AACA,IAAA,OAAOyC,MAAP,CAAA;AACD,GAAA;;AAEDkgB,EAAAA,cAAc,GAAG;IACf,IAAI,IAAA,CAAK/P,OAAT,EAAkB;MAChB,IAAKA,CAAAA,OAAL,CAAaS,OAAb,EAAA,CAAA;;MACA,IAAKT,CAAAA,OAAL,GAAe,IAAf,CAAA;AACD,KAAA;AACF,GA5gBiC;;;EA+gBZ,OAAfzY,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAGsc,OAAO,CAACtd,mBAAR,CAA4B,IAA5B,EAAkC3B,MAAlC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;MAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AA7hBiC,CAAA;AAgiBpC;AACA;AACA;;;AAEA7I,kBAAkB,CAAC8nB,OAAD,CAAlB;;AC1oBA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;;AAEA,MAAM1nB,MAAI,GAAG,SAAb,CAAA;AAEA,MAAMsqB,cAAc,GAAG,iBAAvB,CAAA;AACA,MAAMC,gBAAgB,GAAG,eAAzB,CAAA;AAEA,MAAMliB,SAAO,GAAG,EACd,GAAGqf,OAAO,CAACrf,OADG;AAEd6R,EAAAA,SAAS,EAAE,OAFG;AAGd7B,EAAAA,MAAM,EAAE,CAAC,CAAD,EAAI,CAAJ,CAHM;AAIdhT,EAAAA,OAAO,EAAE,OAJK;AAKduf,EAAAA,OAAO,EAAE,EALK;AAMdD,EAAAA,QAAQ,EAAE,sCACE,GAAA,mCADF,GAEE,kCAFF,GAGE,kCAHF,GAIA,QAAA;AAVI,CAAhB,CAAA;AAaA,MAAMrc,aAAW,GAAG,EAClB,GAAGof,OAAO,CAACpf,WADO;AAElBsc,EAAAA,OAAO,EAAE,gCAAA;AAFS,CAApB,CAAA;AAKA;AACA;AACA;;AAEA,MAAM4F,OAAN,SAAsB9C,OAAtB,CAA8B;AAC5B;AACkB,EAAA,WAAPrf,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GAZ2B;;;AAe5B4oB,EAAAA,cAAc,GAAG;AACf,IAAA,OAAO,IAAKM,CAAAA,SAAL,EAAoB,IAAA,IAAA,CAAKuB,WAAL,EAA3B,CAAA;AACD,GAjB2B;;;AAoB5BrB,EAAAA,sBAAsB,GAAG;IACvB,OAAO;AACL,MAAA,CAACkB,cAAD,GAAkB,IAAKpB,CAAAA,SAAL,EADb;MAEL,CAACqB,gBAAD,GAAoB,IAAA,CAAKE,WAAL,EAAA;KAFtB,CAAA;AAID,GAAA;;AAEDA,EAAAA,WAAW,GAAG;AACZ,IAAA,OAAO,KAAKrF,wBAAL,CAA8B,KAAK1b,OAAL,CAAakb,OAA3C,CAAP,CAAA;AACD,GA7B2B;;;EAgCN,OAAfzkB,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAGof,OAAO,CAACpgB,mBAAR,CAA4B,IAA5B,EAAkC3B,MAAlC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;MAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AA9C2B,CAAA;AAiD9B;AACA;AACA;;;AAEA7I,kBAAkB,CAAC4qB,OAAD,CAAlB;;AC9FA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMxqB,MAAI,GAAG,WAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,cAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AACA,MAAMyB,YAAY,GAAG,WAArB,CAAA;AAEA,MAAMqf,cAAc,GAAI,CAAU5gB,QAAAA,EAAAA,WAAU,CAA5C,CAAA,CAAA;AACA,MAAM8c,WAAW,GAAI,CAAO9c,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAM8F,qBAAmB,GAAI,CAAA,IAAA,EAAM9F,WAAU,CAAA,EAAEuB,YAAa,CAA5D,CAAA,CAAA;AAEA,MAAMsf,wBAAwB,GAAG,eAAjC,CAAA;AACA,MAAMrf,mBAAiB,GAAG,QAA1B,CAAA;AAEA,MAAMsf,iBAAiB,GAAG,wBAA1B,CAAA;AACA,MAAMC,qBAAqB,GAAG,QAA9B,CAAA;AACA,MAAMC,uBAAuB,GAAG,mBAAhC,CAAA;AACA,MAAMC,kBAAkB,GAAG,WAA3B,CAAA;AACA,MAAMC,kBAAkB,GAAG,WAA3B,CAAA;AACA,MAAMC,mBAAmB,GAAG,kBAA5B,CAAA;AACA,MAAMC,mBAAmB,GAAI,CAAA,EAAEH,kBAAmB,CAAA,EAAA,EAAIC,kBAAmB,CAAKD,GAAAA,EAAAA,kBAAmB,CAAIE,EAAAA,EAAAA,mBAAoB,CAAzH,CAAA,CAAA;AACA,MAAME,iBAAiB,GAAG,WAA1B,CAAA;AACA,MAAMC,0BAAwB,GAAG,kBAAjC,CAAA;AAEA,MAAM/iB,SAAO,GAAG;AACdgQ,EAAAA,MAAM,EAAE,IADM;AACA;AACdgT,EAAAA,UAAU,EAAE,cAFE;AAGdC,EAAAA,YAAY,EAAE,KAHA;AAIdxqB,EAAAA,MAAM,EAAE,IAAA;AAJM,CAAhB,CAAA;AAOA,MAAMwH,aAAW,GAAG;AAClB+P,EAAAA,MAAM,EAAE,eADU;AACO;AACzBgT,EAAAA,UAAU,EAAE,QAFM;AAGlBC,EAAAA,YAAY,EAAE,SAHI;AAIlBxqB,EAAAA,MAAM,EAAE,SAAA;AAJU,CAApB,CAAA;AAOA;AACA;AACA;;AAEA,MAAMyqB,SAAN,SAAwB/hB,aAAxB,CAAsC;AACpCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;AAC3B,IAAA,KAAA,CAAM5M,OAAN,EAAe4M,MAAf,CAAA,CAD2B;;AAI3B,IAAA,IAAA,CAAK+iB,YAAL,GAAoB,IAAInlB,GAAJ,EAApB,CAAA;AACA,IAAA,IAAA,CAAKolB,mBAAL,GAA2B,IAAIplB,GAAJ,EAA3B,CAAA;AACA,IAAA,IAAA,CAAKqlB,YAAL,GAAoB9uB,gBAAgB,CAAC,KAAK6M,QAAN,CAAhB,CAAgC8W,SAAhC,KAA8C,SAA9C,GAA0D,IAA1D,GAAiE,KAAK9W,QAA1F,CAAA;IACA,IAAKkiB,CAAAA,aAAL,GAAqB,IAArB,CAAA;IACA,IAAKC,CAAAA,SAAL,GAAiB,IAAjB,CAAA;AACA,IAAA,IAAA,CAAKC,mBAAL,GAA2B;AACzBC,MAAAA,eAAe,EAAE,CADQ;AAEzBC,MAAAA,eAAe,EAAE,CAAA;KAFnB,CAAA;IAIA,IAAKC,CAAAA,OAAL,GAb2B;AAc5B,GAfmC;;;AAkBlB,EAAA,WAAP3jB,OAAO,GAAG;AACnB,IAAA,OAAOA,SAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,aAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GA5BmC;;;AA+BpCgsB,EAAAA,OAAO,GAAG;AACR,IAAA,IAAA,CAAKC,gCAAL,EAAA,CAAA;;AACA,IAAA,IAAA,CAAKC,wBAAL,EAAA,CAAA;;IAEA,IAAI,IAAA,CAAKN,SAAT,EAAoB;MAClB,IAAKA,CAAAA,SAAL,CAAeO,UAAf,EAAA,CAAA;AACD,KAFD,MAEO;AACL,MAAA,IAAA,CAAKP,SAAL,GAAiB,IAAKQ,CAAAA,eAAL,EAAjB,CAAA;AACD,KAAA;;IAED,KAAK,MAAMC,OAAX,IAAsB,IAAA,CAAKZ,mBAAL,CAAyBloB,MAAzB,EAAtB,EAAyD;AACvD,MAAA,IAAA,CAAKqoB,SAAL,CAAeU,OAAf,CAAuBD,OAAvB,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDxiB,EAAAA,OAAO,GAAG;IACR,IAAK+hB,CAAAA,SAAL,CAAeO,UAAf,EAAA,CAAA;;AACA,IAAA,KAAA,CAAMtiB,OAAN,EAAA,CAAA;AACD,GAjDmC;;;EAoDpClB,iBAAiB,CAACF,MAAD,EAAS;AACxB;AACAA,IAAAA,MAAM,CAAC3H,MAAP,GAAgBvD,UAAU,CAACkL,MAAM,CAAC3H,MAAR,CAAV,IAA6BpF,QAAQ,CAACyD,IAAtD,CAAA;AAEA,IAAA,OAAOsJ,MAAP,CAAA;AACD,GAAA;;AAEDyjB,EAAAA,wBAAwB,GAAG;AACzB,IAAA,IAAI,CAAC,IAAA,CAAKxiB,OAAL,CAAa4hB,YAAlB,EAAgC;AAC9B,MAAA,OAAA;AACD,KAHwB;;;IAMzB1oB,YAAY,CAACC,GAAb,CAAiB,IAAA,CAAK6G,OAAL,CAAa5I,MAA9B,EAAsC8lB,WAAtC,CAAA,CAAA;AAEAhkB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK4E,OAAL,CAAa5I,MAA7B,EAAqC8lB,WAArC,EAAkDiE,qBAAlD,EAAyEpoB,KAAK,IAAI;AAChF,MAAA,MAAM8pB,iBAAiB,GAAG,IAAKd,CAAAA,mBAAL,CAAyBvlB,GAAzB,CAA6BzD,KAAK,CAAC3B,MAAN,CAAa0rB,IAA1C,CAA1B,CAAA;;AACA,MAAA,IAAID,iBAAJ,EAAuB;AACrB9pB,QAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACA,QAAA,MAAMvH,IAAI,GAAG,IAAK8sB,CAAAA,YAAL,IAAqB/uB,MAAlC,CAAA;QACA,MAAM8vB,MAAM,GAAGF,iBAAiB,CAACG,SAAlB,GAA8B,IAAA,CAAKjjB,QAAL,CAAcijB,SAA3D,CAAA;;QACA,IAAI9tB,IAAI,CAAC+tB,QAAT,EAAmB;UACjB/tB,IAAI,CAAC+tB,QAAL,CAAc;AAAEC,YAAAA,GAAG,EAAEH,MAAAA;WAArB,CAAA,CAAA;AACA,UAAA,OAAA;AACD,SAPoB;;;QAUrB7tB,IAAI,CAACihB,SAAL,GAAiB4M,MAAjB,CAAA;AACD,OAAA;KAbH,CAAA,CAAA;AAeD,GAAA;;AAEDL,EAAAA,eAAe,GAAG;AAChB,IAAA,MAAMhS,OAAO,GAAG;MACdxb,IAAI,EAAE,KAAK8sB,YADG;AAEdmB,MAAAA,SAAS,EAAE,CAAC,GAAD,EAAM,GAAN,EAAW,CAAX,CAFG;MAGdxB,UAAU,EAAE,KAAKyB,cAAL,EAAA;KAHd,CAAA;AAMA,IAAA,OAAO,IAAIC,oBAAJ,CAAyBnH,OAAO,IAAI,IAAA,CAAKoH,iBAAL,CAAuBpH,OAAvB,CAApC,EAAqExL,OAArE,CAAP,CAAA;AACD,GA5FmC;;;EA+FpC4S,iBAAiB,CAACpH,OAAD,EAAU;AACzB,IAAA,MAAMqH,aAAa,GAAGhI,KAAK,IAAI,IAAA,CAAKuG,YAAL,CAAkBtlB,GAAlB,CAAuB,CAAA,CAAA,EAAG+e,KAAK,CAACnkB,MAAN,CAAaosB,EAAG,EAA1C,CAA/B,CAAA;;IACA,MAAMhP,QAAQ,GAAG+G,KAAK,IAAI;MACxB,IAAK4G,CAAAA,mBAAL,CAAyBC,eAAzB,GAA2C7G,KAAK,CAACnkB,MAAN,CAAa4rB,SAAxD,CAAA;;AACA,MAAA,IAAA,CAAKS,QAAL,CAAcF,aAAa,CAAChI,KAAD,CAA3B,CAAA,CAAA;KAFF,CAAA;;IAKA,MAAM8G,eAAe,GAAG,CAAC,IAAKL,CAAAA,YAAL,IAAqBhwB,QAAQ,CAAC+C,eAA/B,EAAgDohB,SAAxE,CAAA;AACA,IAAA,MAAMuN,eAAe,GAAGrB,eAAe,IAAI,IAAKF,CAAAA,mBAAL,CAAyBE,eAApE,CAAA;AACA,IAAA,IAAA,CAAKF,mBAAL,CAAyBE,eAAzB,GAA2CA,eAA3C,CAAA;;AAEA,IAAA,KAAK,MAAM9G,KAAX,IAAoBW,OAApB,EAA6B;AAC3B,MAAA,IAAI,CAACX,KAAK,CAACoI,cAAX,EAA2B;QACzB,IAAK1B,CAAAA,aAAL,GAAqB,IAArB,CAAA;;AACA,QAAA,IAAA,CAAK2B,iBAAL,CAAuBL,aAAa,CAAChI,KAAD,CAApC,CAAA,CAAA;;AAEA,QAAA,SAAA;AACD,OAAA;;AAED,MAAA,MAAMsI,wBAAwB,GAAGtI,KAAK,CAACnkB,MAAN,CAAa4rB,SAAb,IAA0B,IAAKb,CAAAA,mBAAL,CAAyBC,eAApF,CAR2B;;MAU3B,IAAIsB,eAAe,IAAIG,wBAAvB,EAAiD;AAC/CrP,QAAAA,QAAQ,CAAC+G,KAAD,CAAR,CAD+C;;QAG/C,IAAI,CAAC8G,eAAL,EAAsB;AACpB,UAAA,OAAA;AACD,SAAA;;AAED,QAAA,SAAA;AACD,OAlB0B;;;AAqB3B,MAAA,IAAI,CAACqB,eAAD,IAAoB,CAACG,wBAAzB,EAAmD;QACjDrP,QAAQ,CAAC+G,KAAD,CAAR,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAnImC;;;AAsIpC6H,EAAAA,cAAc,GAAG;AACf,IAAA,OAAO,IAAKpjB,CAAAA,OAAL,CAAa2O,MAAb,GAAuB,CAAE,EAAA,IAAA,CAAK3O,OAAL,CAAa2O,MAAO,CAA7C,WAAA,CAAA,GAA4D,IAAK3O,CAAAA,OAAL,CAAa2hB,UAAhF,CAAA;AACD,GAAA;;AAEDY,EAAAA,gCAAgC,GAAG;AACjC,IAAA,IAAA,CAAKT,YAAL,GAAoB,IAAInlB,GAAJ,EAApB,CAAA;AACA,IAAA,IAAA,CAAKolB,mBAAL,GAA2B,IAAIplB,GAAJ,EAA3B,CAAA;AAEA,IAAA,MAAMmnB,WAAW,GAAG5hB,cAAc,CAACpI,IAAf,CAAoBqnB,qBAApB,EAA2C,IAAKnhB,CAAAA,OAAL,CAAa5I,MAAxD,CAApB,CAAA;;AAEA,IAAA,KAAK,MAAM2sB,MAAX,IAAqBD,WAArB,EAAkC;AAChC;MACA,IAAI,CAACC,MAAM,CAACjB,IAAR,IAAgBvuB,UAAU,CAACwvB,MAAD,CAA9B,EAAwC;AACtC,QAAA,SAAA;AACD,OAAA;;AAED,MAAA,MAAMlB,iBAAiB,GAAG3gB,cAAc,CAACG,OAAf,CAAuB0hB,MAAM,CAACjB,IAA9B,EAAoC,IAAA,CAAK/iB,QAAzC,CAA1B,CANgC;;AAShC,MAAA,IAAIhM,SAAS,CAAC8uB,iBAAD,CAAb,EAAkC;QAChC,IAAKf,CAAAA,YAAL,CAAkBllB,GAAlB,CAAsBmnB,MAAM,CAACjB,IAA7B,EAAmCiB,MAAnC,CAAA,CAAA;;QACA,IAAKhC,CAAAA,mBAAL,CAAyBnlB,GAAzB,CAA6BmnB,MAAM,CAACjB,IAApC,EAA0CD,iBAA1C,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EAEDY,QAAQ,CAACrsB,MAAD,EAAS;AACf,IAAA,IAAI,IAAK6qB,CAAAA,aAAL,KAAuB7qB,MAA3B,EAAmC;AACjC,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKwsB,iBAAL,CAAuB,IAAK5jB,CAAAA,OAAL,CAAa5I,MAApC,CAAA,CAAA;;IACA,IAAK6qB,CAAAA,aAAL,GAAqB7qB,MAArB,CAAA;AACAA,IAAAA,MAAM,CAAC1C,SAAP,CAAiBuQ,GAAjB,CAAqBrD,mBAArB,CAAA,CAAA;;IACA,IAAKoiB,CAAAA,gBAAL,CAAsB5sB,MAAtB,CAAA,CAAA;;AAEA8B,IAAAA,YAAY,CAACyC,OAAb,CAAqB,KAAKoE,QAA1B,EAAoCihB,cAApC,EAAoD;AAAEvmB,MAAAA,aAAa,EAAErD,MAAAA;KAArE,CAAA,CAAA;AACD,GAAA;;EAED4sB,gBAAgB,CAAC5sB,MAAD,EAAS;AACvB;IACA,IAAIA,MAAM,CAAC1C,SAAP,CAAiBC,QAAjB,CAA0BssB,wBAA1B,CAAJ,EAAyD;AACvD/e,MAAAA,cAAc,CAACG,OAAf,CAAuBqf,0BAAvB,EAAiDtqB,MAAM,CAAChD,OAAP,CAAeqtB,iBAAf,CAAjD,CACG/sB,CAAAA,SADH,CACauQ,GADb,CACiBrD,mBADjB,CAAA,CAAA;AAEA,MAAA,OAAA;AACD,KAAA;;IAED,KAAK,MAAMqiB,SAAX,IAAwB/hB,cAAc,CAACO,OAAf,CAAuBrL,MAAvB,EAA+BgqB,uBAA/B,CAAxB,EAAiF;AAC/E;AACA;MACA,KAAK,MAAM8C,IAAX,IAAmBhiB,cAAc,CAACS,IAAf,CAAoBshB,SAApB,EAA+BzC,mBAA/B,CAAnB,EAAwE;AACtE0C,QAAAA,IAAI,CAACxvB,SAAL,CAAeuQ,GAAf,CAAmBrD,mBAAnB,CAAA,CAAA;AACD,OAAA;AACF,KAAA;AACF,GAAA;;EAEDgiB,iBAAiB,CAACtY,MAAD,EAAS;AACxBA,IAAAA,MAAM,CAAC5W,SAAP,CAAiB0I,MAAjB,CAAwBwE,mBAAxB,CAAA,CAAA;AAEA,IAAA,MAAMuiB,WAAW,GAAGjiB,cAAc,CAACpI,IAAf,CAAqB,CAAEqnB,EAAAA,qBAAsB,CAAGvf,CAAAA,EAAAA,mBAAkB,CAAlE,CAAA,EAAqE0J,MAArE,CAApB,CAAA;;AACA,IAAA,KAAK,MAAM8Y,IAAX,IAAmBD,WAAnB,EAAgC;AAC9BC,MAAAA,IAAI,CAAC1vB,SAAL,CAAe0I,MAAf,CAAsBwE,mBAAtB,CAAA,CAAA;AACD,KAAA;AACF,GArMmC;;;EAwMd,OAAfnL,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAGmgB,SAAS,CAACnhB,mBAAV,CAA8B,IAA9B,EAAoC3B,MAApC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI2C,IAAI,CAAC3C,MAAD,CAAJ,KAAiB3N,SAAjB,IAA8B2N,MAAM,CAACvM,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuM,MAAM,KAAK,aAAvE,EAAsF;AACpF,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;MAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AAtNmC,CAAA;AAyNtC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwBiT,qBAAxB,EAA6C,MAAM;EACjD,KAAK,MAAMme,GAAX,IAAkBniB,cAAc,CAACpI,IAAf,CAAoBonB,iBAApB,CAAlB,EAA0D;IACxDW,SAAS,CAACnhB,mBAAV,CAA8B2jB,GAA9B,CAAA,CAAA;AACD,GAAA;AACF,CAJD,CAAA,CAAA;AAMA;AACA;AACA;;AAEAnuB,kBAAkB,CAAC2rB,SAAD,CAAlB;;AC/RA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMvrB,MAAI,GAAG,KAAb,CAAA;AACA,MAAM4J,UAAQ,GAAG,QAAjB,CAAA;AACA,MAAME,WAAS,GAAI,CAAGF,CAAAA,EAAAA,UAAS,CAA/B,CAAA,CAAA;AAEA,MAAM0K,YAAU,GAAI,CAAMxK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMyK,cAAY,GAAI,CAAQzK,MAAAA,EAAAA,WAAU,CAAxC,CAAA,CAAA;AACA,MAAMsK,YAAU,GAAI,CAAMtK,IAAAA,EAAAA,WAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,aAAW,GAAI,CAAOvK,KAAAA,EAAAA,WAAU,CAAtC,CAAA,CAAA;AACA,MAAM0B,oBAAoB,GAAI,CAAO1B,KAAAA,EAAAA,WAAU,CAA/C,CAAA,CAAA;AACA,MAAM0F,aAAa,GAAI,CAAS1F,OAAAA,EAAAA,WAAU,CAA1C,CAAA,CAAA;AACA,MAAM8F,mBAAmB,GAAI,CAAM9F,IAAAA,EAAAA,WAAU,CAA7C,CAAA,CAAA;AAEA,MAAMiF,cAAc,GAAG,WAAvB,CAAA;AACA,MAAMC,eAAe,GAAG,YAAxB,CAAA;AACA,MAAM8H,YAAY,GAAG,SAArB,CAAA;AACA,MAAMC,cAAc,GAAG,WAAvB,CAAA;AAEA,MAAMzL,iBAAiB,GAAG,QAA1B,CAAA;AACA,MAAMT,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMC,iBAAe,GAAG,MAAxB,CAAA;AACA,MAAMkjB,cAAc,GAAG,UAAvB,CAAA;AAEA,MAAM5C,wBAAwB,GAAG,kBAAjC,CAAA;AACA,MAAM6C,sBAAsB,GAAG,gBAA/B,CAAA;AACA,MAAMC,sBAAsB,GAAG,gBAA/B,CAAA;AACA,MAAMC,4BAA4B,GAAG,wBAArC,CAAA;AAEA,MAAMC,kBAAkB,GAAG,qCAA3B,CAAA;AACA,MAAMC,cAAc,GAAG,6BAAvB,CAAA;AACA,MAAMC,cAAc,GAAI,CAAWH,SAAAA,EAAAA,4BAA6B,qBAAoBA,4BAA6B,CAAA,cAAA,EAAgBA,4BAA6B,CAA9J,CAAA,CAAA;AACA,MAAM5iB,oBAAoB,GAAG,0EAA7B;;AACA,MAAMgjB,mBAAmB,GAAI,CAAA,EAAED,cAAe,CAAA,EAAA,EAAI/iB,oBAAqB,CAAvE,CAAA,CAAA;AAEA,MAAMijB,2BAA2B,GAAI,CAAGljB,CAAAA,EAAAA,iBAAkB,4BAA2BA,iBAAkB,CAAA,0BAAA,EAA4BA,iBAAkB,CAArJ,uBAAA,CAAA,CAAA;AAEA;AACA;AACA;;AAEA,MAAMmjB,GAAN,SAAkBjlB,aAAlB,CAAgC;EAC9BV,WAAW,CAACjN,OAAD,EAAU;AACnB,IAAA,KAAA,CAAMA,OAAN,CAAA,CAAA;IACA,IAAKgd,CAAAA,OAAL,GAAe,IAAKpP,CAAAA,QAAL,CAAc3L,OAAd,CAAsBswB,kBAAtB,CAAf,CAAA;;IAEA,IAAI,CAAC,IAAKvV,CAAAA,OAAV,EAAmB;AACjB,MAAA,OADiB;AAGjB;AACD,KARkB;;;AAWnB,IAAA,IAAA,CAAK6V,qBAAL,CAA2B,IAAA,CAAK7V,OAAhC,EAAyC,IAAA,CAAK8V,YAAL,EAAzC,CAAA,CAAA;;AAEA/rB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAA,CAAK2E,QAArB,EAA+B+F,aAA/B,EAA8C/M,KAAK,IAAI,IAAA,CAAKgQ,QAAL,CAAchQ,KAAd,CAAvD,CAAA,CAAA;AACD,GAf6B;;;AAkBf,EAAA,WAAJzC,IAAI,GAAG;AAChB,IAAA,OAAOA,MAAP,CAAA;AACD,GApB6B;;;AAuB9B4V,EAAAA,IAAI,GAAG;AAAE;IACP,MAAMgZ,SAAS,GAAG,IAAA,CAAKnlB,QAAvB,CAAA;;AACA,IAAA,IAAI,IAAKolB,CAAAA,aAAL,CAAmBD,SAAnB,CAAJ,EAAmC;AACjC,MAAA,OAAA;AACD,KAJI;;;AAOL,IAAA,MAAME,MAAM,GAAG,IAAKC,CAAAA,cAAL,EAAf,CAAA;;IAEA,MAAMxV,SAAS,GAAGuV,MAAM,GACtBlsB,YAAY,CAACyC,OAAb,CAAqBypB,MAArB,EAA6Bxa,YAA7B,EAAyC;AAAEnQ,MAAAA,aAAa,EAAEyqB,SAAAA;KAA1D,CADsB,GAEtB,IAFF,CAAA;IAIA,MAAM3V,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqBupB,SAArB,EAAgCxa,YAAhC,EAA4C;AAAEjQ,MAAAA,aAAa,EAAE2qB,MAAAA;AAAjB,KAA5C,CAAlB,CAAA;;IAEA,IAAI7V,SAAS,CAACvT,gBAAV,IAA+B6T,SAAS,IAAIA,SAAS,CAAC7T,gBAA1D,EAA6E;AAC3E,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKspB,WAAL,CAAiBF,MAAjB,EAAyBF,SAAzB,CAAA,CAAA;;AACA,IAAA,IAAA,CAAKK,SAAL,CAAeL,SAAf,EAA0BE,MAA1B,CAAA,CAAA;AACD,GA5C6B;;;AA+C9BG,EAAAA,SAAS,CAACpzB,OAAD,EAAUqzB,WAAV,EAAuB;IAC9B,IAAI,CAACrzB,OAAL,EAAc;AACZ,MAAA,OAAA;AACD,KAAA;;AAEDA,IAAAA,OAAO,CAACuC,SAAR,CAAkBuQ,GAAlB,CAAsBrD,iBAAtB,CAAA,CAAA;;AAEA,IAAA,IAAA,CAAK2jB,SAAL,CAAe1yB,sBAAsB,CAACV,OAAD,CAArC,EAP8B;;;IAS9B,MAAMqO,UAAU,GAAGrO,OAAO,CAACuC,SAAR,CAAkBC,QAAlB,CAA2BwM,iBAA3B,CAAnB,CAAA;;IACA,MAAMuL,QAAQ,GAAG,MAAM;AACrB,MAAA,IAAIlM,UAAJ,EAAgB;AAAE;AAChBrO,QAAAA,OAAO,CAACuC,SAAR,CAAkBuQ,GAAlB,CAAsB7D,iBAAtB,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAIjP,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAA,KAAiC,KAArC,EAA4C;AAC1C,QAAA,OAAA;AACD,OAAA;;AAEDF,MAAAA,OAAO,CAACsd,KAAR,EAAA,CAAA;MACAtd,OAAO,CAAC8L,eAAR,CAAwB,UAAxB,CAAA,CAAA;AACA9L,MAAAA,OAAO,CAAC4L,YAAR,CAAqB,eAArB,EAAsC,IAAtC,CAAA,CAAA;;AACA,MAAA,IAAA,CAAK0nB,eAAL,CAAqBtzB,OAArB,EAA8B,IAA9B,CAAA,CAAA;;AACA+G,MAAAA,YAAY,CAACyC,OAAb,CAAqBxJ,OAArB,EAA8BwY,aAA9B,EAA2C;AACzClQ,QAAAA,aAAa,EAAE+qB,WAAAA;OADjB,CAAA,CAAA;KAbF,CAAA;;AAkBA,IAAA,IAAA,CAAKjlB,cAAL,CAAoBmM,QAApB,EAA8Bva,OAA9B,EAAuCqO,UAAvC,CAAA,CAAA;AACD,GAAA;;AAED8kB,EAAAA,WAAW,CAACnzB,OAAD,EAAUqzB,WAAV,EAAuB;IAChC,IAAI,CAACrzB,OAAL,EAAc;AACZ,MAAA,OAAA;AACD,KAAA;;AAEDA,IAAAA,OAAO,CAACuC,SAAR,CAAkB0I,MAAlB,CAAyBwE,iBAAzB,CAAA,CAAA;AACAzP,IAAAA,OAAO,CAAColB,IAAR,EAAA,CAAA;;AAEA,IAAA,IAAA,CAAK+N,WAAL,CAAiBzyB,sBAAsB,CAACV,OAAD,CAAvC,EARgC;;;IAUhC,MAAMqO,UAAU,GAAGrO,OAAO,CAACuC,SAAR,CAAkBC,QAAlB,CAA2BwM,iBAA3B,CAAnB,CAAA;;IACA,MAAMuL,QAAQ,GAAG,MAAM;AACrB,MAAA,IAAIlM,UAAJ,EAAgB;AAAE;AAChBrO,QAAAA,OAAO,CAACuC,SAAR,CAAkB0I,MAAlB,CAAyBgE,iBAAzB,CAAA,CAAA;AACD,OAAA;;AAED,MAAA,IAAIjP,OAAO,CAACE,YAAR,CAAqB,MAArB,CAAA,KAAiC,KAArC,EAA4C;AAC1C,QAAA,OAAA;AACD,OAAA;;AAEDF,MAAAA,OAAO,CAAC4L,YAAR,CAAqB,eAArB,EAAsC,KAAtC,CAAA,CAAA;AACA5L,MAAAA,OAAO,CAAC4L,YAAR,CAAqB,UAArB,EAAiC,IAAjC,CAAA,CAAA;;AACA,MAAA,IAAA,CAAK0nB,eAAL,CAAqBtzB,OAArB,EAA8B,KAA9B,CAAA,CAAA;;AACA+G,MAAAA,YAAY,CAACyC,OAAb,CAAqBxJ,OAArB,EAA8B0Y,cAA9B,EAA4C;AAAEpQ,QAAAA,aAAa,EAAE+qB,WAAAA;OAA7D,CAAA,CAAA;KAZF,CAAA;;AAeA,IAAA,IAAA,CAAKjlB,cAAL,CAAoBmM,QAApB,EAA8Bva,OAA9B,EAAuCqO,UAAvC,CAAA,CAAA;AACD,GAAA;;EAEDuI,QAAQ,CAAChQ,KAAD,EAAQ;AACd,IAAA,IAAI,CAAE,CAACsM,cAAD,EAAiBC,eAAjB,EAAkC8H,YAAlC,EAAgDC,cAAhD,CAAA,CAAgE9a,QAAhE,CAAyEwG,KAAK,CAACuD,GAA/E,CAAN,EAA4F;AAC1F,MAAA,OAAA;AACD,KAAA;;IAEDvD,KAAK,CAACwY,eAAN,EAAA,CALc;;AAMdxY,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACA,IAAA,MAAMkN,MAAM,GAAG,CAACrE,eAAD,EAAkB+H,cAAlB,CAAkC9a,CAAAA,QAAlC,CAA2CwG,KAAK,CAACuD,GAAjD,CAAf,CAAA;IACA,MAAMopB,iBAAiB,GAAGnuB,oBAAoB,CAAC,IAAA,CAAK0tB,YAAL,EAAoB3mB,CAAAA,MAApB,CAA2BnM,OAAO,IAAI,CAACoC,UAAU,CAACpC,OAAD,CAAjD,CAAD,EAA8D4G,KAAK,CAAC3B,MAApE,EAA4EuS,MAA5E,EAAoF,IAApF,CAA9C,CAAA;;AAEA,IAAA,IAAI+b,iBAAJ,EAAuB;AACrBX,MAAAA,GAAG,CAACrkB,mBAAJ,CAAwBglB,iBAAxB,EAA2CxZ,IAA3C,EAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAED+Y,EAAAA,YAAY,GAAG;AAAE;IACf,OAAO/iB,cAAc,CAACpI,IAAf,CAAoB+qB,mBAApB,EAAyC,IAAA,CAAK1V,OAA9C,CAAP,CAAA;AACD,GAAA;;AAEDkW,EAAAA,cAAc,GAAG;AACf,IAAA,OAAO,IAAKJ,CAAAA,YAAL,EAAoBnrB,CAAAA,IAApB,CAAyByI,KAAK,IAAI,IAAA,CAAK4iB,aAAL,CAAmB5iB,KAAnB,CAAlC,KAAgE,IAAvE,CAAA;AACD,GAAA;;AAEDyiB,EAAAA,qBAAqB,CAAC1Z,MAAD,EAAShJ,QAAT,EAAmB;AACtC,IAAA,IAAA,CAAKqjB,wBAAL,CAA8Bra,MAA9B,EAAsC,MAAtC,EAA8C,SAA9C,CAAA,CAAA;;AAEA,IAAA,KAAK,MAAM/I,KAAX,IAAoBD,QAApB,EAA8B;MAC5B,IAAKsjB,CAAAA,4BAAL,CAAkCrjB,KAAlC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAEDqjB,4BAA4B,CAACrjB,KAAD,EAAQ;AAClCA,IAAAA,KAAK,GAAG,IAAA,CAAKsjB,gBAAL,CAAsBtjB,KAAtB,CAAR,CAAA;;AACA,IAAA,MAAMujB,QAAQ,GAAG,IAAA,CAAKX,aAAL,CAAmB5iB,KAAnB,CAAjB,CAAA;;AACA,IAAA,MAAMwjB,SAAS,GAAG,IAAA,CAAKC,gBAAL,CAAsBzjB,KAAtB,CAAlB,CAAA;;AACAA,IAAAA,KAAK,CAACxE,YAAN,CAAmB,eAAnB,EAAoC+nB,QAApC,CAAA,CAAA;;IAEA,IAAIC,SAAS,KAAKxjB,KAAlB,EAAyB;AACvB,MAAA,IAAA,CAAKojB,wBAAL,CAA8BI,SAA9B,EAAyC,MAAzC,EAAiD,cAAjD,CAAA,CAAA;AACD,KAAA;;IAED,IAAI,CAACD,QAAL,EAAe;AACbvjB,MAAAA,KAAK,CAACxE,YAAN,CAAmB,UAAnB,EAA+B,IAA/B,CAAA,CAAA;AACD,KAAA;;IAED,IAAK4nB,CAAAA,wBAAL,CAA8BpjB,KAA9B,EAAqC,MAArC,EAA6C,KAA7C,EAdkC;;;IAiBlC,IAAK0jB,CAAAA,kCAAL,CAAwC1jB,KAAxC,CAAA,CAAA;AACD,GAAA;;EAED0jB,kCAAkC,CAAC1jB,KAAD,EAAQ;AACxC,IAAA,MAAMnL,MAAM,GAAGvE,sBAAsB,CAAC0P,KAAD,CAArC,CAAA;;IAEA,IAAI,CAACnL,MAAL,EAAa;AACX,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKuuB,wBAAL,CAA8BvuB,MAA9B,EAAsC,MAAtC,EAA8C,UAA9C,CAAA,CAAA;;IAEA,IAAImL,KAAK,CAACihB,EAAV,EAAc;MACZ,IAAKmC,CAAAA,wBAAL,CAA8BvuB,MAA9B,EAAsC,iBAAtC,EAA0D,CAAGmL,CAAAA,EAAAA,KAAK,CAACihB,EAAG,CAAtE,CAAA,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;AAEDiC,EAAAA,eAAe,CAACtzB,OAAD,EAAU+zB,IAAV,EAAgB;AAC7B,IAAA,MAAMH,SAAS,GAAG,IAAA,CAAKC,gBAAL,CAAsB7zB,OAAtB,CAAlB,CAAA;;IACA,IAAI,CAAC4zB,SAAS,CAACrxB,SAAV,CAAoBC,QAApB,CAA6B2vB,cAA7B,CAAL,EAAmD;AACjD,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAMtiB,MAAM,GAAG,CAAC5P,QAAD,EAAWghB,SAAX,KAAyB;MACtC,MAAMjhB,OAAO,GAAG+P,cAAc,CAACG,OAAf,CAAuBjQ,QAAvB,EAAiC2zB,SAAjC,CAAhB,CAAA;;AACA,MAAA,IAAI5zB,OAAJ,EAAa;AACXA,QAAAA,OAAO,CAACuC,SAAR,CAAkBsN,MAAlB,CAAyBoR,SAAzB,EAAoC8S,IAApC,CAAA,CAAA;AACD,OAAA;KAJH,CAAA;;AAOAlkB,IAAAA,MAAM,CAAC0f,wBAAD,EAA2B9f,iBAA3B,CAAN,CAAA;AACAI,IAAAA,MAAM,CAACuiB,sBAAD,EAAyBnjB,iBAAzB,CAAN,CAAA;AACAY,IAAAA,MAAM,CAACwiB,sBAAD,EAAyB5iB,iBAAzB,CAAN,CAAA;AACAmkB,IAAAA,SAAS,CAAChoB,YAAV,CAAuB,eAAvB,EAAwCmoB,IAAxC,CAAA,CAAA;AACD,GAAA;;AAEDP,EAAAA,wBAAwB,CAACxzB,OAAD,EAAU4lB,SAAV,EAAqBxa,KAArB,EAA4B;AAClD,IAAA,IAAI,CAACpL,OAAO,CAAC0C,YAAR,CAAqBkjB,SAArB,CAAL,EAAsC;AACpC5lB,MAAAA,OAAO,CAAC4L,YAAR,CAAqBga,SAArB,EAAgCxa,KAAhC,CAAA,CAAA;AACD,KAAA;AACF,GAAA;;EAED4nB,aAAa,CAACxZ,IAAD,EAAO;AAClB,IAAA,OAAOA,IAAI,CAACjX,SAAL,CAAeC,QAAf,CAAwBiN,iBAAxB,CAAP,CAAA;AACD,GAvM6B;;;EA0M9BikB,gBAAgB,CAACla,IAAD,EAAO;AACrB,IAAA,OAAOA,IAAI,CAACnJ,OAAL,CAAaqiB,mBAAb,CAAoClZ,GAAAA,IAApC,GAA2CzJ,cAAc,CAACG,OAAf,CAAuBwiB,mBAAvB,EAA4ClZ,IAA5C,CAAlD,CAAA;AACD,GA5M6B;;;EA+M9Bqa,gBAAgB,CAACra,IAAD,EAAO;AACrB,IAAA,OAAOA,IAAI,CAACvX,OAAL,CAAauwB,cAAb,KAAgChZ,IAAvC,CAAA;AACD,GAjN6B;;;EAoNR,OAAflV,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;AAC3B,MAAA,MAAMC,IAAI,GAAGqjB,GAAG,CAACrkB,mBAAJ,CAAwB,IAAxB,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAO3B,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,OAAA;AACD,OAAA;;AAED,MAAA,IAAI2C,IAAI,CAAC3C,MAAD,CAAJ,KAAiB3N,SAAjB,IAA8B2N,MAAM,CAACvM,UAAP,CAAkB,GAAlB,CAA9B,IAAwDuM,MAAM,KAAK,aAAvE,EAAsF;AACpF,QAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,OAAA;;MAED2C,IAAI,CAAC3C,MAAD,CAAJ,EAAA,CAAA;AACD,KAZM,CAAP,CAAA;AAaD,GAAA;;AAlO6B,CAAA;AAqOhC;AACA;AACA;;;AAEA7F,YAAY,CAACkC,EAAb,CAAgBpJ,QAAhB,EAA0B8P,oBAA1B,EAAgDD,oBAAhD,EAAsE,UAAU9I,KAAV,EAAiB;EACrF,IAAI,CAAC,GAAD,EAAM,MAAN,CAAA,CAAcxG,QAAd,CAAuB,IAAA,CAAKyO,OAA5B,CAAJ,EAA0C;AACxCjI,IAAAA,KAAK,CAAC0D,cAAN,EAAA,CAAA;AACD,GAAA;;AAED,EAAA,IAAIlI,UAAU,CAAC,IAAD,CAAd,EAAsB;AACpB,IAAA,OAAA;AACD,GAAA;;AAEDwwB,EAAAA,GAAG,CAACrkB,mBAAJ,CAAwB,IAAxB,EAA8BwL,IAA9B,EAAA,CAAA;AACD,CAVD,CAAA,CAAA;AAYA;AACA;AACA;;AACAhT,YAAY,CAACkC,EAAb,CAAgBnI,MAAhB,EAAwBiT,mBAAxB,EAA6C,MAAM;EACjD,KAAK,MAAM/T,OAAX,IAAsB+P,cAAc,CAACpI,IAAf,CAAoBgrB,2BAApB,CAAtB,EAAwE;IACtEC,GAAG,CAACrkB,mBAAJ,CAAwBvO,OAAxB,CAAA,CAAA;AACD,GAAA;AACF,CAJD,CAAA,CAAA;AAKA;AACA;AACA;;AAEA+D,kBAAkB,CAAC6uB,GAAD,CAAlB;;ACxTA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;;AAEA,MAAMzuB,IAAI,GAAG,OAAb,CAAA;AACA,MAAM4J,QAAQ,GAAG,UAAjB,CAAA;AACA,MAAME,SAAS,GAAI,CAAGF,CAAAA,EAAAA,QAAS,CAA/B,CAAA,CAAA;AAEA,MAAMimB,eAAe,GAAI,CAAW/lB,SAAAA,EAAAA,SAAU,CAA9C,CAAA,CAAA;AACA,MAAMgmB,cAAc,GAAI,CAAUhmB,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;AACA,MAAM2T,aAAa,GAAI,CAAS3T,OAAAA,EAAAA,SAAU,CAA1C,CAAA,CAAA;AACA,MAAM+c,cAAc,GAAI,CAAU/c,QAAAA,EAAAA,SAAU,CAA5C,CAAA,CAAA;AACA,MAAMwK,UAAU,GAAI,CAAMxK,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;AACA,MAAMyK,YAAY,GAAI,CAAQzK,MAAAA,EAAAA,SAAU,CAAxC,CAAA,CAAA;AACA,MAAMsK,UAAU,GAAI,CAAMtK,IAAAA,EAAAA,SAAU,CAApC,CAAA,CAAA;AACA,MAAMuK,WAAW,GAAI,CAAOvK,KAAAA,EAAAA,SAAU,CAAtC,CAAA,CAAA;AAEA,MAAMe,eAAe,GAAG,MAAxB,CAAA;AACA,MAAMklB,eAAe,GAAG,MAAxB;;AACA,MAAMjlB,eAAe,GAAG,MAAxB,CAAA;AACA,MAAM8V,kBAAkB,GAAG,SAA3B,CAAA;AAEA,MAAMtY,WAAW,GAAG;AAClB8e,EAAAA,SAAS,EAAE,SADO;AAElB4I,EAAAA,QAAQ,EAAE,SAFQ;AAGlB1I,EAAAA,KAAK,EAAE,QAAA;AAHW,CAApB,CAAA;AAMA,MAAMjf,OAAO,GAAG;AACd+e,EAAAA,SAAS,EAAE,IADG;AAEd4I,EAAAA,QAAQ,EAAE,IAFI;AAGd1I,EAAAA,KAAK,EAAE,IAAA;AAHO,CAAhB,CAAA;AAMA;AACA;AACA;;AAEA,MAAM2I,KAAN,SAAoBzmB,aAApB,CAAkC;AAChCV,EAAAA,WAAW,CAACjN,OAAD,EAAU4M,MAAV,EAAkB;IAC3B,KAAM5M,CAAAA,OAAN,EAAe4M,MAAf,CAAA,CAAA;IAEA,IAAKmf,CAAAA,QAAL,GAAgB,IAAhB,CAAA;IACA,IAAKsI,CAAAA,oBAAL,GAA4B,KAA5B,CAAA;IACA,IAAKC,CAAAA,uBAAL,GAA+B,KAA/B,CAAA;;AACA,IAAA,IAAA,CAAKlI,aAAL,EAAA,CAAA;AACD,GAR+B;;;AAWd,EAAA,WAAP5f,OAAO,GAAG;AACnB,IAAA,OAAOA,OAAP,CAAA;AACD,GAAA;;AAEqB,EAAA,WAAXC,WAAW,GAAG;AACvB,IAAA,OAAOA,WAAP,CAAA;AACD,GAAA;;AAEc,EAAA,WAAJtI,IAAI,GAAG;AAChB,IAAA,OAAOA,IAAP,CAAA;AACD,GArB+B;;;AAwBhC4V,EAAAA,IAAI,GAAG;IACL,MAAMqD,SAAS,GAAGrW,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC2K,UAApC,CAAlB,CAAA;;IAEA,IAAI6E,SAAS,CAACvT,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK0qB,aAAL,EAAA,CAAA;;AAEA,IAAA,IAAI,IAAK1mB,CAAAA,OAAL,CAAa0d,SAAjB,EAA4B;AAC1B,MAAA,IAAA,CAAK3d,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B9D,eAA5B,CAAA,CAAA;AACD,KAAA;;IAED,MAAMuL,QAAQ,GAAG,MAAM;AACrB,MAAA,IAAA,CAAK3M,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B8Z,kBAA/B,CAAA,CAAA;;AACAhe,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC4K,WAApC,CAAA,CAAA;;AAEA,MAAA,IAAA,CAAKgc,kBAAL,EAAA,CAAA;KAJF,CAAA;;IAOA,IAAK5mB,CAAAA,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BipB,eAA/B,EApBK;;;IAqBLhxB,MAAM,CAAC,IAAK0K,CAAAA,QAAN,CAAN,CAAA;;IACA,IAAKA,CAAAA,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4B7D,eAA5B,EAA6C8V,kBAA7C,CAAA,CAAA;;IAEA,IAAK3W,CAAAA,cAAL,CAAoBmM,QAApB,EAA8B,IAAA,CAAK3M,QAAnC,EAA6C,IAAA,CAAKC,OAAL,CAAa0d,SAA1D,CAAA,CAAA;AACD,GAAA;;AAEDzR,EAAAA,IAAI,GAAG;AACL,IAAA,IAAI,CAAC,IAAA,CAAK6T,OAAL,EAAL,EAAqB;AACnB,MAAA,OAAA;AACD,KAAA;;IAED,MAAMjQ,SAAS,GAAG3W,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC6K,UAApC,CAAlB,CAAA;;IAEA,IAAIiF,SAAS,CAAC7T,gBAAd,EAAgC;AAC9B,MAAA,OAAA;AACD,KAAA;;IAED,MAAM0Q,QAAQ,GAAG,MAAM;MACrB,IAAK3M,CAAAA,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BohB,eAA5B,EADqB;;;MAErB,IAAKtmB,CAAAA,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+B8Z,kBAA/B,EAAmD9V,eAAnD,CAAA,CAAA;;AACAlI,MAAAA,YAAY,CAACyC,OAAb,CAAqB,IAAKoE,CAAAA,QAA1B,EAAoC8K,YAApC,CAAA,CAAA;KAHF,CAAA;;AAMA,IAAA,IAAA,CAAK9K,QAAL,CAAcrL,SAAd,CAAwBuQ,GAAxB,CAA4BiS,kBAA5B,CAAA,CAAA;;IACA,IAAK3W,CAAAA,cAAL,CAAoBmM,QAApB,EAA8B,IAAA,CAAK3M,QAAnC,EAA6C,IAAA,CAAKC,OAAL,CAAa0d,SAA1D,CAAA,CAAA;AACD,GAAA;;AAEDvd,EAAAA,OAAO,GAAG;AACR,IAAA,IAAA,CAAKumB,aAAL,EAAA,CAAA;;IAEA,IAAI,IAAA,CAAK5G,OAAL,EAAJ,EAAoB;AAClB,MAAA,IAAA,CAAK/f,QAAL,CAAcrL,SAAd,CAAwB0I,MAAxB,CAA+BgE,eAA/B,CAAA,CAAA;AACD,KAAA;;AAED,IAAA,KAAA,CAAMjB,OAAN,EAAA,CAAA;AACD,GAAA;;AAED2f,EAAAA,OAAO,GAAG;IACR,OAAO,IAAA,CAAK/f,QAAL,CAAcrL,SAAd,CAAwBC,QAAxB,CAAiCyM,eAAjC,CAAP,CAAA;AACD,GApF+B;;;AAwFhCulB,EAAAA,kBAAkB,GAAG;AACnB,IAAA,IAAI,CAAC,IAAA,CAAK3mB,OAAL,CAAasmB,QAAlB,EAA4B;AAC1B,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAI,IAAKE,CAAAA,oBAAL,IAA6B,IAAA,CAAKC,uBAAtC,EAA+D;AAC7D,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAKvI,QAAL,GAAgB5mB,UAAU,CAAC,MAAM;AAC/B,MAAA,IAAA,CAAK2U,IAAL,EAAA,CAAA;AACD,KAFyB,EAEvB,IAAA,CAAKjM,OAAL,CAAa4d,KAFU,CAA1B,CAAA;AAGD,GAAA;;AAEDgJ,EAAAA,cAAc,CAAC7tB,KAAD,EAAQ8tB,aAAR,EAAuB;IACnC,QAAQ9tB,KAAK,CAACK,IAAd;AACE,MAAA,KAAK,WAAL,CAAA;AACA,MAAA,KAAK,UAAL;QACE,IAAKotB,CAAAA,oBAAL,GAA4BK,aAA5B,CAAA;AACA,QAAA,MAAA;;AACF,MAAA,KAAK,SAAL,CAAA;AACA,MAAA,KAAK,UAAL;QACE,IAAKJ,CAAAA,uBAAL,GAA+BI,aAA/B,CAAA;AACA,QAAA,MAAA;AARJ,KAAA;;AAaA,IAAA,IAAIA,aAAJ,EAAmB;AACjB,MAAA,IAAA,CAAKH,aAAL,EAAA,CAAA;;AACA,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,MAAM9c,WAAW,GAAG7Q,KAAK,CAAC0B,aAA1B,CAAA;;AACA,IAAA,IAAI,IAAKsF,CAAAA,QAAL,KAAkB6J,WAAlB,IAAiC,IAAA,CAAK7J,QAAL,CAAcpL,QAAd,CAAuBiV,WAAvB,CAArC,EAA0E;AACxE,MAAA,OAAA;AACD,KAAA;;AAED,IAAA,IAAA,CAAK+c,kBAAL,EAAA,CAAA;AACD,GAAA;;AAEDpI,EAAAA,aAAa,GAAG;AACdrlB,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+BomB,eAA/B,EAAgDptB,KAAK,IAAI,KAAK6tB,cAAL,CAAoB7tB,KAApB,EAA2B,IAA3B,CAAzD,CAAA,CAAA;AACAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+BqmB,cAA/B,EAA+CrtB,KAAK,IAAI,KAAK6tB,cAAL,CAAoB7tB,KAApB,EAA2B,KAA3B,CAAxD,CAAA,CAAA;AACAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+BgU,aAA/B,EAA8Chb,KAAK,IAAI,KAAK6tB,cAAL,CAAoB7tB,KAApB,EAA2B,IAA3B,CAAvD,CAAA,CAAA;AACAG,IAAAA,YAAY,CAACkC,EAAb,CAAgB,IAAK2E,CAAAA,QAArB,EAA+Bod,cAA/B,EAA+CpkB,KAAK,IAAI,KAAK6tB,cAAL,CAAoB7tB,KAApB,EAA2B,KAA3B,CAAxD,CAAA,CAAA;AACD,GAAA;;AAED2tB,EAAAA,aAAa,GAAG;IACdvd,YAAY,CAAC,IAAK+U,CAAAA,QAAN,CAAZ,CAAA;IACA,IAAKA,CAAAA,QAAL,GAAgB,IAAhB,CAAA;AACD,GA3I+B;;;EA8IV,OAAfznB,eAAe,CAACsI,MAAD,EAAS;IAC7B,OAAO,IAAA,CAAK0C,IAAL,CAAU,YAAY;MAC3B,MAAMC,IAAI,GAAG6kB,KAAK,CAAC7lB,mBAAN,CAA0B,IAA1B,EAAgC3B,MAAhC,CAAb,CAAA;;AAEA,MAAA,IAAI,OAAOA,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,QAAA,IAAI,OAAO2C,IAAI,CAAC3C,MAAD,CAAX,KAAwB,WAA5B,EAAyC;AACvC,UAAA,MAAM,IAAIY,SAAJ,CAAe,CAAmBZ,iBAAAA,EAAAA,MAAO,GAAzC,CAAN,CAAA;AACD,SAAA;;AAED2C,QAAAA,IAAI,CAAC3C,MAAD,CAAJ,CAAa,IAAb,CAAA,CAAA;AACD,OAAA;AACF,KAVM,CAAP,CAAA;AAWD,GAAA;;AA1J+B,CAAA;AA6JlC;AACA;AACA;;;AAEA6B,oBAAoB,CAAC2lB,KAAD,CAApB,CAAA;AAEA;AACA;AACA;;AAEArwB,kBAAkB,CAACqwB,KAAD,CAAlB;;;;"}
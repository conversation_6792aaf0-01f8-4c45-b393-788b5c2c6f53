// JavaScript Document

var ie =navigator.appName=="Microsoft Internet Explorer"?true:false;
function $sb(objID){
return document.getElementById(objID);
}
var controlid = null;
var currdate = null;
var startdate = null;
var enddate  = null;
var yy = null;
var mm = null;
var hh = null;
var ii = null;
var currday = null;
var addtime = false;
var today = new Date(zyear,zmonth,zday);
var lastcheckedyear = false;
var lastcheckedmonth = false;
var nElement;
function _cancelBubble(event) {
e = event ? event : window.event ;
if(ie) {
	e.cancelBubble = true;
} else {
	e.stopPropagation();
}
}
function getposition(obj) {
	var r = new Array();
	r['x'] = obj.offsetLeft;
	r['y'] = obj.offsetTop;
	while(obj = obj.offsetParent) {
	r['x'] += obj.offsetLeft;
	r['y'] += obj.offsetTop;
	}
	return r;
}
function loadcalendar(nxdt) {

s = '';
s += '<div id="calendar" style="display:none; position:absolute; z-index:9;" onclick="_cancelBubble(event)">';
if (ie)
{
s += '<iframe width="200" height="160" src="about:blank" style="position: absolute;z-index:-1;"></iframe>';
}
s += '<div style="width: 200px;"><table class="tableborder" cellspacing="0" cellpadding="0" width="100%" style="text-align: center">';
s += '<tr align="center" class="header"><td class="header"><a href="#" onclick="refreshcalendar(yy, mm-1 ,\''+nxdt+'\');return false" title="上一月"><<</a></td><td colspan="5" style="text-align: center" class="header"><a href="#" onclick="showdiv(\'year\');_cancelBubble(event);return false" title="点击选择年份" id="year"></a>  -  <a id="month" title="点击选择月份" href="#" onclick="showdiv(\'month\');_cancelBubble(event);return false"></a></td><td class="header"><A href="#" onclick="refreshcalendar(yy, mm+1,\''+nxdt+'\');return false" title="下一月">>></A></td></tr>';
s += '<tr class="category"><td>日</td><td>一</td><td>二</td><td>三</td><td>四</td><td>五</td><td>六</td></tr>';
for(var i = 0; i < 6; i++) {
s += '<tr class="altbg2">';
for(var j = 1; j <= 7; j++)
s += "<td id=d" + (i * 7 + j) + " height=\"19\">0</td>";
s += "</tr>";
}
s += '<tr id="hourminute"><td colspan="7" align="center"><input type="text" size="1" value="" id="hour" onKeyUp=\'this.value=this.value > 24 ? 24 : zerofill(this.value); \'> 时 <input type="text" size="1" value="" id="minute" onKeyUp=\'this.value=this.value > 59 ? 59 : zerofill(this.value); \'> 分</td></tr>';
s += '<tr id="tsmsg"><td colspan="7"><span style="color:green; line-height:25px; font-weight:bold;">先填写时间，再选择日期</span></td></tr>';
s += '</table></div></div>';
s += '<div id="calendar_year" onclick="_cancelBubble(event)"><div class="col">';
for(var k = 2000; k <= 2059; k++) {
s += k != 2000 && k % 10 == 0 ? '</div><div class="col">' : '';
s += '<a href="#" onclick="refreshcalendar(' + k + ', mm,\''+nxdt+'\');$sb(\'calendar_year\').style.display=\'none\';return false"><span' + (today.getFullYear() == k ? ' class="today"' : '') + ' id="calendar_year_' + k + '">' + k + '</span></a><br />';
}
s += '</div></div>';
s += '<div id="calendar_month" onclick="_cancelBubble(event)">';
for(var k = 1; k <= 12; k++) {
s += '<a href="#" onclick="refreshcalendar(yy, ' + (k - 1) + ',\''+nxdt+'\');$sb(\'calendar_month\').style.display=\'none\';return false"><span' + (today.getMonth()+1 == k ? ' class="today"' : '') + ' id="calendar_month_' + k + '">' + k + ( k < 10 ? ' ' : '') + ' 月</span></a><br />';
}
s += '</div>';
//var nElement = document.createElement("div");
if(nElement){ 
	nElement.innerHTML=s;
}else{ 
	nElement = document.createElement("div");
	nElement.innerHTML=s;
}
document.getElementsByTagName("body")[0].appendChild(nElement);
//document.write(s);
document.onclick = function(event) {
$sb('calendar').style.display = 'none';
$sb('calendar_year').style.display = 'none';
$sb('calendar_month').style.display = 'none';
}
$sb('calendar').onclick = function(event) {
_cancelBubble(event);
$sb('calendar_year').style.display = 'none';
$sb('calendar_month').style.display = 'none';
}
}

function xz(o){
	if(o.id=="hour"){
		if(o.value<hh){alert("时间不能小于"+hh+":"+ii);o.value=hh;}
		if($sb("minute").value<ii){alert("时间不能小于"+hh+":"+ii);$sb("minute").value=ii;}
	}else{
		if($sb("hour").value==hh){
			if(o.value<ii){alert("时间不能小于"+hh+":"+ii);o.value=ii;}	
		}
	}
}

function parsedate(s) {
/(\d+)\-(\d+)\-(\d+)\s*(\d*):?(\d*)/.exec(s);
var m1 = (RegExp.$1 && RegExp.$1 > 1899 && RegExp.$1 < 2101) ? parseFloat(RegExp.$1) : today.getFullYear();
var m2 = (RegExp.$2 && (RegExp.$2 > 0 && RegExp.$2 < 13)) ? parseFloat(RegExp.$2) : today.getMonth() + 1;
var m3 = (RegExp.$3 && (RegExp.$3 > 0 && RegExp.$3 < 32)) ? parseFloat(RegExp.$3) : today.getDate();
var m4 = (RegExp.$4 && (RegExp.$4 > -1 && RegExp.$4 < 25)) ? parseFloat(RegExp.$4) : 0;
var m5 = (RegExp.$5 && (RegExp.$5 > -1 && RegExp.$5 < 60)) ? parseFloat(RegExp.$5) : 0;
/(\d+)\-(\d+)\-(\d+)\s*(\d*):?(\d*)/.exec("0000-00-00 00\:00");
return new Date(m1, m2 - 1, m3, m4, m5);
}

function settime(d) {
$sb('calendar').style.display = 'none';
//alert(yy + "-" + zerofill(mm + 1) + "-" + zerofill(d)==today);
//alert($sb('hour').value);
//alert(addtime);
if(addtime==1||addtime==2){
	//alert(zyear+"##"+zmonth+"##"+zday);
  if(new Date(yy,zerofill(mm),zerofill(d)).valueOf()==today.valueOf()){
	  if($sb('hour').value<hh){alert("时间不能小于 "+zyear+"-"+eval(zmonth+1)+"-"+zday+" "+hh+":"+ii);$sb('hour').value=hh;}
	  if($sb('hour').value==hh){
		  if($sb('minute').value<ii){alert("时间不能小于 "+zyear+"-"+eval(zmonth+1)+"-"+zday+" "+hh+":"+ii);$sb('minute').value=ii;}	
	  }
  }
  /*
  if(new Date(yy,zerofill(mm),zerofill(d)).valueOf()>today.valueOf()&&controlid.id=="iactivedat"){
	  
	  controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + " 00:00";

	  if($sb("iexprdat")){
		  $sb("iexprdat").value=$sb("iexprdat").value.split(" ")[0]+" 24:00";
	  }
	  
  }else if(new Date($sb("iactivedat").value.split(" ")[0].split("-")[0],$sb("iactivedat").value.split(" ")[0].split("-")[1]-1,$sb("iactivedat").value.split(" ")[0].split("-")[2]).valueOf()>today.valueOf()&&controlid.id=="iexprdat"){
		controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + " 24:00";
		
  }else{*/
	   if($sb("iexprdat")){
		  var dathh=$sb("iexprdat").value.split(" ")[1].split(":")[0];
		  var datii=$sb("iexprdat").value.split(" ")[1].split(":")[1];
	   }
	  controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + (addtime ? ' ' + zerofill($sb('hour').value) + ':' + zerofill($sb('minute').value) : '');
	  if($sb("iexprdat")){
		  if(zerofill($sb('hour').value)=="00" && zerofill($sb('minute').value)=="00"){
			$sb("iexprdat").value=$sb("iexprdat").value.split(" ")[0]+" 24:00";
		  }else{
			$sb("iexprdat").value=$sb("iexprdat").value.split(" ")[0]+" "+zerofill($sb('hour').value) + ':' + zerofill($sb('minute').value);
		  }
	  }
	  if(controlid.id=="iexprdat"){
		  $sb("iexprdat").value=$sb("iexprdat").value.split(" ")[0]+" "+dathh + ':' + datii;
	  }
  
  //}
 
}else if(addtime==3||!addtime){
//	alert($sb('hour').value);
			if(zerofill($sb('hour').value)!= "00" || zerofill($sb('minute').value!="00")){
				controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + (addtime ? ' ' + zerofill($sb('hour').value) + ':' + zerofill($sb('minute').value) : '');
			}else{controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + " " + "24:00";}

	
//	controlid.value = yy + "-" + zerofill(mm + 1) + "-" + zerofill(d) + (addtime ? ' ' + zerofill($sb('hour').value) + ':' + zerofill($sb('minute').value) : '');
	}
  try {  
     if(typeof(eval(billing))=="function")  
        {
            billing();
        }
  }catch(e)
     {
     
      }  

}
function showcalendar(event, controlid1, addtime1, xdt, timeset, startdate1, enddate1) {
try{
	var ft=firsttime.split("-");
	today = new Date(ft[0],eval(ft[1]-1),eval(ft[2]-1));
}catch(e){
}
controlid = controlid1;
addtime = addtime1;
startdate = startdate1 ? parsedate(startdate1) : false;
enddate = enddate1 ? parsedate(enddate1) : false;
currday = controlid.value ? parsedate(controlid.value) : today;
//hh = currday.getHours();
if(timeset){
	hh = timeset.split(":")[0];
	ii = timeset.split(":")[1];
}else{
	//alert(controlid.value);
	hh=controlid.value.split(" ")[1].split(":")[0];
	ii=controlid.value.split(" ")[1].split(":")[1];
}

var p = getposition(controlid);
$sb('calendar').style.display = 'block';
$sb('calendar').style.left = p['x']+'px';
$sb('calendar').style.top	= (p['y'] + 20)+'px';
_cancelBubble(event);
refreshcalendar(currday.getFullYear(), currday.getMonth(), xdt);
if(lastcheckedyear != false) {
$sb('calendar_year_' + lastcheckedyear).className = 'default';
$sb('calendar_year_' + today.getFullYear()).className = 'today';
}
if(lastcheckedmonth != false) {
$sb('calendar_month_' + lastcheckedmonth).className = 'default';
$sb('calendar_month_' + (today.getMonth() + 1)).className = 'today';
}
$sb('calendar_year_' + currday.getFullYear()).className = 'checked';
$sb('calendar_month_' + (currday.getMonth() + 1)).className = 'checked';
$sb('hourminute').style.display = addtime ? '' : 'block';
$sb('tsmsg').style.display = addtime ? '' : 'block';
if(addtime==2||addtime==3){
	$sb('hourminute').style.display="none";
	$sb('tsmsg').style.display="none";
}
lastcheckedyear = currday.getFullYear();
lastcheckedmonth = currday.getMonth() + 1;
}

function refreshcalendar(y, m, xdt) {
var x = new Date(y, m, 1);
var mv = x.getDay();
var d = x.getDate();
var dd = null;

yy = x.getFullYear();
mm = x.getMonth();
$sb("year").innerHTML = yy;
$sb("month").innerHTML = mm + 1 > 9  ? (mm + 1) : '0' + (mm + 1);
for(var i = 1; i <= mv; i++) {
	dd = $sb("d" + i);
	dd.innerHTML = " ";
	dd.className = "";
}
//alert(y);
while(x.getMonth() == mm) {
	dd = $sb("d" + (d + mv));
	dd.innerHTML = '<a href="###" onclick="settime(' + d + ');return false">' + d + '</a>';
//	alert(today);
//	alert(x);
//	alert(x.getTime()==today.getTime());
//	alert(xdt);
	if(xdt){
		//alert("jishi");
		if(x.getTime() < today.getTime() || (enddate && x.getTime() > enddate.getTime()) || (startdate && x.getTime() < startdate.getTime())) {
			dd.className = '';
//			dd.innerHTML = '<a href="###" onclick="return false">' + d + '</a>';
			//alert(dd.innerHTML);
		} else {
			dd.className = 'default';
		}
	}else{
		//alert("bujj");
		if(x.getTime() < today.getTime() || (enddate && x.getTime() > enddate.getTime()) || (startdate && x.getTime() < startdate.getTime())) {
			dd.className = '';
//			dd.innerHTML = '<a href="###" onclick="return false">' + d + '</a>';
			//alert(dd.innerHTML);
		} else {
			dd.className = 'default';
		}
	}
if(x.getFullYear() == today.getFullYear() && x.getMonth() == today.getMonth() && x.getDate() == today.getDate()) {
	dd.className = 'today';
	dd.firstChild.title = '今天';
}
if(x.getDay()==0||x.getDay()==6) dd.className="zmo";
if(x.getFullYear() == currday.getFullYear() && x.getMonth() == currday.getMonth() && x.getDate() == currday.getDate()) {
dd.className = 'checked';
}
x.setDate(++d);
}
while(d + mv <= 42) {
	dd = $sb("d" + (d + mv));
	dd.innerHTML = " ";
	d++;
}
if(addtime) {
	
$sb('hour').value = zerofill(hh);
$sb('minute').value = zerofill(ii);
}
}
function showdiv(id) {
var p = getposition($sb(id));
$sb('calendar_' + id).style.left = p['x']+'px';
$sb('calendar_' + id).style.top = (p['y'] + 16)+'px';
$sb('calendar_' + id).style.display = 'block';
}
function zerofill(s) {
var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
s = isNaN(s) ? 0 : s;
return (s < 10 ? '0' : '') + s.toString();
}
loadcalendar();

//计算投保区间为多少天
function zdays(sdval,edval){
//	var zyear = $!{zyear};
//	var zmonth = $!{zmonth};
//	var zday = $!{zday};
/*	var myDate = new Date(); 
	var currentyear = zyear;
	var currentmonth = zmonth+1;
	currentmonth = currentmonth> 9? currentmonth:"0"+currentmonth;
	var currentday = zday;
	currentday = currentday> 9? currentday:"0"+currentday;
	var currenthour = myDate.getHours();      //获取当前小时数(0-23) 
	currenthour = currenthour> 9? currenthour:"0"+currenthour;
	var currentminute = myDate.getMinutes();    //获取当前分钟数(0-59)
	currentminute = currentminute> 9? currentminute:"0"+currentminute;

	var sds = sdval.split(" ");//选择的年月日+时间
	var sdyear = sdval.split(" ")[0].split("-")[0]; //选择的开始年份
	var sdmonth = sdval.split(" ")[0].split("-")[1]; //选择的开始年份
	var sdday = sdval.split(" ")[0].split("-")[2]; //选择的开始日期
	var sdhour = sdval.split(" ")[1].split(":")[0]; //选择的开始小时
	var sdminute = sdval.split(" ")[1].split(":")[1]; //选择的开始分钟

	var edyear = sdval.split(" ")[0].split("-")[0]; //选择的结束年份
	var edmonth = sdval.split(" ")[0].split("-")[1]; //选择的结束年份
	var edday = sdval.split(" ")[0].split("-")[2]; //选择的结束日期
	var edhour = sdval.split(" ")[1].split(":")[0]; //选择的结束小时
	var edminute = sdval.split(" ")[1].split(":")[1]; //选择的结束分钟

	//$("#iexprdat").val(edyear+"-"+edmonth+"-"+edday+" "+"23:59");

	if ((sdmonth != currentmonth || sdday > currentday) && sds[1]!="00:00" ){
	alert("非零时起保仅限于当天！");
	$("#iactivedat").val(sdyear+"-"+sdmonth+"-"+sdday+" "+"00:00");
	$("#iexprdat").val(sdyear+"-"+sdmonth+"-"+sdday+" "+"24:00");
	}
	if (sdday == currentday && sdhour < currenthour){
		currenthour = currenthour * 1 + 1;
	currenthour = currenthour> 9? currenthour:"0"+currenthour;
	alert("非零时起保时间不能早于"+currentyear+"-"+currentmonth+"-"+currentday+" "+currenthour+":"+currentminute);
	$("#iactivedat").val(currentyear+"-"+currentmonth+"-"+currentday+" "+currenthour+":"+currentminute);
	currentday=currentday * 1 + 1;
	currentday = currentday> 9? currentday:"0"+currentday;
	$("#iexprdat").val(currentyear+"-"+currentmonth+"-"+currentday+" "+currenthour+":"+currentminute);
	}

	sdval = $("#iactivedat").val();
	edval = $("#iexprdat").val();
*/
	var sd=new Date(sdval.split(" ")[0].split("-")[0],sdval.split(" ")[0].split("-")[1]-1,sdval.split(" ")[0].split("-")[2],sdval.split(" ")[1].split(":")[0],sdval.split(" ")[1].split(":")[1]);
	var ed=new Date(edval.split(" ")[0].split("-")[0],edval.split(" ")[0].split("-")[1]-1,edval.split(" ")[0].split("-")[2],edval.split(" ")[1].split(":")[0],edval.split(" ")[1].split(":")[1]);
	var ts=((ed.valueOf()-sd.valueOf())/86400000);
	if (ts < 0){ ts = "";}
	ts=Math.round(ts);
	return ts;
	
}


//checkbox美化start				------------------------------------------------------------------------
$("body").on("change","input:checkbox",function(){//给checkbox制定统一的样式
	$(this).trigger("changeStatus");
})
$("body").on("changeStatus","input:checkbox",function(){//给checkbox制定统一的样式
	if($(this).is(":checked")){
		$(this).closest(".checkBoxMain").addClass("checkOn");
	}else{
		$(this).closest(".checkBoxMain").removeClass("checkOn");
	}
})
$(function(){//加载完成后checkbox进行一轮检测
	$("input:checkbox").each(function(){
		if($(this).is(":checked")){
			$(this).closest(".checkBoxMain").addClass("checkOn");
		}
	})
})
//checkbox美化start				------------------------------------------------------------------------


//select重写start				------------------------------------------------------------------------
var startx = 0, starty =0;
function sMove(e){//判定上下滑动
	if($(e.target).closest(".selectMain").length == 0){
		e.preventDefault();
	}else{
		var touch = event.touches[0];
		if(starty - touch.pageY < 0 && $(".selectMain")[0].scrollTop == 0){
			e.preventDefault();
		}else if(starty - touch.pageY > 0 && $(".selectMain")[0].scrollTop + $(".selectMain").height() == $(".selectUl").height()){
			e.preventDefault();
		}
	}
}

function sStartMove(e){//判定开始滑动
	var touch = event.touches[0];
	starty = touch.pageY;
}

$("body").on("touch",".Select_tr",function(e){//重写下拉列表事件
	$("body *:focus").blur();
	var _this = $(e.target).find("select");
	e.preventDefault();
	e.stopPropagation();
	if(!_this.prop("disabled")){
		_this.prop("disabled",true);
		showSelect(_this);
	}
})
if(navigator.userAgent.toLowerCase().indexOf("android") > 0){
	$("body").on("mousedown",".Select_tr",function(e){
		$("body *:focus").blur();
		e.preventDefault();
		e.stopPropagation();
		$(this).trigger("touch");
	})
}else{
	$("body").on("click",".Select_tr",function(e){
		$("body *:focus").blur();
		e.preventDefault();
		e.stopPropagation();
		$(this).trigger("touch");
	})
}
function userInfoBoxclose() {
    sx.data("target").prop("disabled",false);
    $("body").off("touchmove.selectEvent").off("touchstart.selectEvent");
    sx.hide();
    sx.find(".selectMain").height("auto");
}

var f1="选择信息";
var f2="完成";
// var checkbtn =$("<i class='fa fa-check'></i>");
var sx = $("<div class='selectBox'>" +
    "<div class='policyHead'><div class='userInfoBox'><span class='goback' onclick='userInfoBoxclose()'>" +
	"<i class='fa fa-chevron-left' style='color:#ffb408 '></i></span></div>" +
	"<span class='header-title'>"+f1+"</span><span class='declareClose' id='selectOK'>"+f2+"</span></div>"+
	"<div class='selectMain'><ul class='selectUl'></ul></div></div><div class='selectOpa'></div>");
function showSelect(o){//下拉列表的展开
	sx.appendTo("body");
	showSelect = function(o){
		sx.data("target",o);
		sx.data("nooptions",false);
		var options = "" , idx = 0;
		for(var i = 0 ; i < o.find("option").length ; i++){
			if(!o.find("option").eq(i).attr("ishidden")){
				var seclass = "";
                var display = "";
				if(o.val() == o.find("option").eq(i).val()){
					seclass = "class='selectedOption'";
                    display ="style='display:block'";
					idx = i;
				}
				if (!(o.attr("id")=="plaSelect"&&o.find("option").eq(i).attr("isplanshow")=="0")){
					options += "<li "+seclass+" val='"+o.find("option").eq(i).val()+"'>"+o.find("option").eq(i).html()+"<div class='liSelect' "+display+"><i class='fa fa-check'></i></div></li>";
				}
			}
		}
		if(options == ""){
			options = "<li>没有可选项目</li>";
			sx.data("nooptions",true);
		}
		sx.find(".selectUl").html(options);
        sx.find(".selectUl").height((sx.find(".selectUl li").height()+1) * (o.find("option").length) -1 )
		if($(window).height() * 0.9  < sx.eq(0).height()){
			sx.eq(0).css("top","0");
			sx.find(".selectMain").height($(window).height()*0.9 );
		}else{
			sx.eq(0).css("top",($(window).height() - sx.eq(0).height())/2 + "px");
		}
		sx.show();
		sx.find(".selectMain")[0].scrollTop = idx * 32;
		$("body").on("touchmove.selectEvent",sMove).on("touchstart.selectEvent",sStartMove);
	}
	showSelect(o);
}


sx.on("click","li",function(e){//点击选项，关闭下拉列表
	sx.data("target").prop("disabled",false);
	if(!sx.data("nooptions") && sx.data("target").val() != $(this).attr("val")){
		sx.data("target").val($(this).attr("val"));
		setTimeout(function(){sx.data("target").change();},50);
	}
	$("body").off("touchmove.selectEvent").off("touchstart.selectEvent");
	sx.hide();
	sx.find(".selectMain").height("auto");
});

$("body").on("click",".selectOpa ,#selectOK",function(){//点击其他地方或者确认，关闭下拉列表
	sx.data("target").prop("disabled",false);
	$("body").off("touchmove.selectEvent").off("touchstart.selectEvent");
	sx.hide();
	sx.find(".selectMain").height("auto");
})
//select重写start				------------------------------------------------------------------------

var sendProduct;
function showIntrdct(n){//打开投保声明
	$("html,body").css({"height":"90%","overflow":"hidden"});
	sendProduct = n;
	$("#" + n).show();
	$("#insureDeclare").show();
}
$(".Read").on("click",function(){//关闭投保声明和须知
	// $("html,body").css({"height":"auto","overflow":"auto"});
	document.body.scrollTop =100000;
	setTimeout(function(){
		$(".productIntroduction").hide();
		$(".declareBox").hide();
	},100);
})
function showMailBox(){
	$("#insureDeclare").css({"height":"90%","overflow":"hidden"});
	$("#insureMail").show();
}
$("#send_mail_cancel").on("click",function(){//关闭投保声明和须知
	$("#insureDeclare").css({"height":"auto","overflow":"auto"});
	document.body.scrollTop =100000;
	setTimeout(function(){
		$("#insureMail").hide();
	},100);
})
function emailSend(){
	var proList = {"zyr":"bc-introduction-fw.htm","rzlt":"bc-introduction-asrzlt.htm","wgyz":"bc-introduction-ijs.htm","lyy":"bc-introduction-ojs.htm","ldwyqz":"bc-introduction-Liability.htm"};
	var introduceUrl=proList[sendProduct];
	$("#insureDeclare").css({"height":"auto","overflow":"auto"});
	document.body.scrollTop =100000;
	setTimeout(function(){
		$("#insureMail").hide();
	},100);
	var email = $("#email_text").val();
	if(email!=""){
		$.ajax({
			url:"/productCenter/quote/intrdctMailSend?email="+email+"&introduceUrl="+introduceUrl,
			type:"post",
			succcess:function(data){
				alert("邮件已发送");
				$("#email_text").focus();
			}
		})
	}else{
		alert("请您填写正确的邮箱地址！");
		$("#email_text").focus();
	}
}

//仿苹果日历版本的日历
$(function(){
	/**
	* 相关参数默认值
	*/
	var calendarDefaut = {
		"title":"",//所属标题
		"minDay":0,//区段内最小能选多少天
		"maxDay":0,//区段内最大能选多少天
		"limitedStart":"1900-01-01",//限制的开始日期
		"limitedEnd":"2020-12-31",//限制的结束日期
		"startTime":"",//录入的开始日期
		"endTime":"",//录入的结束日期
		"startSrc":"",//确定后需要填充的起始日期
		"endSrc":"",//确定后需要填充的结束日期
		"type":"start",//所选日期的类型start,end,select
		"callBack":"",//回调函数
	}
	var calendar = {},weekHeight = 0, thisYear, thisMonth, today;
	calendar["firstDayOfWeek"] = 0;//0代表周日,1代表周1,6代表周6
	calendar["weekList"] = ["日","一","二","三","四","五","六"];
	/**
	* 加载初始的界面框架
	*/
	(function(){
		var monthBox = "", weekTitle = "<div class='weekTitle'><table><tr><td>"+calendar["weekList"].join("</td><td>")+"</td></tr></table></div>";
		for(var i = 0; i < 4; i++){
			monthBox += "<div class='showMonth'></div><table>";
			monthBox += "<tr class='weekTitle'><td>" + calendar["weekList"].join("</td><td>") + "</td></tr>";
			for(var j = 0; j < 6; j++){
				monthBox += "<tr>";
				for(var k = 0; k < 7; k++){
					monthBox += "<td></td>";
				}
				monthBox += "</tr>";
			}
			monthBox += "</table>";
		}
		var calendarBox = "<div class='calendarBox calendarBoxHidden'><div class='selectTimeAction'></div><div class='calendarBoxMenuBox'><div class='backBtn'><i class='fa fa-chevron-left' style='color:#FA9128 '></i><!--<span class='showYearMonth'></span>--></div><div class='calendarBoxTitle'>时间与日期</div><div class='confirmBtn'>完成</div></div><div class='beh'></div><div class='showSelectTime clearfix'><div class='fleft'><div class='selectTimeTitle'>生效时间</div><div class='selectTimeStart'></div></div><div class='fright'><div class='selectTimeTitle'>失效时间</div><div class='selectTimeEnd'></div></div></div><div class='showCalendarBody'><div class='calendarBoxScrollMonthBox'><div class='calendarBoxShowMonthBox'>"+monthBox+"</div></div><div class='calendarBoxScrollYearBox'><div id='calendarBoxShowYearBox' class='calendarBoxShowYearBox'></div></div><div class='calendarFilter'></div></div></div>";
		var calendarStyle = "<style type='text/css'>.monthTable.tblMonthMove{transition:all .3s linear;position:absolute;background-color:#fff;z-index:1;}.calendarBoxTitle{ margin: 0 2.6rem;text-align: center;color: #FA9128;}.monthTable.tblMonthResult{font-size:0.2rem;line-height:0.54rem;left:0;top:0;width:100%;}.monthTable.tblMonthMove .monthTitle,.monthTable.tblMonthMove .weekTitle td{transition:all .3s linear;}.monthTable.tblMonthResult .monthTitle{line-height:0.42rem;font-size:0.32rem;padding-left:0.15rem;}.monthTable.tblMonthMove .weekTitle td{font-size:0px;line-height:0px;}.monthTable.tblMonthResult .weekTitle td{font-size:0.16rem;line-height:0.28rem;}.clearfix:after{content:' ';clear:both;}.fleft{float:left;}.fright{float:right}.calendarBoxMenuBox {text-align: center;background-color: rgb(255, 255, 255);height: 3.6rem;line-height: 3.6rem;z-index: 2;position: absolute;top: 0;left: 0;right: 0;border-bottom: 1px solid #ddd;}.calendarBoxMenuBox .backBtn{color: #FA9128;height: 2.3rem;line-height: 3.6rem;float: left;position: relative; width: 2.7em;padding-top: 1.3rem;}.calendarBoxMenuBox span{vertical-align:top;line-height:0.35rem;}.beh{margin-top:4rem}.showSelectTime{}.showSelectTime > div{width:50%;color:#ccc;border-bottom:0.18rem solid transparent;text-align:center;font-size:0.9rem;transition:border-bottom-color,color .3s,.3s;}.showSelectTime > div.selected{color:#666;border-bottom-color:#FA9128;}.showSelectTime .selectTimeTitle{height:1.2rem;line-height:1.22rem;overflow:hidden;}.showSelectTime .selectTimeStart,.showSelectTime .selectTimeEnd{height:0.9rem;line-height:0.9rem;}.calendarBoxMenuBox .confirmBtn{float: right;position: absolute;right: 0.0rem;color: #FA9128;width: 4em;top: 0;font-size: 1rem;}.calendarBox{position:fixed;width:100%;height:100%;top:0;left:0;background-color:#fff;z-index:9999;transform-origin:100% 0;}.calendarBox.calendarBoxHidden{transform:scale(0);transition:transform .3s;}.calendarBoxShowMonthBox{position:relative;}.calendarBoxShowMonthBox .showMonth{font-size:1.8rem;line-height:2rem;font-weight:800;padding-left:1.15rem;}.calendarBoxShowMonthBox td{text-align:center;font-size:1.2rem;line-height:3.54rem;}.calendarBoxShowMonthBox .dayEnd,.calendarBoxShowMonthBox .dayStart,.calendarBoxShowMonthBox .dayPeriod,.monthTable .dayEnd,.monthTable .dayStart,.monthTable .dayPeriod{position:relative;color:#fff;}.calendarBoxShowMonthBox .dayEnd:after,.calendarBoxShowMonthBox .dayStart:after,.calendarBoxShowMonthBox .dayPeriod:after,.monthTable .dayEnd:after,.monthTable .dayStart:after,.monthTable .dayPeriod:after{content: '';position: absolute;width: 80%;height: 85%;background-color: #FF9800;left: 10%;top: 5%;border-radius: 0.03rem;z-index: -1;}.monthTable .dayEnd:after,.monthTable .dayStart:after,.monthTable .dayPeriod:after{border-radius:0.02rem;}.calendarBoxShowMonthBox .dayPeriod:after,.monthTable .dayPeriod:after{background-color:#FF9800;}.showCalendarBody,.calendarBoxScrollYearBox{position:absolute;left:0;top:0;bottom:0;right:0;overflow:scroll;padding:0 ;}.calendarBoxScrollYearBox{left:0;right:0;-webkit-overflow-scrolling:touch;}.showCalendarBody{padding:0;}.showCalendarBody .weekTitle td{text-align:center;color:#333;font-size:1rem;line-height:2.28rem;}.calendarBoxShowYearBox .weekTitle  td{line-height:0;overflow:hidden;font-size:0;}.calendarBoxScrollMonthBox{position:absolute;left:0;top:0;right:0;padding:0;bottom:0;display:none;overflow:hidden;}.calendarBoxShowYearBox{transition:transform .3s;transform: scale(1)}.calendarBoxShowYearBox.isZoom{transform: scale(3)}.calendarBoxShowYearBox .oneMonth{width:33.33%;padding:0 2%;}.yearTitle{font-size:1.35rem;}.calendarBox table{width:100%;border-collapse:collapse;}.calendarBox td{border:0;padding:0;}.calendarBox td.disabled,.monthTable.tblMonthMove .disabled{color:#ccc;}.monthTable{font-size:0.1rem;text-align:center;line-height:1.8em}.monthTable .monthTitle{text-align:left;font-size: 0.2rem;margin-left:0.02rem;line-height:1.6em;font-weight:800;}.calendarFilter{position:absolute;left:0;right:0;bottom:0;height:0;background-color:#fff;}.selectTimeAction{position:absolute;height: 0.18rem;line-height: 0.18rem;font-size:0.14rem;width:0.8rem;display:none;}</style>";
		$("body").append(calendarBox);
		$("body").append(calendarStyle);
	})()
	
	/**
	* 渲染年界面，只添加一年
	*/
	function buildNewYear(year){
		var ls = calendar["limitedStart"] ? formatDate(calendar["limitedStart"].split(" ")[0]).getDate : false;
		var le = calendar["limitedEnd"] ? formatDate(calendar["limitedEnd"].split(" ")[0]).getDate : false;
		var st = calendar["startTime"] ? formatDate(calendar["startTime"].split(" ")[0]).getDate : false;
		var et = calendar["endTime"] && calendar["endSrc"] ? formatDate(calendar["endTime"].split(" ")[0]).getDate : false;
		var sd = formatDate(calendar["selectedDay"]).getDate;
		var yearBox = "<div year='"+year+"' class='yearBox"+(year==thisYear?" thisYear":"")+"'><div class='yearTitle'>"+year+"年</div><div class='yearDetailBox'><table class='yearTable'>";
		for(var i = 0; i < 12; i++){
			var weekStart = (new Date(year,i,1).getDay() - calendar["firstDayOfWeek"] + 7)%7;//日历表第一个TD格从几开始
			var dayNum = new Date(year,i+1,0).getDate();//此月天数
			(i%3 == 0) && (yearBox += "<tr>");
			yearBox += (i+1 == thisMonth ? "<td class='thisMonth oneMonth'>" : "<td class='oneMonth'>");
			yearBox += "<table class='monthTable' month="+(i+1)+"><tr><td colspan='7'><div class='monthTitle'>"+(i+1)+"月</div></td></tr>";
			yearBox += "<tr class='weekTitle'><td>" + calendar["weekList"].join("</td><td>") + "</td></tr>";
			for(var j = 0; j < 6; j++){
				yearBox += "<tr>";
				for(var k = 0; k < 7; k++){
					var disabled = "",dayStart = "", dayEnd = "", today = "", dayPeriod = "";
					var dd = j*7 + k + 1 - weekStart;
					if(dd <= 0 || dd > dayNum){
						dd = "&nbsp;";
						disabled = "disabled "
					}else{
						var f = new Date(year,i,dd);
						if((ls && f-ls < 0) || (le && f-le > 0)){//先清空所有class只留下可选或者不可选的选项
							disabled = "disabled ";
						}
						if(st && f - st == 0){//已选起始时间的开头时间
							dayStart = "dayStart ";
						}
						if(et && f - et == 0){//已选起始时间的结束时间
							dayEnd = "dayEnd ";
						}
						if(st && et && f - st > 0 && f - et < 0){
							dayPeriod = "dayPeriod";
						}
						if(today && f - today == 0){//已选起始时间的结束时间
							today = "today ";
						}
					}
					yearBox += "<td class='"+disabled+dayStart+dayEnd+today+dayPeriod+"'>"+dd+"</td>";
				}
				yearBox += "</tr>";
			}
			yearBox += "</table>";
			i%3 == 2 && (yearBox += "</tr>");
		}
		yearBox += "</table></div>"
		return yearBox;
	}
	/**
	* 渲染月界面
	*/
	function buildMonth(year,month){
		var ls = calendar["limitedStart"] ? formatDate(calendar["limitedStart"].split(" ")[0]).getDate : false;
		var le = calendar["limitedEnd"] ? formatDate(calendar["limitedEnd"].split(" ")[0]).getDate : false;
		var st = calendar["startTime"] ? formatDate(calendar["startTime"].split(" ")[0]).getDate : false;
		var et = calendar["endTime"] && calendar["endSrc"] ? formatDate(calendar["endTime"].split(" ")[0]).getDate : false;
		//结束日期的最大期限由起始日期决定
		if(calendar["maxDay"] && st && $(".showSelectTime .fright").hasClass("selected")){
			le = formatDate(new Date(st.getFullYear(),st.getMonth(),st.getDate()+calendar["maxDay"]*1-1)).getDate;
		}
		if(st && $(".showSelectTime .fright").hasClass("selected")){
			ls = formatDate(st).getDate;
		}
		var sd = formatDate(calendar["selectedDay"]).getDate;
		var calendarBoxShowMonthBox = $(".calendarBoxShowMonthBox");
		var monthInfo = new Date(year,(month - 1),1);
		calendarBoxShowMonthBox.data({"year":monthInfo.getFullYear(),"month":zerofill(monthInfo.getMonth()+1)});
		$(".calendarBoxTitle").text(monthInfo.getFullYear()+"年"+(monthInfo.getMonth()+1)+"月");
		for(var i = 0; i < 4; i++){
			var weekStart = (new Date(year,(month - 2 +  i),1).getDay() - calendar["firstDayOfWeek"] + 7)%7;//日历表第一个TD格从几开始
			var dayNum = new Date(year,month - 1 +  i,0).getDate();//此月天数
			$(".calendarBoxShowMonthBox .showMonth").eq(i).html(new Date(year,(month - 2 +  i),1).getMonth() + 1 + "月");
			for(var j = 7; j < 49; j++){
				var dd = j - 7 + 1 - weekStart;
				var o = calendarBoxShowMonthBox.find("table").eq(i).find("td").eq(j).removeClass();
				if(dd <= 0 || dd > dayNum){
					o.html("&nbsp;");
					o.addClass("noDate");
				}else{
					var f = new Date(year,month - 2 +  i,dd);
					o.html(dd);
					if((ls && f-ls < 0) || (le && f-le > 0)){//先清空所有class只留下可选或者不可选的选项
						o.addClass("disabled");
					}
					if(st && f - st == 0){//已选起始时间的开头时间
						o.addClass("dayStart");
					}
					if(et && f - et == 0){//已选起始时间的结束时间
						o.addClass("dayEnd");
					}
					if(st && et && f - st > 0 && f - et < 0){
						o.addClass("dayPeriod");
					}
					if(today && f - today == 0){//已选起始时间的结束时间
						o.addClass("today");
					}
				}
			}
		}
	}
	/**
	* 格式化日期
	*/
	function formatDate(d){
		var newDate = d;
		if(typeof d == "string"){
			newDate = d.replace(/[,-]/gi,"/");
			if(newDate.split("/").length == 2){
				newDate += "/01";
			}
		}
		newDate =new Date(newDate);
		var returnDate = {};
		returnDate.year = newDate.getFullYear();//年
		returnDate.month = newDate.getMonth() > 8 ? (newDate.getMonth() + 1) : "0" +  (newDate.getMonth() + 1);//月
		returnDate.day = newDate.getDate() > 9 ? newDate.getDate() : "0" +  newDate.getDate();//日
		returnDate.week = newDate.getDay();
		returnDate.getDate = newDate;//带或者不带小时分钟的时间对象
		return returnDate;
	}
	/**
	* 从年界面跳转到月界面的特效
	*/
	var isTouchMove = false;
	$(".calendarBoxShowYearBox").on("touchstart",function(e){
		isTouchMove = false;
	}).on("touchmove",function(e){
		isTouchMove = true;
	}).on("touchend",".monthTable",function(e){
		var _this = this;
		if(!isTouchMove){
			if($(".calendarBoxShowYearBox").hasClass("isZoom")){
				$(".calendarBoxShowYearBox").removeClass("isZoom");
			}else{
				var tbl = $(this).clone();
				tbl.addClass("tblMonthMove").css({"width":$(this).width(),"left":$(this).position().left,"top":$(this).position().top});
				tbl.appendTo(".showCalendarBody");
				setTimeout(function(){tbl.removeAttr("style").addClass("tblMonthResult");},1)
				$(this).css("opacity",0);
				var relativeY = $(".calendarBoxScrollYearBox")[0].scrollTop+ 28 +($(this).position().top + $(this).find(".monthTitle").height()) * 1.5+ "px";
				var relativeX = $(this).closest("td").index() * 50 + "% ";
				$(".calendarBoxShowYearBox").addClass("isZoom").css({"transform-origin":relativeX + relativeY});
                $(".calendarBoxTitle").text($(this).closest(".yearBox").attr("year")+"年"+$(this).closest(".monthTable").attr("month")+"月");
				$(".calendarFilter").animate({"height":$(".showCalendarBody").height() - $(".monthTable td").eq(8).height()*18 - 24},300);
				buildMonth($(this).closest(".yearBox").attr("year"),$(this).closest(".monthTable").attr("month"));
				setTimeout(function(){tbl.removeAttr("style").addClass("tblMonthResult");},1)
				setTimeout(function(){
					$(".calendarBoxScrollMonthBox").show();
					$(".calendarBoxScrollYearBox").hide();
					$(".calendarBoxShowMonthBox").css("top",-$(".calendarBoxShowMonthBox .showMonth").eq(1).position().top);
					tbl.remove();
					$(_this).css("opacity",1)
				},300)
				setTimeout(function(){
					$(".calendarFilter").animate({"height":0},300);
				},600)
			}
		}else{
		}
		isTouchMove = false;
	})
	/*$(".calendarBoxScrollYearBox").on("scroll",function(){
		var le = calendar["limitedEnd"] ? formatDate(calendar["limitedEnd"].split(" ")[0]).getDate : false;
		var year = $(".yearBox:last").attr("year");
		if(!le || le && new Date(year*1+1,0,1) - le < 0){
			if($(".yearBox:last").position().top < $(".calendarBoxScrollYearBox").height() + $(".yearBox:last").height()*2){
				$(".calendarBoxShowYearBox").append(buildNewYear(year*1+1));
			}
		}
	})*/
	/**
	* 从月界面返回到年界面,如果本来在年界面，那么关闭日历
	*/
	$(".calendarBoxMenuBox").on("click",".backBtn",function(){
		if($(".calendarBoxShowYearBox").hasClass("isZoom")){
			var buildYear = "";
			for(var i = calendar["limitedStart"].split("-")[0]; i < calendar["limitedStart"].split("-")[0]*1+3; i++){
				buildYear += buildNewYear(i);
			}
			$(".calendarBoxShowYearBox").html(buildYear);
			var date = $(".calendarBoxShowMonthBox").data(), originTbl = $(".calendarBoxShowYearBox .yearBox[year='"+date["year"]+"'] .monthTable[month='"+date["month"]*1+"']"), tbl = $(originTbl[0].outerHTML).addClass("tblMonthMove tblMonthResult");
			$(".calendarBoxShowYearBox").attr("style","transition:initial").removeClass("isZoom");
			$(".calendarBoxScrollYearBox").css("opacity",0).show().scrollTop($(".calendarBoxShowYearBox .yearBox[year='"+date["year"]+"'] .monthTable[month='"+date["month"]*1+"']").offset().top - $(".calendarBoxShowYearBox").offset().top);
			var relativeY = $(".calendarBoxScrollYearBox")[0].scrollTop+($(originTbl).position().top + $(originTbl).find(".monthTitle").height()) * 1.5 + "px";
			var relativeX = ((date["month"]-1)%3) * 50 + "% ";
			var _width = originTbl.width(), _left = originTbl.position().left, _top = originTbl.position().top;
			$(".calendarBoxShowYearBox").addClass("isZoom");
			$(".calendarBoxScrollMonthBox").hide();
            $(".calendarBoxTitle").text("时间与日期");
			$(".calendarFilter").animate({"height":0},300);
			tbl.appendTo(".showCalendarBody");
			originTbl.css("opacity",0);
			//$(".showCalendarBody .weekTitle").animate({"height":0},300);
			setTimeout(function(){
				tbl.css({"width":_width,"left":_left,"top":_top}).removeClass("tblMonthResult");
				$(".calendarBoxShowYearBox").removeAttr("style");
				$(".calendarBoxShowYearBox").css({"transform-origin":relativeX + relativeY});
				$(".calendarBoxShowYearBox").removeClass("isZoom");
				setTimeout(function(){
					$(".calendarBoxScrollYearBox").css("opacity",1);
					tbl.remove();
					originTbl.css("opacity",1);
				},300)
				
			},1)
		}else{
			$(".calendarBox").addClass("calendarBoxHidden");
			$("body,html").css("overflow","auto");
		}
	})
	/**
	* 点击确定，关闭日历，给赋值
	*/
	$(".calendarBoxMenuBox").on("click",".confirmBtn",function(){
		if(calendar["startSrc"] && !calendar["startTime"]){
			alert("请选择生效时间！");
			return;
		}
		if(calendar["endSrc"] && !calendar["endTime"]){
			alert("请选择失效时间！");
			return;
		}
		if(calendar["maxDay"] && calendar["startSrc"] && calendar["endSrc"] && new Date(calendar["endTime"]) - new Date(calendar["startTime"]) > calendar["maxDay"]*60*60*24*1000){
			alert("已超过最大天数！");
			return;
		}
		if(calendar["startSrc"] && calendar["endSrc"] && new Date(calendar["endTime"]) - new Date(calendar["startTime"]) < 0){
			alert("失效时间不能早于生效时间！");
			return;
		}
		if(calendar["startSrc"]){
			$(calendar["startSrc"]).val1(calendar["startTime"]+" 00:00:00");
		}
		if(calendar["endSrc"]){
			$(calendar["endSrc"]).val1(calendar["endTime"]+" 23:59:59");
		}
		$(calendar["startSrc"]).change();
		var sm = "";
		if(calendar["type"]=="start"){
			sm = calendar["startTime"];
		}else if(calendar["type"]=="end"){
			sm = calendar["endTime"];
		}
		initCalendarBox();
		typeof calendar["callBack"] == "function" && calendar["callBack"](sm);
		$(".calendarBox").addClass("calendarBoxHidden");
		$("body,html").css("overflow","auto");
	})
	/**
	* 月界面上下滑动时换月份特效
	*/
	var monthTouchY = 0, originalTouchY;
	$(".calendarFilter,.calendarBoxScrollMonthBox").on("touchstart",function(e){
		originalTouchY = monthTouchY = e.originalEvent.touches[0].clientY;
		e.preventDefault();
		e.stopPropagation();
	}).on("touchmove",function(e){
		var tempTouchY = e.originalEvent.touches[0].clientY;
		$(".calendarBoxShowMonthBox").css("top",$(".calendarBoxShowMonthBox").position().top + tempTouchY - monthTouchY);
		monthTouchY = tempTouchY;
	}).on("touchend",function(e){
		var ls = calendar["limitedStart"] ? formatDate(calendar["limitedStart"].split(" ")[0]).getDate : false;
		var le = calendar["limitedEnd"] ? formatDate(calendar["limitedEnd"].split(" ")[0]).getDate : false;
		var st = calendar["startTime"] ? formatDate(calendar["startTime"].split(" ")[0]).getDate : false;
		var et = calendar["endTime"] ? formatDate(calendar["endTime"].split(" ")[0]).getDate : false;
		//结束日期的最大期限由起始日期决定
		if(calendar["maxDay"] && st && $(".showSelectTime .fright").hasClass("selected")){
			le = formatDate(new Date(st.getFullYear(),st.getMonth(),st.getDate()+calendar["maxDay"]*1-1)).getDate;
		}
		if(st && $(".showSelectTime .fright").hasClass("selected")){
			ls = formatDate(st).getDate;
		}
		var date = $(".calendarBoxShowMonthBox").data();
		if(e.originalEvent.changedTouches[0].clientY - originalTouchY < -30 && (!le || le && new Date(date["year"],date["month"]*1,1)-le <= 0)){
			$(".calendarBoxShowMonthBox").animate({"top":-$(".calendarBoxShowMonthBox .showMonth").eq(2).position().top},300);
			setTimeout(function(){
				buildMonth(date["year"],date["month"]*1 + 1);
				$(".calendarBoxShowMonthBox").css("top",-$(".calendarBoxShowMonthBox .showMonth").eq(1).position().top);
			},350);
		}else if(e.originalEvent.changedTouches[0].clientY - originalTouchY > 30 && (!ls || ls && new Date(date["year"],date["month"]*1-1,0)-ls >= 0)){
			$(".calendarBoxShowMonthBox").animate({"top":-$(".calendarBoxShowMonthBox .showMonth").eq(0).position().top},300);
			setTimeout(function(){
				buildMonth(date["year"],date["month"]*1 - 1);
				$(".calendarBoxShowMonthBox").css("top",-$(".calendarBoxShowMonthBox .showMonth").eq(1).position().top);
			},350);
		}else{
			$(".calendarBoxShowMonthBox").animate({"top":-$(".calendarBoxShowMonthBox .showMonth").eq(1).position().top},300);
		}
	})
	/**
	* 月界面选择具体的日期
	*/
	$(".calendarBoxShowMonthBox").on("touchend","td",function(e){
		if($(".showSelectTime .selected").length == 0){return;};
		if(Math.abs(e.originalEvent.changedTouches[0].clientY - originalTouchY) < 10){
			var _this = this;
			if(!$(this).hasClass("disabled") && !$(this).hasClass("noDate")){
				var date = $(".calendarBoxShowMonthBox").data();
				if(calendar["startSrc"] && calendar["endSrc"]){
					var position = $(this).position();
					var timePosition = $(".showSelectTime .selected div").eq(1).position();
					var width = $(".showSelectTime .selected div").eq(1).width()/2 - $(".selectTimeAction").width()/2;
					$(".selectTimeAction").html(date["year"]+"-"+date["month"]+"-"+$(this).html()).show().css({"top":position.top+$(".calendarBoxShowMonthBox").position().top+$(".showCalendarBody").position().top,"left":position.left});
					$(".selectTimeAction").animate({"top":timePosition.top,"left":timePosition.left+width},300);
					if($(".showSelectTime .selected").hasClass("fleft")){
						$(".calendarBoxShowMonthBox").find(".dayStart").removeClass("dayStart");
						$(this).addClass("dayStart");
						calendar["startTime"] = date["year"]+"-"+date["month"]+"-"+zerofill($(this).html());
						if(calendar["endTime"] && calendar["startTime"] && formatDate(calendar["endTime"]).getDate - formatDate(calendar["startTime"]).getDate < 0){
							calendar["endTime"] = "";
							$(".showSelectTime .selectTimeEnd").html("");
						}
					}else{
						$(".calendarBoxShowMonthBox").find(".dayEnd").removeClass("dayEnd");
						$(this).addClass("dayEnd");
						calendar["endTime"] = date["year"]+"-"+date["month"]+"-"+zerofill($(this).html());
					}
					buildMonth(date["year"],date["month"])
					setTimeout(function(){
						$(".showSelectTime .selected div").eq(1).html($(".selectTimeAction").html());
						$(".selectTimeAction").hide();
						if($(".showSelectTime .selected").hasClass("fleft")){
							$(".showSelectTime .fright").click();
						}else{
							$(".showSelectTime .selected").removeClass("selected");
						}
					},300)
				}else{
					calendar["startTime"] = date["year"]+"-"+date["month"]+"-"+zerofill($(this).html());
					$(".calendarBoxShowMonthBox").find(".dayStart").removeClass("dayStart");
					$(this).addClass("dayStart");
				}
			}
		}
	})
	/**
	* 选择生效日期或者失效日期
	*/
	$(".showSelectTime").on("click",".fleft,.fright",function(){
		if(!$(this).hasClass("selected") && !$(".calendarBoxScrollMonthBox").is(":hidden")){
			$(".showSelectTime .selected").removeClass("selected");
			$(this).addClass("selected");
			if($(this).hasClass("fleft")){
				if(calendar["startTime"]){
					buildMonth(calendar["startTime"].split("-")[0],calendar["startTime"].split("-")[1]);
				}else if(calendar["endTime"]){
					buildMonth(calendar["endTime"].split("-")[0],calendar["endTime"].split("-")[1]);
				}
			}else{
				if(calendar["endTime"]){
					buildMonth(calendar["endTime"].split("-")[0],calendar["endTime"].split("-")[1]);
				}else if(calendar["startTime"]){
					buildMonth(calendar["startTime"].split("-")[0],calendar["startTime"].split("-")[1]);
				}
			}
		}else if($(".calendarBoxScrollMonthBox").is(":hidden")){
			$(".showSelectTime .selected").removeClass("selected");
			$(this).addClass("selected");
		}
	})
	/**
	* 使日历面板初始化
	*/
	function initCalendarBox(){
		$(".fleft,.fright",".showSelectTime").removeClass("selected");
		$(".calendarBoxScrollMonthBox").hide();
		$(".calendarBoxScrollYearBox").show().find(".calendarBoxShowYearBox").removeClass("isZoom");
        $(".calendarBoxTitle").text("时间与日期");
		$(".calendarFilter").height(0);
		
	}
	
	jQuery.fn.showCalendar = function(param){
		for(var i in calendarDefaut){calendar[i] = calendarDefaut[i];}
		for(var i in param){calendar[i] = param[i];}
		initCalendarBox();
		if(calendar["startSrc"] && calendar["endSrc"]){
			$(".showSelectTime").show();
			$(".showCalendarBody").css("top","7.0rem");
		}else{
			$(".showSelectTime").hide();
			$(".showCalendarBody").css("top","4rem");
			calendar["endTime"] = false;
		}
		$(".selectTimeStart,.selectTimeEnd",".showSelectTime").html("");
		if(calendar["startTime"]){
			calendar["startTime"] = calendar["startTime"].split(" ")[0];
			$(".showSelectTime .selectTimeStart").html(calendar["startTime"]);
		}
		if(calendar["endTime"]){
			calendar["endTime"] = calendar["endTime"].split(" ")[0];
			$(".showSelectTime .selectTimeEnd").html(calendar["endTime"]);
		}
		if(calendar["type"] == "start"){
			$(".showSelectTime").find(".fleft").addClass("selected");
		}else if(calendar["type"] == "end"){
			$(".showSelectTime").find(".fright").addClass("selected");
		}
		/*limitedEnd = calendar["limitedStart"].split("-")[0]*1+3;
		if(calendar["limitedEnd"]){
			var limitedEnd = new Date(calendar["limitedEnd"].replace(/-/g,"/")) - new Date(calendar["limitedStart"].split("-")[0]+10,calendar["limitedStart"].split("-")[1]-1,calendar["limitedStart"].split("-")[2]) > 0 ? calendar["limitedStart"].split("-")[0]*1+10 : calendar["limitedEnd"].split("-")[0];
		}*/
		if(calendar["limitedStart"]){
			if(calendar["limitedEnd"] && new Date(calendar["limitedEnd"].replace(/-/g,"/")) - new Date(calendar["limitedStart"].split(/-|\//)[0]*1+1,calendar["limitedStart"].split(/-|\//)[1]*1-1,calendar["limitedStart"].split(/-|\//)[2]*1-1) < 0){
				
			}else{
				var d = new Date(calendar["limitedStart"].split(/-|\//)[0]*1+1,calendar["limitedStart"].split(/-|\//)[1]*1-1,calendar["limitedStart"].split(/-|\//)[2]*1-1);
				calendar["limitedEnd"] = d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate();
			}
		}
		var buildYear = "";
		for(var i = calendar["limitedStart"].split("-")[0]; i < calendar["limitedStart"].split("-")[0]*1+3; i++){
			buildYear += buildNewYear(i);
		}
		$(".calendarBoxShowYearBox").html(buildYear);
		$(".calendarBox").removeClass("calendarBoxHidden");
		$(".showCalendarBody .weekTitle").height(0);
		$("body,html").css("overflow","hidden");
		scrollDate = "";
		if(calendar["type"] == "start" && calendar["startTime"]){
			scrollDate = calendar["startTime"].split(/-|\//g);
		}else if(calendar["type"] == "end" && calendar["endTime"]){
			scrollDate = calendar["endTime"].split(/-|\//g);
		}else if(calendar["type"] == "end" && calendar["startTime"]){
			scrollDate = calendar["startTime"].split(/-|\//g);
		}else{
			scrollDate = [new Date().getFullYear(),new Date().getMonth()+1];
		}
		$(".calendarBoxScrollYearBox").scrollTop(-$(".calendarBoxShowYearBox").offset().top + $(".calendarBoxShowYearBox .yearBox[year='"+scrollDate[0]*1+"'] .monthTable[month='"+scrollDate[1]*1+"']").offset().top);
	}
	/**
	* 数字小于10自动补0
	*/
	function zerofill(s) {
		var s = parseFloat(s.toString().replace(/(^[\s0]+)|(\s+$)/g, ''));
		s = isNaN(s) ? 0 : s;
		return (s < 10 ? '0' : '') + s.toString();
	}
	/*showCalendar({
		"src":$("body"),
		"limitedStart":"2017-01-01",
		"limitedEnd": "2019-01-01",
		"startTime":"2017-10-26",
		"startSrc":"#activeDat",
		"endSrc":$("#exprDat"),
		"type":"start"
	})*/
})
function showCalendar(param){
	$(param["src"]).showCalendar(param);
}
$(function(){
	$("#department_edit").attr("disabled",true);	   
	//用户后台数据
	var department_power = [];
	var power = [];
	var data_power = [];
	var department_info = [];
	var user_info = [];
	var user_power_array = [];
	$(".tab").hide();
	//部门信息后台数据
	//var department_info = ["0001","人力资源部","HR",""];

//树菜单点击事件-----------------------------------------------------------------------------------------------------------------
$("#left_scroll a").live('click',function(){
	$(".tab").show();
	$("#alert_opacity").show();
	$("#left_scroll a").css("background-color","#E0E8FC");
    $(this).css("background-color","#ccc");
	$("#user").attr("current_id_see",$(this).attr("id"));
	$("#user_content  table").empty();
	$("#main_power").empty();
	$("#c_name").val("");
	$("#c_add").val("");
	$("#c_phone").val("");
	$("#c_sell").val("");
	
	    var html_width = document.body.clientWidth;
	    var html_height = document.body.clientHeight;
	    var closed_width = $("#img_closed").css("width");
	    var closed_height = $("#img_closed").css("height");
	    var closed_top = (parseInt(html_height) - parseInt(closed_height))/2;
	    var closed_left = (parseInt(html_width) - parseInt(closed_width))/2;
	    $("#img_closed").css("top",closed_top+"px");
	    $("#img_closed").css("left",closed_left+"px");
	    $("#img_closed").show();
	
	var currentid = $(this).attr("temid");
	$.ajax({ 
	   url: "/china/client/edit",
	   dataType:"JSON",
	   timeout:30000,
	   error:function(){
		 alert("网络超时，操作中断！");
		 $("#alert_opacity").hide();
	     $("#img_closed").hide();
					   },
	   type:"post",
	   data:"clientid="+currentid,			  
	   complete:function(xr){
		  $("#alert_opacity").hide(); 
		  var str = (xr.responseText);
		  if(str != null){
		  if(str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0){
			  alert("用户超时，请重新登陆！");
			  location.assign("/china/client/edit");
			  }}
	      $("#img_closed").hide();
							 },				 
	   success: function(data){
			//用户权限提示---------------------------------------------------------------------
			    if(data == null){
					alert("数据错误！");
					}else{
                user_power_array = data.userlist;
				department_info = data.agency;
				department_power = data.actionlist;
				var client_product = data.productlist;
				var client_pcclist = data.pcclist;
			//数据清空-------------------------------------------------------------------------	
			    select_power("0",$("#main_power input[checked=checked]").length);
			//用户信息-------------------------------------------------------------------------	
				$.each(user_power_array,function(i){
					var isuser = "";
					var bind = "";
					var isadmin = "";
					if(user_power_array[i].isuser == 1){
						isuser = "checked='checked'";
						};
					if(user_power_array[i].bind == 1){
						bind = "checked='checked'";
						}
					if(user_power_array[i].isadmin == 1){
						isadmin = "checked='checked'";
						}
					user_power_array[i].real_name == null || user_power_array[i].real_name == "null"?real_name = "":real_name = user_power_array[i].real_name;
	                user_power_array[i].phone == null || user_power_array[i].phone == "null"?phone = "":phone = user_power_array[i].phone;
	                user_power_array[i].gh == null || user_power_array[i].gh == "null"?gh = "":gh = user_power_array[i].gh;
	                user_power_array[i].addr == null || user_power_array[i].addr == "null"?addr = "":addr = user_power_array[i].addr;
					user_power_array[i].subCode == null || user_power_array[i].subCode == "null"?subCode = "":subCode = user_power_array[i].subCode;
			var new_tr =$("<tr class='power_content' align='center' height='25'><td width='46' align='left'>"+parseInt(i+1)+"</td><td width='100' align='left'>"+user_power_array[i].name+"</td><td width='75' align='left'>"+real_name+"</td><td width='88' align='left'>"+phone+"</td><td width='45' align='center'><a href='#'>浏览</a></td><td width='50' align='left'><input class='check' type='checkbox' name='bind' "+bind+" disabled='disabled'/></td><td width='140'>"+addr+"</td><td width='90'>"+subCode+"</td><td width='90'><input type='checkbox' class='check' "+isadmin+" disabled='disabled'/></td><td width='50'><input type='checkbox' class='check' "+isuser+" disabled='disabled'/></td></tr>");
		    new_tr.appendTo($("#user_content  table"));
			});
			$("#user_content  table tr:odd").css("background-color","#F6F6F6");
			//部门权限------------------------------------------------------------------------
			var main_power = "main_power";
	        see_power_list(main_power,department_power,4);
			
	        var power_prototype = $("#main_power").html();
	        select_power(department_power.length,$("#main_power input[checked=checked]").length);
			
			//用户权限赋值---------------------------------------------------------------------
			power = data.userlist.power;
            data_power = data.userlist.data;
							
			//部门信息------------------------------------------------------------------------
			$("#see_client_name").val(department_info.name);
			$("#see_client_sname").val(department_info.abbrname);
			$("#see_client_addr").val(department_info.address);
			$("#see_client_num").val(department_info.code);
			$("#see_client_mail_num").val(department_info.post);
			$("#see_client_phone").val(department_info.telphone);
			$("#see_client_fax").val(department_info.fax);
			$("#see_client_person").val(department_info.person);
			$("#see_client_list").val(department_info.mobile);
			$("#see_client_companey_num").val(department_info.organization);
			var sell_person = $("#left_scroll_top").data("sell_person");
			var cu_sell_id = department_info.salesManID;
			var select_sell_person_gh = "";
				}
    }});     
});

 //用户权限点击事件--------------------------------------------------------------------------------------------------------------
	$("#user_content a").live('click',function(){
			   var id = "";								   
			   $("#user_power_list").empty();
			   $("#user_power_data_list").empty();
			   var current_login = $(this).parent().siblings().eq(1).text();
			   var current_name = $(this).parent().siblings().eq(2).text();
	           user_power(user_power_top,user_power_left);
			   var tr_number = $(this).parent().parent().index();
			   $("#user_power_list").html("");
				//功能权限
				if(user_power_array[tr_number].power != null){
				var power = user_power_array[tr_number].power;
				}else{ power = [];}
				user_power_list("user_power_list",department_power,3,power,id,tr_number);
				//数据权限用户行
				var aa = 0;
	            $("#user_power_2 input[type ='checkbox']").each(function(i){
		          if($(this).is(":checked")){
			        aa+=1;
		          }
	            });
			    $("#current_user").html("当前登陆名："+current_login);
				$("#current_name").html("姓名："+current_name);
			    user_power_current = $("#user_power_list input[type=checkbox]").length;
				$("#current_user_select").html("功能权限："+aa+"项");
			
   });
	
//权限关闭按铵-------------------------------------------------------------------------------------------------------

	$("#user_power_cancel").click(function(){
			   $("#user_opacity").hide();
			   $("#user_power").hide();						   
										   });
//屏幕居中------------------------------------------------------------------------------------------------------------
		   
    var html_width = document.body.clientWidth;
	var html_height = document.body.clientHeight;
	var user_power_width = $("#user_power").css("width");
	var user_power_height = $("#user_power").css("height");
	var user_power_top = (parseInt(html_height) - parseInt(user_power_height))/2;
	var user_power_left = (parseInt(html_width) - parseInt(user_power_width))/2;
	
	var tab_box_widt = $(".tab_box > div").css("width");
	var tab_box_height = $(".tab_box > div").css("height");
	var loadImg_width  = $(".loadImg").css("width");
	var loadImg_height  = $(".loadImg").css("height");
	var load_top = (parseInt(tab_box_widt) - parseInt(loadImg_width))/2;
	var load_left = (parseInt(tab_box_height) - parseInt(loadImg_height))/2;
	
//-------------------------------------------------------------------------------------------------------------------	
	}); 



//-------------------------------------------------------------------------------------------------------------------
//用户弹出层----------------------------------------------------------------------------------------------------------		
   function user_power(user_power_top,user_power_left){
	   $("#user_power").css("top",user_power_top);
	   $("#user_power").css("left",user_power_left);
	   $("#user_opacity").show();
	   $("#user_power").show();
	   }
//------------------------------------------------------------------------------------------------------------------		
   function select_power(length,select_length){
	   $("#select_power").html("共有"+length+"项功能权限");
	   }
//权限列表函数-----------------------------------------------------------------------------------------------------------------------------
function see_power_list(place,data,number){
	$("#"+place).empty();
	var p_length = data.length;
	var f = number;
	var col_power_number = Math.ceil(p_length/f);
	var col_power = "";
	for(var i=0;i<col_power_number;i++){
	  $("<tr></tr>").appendTo($("#"+place));
	}
	var c_number = 0;
	$.each(data,function(i,n){
			if(i<f){
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' checked='checked' disabled='disable' style='width:20px;'/></td><td  width='180'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));
				
			 }else{
				f+=number;
				c_number+=1;

				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' checked='checked' disabled='disable' style='width:20px;'/></td><td  width='180'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));
			 }
			});
}

function user_power_list(place,data,number,state,id,tr){//dom对象，父部门给予的权限，每行显示的列，用户拥有的权限数组,当前部门ID，当前用户所在行
	$("#"+place).empty();
	$("#user_power_title_edit").attr("depart_id",id);//存入当部门id
	$("#user_power_title_edit").attr("depart_tr",tr);//存入当前用户所在行数
	var p_length = data.length;
	var f = number;
	var col_power_number = Math.ceil(p_length/f);
	var col_power = "";
	for(var i=0;i<col_power_number;i++){
	  $("<tr></tr>").appendTo($("#"+place));
	}
	var c_number = 0;
	$.each(data,function(i,n){			 
			if(i<f){
				if(	department_power_validator(state,n.id) >= 0){
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' checked='checked' disabled='disable'  style='width:20px;'/></td><td  width='170'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));
				}else{col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' disabled='disable' style='width:20px;'/></td><td  width='170'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));}
			 }else{
				f+=number;
				c_number+=1;
				if(department_power_validator(state,n.id) >= 0){
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' disabled='disable' checked='checked' style='width:20px;'/></td><td  width='170'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));
				}else{col_power = $("<td height='40' width='20' align='center'><input name='items' alt='"+n.id+"' type='checkbox' disabled='disable' style='width:20px;'/></td><td  width='170'><label class='edit_items'>"+n.name+"</label></td>");
				col_power.appendTo($("#"+place+" tr:eq("+c_number+")"));}
				 }
			 
			});
}
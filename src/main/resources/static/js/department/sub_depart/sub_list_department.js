$(function () {

	//用户权限为单独存储data   user_power
	menu_flash();
	power_flash(0);
	move_user();
	var all_power = []; //初始化默认权限
	var cu_select = [];
	$.each($("#hide_power li"), function (i) {
		var power = {
			"id" : "",
			"name" : ""
		};
		power.id = $(this).children(".all_id").text();
		power.name = $(this).children(".all_name").text();
		all_power[i] = power;
	});
	$("#department_edit_body_left").data("all_power", all_power);
	//-------------------------------------------------------------------------------------------------------
	//部门信息
	//功能权限格式
	var power_manager;
	//用户变量
	var user_info = []; //
	var power_change = new Array(); //定义权限更改的部门存储的数组
	var power_data = [];
	var department_del = new Array();
	var department_add_new = new Array(); //定义 新增部门信息  存储的变量 格式：{id:"",number:"",name:"",simple:"",sell:""}
	var del = [];
	$("#department_edit_body_left").data("del", del);
	//菜单初始化---------------------------------------------------------------------------------------------
	var add_click_cou = 0; //新增菜单分配ID
	//菜单伸展及动态图片--------------------------------------------------------------------------------------
	var $list_companey = $(".leve");
	//$list_companey.parent().has("ul").css("list-style-image","url(images/1.gif)");
	$list_companey.live('dblclick', function () {
		if ($(this).parent().has("ul").length > 0) {
			$(this).parent("li").children("ul").slideToggle("fast");
			ocIco($(this).parent("li"));
		}
	});
	$("#list_companey img").live('click', function () {
		if ($(this).parent().has("ul").length > 0) {
			$(this).parent("li").children("ul").slideToggle("fast");
			ocIco($(this).parent("li"));
		}
	});
	var $list_companey = $(".leve");
	//条数统计
	$("#all_select_count").html("共有" + $("#manager_power input[type=checkbox]").length + "项功能权限");
	$("#current_select_count").html("已选择" + $("#manager_power input[checked=checked]").length + "项功能权限");
	//事件绑定-----------------------------------------------------------------------------------------------
	var $tab_ui = $(".tab_menu li");
	var index = "";
	$tab_ui.click(function () {
		$(this).addClass("selected");
		$(this).parent("td").siblings().children("li").removeClass("selected");
		index = $(this).parent("td").index();
		$(".tab_box > div").eq(index).show().siblings().hide();
	});
	function init_user_info(u){//客户编辑器->用户管理->字段传输函数
		function trim(t){return (typeof t == "undefined" || t == null || t == "");}
		var uid = u.id == "new" ? "0" : u.id;
		var u_phone = trim(u.phone) ? "" : u.phone;
		var one_user_power_str = trim(u.power) ? "" : u.power.join(",");
		var one_user_data_str = trim(u.data) ? "" : u.data.join(",");
		var isuser = trim(u.isuser) ? "0" : u.isuser;
		var gh = trim(u.gh) ? "" : u.gh;
		var name = trim(u.name) ? "" : u.name;
		var real_name = trim(u.real_name) ? "" : u.real_name;
		var addr = trim(u.addr) ? "" : u.addr;
		var bind = trim(u.bind) ? "0" : u.bind;
		var isadmin = trim(u.isadmin) ? "0" : u.isadmin;
		var password = trim(u.password) ? "0" : u.password;
        var subCode = trim(u.subCode) ? "" : u.subCode;
		return "{'id':"+uid+",'phone':'"+u_phone+"','password':"+password+",'isuser':"+isuser+",'gh':'"+gh+"','name':'"+name+"','real_name':'"+real_name+"','data':["+one_user_data_str+"],'power':["+one_user_power_str+"],'addr':'"+addr+"','bind':"+bind+",'isadmin':"+isadmin+",'subCode':'"+subCode+"'}";
	}
	$("#department_edit").click(function () { //部门编辑
		department_del = [];
		$("#department_edit_body_left").data("del", department_del);
		scren_point($("#department_edit_box"));
		$("#department_edit_body_left").empty();
		$("#user_opacity").show();
		$("#department_edit_box").show();
		$("#department_number").val("");
		$("#department_id").val("");
		$("#department_sname").val("");
		$("#department_sell").val("");
		$("#department_sname").attr("currentid", "");
		$("#department_number").attr("currentid", "");
		$("#department_id").attr("currentid", "");
		$("#department_sname").attr("currentid", "");
		$("#department_sell").attr("disabled", true);
		$("#department_id").attr("disabled", true);
		$("#department_sname").attr("disabled", true);
		$("#department_sell").attr("disabled", true);
		$("#list_companey").clone(true).appendTo($("#department_edit_body_left"));
		$("#department_sname").attr("currentid", "");
		$("#power_name").html("");
		$("#manager_power").empty();
		$("#right_main_user_sub tr:not(:first)").empty();
		$("#right_main_user_sub").empty();
		$("#add_user_button input").attr("disabled", true);
		var add_user_validate = [];
		$("#department_edit_body_left").removeData("select_user");
		$("#department_edit_body_left").data("select_user", add_user_validate);
		$("#department_edit_body_left").removeData("add_id");
		$("#scroll_right").hide();
		power_change = []//定义权限更改的部门存储的数组
		$(".department_edit_body_left a").css("color", "#006");
		$("#department_edit_body_left a").css("background-color", "#EFF1FE");
		$("#add_user_button input").attr("disabled", true);
		$("#department_edit_body_left a").each(function () {
			$this = $(this);
			if ($this.attr("parentid") != "") {
				$this.attr("parentid", "c" + $this.attr("parentid"))
			}
			var id = $this.attr("id");
			$this.attr("id", "c" + id);
			//把每个部门的权限读取出来并放入到相应的部门缓存中！
		});
		var frist_power = $("#department_edit_body_left").data("hide_power");
		for (ind in frist_power) {
			$("#c" + ind).data("power_manager", frist_power[ind]);
		}
		$("#department_button_accept").attr("disabled", true);
	});
	$("#department_label span").click(function () { //标签页效果
		$("#department_label span").removeClass("department_top_select");
		$(this).addClass("department_top_select");
		if ($(this).attr("id") == "department_top_select1") {
			$("#department_edit_body_right").show();
			$("#right_main_user").hide();
			$("#right_main_power").hide();
		}
		if ($(this).attr("id") == "department_top_select2") {
			$("#department_edit_body_right").hide();
			$("#right_main_user").show();
			$("#right_main_power").hide();
		}
		if ($(this).attr("id") == "department_top_select3") {
			$("#department_edit_body_right").hide();
			$("#right_main_user").hide();
			$("#right_main_power").show();
		}
		$("#right_main > div").eq($(this).index() - 1).show().siblings().hide();
	});
	$("#department_edit_body_left a").live('click', function () { //部门编辑树的点击事件
		$("#add_user_button input").attr("disabled", false);
		$("#scroll_right").show();
		$("#alert_opacity").show();
		$("#manager_power").empty();
		var $this = $(this);
		$this.attr("id", "c" + $this.attr("temid"));
		$("#department_id").attr("currentid", $this.attr("id"));
		$("#department_number").attr("currentid", $this.attr("id"));
		$("#department_sname").attr("currentid", $this.attr("id"));
		$("#department_sell").attr("currentid", $this.attr("id"));
		$("#department_number").attr("disabled", false);
		$("#department_id").attr("disabled", false);
		$("#department_sname").attr("disabled", false);
		$("#department_sell").attr("disabled", false);
		$("#add_user_button input").attr("disabled", false);
		$("#department_edit_body_left a").css("background-color", "#EFF1FE");
		$this.css("background-color", "#ccc");

		var html_width = document.body.clientWidth;
		var html_height = document.body.clientHeight;
		var closed_width = $("#img_closed").css("width");
		var closed_height = $("#img_closed").css("height");
		var closed_top = (parseInt(html_height) - parseInt(closed_height)) / 2;
		var closed_left = (parseInt(html_width) - parseInt(closed_width)) / 2;
		$("#img_closed").css("top", closed_top + "px");
		$("#img_closed").css("left", closed_left + "px");
		$("#img_closed").show();
		if ($this.attr("id").substr(0, 4) == "cnew") { //如果为新建部门则不读取数据库
			$("#alert_opacity").hide();
			$("#img_closed").hide();
			if ($this.data("department_info") == null) {
				$("#department_number").val("");
				$("#department_id").val("");
				$("#department_sname").val("");
				$("#department_sell").val("");
			} else {
				$("#department_number").val($this.data("department_info").number);
				$("#department_id").val($this.data("department_info").name);
				$("#department_sname").val($this.data("department_info").simple_name);
				$("#department_sell").val($this.data("department_info").sell);
				user_info_list($(this).data("user_info"));
			}

			if ($this.data("user_info") == null) {
				user_info_list(user_info);
				var user_info = [];
				$this.data("user_info", user_info);

			} else {
				user_info_list($this.data("user_info"));
			}

			if ($this.data("power_manager") == null) {
				//功能权限格式----------------------------------------------------
				power_manager = [];
				$this.data("power_manager", power_manager);
				power_list("manager_power", power_manager, $this);
			} else {
				power_manager = $this.data("power_manager");
				power_list("manager_power", power_manager, $this);
			}
		} else {
			if ($this.data("department_info") == null) { //如果缓存为空则读取后台数据并放入缓存中，否则直接读缓存的数据
				var department_info_data = {};
				var currentid = $("#department_sname").attr("currentid").substring(1);
				$.ajax({
					url : "/sale/department/edit",
					dataType : "JSON",
					timeout : 100000,
					type : "post",
					error : function () {
						alert("网络超时，操作中断！");
						$("#alert_opacity").hide();
						$("#img_closed").hide();
					},
					complete : function (xr) {
						$("#alert_opacity").hide();
						$("#img_closed").hide();
						var str = xr.responseText;
						if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
							alert("用户超时，请重新登陆！");
							location.assign("/sale/department/edit");
						}
					},
					data : "departmentid=" + currentid,
					success : function (data) {
						department_info_data = data.department;
						var local_info = {
							"number" : "",
							"name" : "",
							"simple_name" : "",
							"sell" : "",
							"existsdata" : ""
						}
						local_info.number = department_info_data.number;
						local_info.name = department_info_data.name;
						local_info.simple_name = department_info_data.simple_name;
						local_info.sell = department_info_data.sell;
						local_info.existsdata = department_info_data.ExistsData;
						$this.data("department_info", local_info);
						$("#department_number").val($this.data("department_info").number);
						$("#department_id").val($this.data("department_info").name);
						$("#department_sname").val($this.data("department_info").simple_name);
						$("#department_sell").val($this.data("department_info").sell);
						var user_info = data.userlist;
						var add_id = $("#department_edit_body_left").data("add_id"); //删除已转移的用户
						if ($.inArray(parseInt($("#department_sname").attr("currentid").substr(1)), add_id) >= 0) {
							var user_length = user_info.length;
							var userid = [];
							$.each($("#department_edit_body_left").data("select_user"), function (f) {
								userid[f] = $("#department_edit_body_left").data("select_user")[f].userid
							});
							for (var i = 0; i < user_info.length; i++) {
								if ($.inArray(user_info[i].id, userid) >= 0) {
									user_info.splice(i, 1);
									i = i - 1;
								}
							}
						};
						ajax_user_list(user_info);
						ajax_power_list($this);
						var aa = 0;
						$("#power_container input[type ='checkbox']").each(function (i) {
							if ($(this).is(":checked")) {
								aa += 1;
							}
						});
						$("#all_select_count").html("共有" + $("#manager_power input[type=checkbox]").length + "项功能权限");
						$("#current_select_count").html("已选择" + aa + "项功能权限");
					}
				});
				//-----------------------------------------------------------------------------------------------------------------------------------------------------


			} else {
				$("#alert_opacity").hide();
				$("#img_closed").hide();
				$("#department_number").val($this.data("department_info").number);
				$("#department_id").val($this.data("department_info").name);
				$("#department_sname").val($this.data("department_info").simple_name);
				$("#department_sell").val($this.data("department_info").sell);
				//权限列表------------------------------------------
				power_manager = $this.data("power_manager");
				power_list("manager_power", power_manager, $this);
				user_info_list($this.data("user_info"));
			}
			//部门用户信息----------------------------------------------------
			function ajax_user_list(user_info) {
				$this.data("user_info", user_info);
				user_info_list($this.data("user_info"));
			}
			//功能权限格式----------------------------------------------------
			function ajax_power_list($this) {
				if ($this.data("power_manager") != null) {
					var power_manager = $this.data("power_manager");
					power_list("manager_power", power_manager, $this); //缓存名称,缓存数据,当前对象
					var power_prototype = new Array(); //定义原型数组
					$.each(power_manager, function (i) { //循环后台的数据，将已选择的权限ID赋值给原型数组用于校验
						if (power_manager[i].state == "true") {
							power_prototype[i] = power_manager[i].id
						}
					});
					$this.data("power_prototype", power_prototype);

				} else {
					power_manager = $this.data("power_manager");
					power_list("manager_power", power_manager, $this);
				}
			}
		}
		var value_replace = $(this).text();
		var reg = new RegExp("&", "g"); //创建正则RegExp对象
		var newstr = value_replace.replace(reg, "&amp;");
		value_replace = newstr;
		$("#power_name").html(value_replace);
		var aa = 0;
		$("#power_container input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				aa += 1;
			}
		});
		$("#all_select_count").html("共有" + $("#manager_power input[type=checkbox]").length + "项功能权限");
		$("#current_select_count").html("已选择" + aa + "项功能权限");
	});

	$("#add_new_department").click(function () { //添加新部门
		var id = $("#department_id").attr("currentid");
		$parent_id = $("#" + id).parent();
		add_click_cou += 1;
		add_siblings_department($parent_id, "new" + add_click_cou);
		$("#department_number").val("");
		$("#department_id").val("");
		$("#department_sname").val("");
		$("#department_sell").val("");
		$("#department_button_accept").attr("disabled", false);
	});
	$("#add_sub_department").click(function () { //添加子部门
		var id = $("#department_id").attr("currentid");
		$parent_id = $("#" + id).parent();
		add_click_cou += 1;
		add_sub_department($parent_id, "new" + add_click_cou);
		$("#department_number").val("");
		$("#department_id").val("");
		$("#department_sname").val("");
		$("#department_sell").val("");
		$("#department_button_accept").attr("disabled", false);
	});
	$("#delete_new_department").click(function () { //删除部门
		var id = $("#department_sname").attr("currentid");
		if (id != "") {
			if ($("#" + id).data("department_info").existsdata) {
				var exsis = $("#" + id).data("department_info").existsdata;
			} else {
				var exsis = "";
			}
		} else {
			alert("请您选择要删除的部门！")
		}
		$parent_id = $("#" + id).parent();
		$("#department_button_accept").attr("disabled", false);
		delete_siblings_department($parent_id, id, department_del, exsis);
		$("#all_select_count").html("共有" + $("#manager_power input[type=checkbox]").length + "项功能权限");
		$("#current_select_count").html("已选择" + $("#manager_power input[checked=checked]").length + "项功能权限");
	});
	$("#department_sname").bind('keyup', function () { //部门名称绑定keyup事件
		var cu_id = $("#department_id").attr("currentid");
		var value_replace = $(this).val();
		var reg = new RegExp("&", "g"); //创建正则RegExp对象
		var newstr = value_replace.replace(reg, "&amp;");
		value_replace = newstr;
		$("#" + cu_id).html(value_replace);
		if ($(this).val() == "") {
			$("#" + cu_id).html("&nbsp;");
			$("#" + cu_id).css("border", "1px dashed #ccc");
		} else {
			$("#" + cu_id).css("border", "none");
		}
	});
	$("#department_button_cancel").click(function () { //取消全部修改按钮
		if (check_data_cancle() == "true") {
			scren_point($("#my_alert"));
			$("#alert_opacity").show();
			$("#my_alert").show();
		} else {
			$("#right_main_user_sub").empty();
			$("#user_opacity").hide();
			$("#department_edit_box").hide();
		}
	});
	$("#close_power").click(function () {
		$("#user_opacity").hide();
		$("#department_edit_box").hide();
	});
	$("#close_power_edit").click(function () {
		$("#user_opacity_edit").hide();
		$("#user_power_edit").hide();
	});
	$("#close_power_brows").click(function () {
		$("#user_opacity").hide();
		$("#user_power").hide();
	});
	$("#alert_yes").click(function () {
		$("#alert_opacity").hide();
		$("#my_alert").hide();
		$("#right_main_user_sub").empty();
		$("#user_opacity").hide();
		$("#department_edit_box").hide();
	});
	$("#alert_no").click(function () {
		$("#alert_opacity").hide();
		$("#my_alert").hide();
	});
	$(".passw").click(function () {});
	$("#department_input input").focus(function () {
		var element = $(this).attr("currentid");
		if ($(this).attr("currentid") != null && $("#" + element).length > 0) {
			var department_info = {
				"number" : "",
				"name" : "",
				"simple_name" : "",
				"sell" : ""
			};
			$("#department_input input").bind('blur', function () {
				var currentid = $(this).attr("currentid");
				if ($(this).attr("id") == "department_number") {
					if (currentid.substr(0, 4) != "cnew") {
						if ($.trim($("#" + currentid).data("department_info").number) != $.trim($(this).val())) {
							$("#" + currentid).attr("state", "modify");
							$("#department_button_accept").attr("disabled", false);
							$("#" + currentid).data("department_info").number = $(this).val();
						}
					} else {
						if ($.trim($(this)) != "") {
							$("#" + currentid).data("department_info").number = $(this).val();
						} else {
							$("#" + currentid).data("department_info").number = "";
						}
					}
				}
				if ($(this).attr("id") == "department_id") {
					if (currentid.substr(0, 4) != "cnew") {
						if ($.trim($("#" + currentid).data("department_info").name) != $.trim($(this).val())) {
							$("#" + currentid).attr("state", "modify");
							$("#department_button_accept").attr("disabled", false);
							$("#" + currentid).data("department_info").name = $(this).val();
						}
					} else {
						if ($.trim($(this)) != "") {
							$("#" + currentid).data("department_info").name = $(this).val();
						} else {
							$("#" + currentid).data("department_info").name = "";
						}
					}

				}
				if ($(this).attr("id") == "department_sname") {
					if (currentid.substr(0, 4) != "cnew") {
						if ($.trim($("#" + currentid).data("department_info").simple_name) != $.trim($(this).val())) {
							$("#" + currentid).attr("state", "modify");
							$("#department_button_accept").attr("disabled", false);
							$("#" + currentid).data("department_info").simple_name = $(this).val();
						}
					} else {
						if ($.trim($(this)) != null) {
							$("#" + currentid).data("department_info").simple_name = $(this).val();
						} else {
							$("#" + currentid).data("department_info").simple_name = "";
						}
					}

				}
				if ($(this).attr("id") == "department_sell") {
					if (currentid.substr(0, 4) != "cnew") {
						if ($.trim($("#" + currentid).data("department_info").sell) != $.trim($(this).val())) {
							$("#" + currentid).attr("state", "modify");
							$("#department_button_accept").attr("disabled", false);
							$("#" + currentid).data("department_info").sell = $(this).val();
						}
					} else {
						if ($.trim($(this)) != "") {
							$("#" + currentid).data("department_info").sell = $(this).val();
						} else {
							$("#" + currentid).data("department_info").sell = "";
						}
					}

				}
			});
		} else {
			$("#department_input input").unbind('blur');
		}

	});
	//保存修改过值纪录并传到后台（应用全部修改）-------------------------------------------------------------------------------------------------
	$("#department_button_accept").click(function () {
		//定义 修改部门信息 存储的变量 格式"number":"","name":"","simple_name":"","sell":""
		var modify = [];
		var add = "";
		var edit = "";
		var d_power_manager = "";
		var one_user_power_str = "";
		var new_id = "";
		//------------------------------------------------------

		$("#department_edit_body_left a[state='modify']").each(function (i) {
			var id = $(this).attr("id");
			var m_id = id.substr(0, 4);
			if (m_id != "cnew") {
				modify[i] = id;
			}
		});
		//------------------------------------------------------

		var id;
		var isuser = "0";
		var bind = "0";
		var isadmin = "0";
		var d_number = "";
		var d_name = "";
		var d_simple_name = "";
		var d_sell = "";
		var password = "0"
			//----------------------------------------------------------------------------------------------------------------------------------------------------------
			$.each(modify, function (i) {
				var user_info_simple = "";
				id = modify[i];
				id.substr(0, 4) == "cnew" ? yid = id.substr(4) : yid = id.substr(1);

				//如果用户不为空，则循环取出用户值并放入字符串中---------------------------------------------------------------用户信息循环
				if ($("#" + id).data("user_info") != null) {
					if ($("#" + id).data("change_user") != null) {
						$.each($("#" + id).data("user_info"), function (f) { //当前部门下的用户信息--------------------------------
							if ($.inArray(parseInt($("#" + id).data("user_info")[f].id), $("#" + id).data("change_user")) >= 0 || $("#" + id).data("user_info")[f].id == "new") {
								if (user_info_simple == "") {
									user_info_simple = user_info_simple + init_user_info($("#" + id).data("user_info")[f]);
								} else {
									user_info_simple = user_info_simple + "," + init_user_info($("#" + id).data("user_info")[f]);
								}
							}
						})
					};
				} else {
					user_info_simple = "";
				}
				//------------------------------------------------------------------------------------------------------------------------用户信息结尾-------------


				//部门权限保存字符串-----------------------------------------------------------------
				var power_manager = [];
				var d_power_manager = "";
				power_manager = $("#" + id).data("power_manager");
				if ($("#" + id).data("power_manager") != "") {

					$.each(power_manager, function (i) {
						if (d_power_manager == "") {
							d_power_manager += power_manager[i].id;
						} else {
							d_power_manager += "," + power_manager[i].id;
						}
					});
				} else {
					d_power_manager = "";
				}
				//-----------------------------------------------------------------------------------


				if ($("#" + id).data("department_info").number != null && $("#" + id).data("department_info").number != "") {
					d_number = $("#" + id).data("department_info").number;
				} else {
					d_number = ""
				}
				if ($("#" + id).data("department_info").name != null && $("#" + id).data("department_info").name != "") {
					d_name = $("#" + id).data("department_info").name;
				} else {
					d_name = ""
				}
				if ($("#" + id).data("department_info").simple_name != null && $("#" + id).data("department_info").simple_name != "") {
					d_simple_name = $("#" + id).data("department_info").simple_name;
				} else {
					d_simple_name = ""
				}
				if ($("#" + id).data("department_info").sell != null && $("#" + id).data("department_info").sell != "") {
					d_sell = $("#" + id).data("department_info").sell;
				} else {
					d_number = ""
				}

				if (edit == "") {

					edit = edit + yid + ":{'d_info':{'d_number':'" + d_number + "','d_name':'" + d_name + "','d_simple_name':'" + d_simple_name + "','d_sell':'" + d_sell + "'},'d_user':[" + user_info_simple + "],'d_power':[" + d_power_manager + "]}";
				} else {
					edit = edit + "," + yid + ":{'d_info':{'d_number':'" + d_number + "','d_name':'" + d_name + "','d_simple_name':'" + d_simple_name + "','d_sell':'" + d_sell + "'},'d_user':[" + user_info_simple + "],'d_power':[" + d_power_manager + "]}";

				}

			});

		//edit编辑---------------------------------------------------------------------


		//新增部门--------------------------------------------------------------------
		var add = "";
		var new_add = "";
		$("#department_edit_body_left a[propertis='new_department']").each(function (i) {
			new_id = $(this).attr("id");
			var new_d_number = "";
			var new_d_name = "";
			var new_d_simple_name = "";
			var new_d_sell = "";
			var new_d_power_manager = "";
			var parentid = "0";
			var oldid = "0";
			var new_one_user_power_str = "";
			if ($("#" + new_id).attr("parentid") != "") {
				if ($("#" + new_id).attr("parentid").substr(0, 4) == "cnew") {
					parentid = $("#" + new_id).attr("parentid");
				} else {
					oldid = $("#" + new_id).attr("parentid");
					oldid = oldid.substr(1)
				}
			} else {
				parentid = "0";
				oldid = "0";
			}

			//新增部门信息------------------------------------------
			if ($("#" + new_id).data("department_info").number != null && $("#" + new_id).data("department_info").number != "") {
				new_d_number = $("#" + new_id).data("department_info").number;
			} else {
				new_d_number = ""
			}
			if ($("#" + new_id).data("department_info").name != null && $("#" + new_id).data("department_info").name != "") {
				new_d_name = $("#" + new_id).data("department_info").name;
			} else {
				new_d_name = ""
			}
			if ($("#" + new_id).data("department_info").simple_name != null && $("#" + new_id).data("department_info").simple_name != "") {
				new_d_simple_name = $("#" + new_id).data("department_info").simple_name;
			} else {
				new_d_simple_name = ""
			}
			if ($("#" + new_id).data("department_info").sell != null && $("#" + new_id).data("department_info").sell != "") {
				new_d_sell = $("#" + new_id).data("department_info").sell;
			} else {
				new_d_number = ""
			}
			//--------------------------------------------------------
			//新增部门权限----------------------------------------------------------------
			var new_power_manager = [];
			new_power_manager = $("#" + new_id).data("power_manager");
			if ($("#" + new_id).data("power_manager") != "") {

				$.each(new_power_manager, function (i) {
					if (new_d_power_manager == "") {
						new_d_power_manager += new_power_manager[i].id;
					} else {
						new_d_power_manager += "," + new_power_manager[i].id;
					}
				});
			} else {
				new_d_power_manager = "";
			}
			//-----------------------------------------------------------------------------------
			//新增用户列表-------------------------------------------------------------------------

			if ($("#" + new_id).data("user_info") != null) {
				var new_user_info_simple = "";
				$.each($("#" + new_id).data("user_info"), function (f) { //当前部门下的用户信息--------------------------------
					if (new_user_info_simple == "") {
						new_user_info_simple = new_user_info_simple + init_user_info($("#" + new_id).data("user_info")[f]);
					} else {
						new_user_info_simple = new_user_info_simple + "," + init_user_info($("#" + new_id).data("user_info")[f]);
					}

				});
			} else {
				new_user_info_simple = "";
			}

			//-----------------------------------------------------------------------------------用户信息列表结尾---------------------------------------

			if (new_add == "") {
				new_add = new_add + "{'d_info':{'d_number':'" + new_d_number + "','d_name':'" + new_d_name + "','d_simple_name':'" + new_d_simple_name + "','d_sell':'" + new_d_sell + "','id':'" + new_id + "','parentid':'" + parentid + "','oldid':'" + oldid + "'},'d_user':[" + new_user_info_simple + "],'d_power':[" + new_d_power_manager + "]}"
			} else {
				new_add = new_add + ",{'d_info':{'d_number':'" + new_d_number + "','d_name':'" + new_d_name + "','d_simple_name':'" + new_d_simple_name + "','d_sell':'" + new_d_sell + "','id':'" + new_id + "','parentid':'" + parentid + "','oldid':'" + oldid + "'},'d_user':[" + new_user_info_simple + "],'d_power':[" + new_d_power_manager + "]}"
			}

		});
		//定义删除数组---------------------------------------------------------------------------

		var del = [];
		del = $("#department_edit_body_left").data("del");
		var del_str = "";
		if (del != null) {
			$.each(del, function (i) {
				if (del_str == "") {
					del_str = del[i];
				} else {
					del_str = del_str + "," + del[i];
				}
			});
		}
		//定义删除老用户-------------------------------------------------------------------------
		var user_del_str = "";
		$.each($("#left_scroll_top").data("user_del"), function (i) {
			$("#left_scroll_top").data("user_del")[i];
			if (user_del_str == "") {
				user_del_str += $("#left_scroll_top").data("user_del")[i];
			} else {
				user_del_str += "," + $("#left_scroll_top").data("user_del")[i];
			}
		});

		//定义json字符串格式--------------------------------------------------------------------

		var save = "{'edit':{" + edit + "},'add':[" + new_add + "],'del':[" + del_str + "],'user_del':[" + user_del_str + "]}"
			//-------------------------------------------------------------------------------------
			if (modify.length > 0 || del_str.length > 0 || new_add.length > 0) {
				$.ajax({
					url : "/sale/department/save",
					type : "POST",
					data : {
						"department" : save
					},
					dataType : "JSON",
					timeout : 10000,
					error : function () {
						alert("网络超时，操作中断！");
						$("#alert_opacity").hide();
						$("#img_closed").hide();
					},
					complete : function (xr) {
						var str = xr.responseText;
						if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
							alert("用户超时，请重新登陆！");
							location.assign("/sale/department/save");
						}
					},
					success : function (data) {
						var check = data;
						if (check.result == 1) {
							var dell = "0";
							if ($("#department_edit_body_left").data("del") != null) {
								if ($("#department_edit_body_left").data("del").length > 0) {
									dell = "1";
								}
							}
							$("#department_edit_body_left a[state='modify']").each(function (i) {
								$(this).attr("state", "");
							});
							$("#department_edit_body_left a[propertis='new_department']").each(function (i) {
								$(this).attr("propertis", "");
							});
							modify = [];
							del = [];
							var id = $("#department_sname").attr("currentid");
							if (check.department.length > 0 || check.user.length > 0 || dell == "1") {
								$("#list_companey").empty();
								$("#department_number").attr("disabled", true);
								$("#department_id").attr("disabled", true);
								$("#department_sname").attr("disabled", true);
								$("#department_sell").attr("disabled", true);
								accept_menu_flash();
								move_user();
							} else {
								move_user();
								power_flash(0);
							}
							alert("保存成功！");
							$("#department_edit_body_left").removeData("del");
							$("#department_button_accept").attr("disabled", true);
						} else {
							alert(data.message);
						}
					}
				})
			} else {
				alert("您没有修改或新增记录！");
			}
	});

	//保存修改过值纪录并传到后台（保存全部修改)--------------------------------------------------------------------------------------------------
	$("#department_button_save").click(function () {

		//检查必填信息
		if(!validityCheck()) return false;

		//定义 修改部门信息 存储的变量 格式"number":"","name":"","simple_name":"","sell":""
		var modify = [];
		var add = "";
		var edit = "";
		var d_power_manager = "";
		var new_id = "";
		//------------------------------------------------------
		$("#department_edit_body_left a[state='modify']").each(function (i) {
			var id = $(this).attr("id");
			var m_id = id.substr(0, 4)
				if (m_id != "cnew") {
					modify[i] = id;
				}
		});
		//-------------------------------------------

		var id;
		var isuser = "0";
		var bind = "0";
		var isadmin = "0";
		var d_number = "";
		var d_name = "";
		var d_simple_name = "";
		var d_sell = "";
		var password = "0";
		//----------------------------------------------------------------------------------------------------------------------------------------------------------
		$.each(modify, function (i) {
			var user_info_simple = "";
			id = modify[i];
			id.substr(0, 4) == "cnew" ? yid = id.substr(4) : yid = id.substr(1);

			//如果用户不为空，则循环取出用户值并放入字符串中---------------------------------------------------------------用户信息循环
			if ($("#" + id).data("user_info") != null) {
				if ($("#" + id).data("change_user") != null) {
					$.each($("#" + id).data("user_info"), function (f) { //当前部门下的用户信息--------------------------------
						if ($.inArray(parseInt($("#" + id).data("user_info")[f].id), $("#" + id).data("change_user")) >= 0 || $("#" + id).data("user_info")[f].id == "new") {
							if (user_info_simple == "") {
								user_info_simple = user_info_simple + init_user_info($("#" + id).data("user_info")[f]);
							} else {
								user_info_simple = user_info_simple + "," + init_user_info($("#" + id).data("user_info")[f]);
							}
						}
					})
				};

			} else {
				user_info_simple = "";
			}
			//------------------------------------------------------------------------------------------------------------------------用户信息结尾-------------


			//部门权限保存字符串-----------------------------------------------------------------
			var power_manager = [];
			var d_power_manager = "";
			power_manager = $("#" + id).data("power_manager");
			if ($("#" + id).data("power_manager") != "") {

				$.each(power_manager, function (i) {
					if (d_power_manager == "") {
						d_power_manager += power_manager[i].id;
					} else {
						d_power_manager += "," + power_manager[i].id;
					}
				});
			} else {
				d_power_manager = "";
			}
			//-----------------------------------------------------------------------------------

			if ($("#" + id).data("department_info").number != null && $("#" + id).data("department_info").number != "") {
				d_number = $("#" + id).data("department_info").number;
			} else {
				d_number = ""
			}
			if ($("#" + id).data("department_info").name != null && $("#" + id).data("department_info").name != "") {
				d_name = $("#" + id).data("department_info").name;
			} else {
				d_name = ""
			}
			if ($("#" + id).data("department_info").simple_name != null && $("#" + id).data("department_info").simple_name != "") {
				d_simple_name = $("#" + id).data("department_info").simple_name;
			} else {
				d_simple_name = ""
			}
			if ($("#" + id).data("department_info").sell != null && $("#" + id).data("department_info").sell != "") {
				d_sell = $("#" + id).data("department_info").sell;
			} else {
				d_number = ""
			}

			if (edit == "") {

				edit = edit + yid + ":{'d_info':{'d_number':'" + d_number + "','d_name':'" + d_name + "','d_simple_name':'" + d_simple_name + "','d_sell':'" + d_sell + "'},'d_user':[" + user_info_simple + "],'d_power':[" + d_power_manager + "]}";
			} else {
				edit = edit + "," + yid + ":{'d_info':{'d_number':'" + d_number + "','d_name':'" + d_name + "','d_simple_name':'" + d_simple_name + "','d_sell':'" + d_sell + "'},'d_user':[" + user_info_simple + "],'d_power':[" + d_power_manager + "]}";

			}

		});

		//edit编辑---------------------------------------------------------------------


		//新增部门--------------------------------------------------------------------
		var add = "";
		var new_add = "";
		if ($("#department_edit_body_left a").html() != null) {
			$("#department_edit_body_left a[propertis='new_department']").each(function (i) {
				new_id = $(this).attr("id");
				var new_d_number = "";
				var new_d_name = "";
				var new_d_simple_name = "";
				var new_d_sell = "";
				var new_d_power_manager = "";
				var parentid = "0";
				var oldid = "0";
				var new_one_user_power_str = "";
				if ($("#" + new_id).attr("parentid") != "") {
					if ($("#" + new_id).attr("parentid").substr(0, 4) == "cnew") {
						parentid = $("#" + new_id).attr("parentid");
					} else {
						oldid = $("#" + new_id).attr("parentid");
						oldid = oldid.substr(1)
					}
				} else {
					parentid = "0";
					oldid = "0";
				}

				//新增部门信息------------------------------------------
				if ($("#" + new_id).data("department_info").number != null && $("#" + new_id).data("department_info").number != "") {
					new_d_number = $("#" + new_id).data("department_info").number;
				} else {
					new_d_number = ""
				}
				if ($("#" + new_id).data("department_info").name != null && $("#" + new_id).data("department_info").name != "") {
					new_d_name = $("#" + new_id).data("department_info").name;
				} else {
					new_d_name = ""
				}
				if ($("#" + new_id).data("department_info").simple_name != null && $("#" + new_id).data("department_info").simple_name != "") {
					new_d_simple_name = $("#" + new_id).data("department_info").simple_name;
				} else {
					new_d_simple_name = ""
				}
				if ($("#" + new_id).data("department_info").sell != null && $("#" + new_id).data("department_info").sell != "") {
					new_d_sell = $("#" + new_id).data("department_info").sell;
				} else {
					new_d_number = ""
				}
				//--------------------------------------------------------
				//新增部门权限----------------------------------------------------------------
				var new_power_manager = [];
				new_power_manager = $("#" + new_id).data("power_manager");
				if ($("#" + new_id).data("power_manager") != "") {

					$.each(new_power_manager, function (i) {
						if (new_d_power_manager == "") {
							new_d_power_manager += new_power_manager[i].id;
						} else {
							new_d_power_manager += "," + new_power_manager[i].id;
						}
					});
				} else {
					new_d_power_manager = "";
				}
				//-----------------------------------------------------------------------------------
				//新增用户列表-------------------------------------------------------------------------

				if ($("#" + new_id).data("user_info") != null) {
					var new_user_info_simple = "";
					$.each($("#" + new_id).data("user_info"), function (f) { //当前部门下的用户信息--------------------------------
						if (new_user_info_simple == "") {
							new_user_info_simple = new_user_info_simple + init_user_info($("#" + new_id).data("user_info")[f]);
						} else {
							new_user_info_simple = new_user_info_simple + "," + init_user_info($("#" + new_id).data("user_info")[f]);
						}

					});

				} else {
					new_user_info_simple = "";
				}

				//-----------------------------------------------------------------------------------用户信息列表结尾---------------------------------------

				if (new_add == "") {
					new_add = new_add + "{'d_info':{'d_number':'" + new_d_number + "','d_name':'" + new_d_name + "','d_simple_name':'" + new_d_simple_name + "','d_sell':'" + new_d_sell + "','id':'" + new_id + "','parentid':'" + parentid + "','oldid':'" + oldid + "'},'d_user':[" + new_user_info_simple + "],'d_power':[" + new_d_power_manager + "]}"
				} else {
					new_add = new_add + ",{'d_info':{'d_number':'" + new_d_number + "','d_name':'" + new_d_name + "','d_simple_name':'" + new_d_simple_name + "','d_sell':'" + new_d_sell + "','id':'" + new_id + "','parentid':'" + parentid + "','oldid':'" + oldid + "'},'d_user':[" + new_user_info_simple + "],'d_power':[" + new_d_power_manager + "]}"
				}

			});
		}
		//定义删除数组---------------------------------------------------------------------------

		var del = [];
		del = $("#department_edit_body_left").data("del");
		var del_str = "";
		if (del != null) {
			$.each(del, function (i) {
				if (del_str == "") {
					del_str = del[i];
				} else {
					del_str = del_str + "," + del[i];
				}
			});
		}

		//定义删除老用户-------------------------------------------------------------------------
		var user_del_str = "";
		if ($("#left_scroll_top").data("user_del") != null) {
			$.each($("#left_scroll_top").data("user_del"), function (i) {
				$("#left_scroll_top").data("user_del")[i];
				if (user_del_str == "") {
					user_del_str += $("#left_scroll_top").data("user_del")[i];
				} else {
					user_del_str += "," + $("#left_scroll_top").data("user_del")[i];
				}
			});
		}
		//定义json字符串格式--------------------------------------------------------------------

		var save = "{'edit':{" + edit + "},'add':[" + new_add + "],'del':[" + del_str + "],'user_del':[" + user_del_str + "]}"
			//-------------------------------------------------------------------------------------
			if (modify.length > 0 || del_str.length > 0 || new_add.length > 0) {
				$.ajax({
					url : "/sale/department/save",
					type : "POST",
					timeout : 10000,
					dataType : "JSON",
					error : function () {
						alert("网络超时，操作中断！");
						$("#alert_opacity").hide();
						$("#img_closed").hide();
					},
					data : {
						"department" : save
					},
					complete : function (xr) {
						var str = xr.responseText;
						if (str.indexOf("http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd") > 0) {
							alert("用户超时，请重新登陆！");
							location.assign("/sale/department/save");
						}
					},
					success : function (data) {
						if (data.result == 1) {
							alert("保存成功！")
							$("#department_edit_body_left").removeData("del");
							$("#department_edit_body_left a[state='modify']").each(function (i) {
								$(this).attr("state", "");
							});
							$("#department_edit_body_left a[propertis='new_department']").each(function (i) {
								$(this).attr("propertis", "");
							});
							modify = [];
							del = [];
							$("#user_opacity").hide();
							$("#department_edit_box").hide();
							$("#list_companey").empty();
							menu_flash();
							move_user();
							power_flash(0);
						} else {
							alert(data.message);
						}
					}
				});
			} else {
				$("#user_opacity").hide();
				$("#department_edit_box").hide();
			}

	});
	//-------------------------------------------------------------------------------------------------------------------------------------
	//部门查找
	$(".department_serch > input").focus(function () {
		if ($(this).val() == "部门查找") {
			$(this).val("");
			$(this).css("color", "red");
		} else {
			$(this).css("color", "red");
		}
	});
	$(".department_serch > input").blur(function () {
		if ($(this).val() == "") {
			$(this).val("部门查找");
			$(this).css("color", "#ccc");
		} else {
			$(this).css("color", "red");
		}
	});
	$("#department_serch_edit > input").focus(function () {
		if ($(this).val() == "部门查找") {
			$(this).val("");
			$(this).css("color", "red");
		} else {
			$(this).css("color", "red");
		}
	});
	$("#department_serch_edit > input").blur(function () {
		if ($(this).val() == "") {
			$(this).val("部门查找");
			$(this).css("color", "#ccc");
		} else {
			$(this).css("color", "red");
		}
	});
	$("#add_user_window_top > input").focus(function () {
		if ($(this).val() == "用户查找") {
			$(this).val("");
			$(this).css("color", "red");
		} else {
			$(this).css("color", "red");
		}
	});
	$("#add_user_window_top > input").blur(function () {
		if ($(this).val() == "") {
			$(this).val("用户查找");
			$(this).css("color", "#ccc");
		} else {
			$(this).css("color", "red");
		}
	});
	$(".department_serch > input").keyup(function () {
		$("#list_companey li").hide().filter(":contains('" + ($(this).val()) + "')").show();
	}).keyup();
	$("#department_serch_edit > input").keyup(function () {
		$("#department_edit_body_left li").hide().filter(":contains('" + ($(this).val()) + "')").show();
	}).keyup();

	$("#add_user_window_top > input").keyup(function () {
		$("#add_user_window_table tr").hide().filter(":contains('" + ($(this).val()) + "')").show();
	}).keyup();
	//权限选择事件
	$("#edit_all_select").click(function () { //全选
		$("#manager_power input[type=checkbox]").attr("checked", "checked");
		var aa = $("#department_sname").attr("currentid");
		var $this = $("#" + aa);
		var depart_power = new Array(); //只存储部门权限的ID
		$.each($("#manager_power input[checked=checked]"), function (i) {
			var power = {
				"id" : "",
				"name" : ""
			};
			power.id = $(this).attr("alt");
			power.name = $(this).attr("power_name");
			depart_power[i] = power;
		});
		$this.attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		$this.data("power_manager", depart_power);
		$("#current_select_count").html("已选择" + $("#manager_power input[checked=checked]").length + "项功能权限");
	});

	$("#edit_cancel_select").click(function () { //取消
		$("#manager_power input[type=checkbox]").attr("checked", false);
		$("#current_select_count").html("已选择" + $("#manager_power input[checked=checked]").length + "项功能权限");
		var aa = $("#department_sname").attr("currentid");
		var $this = $("#" + aa);
		var power = [];
		var abc = [];
		$("#department_button_accept").attr("disabled", false);
		$this.attr("state", "modify");
		$this.data("power_manager", power);
		$("#del_power_select").empty();
		$("#" + aa).parent().clone().appendTo($("#del_power_select"));
		$("#del_power_select a").each(function (i) {
			var id = $(this).attr("id");
			$("#" + id).data("power_manager", power);
		});

	});

	$("#manager_power input[type=checkbox]").live('change', function () { //复选框改变事件
		var aa = $("#department_sname").attr("currentid");
		var $this = $("#" + aa);
		var d_power = new Array(); //只存储部门权限的ID
		var u_power = new Array();
		var power_id = $(this).attr("alt");
		power_manager = $this.data("power_manager");
		if ($(this).attr("checked") == "checked") {
			$(this).attr("checked", "checked");
		} else {
			var validator_id = $(this).attr("alt");
			$("#del_power_select").empty();
			$("#" + aa).parent().clone().appendTo($("#del_power_select"));
			var clone_id = [];
			$("#del_power_select a").each(function (i) {
				clone_id[i] = $(this).attr("id");
			});
			$("#del_power_select").empty();
			$.each(clone_id, function (i) {
				var sub_power_array = [];
				sub_power_array = $("#" + clone_id[i]).data("power_manager");
				del_validator_power(clone_id[i], validator_id, sub_power_array);
			});
			$(this).attr("checked", false);
		}
		var aa = 0;
		$("#power_container input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				var power = {
					"id" : "",
					"name" : ""
				};
				power.id = $(this).attr("alt");
				power.name = $(this).attr("power_name");
				d_power[aa] = power;
				aa += 1;
			}
		});
		$.each($this.data("user_info"), function (i) {
			var user_power = $this.data("user_info")[i].power;
			var del_u = $.inArray(parseInt(power_id), user_power);
			if (del_u >= 0) {
				$this.data("user_info")[i].power.splice(del_u, 1);
			};
		});

		$this.attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		$this.removeData("power_manger");
		$this.data("power_manager", d_power);
		$("#current_select_count").html("已选择" + aa + "项功能权限");
	});

	$("#power_user").live('click', function () { //用户权限
		//$("#department_sname").attr("currentid");
		if ($.trim($(this).siblings().eq(1).children().val()) != "") {
			$("#user_power_list_edit").empty();
			$("#user_power_data_list_edit").empty();
			var current_login = $(this).siblings().eq(1).children().val();
			var current_name = $(this).siblings().eq(3).children().val();
			tr_number = $(this).parent().index(); //当前行
			$("#user_power_list_edit").html("");
			var main_power = "user_power_list_edit";
			var main2_power = "user_power_data_list_edit";
			scren_point($("#user_power_edit"));
			$("#user_opacity_edit").show();
			$("#user_power_edit").show();
			//功能权限
			var parentid_power_manager = [];
			var id = $("#department_sname").attr("currentid"); //父部门ID，如果为空则读取默认权限
			var parrentid = $("#" + id).attr("parentid");
			parentid_power_manager = $("#" + id).data("power_manager"); //当前部门权限   [{"id":"","name":""}]
			var user_info_power = [];
			if ($("#" + id).data("user_info")[tr_number].power != null) {
				user_info_power = $("#" + id).data("user_info")[tr_number].power; //当前用户拥有权限
			}
			if (parentid_power_manager != null) {
				edit_power_list("user_power_list_edit", parentid_power_manager, 3, user_info_power, id, tr_number); //显示容器，父部门给予的权限，显示行数，用户拥有的权限，当前部门ID，当前用户行
			};
			//数据权限
			var parentid_power_manager = [];
			var n = 0;
			$("#left_scroll a").each(function (i) {
				var da_power = {
					"id" : "",
					"name" : ""
				};
				da_power.id = $(this).attr("id");
				da_power.name = $(this).text();

				if (da_power.id.substr(0, 4) != "cnew") {
					parentid_power_manager[n] = da_power;
					n += 1;
				}
			});
			if ($("#" + id).data("user_info")[tr_number].data != null) {
				user_info_data_power = $("#" + id).data("user_info")[tr_number].data; //当前用户拥有权限
			}
			if (parentid_power_manager != null) {
				edit_power_list("user_power_data_list_edit", parentid_power_manager, 3, user_info_data_power, id, tr_number);
			} else {
				var user_info_data_power = [];
			}
			var aa = 0;
			$("#user_power_2_edit input[type ='checkbox']").each(function (i) {
				if ($(this).is(":checked")) {
					aa += 1;
				}
			});
			var dd = 0;
			$("#user_power_4_edit input[type ='checkbox']").each(function (i) {
				if ($(this).is(":checked")) {
					dd += 1;
				}
			});
			$("#current_user_edit").html("当前登陆名：" + current_login);
			$("#current_name_edit").html("姓名：" + current_name);
			user_power_current = $("#user_power_list_edit input[type=checkbox]").length;
			$("#current_user_select_edit").html("已选功能权限：" + aa + "项");
			$("#edit_power_center").html("已选数据权限：" + dd + "项");
		} else {
			alert("请先输入用户名！");
			$(this).siblings().eq(1).children().focus();
		}
	});

	//用户功能权限复选框点击事件-------------------------------------------------------------------------------------------
	$('#user_power_2_edit input').live('change', function () {
		if ($(this).attr("checked") == "checked") {
			$(this).attr("checked", "checked");

		} else {
			$(this).attr("checked", false);
		}
		var id = $("#department_sname").attr("currentid");
		$("#" + id).attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		var aa = 0;
		$("#user_power_2_edit input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				aa += 1;
			}
		});
		$("#current_user_select_edit").html("已选功能权限：" + aa + "项");
		var tr_number = $("#user_power_title_edit").attr("depart_tr");
		var user_id = $("#right_main_user_sub tr").eq(tr_number).children("td").siblings(0).html()
			if ($("#" + id).data("change_user") == null) {
				var change_user = [];
				change_user.push(parseInt(user_id));
				$("#" + id).data("change_user", change_user);
			} else {
				if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
					$("#" + id).data("change_user").push(parseInt(user_id));
				}
			};
	});

	$('#user_power_4_edit input').live('change', function () {
		if ($(this).attr("checked") == "checked") {
			$(this).attr("checked", "checked");
		} else {
			$(this).attr("checked", false);
		}
		var id = $("#department_sname").attr("currentid");
		$("#" + id).attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		var dd = 0;
		$("#user_power_4_edit input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				dd += 1;
			}
		});
		$("#edit_power_center").html("已选数据权限：" + dd + "项");
		var tr_number = $("#user_power_title_edit").attr("depart_tr");
		var user_id = $("#right_main_user_sub tr").eq(tr_number).children("td").siblings(0).html()
			if ($("#" + id).data("change_user") == null) {
				var change_user = [];
				change_user.push(parseInt(user_id));
				$("#" + id).data("change_user", change_user);
			} else {
				if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
					$("#" + id).data("change_user").push(parseInt(user_id));
				}
			};
	});
	$("#current_user_select_all").click(function () { //用户权限全选
		var id = $("#department_sname").attr("currentid");
		$("#" + id).attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		$("#user_power_2_edit input").each(function (i) {
			$(this).attr("checked", "checked");
		});
		var tr_number = $("#user_power_title_edit").attr("depart_tr");
		var user_id = $("#right_main_user_sub tr").eq(tr_number).children("td").siblings(0).html();
		if ($("#" + id).data("change_user") == null) {
			var change_user = [];
			change_user.push(parseInt(user_id));
			$("#" + id).data("change_user", change_user);
		} else {
			if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
				$("#" + id).data("change_user").push(parseInt(user_id));
			}
		};
		var aa = 0;
		$("#user_power_2_edit input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				aa += 1;
			}
		});
		$("#current_user_select_edit").html("已选功能权限：" + aa + "项");
	});
	$("#current_user_select_cancel").click(function () { //用户权限取消全选
		var id = $("#department_sname").attr("currentid");
		$("#" + id).attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		$("#user_power_2_edit input").each(function (i) {
			$(this).attr("checked", false);
		});
		var tr_number = $("#user_power_title_edit").attr("depart_tr");
		var user_id = $("#right_main_user_sub tr").eq(tr_number).children("td").siblings(0).html();
		if ($("#" + id).data("change_user") == null) {
			var change_user = [];
			change_user.push(parseInt(user_id));
			$("#" + id).data("change_user", change_user);
		} else {
			if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
				$("#" + id).data("change_user").push(parseInt(user_id));
			}
		};
		var aa = 0;
		$("#user_power_2_edit input[type ='checkbox']").each(function (i) {
			if ($(this).is(":checked")) {
				aa += 1;
			}
		});
		$("#current_user_select_edit").html("已选功能权限：" + aa + "项");
	});
	$("#user_power_cancel_edit").click(function () { //关闭弹出层
		$("#user_opacity_edit").hide();
		$("#user_power_edit").hide();
	});
	//用户列表事件-----------------------------------------------------------------------------------
	$("#right_main_user_sub input").live('blur', function () {
		$this = $(this);
		var current_tr_length = $("#right_main_user_sub tr").length;
		var id = $("#department_sname").attr("currentid");
		var user_info = $("#" + id).data("user_info");
		var prototype_user_info = user_info.length;
		var li_number = $this.parent().index();
		var tr_number = $this.parent().parent().index();

		var temarray = {
			"name" : "",
			"real_name" : "",
			"phone" : "",
			"gh" : "",
			"bind" : "",
			"addr" : "",
			"isadmin" : "",
			"isuser" : "",
			"password" : "0"
		};
		//如果当前行用户名不为空则保存数据，否则提示并触发焦点到用户框

		if ($.trim($this.parent().siblings(1).children().val()) != "" && $.trim($this.parent().siblings(1).children().val()) != null) {
			if (!$.isNumeric(parseInt($this.parent().siblings(0).html()))) {
				if ($("#" + id).data("user_info").length < $(this).parent().parent().index() + 1) {
					$("#" + id).data("user_info").push(temarray);
				}

				if ($this.parent().siblings(0).html() == "new" || $this.parent().siblings(0).html() == "") {
					$("#" + id).data("user_info")[tr_number].id = "new";
				}
			}
			if (li_number == 1) {
				$("#" + id).data("user_info")[tr_number].name = $this.val()
			}
			if (li_number == 3) {
				$("#" + id).data("user_info")[tr_number].real_name = $this.val()
			}
			if (li_number == 4) {
				$("#" + id).data("user_info")[tr_number].phone = $this.val()
			}
			if (li_number == 5) {
				$("#" + id).data("user_info")[tr_number].gh = $this.val()
			}
			if (li_number == 7) {
				$this.attr("checked") == "checked" ? $("#" + id).data("user_info")[tr_number].bind = 1 : $("#" + id).data("user_info")[tr_number].bind = 0;
			}
			if (li_number == 8) {
				$("#" + id).data("user_info")[tr_number].addr = $this.val()
			}
			if (li_number == 9) {
				$this.attr("checked") == "checked" ? $("#" + id).data("user_info")[tr_number].isadmin = 1 : $("#" + id).data("user_info")[tr_number].isadmin = 0;
			}
			if (li_number == 10) {
				$this.attr("checked") == "checked" ? $("#" + id).data("user_info")[tr_number].isuser = 1 : $("#" + id).data("user_info")[tr_number].isuser = 0;
				if ($this.attr("checked") != "checked") {
					$this.attr("disabled", true);
				};
			}
			if (!$.isNumeric(parseInt($this.parent().siblings(0).html()))) {
				if ($this.val() == "" && $this.parent().index() == 1) {
					var length_arr = $this.parent().parent().index();

					$("#" + id).data("user_info").splice(length_arr, 1);

				}
			}
		}
		var number_tr = $(this).parent().parent().index() + 1;
		var number_cound_tr = $("#right_main_user_sub tr").length;

		if ($this.parent().index() == 1) {
			if ($.trim($this.val()) == "") {
				if ($.isNumeric(parseInt($this.parent().siblings(0).html()))) {
					//alert("用户名不能为空！");
					//$this.trigger("focus").trigger("select");
				}
			} else if ($.trim($this.val()) != "" && $.trim($this.parent().siblings().eq(2).children().val()) == "" && $this.parent().siblings().eq(2).children().is(":focus") == false) {
				//alert("请输入真实姓名！");
			} else {
				var name = $this.val();
				var uid = ($this.parent().siblings(0).html());
				if (uid == "new") {
					uid = "0";
				}
			}
			var all_name = []
			$.each($("#right_main_user_sub input[name='login']"), function (i) {
				all_name[i] = $(this).val();
			});
			var myname_n = $.inArray($this.val(), all_name);
			all_name.splice(myname_n, 1);
			var t = $.inArray($this.val(), all_name);
			if (t >= 0) {
				//alert("用户名重复，请更改！")
			}
			check_user(uid, name);
		}
		if (li_number == 3) {
			if ($.trim($this.val()) == "" && $.trim($this.parent().siblings().eq(1).children().val()) != "") {
				//alert("请输入真实姓名！");
			} else if ($.trim($this.parent().siblings().eq(1).children().val()) != "" && $.trim($this.parent().siblings().eq(3).children().val()) == "" && $this.parent().siblings().eq(3).children().is(":focus") == false) {
				//alert("请输入联系方式！");
			}
		}
		if (li_number == 4) {
			if ($.trim($this.val()) == "" && $.trim($this.parent().siblings().eq(3).children().val()) != "") {
				//alert("请输入联系方式！");
			} else if ($.trim($this.parent().siblings().eq(3).children().val()) != "" && $.trim($this.parent().siblings().eq(4).children().val()) == "" && $this.parent().siblings().eq(4).children().is(":focus") == false) {
				//alert("请输入工号！");
			}
		}
		if (li_number == 5) {
			if ($.trim($this.val()) == "" && $.trim($this.parent().siblings().eq(4).children().val()) != "") {
				//alert("请输入工号！");
			}
		}
		if ($this.parent().index() == 1 && number_cound_tr == number_tr && $.trim($this.val()) != "") { //增加一行用户
			var user_list = '<tr style="font-size:12px;" align="center"><td width="80" height="20">new</td><td width="100" height="20"><input name="login" type="text" style="width:90px; height:15px;" /></td><td width="50"><input type="button" value="初始化" style="width:50px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="200"><input type="type"  style="width:200px; height:15px;"/></td><td width="50" id="power_user">设置</td><td width="50"><input type="checkbox"  disabled="disabled"/></td><td width="120"><input type="text" style="width:90px; height:15px;"  disabled="disabled"/></td><td width="50"><input type="checkbox" /></td><td width="50"><input type="checkbox" disabled="disabled" /></td><td><input type="button" id="user_del" value="删除"/></td></tr>';
			$("#right_main_user_sub").append(user_list);
		}
	});

	//如果用户框内值被改变，则标记状态，（保存，应用）-------------------------------------------------------------------------
	$("#right_main_user_sub input").live('change', function () {
		var id = $("#department_sname").attr("currentid");
		$("#" + id).attr("state", "modify");
		$("#department_button_accept").attr("disabled", false);
		var user_id = $(this).parent().siblings(0).html();
		if ($("#" + id).data("change_user") == null) {
			var change_user = [];
			change_user.push(parseInt(user_id));
			$("#" + id).data("change_user", change_user);
		} else {
			if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
				$("#" + id).data("change_user").push(parseInt(user_id));
			}
		};
	});
	//-----------------------------------------------------------------------------------------------------------------
	$("#right_main_user_sub input[name='password']").live('click', function () {
		$.ajax({
			url:"/sale/client/getPwAjax",
			type:"post",
			success:function(data){
				var pasw = eval('(' + data + ')');
				alert("密码初始化为："+pasw.pw);
			}
		})
		var tr_number = $(this).parent().parent().index();
		var id = $("#department_sname").attr("currentid");
		$("#" + id).data("user_info")[tr_number].password = "1";
		$("#" + id).attr("state", "modify");
		var user_id = $(this).parent().siblings(0).html();
		if ($("#" + id).data("change_user") == null) {
			var change_user = [];
			change_user.push(parseInt(user_id));
			$("#" + id).data("change_user", change_user);
		} else {
			if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
				$("#" + id).data("change_user").push(parseInt(user_id));
			}
		};
		$("#department_button_accept").attr("disabled", false);
	});
	/*$("#right_main_user_sub input:not(:last)").live('focus',function(){//规定先输入用户名
	var $this = $(this);
	if( $.trim($this.parent().siblings().eq(1).children().val()) == ""){
	$this.parent().siblings().eq(1).children().focus();
	alert("请先输入用户名！");
	}
	})*/

	$("#user_del").live('click', function () { //删除一行用户
		var id = $("#department_sname").attr("currentid");
		$("#" + id).data("user_info").splice($(this).parent().parent().index(), 1);
		$(this).parent().parent().remove("tr");
		if ($(this).parent().siblings(0).html() != "new") {
			$("#left_scroll_top").data("user_del").push($(this).parent().siblings(0).html());
			var id = $("#department_sname").attr("currentid");
			$("#" + id).attr("state", "modify");
			$("#department_button_accept").attr("disabled", false);
		}
	});

	//用户权限确定按钮----------------------------------------------------------------------------------------------------------------------
	$("#user_power_sure_edit").click(function () {
		var depart_id = $("#user_power_title_edit").attr("depart_id");
		var depart_tr = $("#user_power_title_edit").attr("depart_tr");
		var power = [];
		var data_power = [];
		var f = 0;
		var n = 0;
		$("#user_power_2_edit input[type=checkbox]").each(function () {
			var $this = $(this);
			if ($this.attr("checked") == "checked") {
				power[f] = parseInt($this.attr("alt"));
				f += 1;
			};
		});

		$("#user_power_4_edit input[type=checkbox]").each(function () {
			var $this = $(this);
			if ($this.attr("checked") == "checked") {
				data_power[n] = parseInt($this.attr("alt"));
				n += 1;
			};
		});

		$("#" + depart_id).data("user_info")[depart_tr].power = power;
		$("#" + depart_id).data("user_info")[depart_tr].data = data_power;
		$("#user_opacity_edit").hide();
		$("#user_power_edit").hide();
	});
	$("#add_user_button input").click(function () {
		scren_point($("#add_user_window"));
		$("#add_user_window").show();
		var listUser = $("#user_content").data("listUser");
		$("#add_user_window_table").empty();
		var current = [];
		cu_select = [];
		$.each($("#right_main_user_sub tr"), function (i) {
			current[i] = parseInt($(this).children().siblings(0).html());
			$("#add_user_search").val("");
		});
		$.each(listUser, function (i) {
			if ($.inArray(listUser[i].id, current) < 0) {
				var check_isadmin = "";
				var check_user = "";
				var real_name = "";
				var phone = "";
				var gh = "";
				if (listUser[i].isadmin == "1") {
					check_isadmin = "checked='checked'";
				}
				if (listUser[i].isuser == "1") {
					check_user = "checked='checked'";
				}
				if (listUser[i].real_name != null && listUser[i].real_name != "null") {
					real_name = listUser[i].real_name;
				}
				if (listUser[i].phone != null && listUser[i].phone != "null") {
					phone = listUser[i].phone;
				}
				if (listUser[i].gh != null && listUser[i].gh != "null") {
					gh = listUser[i].gh;
				}
				var user_tr = $("<tr><td width='80' align='center'><input type='checkbox' class='add_user_select'/></td><td width='80' align='center'>" + listUser[i].id + "</td><td width='100' align='center'>" + listUser[i].name + "</td><td width='100' align='center'>" + real_name + "</td><td width='120' align='center'>" + phone + "</td><td width='100' align='center'>" + gh + "</td><td width='50' align='center'><input type='checkbox' disabled='disabled' " + check_isadmin + "/></td><td width='50' align='center'><input type='checkbox' disabled='disabled' " + check_user + "/></td></tr>");
				user_tr.appendTo($("#add_user_window_table"));
			}
		});

		$("#add_user_cound").html("其它部门共有" + $("#add_user_window_table tr").length + "条纪录");
	});
	$("#add_user_close").click(function () {
		$("#add_user_window").hide();
	});

	$(".add_user_select").live('change', function () {

		if ($(this).attr("checked") != null) {
			cu_select[cu_select.length] = parseInt($(this).parent().siblings(1).html());
		} else {
			var d = $.inArray(parseInt($(this).parent().siblings(1).html()), cu_select);
			cu_select.splice(d, 1);
		};
	});
	$("#add_user_sure").click(function () {
		var listUser = $("#user_content").data("listUser");
		var id = $("#department_sname").attr("currentid");
		var cc_id = [];
		$.each($("#department_edit_body_left").data("select_user"), function (f) {
			cc_id[f] = $("#department_edit_body_left").data("select_user")[f].userid;
		});
		$.each(listUser, function (i) {
			if ($.inArray(listUser[i].id, cu_select) >= 0) {
				$("#" + id).data("user_info")[$("#" + id).data("user_info").length] = $("#user_content").data("listUser")[i];
				if ($.inArray(parseInt(listUser[i].id), cc_id) >= 0) {
					var d_l = $.inArray(parseInt(listUser[i].id), cc_id);
					$("#department_edit_body_left").data("select_user")[d_l].parentid = $("#department_edit_body_left").data("select_user")[d_l].currentid;
					$("#department_edit_body_left").data("select_user")[d_l].currentid = id;
					$("#" + id).attr("state", "modify");
				} else {

					var user_id = listUser[i].id;
					if ($("#" + id).data("change_user") == null) {
						var change_user = [];
						change_user.push(parseInt(user_id));
						$("#" + id).data("change_user", change_user);
					} else {
						if ($.inArray(parseInt(user_id), $("#" + id).data("change_user")) < 0) {
							$("#" + id).data("change_user").push(parseInt(user_id));
						}
					}

					var add_user_array = {
						"userid" : "",
						"currentid" : "",
						"parentid" : "",
						"prototype" : ""
					};
					add_user_array.userid = listUser[i].id;
					add_user_array.currentid = id;
					add_user_array.parentid = listUser[i].departmentID;
					add_user_array.prototype = listUser[i].departmentID;
					$("#" + id).attr("state", "modify");
					$("#department_edit_body_left").data("select_user")[$("#department_edit_body_left").data("select_user").length] = add_user_array;
				};
				$("#department_button_accept").attr("disabled", false);
			}
		});
		var add_id = [];
		$.each($("#department_edit_body_left").data("select_user"), function (i) {
			var p_id = $("#department_edit_body_left").data("select_user")[i].parentid;
			add_id[i] = p_id;
			p_id = String(p_id);
			if (p_id.substr(0, 1) != "c") {
				p_id = "c" + p_id;
			};
			if ($("#" + p_id).data("user_info") != null) {
				$.each($("#" + p_id).data("user_info"), function (f) {
					if ($("#" + p_id).data("user_info")[f].id == $("#department_edit_body_left").data("select_user")[i].userid) {
						$("#" + p_id).data("user_info").splice(f, 1);
						return false;
					}
				});
			}
		});
		$("#department_edit_body_left").data("add_id", add_id);
		$("#add_user_window").hide();
		user_info_list($("#" + id).data("user_info"));
	});
	// $("#department_sname").blur(function () {
	// 	if ($.trim($(this).val()) == "" && $("#department_button_cancel").is(":focus") == false) {
	// 		alert("请输入部门简称！");
	// 		$("#department_sname").trigger("focus").trigger("select");
	// 	} else if ($.trim($("#department_id").val()) == "" && $("#department_id").is(":focus") == false && $("#department_button_cancel").is(":focus") == false) {
	// 		alert("请输入部门名称！");
	// 		$("#department_id").trigger("focus").trigger("select");
    //
	// 	};
	// });
    //
	// $("#department_id").blur(function () {
	// 	if ($.trim($(this).val()) == "" && $.trim($("#department_sname").val()) != "" && $("#department_button_cancel").is(":focus") == false) {
	// 		alert("请输入部门名称！");
	// 		$("#department_id").trigger("focus").trigger("select");
	// 	} else if ($.trim($("#department_sname").val()) == "" && $("#department_button_cancel").is(":focus") == false) {
	// 		$("#department_sname").trigger("focus").trigger("select");
	// 	}
	// });

	//部门必填项，验证
	$(".checkBasicInfo").on('click',function () {
		validityCheck();
	});

});

function validityCheck() {
	if ($.trim($("#department_sname").val()) == ""){
		$("#department_top_select1").click();
		$("#department_sname").focus();
		alert("请输入部门简称！");
		return false;
	}

	if ($.trim($("#department_id").val()) == ""){
		$("#department_top_select1").click();
		$("#department_id").focus();
		alert("请输入部门名称！");
		return false;
	}
	return true;
};



//-----------------------------------------------------------------------------------------------------------------------------------
//菜单列表函数-------------------------------------------------------------------------------------------------------------------------
function list_menu_function(array, object) {
	$.each(array, function (i) {
		if (array[i].parentid == "") {
			var menu_line = $('<li><a href="#" class="leve" id="' + array[i].id + '" title="' + array[i].name + '" propertis="" existsdata="" state="" temid="' + array[i].id + '" parentid="" l_index="' + i + '">' + (array[i].abbrName==null?"":array[i].abbrName.substr(0, 10)) + '</a></li>');
			menu_line.appendTo(object);
		} else {
			if ($("#" + array[i].parentid).parent().has("ul").length > 0) {
				var menu_line_sub = $('<li><a href="#" class="leve" state="" propertis="" title="' + array[i].name + '" existsdata="" id="' + array[i].id + '" temid="' + array[i].id + '" parentid="' + array[i].parentid + '" l_index="' + i + '">' + (array[i].abbrName==null?"":array[i].abbrName.substr(0, 10)) + '</a></li>');
				menu_line_sub.appendTo($("#" + array[i].parentid).parent().children("ul"));
			} else {
				var menu_line_sub = $('<ul><li><a href="#" class="leve" state="" propertis="" title="' + array[i].name + '" existsdata="" id="' + array[i].id + '" temid="' + array[i].id + '" parentid="' + array[i].parentid + '" l_index="' + i + '">' +(array[i].abbrName==null?"":array[i].abbrName.substr(0, 10)) + '</a></li></ul>');
				menu_line_sub.appendTo($("#" + array[i].parentid).parent());
			}
		}
	});
}
//修改图片函数--------------------------------------------------------------------------------------------------------------------------
function ocIco(node) {
	if (node) {
		if (node.children("img").attr("src").indexOf("1.gif") >= 0) {
			node.children("img").attr("src", "/img/department/2.gif");
		} else {
			node.children("img").attr("src", "/img/department/1.gif");
			//node.css("list-style-image","url(images/1.gif)");
		}
	}
}

//数组分割-------------------------------------------------------------------------------------------------------------------------------
function list_menu_array_split(array) {
	$.each(array, function (i) {
		list_menu_tools = array[i].split("@");
		list_menu_id[i] = list_menu_tools[0];
		list_menu_name[i] = list_menu_tools[1];
	});
	list_menu_tools = [];
	list_menu_tools[0] = list_menu_id;
	list_menu_tools[1] = list_menu_name;
	return list_menu_tools;
};
//当前屏幕位置弹出层------------------------------------------------------------------------------------------------------------------------

function scren_point(param_object) {
	var html_width = document.body.clientWidth;
	var html_height = document.body.clientHeight;
	var user_width = param_object.css("width");
	var user_height = param_object.css("height");
	var user_top = (parseInt(html_width) - parseInt(user_width)) / 2;
	var user_left = (parseInt(html_height) - parseInt(user_height)) / 2;
	param_object.css("top", user_left);
	param_object.css("left", user_top);
}

//ajax等待图片居中-------------------------------------------------------------------------------------------------------------------------

function ajax_load_point(param_object, parent) {
	var parent_width = $("#" + parent).css("width");
	var parent_height = $("#" + parent).css("height");
	var user_width = $("#" + param_object).css("width");
	var user_height = $("#" + param_object).css("height");
	var user_top = (parseInt(parent_width) - parseInt(user_width)) / 2;
	var user_left = (parseInt(parent_height) - parseInt(user_height)) / 2;
	$("#" + param_object).css("top", user_left);
	$("#" + param_object).css("left", user_top);

}

//添加部门--------------------------------------------------------------------------------------------------------------------------------

function add_siblings_department($parent_id, id) {
	var $object_clone = $("#department_edit_body_left ul");
	var parent_id = "";
	if ($parent_id.parent().parent().children("a").attr("id") != null) {
		parent_id = $parent_id.parent().parent().children("a").attr("id");
	}
	if ($.trim($object_clone.html()) == "" || $.trim($object_clone.html()) == null) {
		$('<li><a href="#" class="leve" id="' + id + '" temid="' + id + '" propertis="new_department" parentid="' + parent_id + '"></a></li>').appendTo($object_clone);
		$("#" + id).html("&nbsp;");
		$("#" + id).css("border", "1px dashed #ccc");
		$("#department_input input").attr("disabled", false);
		$("#department_number").attr("currentid", "c" + id);
		$("#department_id").attr("currentid", "c" + id);
		$("#department_sname").attr("currentid", "c" + id);
		$("#department_sell").attr("currentid", "c" + id);
		$("#add_user_button input").attr("disabled", false);
		var department_info_data = {
			"number" : "",
			"name" : "",
			"simple_name" : "",
			"sell" : ""
		};
		$("#" + id).click();
		$("#" + id).html("&nbsp;");
		$("#department_label span").removeClass("department_top_select");
		$("#department_top_select1").addClass("department_top_select");
		$("#department_edit_body_right").show();
		$("#right_main_user").hide();
		$("#right_main_power").hide();
		$("#department_sname").trigger("focus").trigger("select");
		$("#c" + id).data("department_info", department_info_data);
	} else {
		if ($parent_id.html() == null || $parent_id.html() == "") {
			alert("请选择部门！");
		} else {
			$('<li><a href="#" class="leve" id="' + id + '" temid="' + id + '" propertis="new_department" parentid="' + parent_id + '"></a></li>').insertAfter($parent_id);
			$("#" + id).html("&nbsp;");
			$("#" + id).css("border", "1px dashed #ccc");
			$("#" + id).click();
			$("#department_input input").attr("disabled", false);
			$("#department_number").attr("currentid", "c" + id);
			$("#department_id").attr("currentid", "c" + id);
			$("#department_sname").attr("currentid", "c" + id);
			$("#department_sell").attr("currentid", "c" + id);
			$("#add_user_button input").attr("disabled", false);
			var department_info_data = {
				"number" : "",
				"name" : "",
				"simple_name" : "",
				"sell" : ""
			};
			$("#department_input input").attr("disabled", false);
			$("#department_label span").removeClass("department_top_select");
			$("#department_top_select1").addClass("department_top_select");
			$("#department_edit_body_right").show();
			$("#right_main_user").hide();
			$("#right_main_power").hide();
			$("#department_sname").trigger("focus").trigger("select");
			$("#c" + id).data("department_info", department_info_data);
		}
	}
}
//添加子部门------------------------------------------------------------------------------------------------------------------------------
function add_sub_department($parent_id, id, user_info) {
	if ($parent_id.html() == null || $parent_id.html() == "") {
		alert("上级部门不能为空！");
	} else {
		if ($parent_id.has("ul").length > 0) {
			var $place = $parent_id.children("ul");
			$('<li><a href="#" class="leve" propertis="new_department" id="' + id + '" temid="' + id + '" parentid="' + $parent_id.children("a").attr("id") + '" ></a></li>').appendTo($place);
			$("#" + id).html("&nbsp;");
			$("#" + id).css("border", "1px dashed #ccc");
			$("#" + id).click();
			$("#department_sname").trigger("focus").trigger("select");
			$("#department_number").attr("currentid", "c" + id);
			$("#department_id").attr("currentid", "c" + id);
			$("#department_sname").attr("currentid", "c" + id);
			$("#department_sell").attr("currentid", "c" + id);
			$("#department_label span").removeClass("department_top_select");
			$("#department_top_select1").addClass("department_top_select");
			$("#department_edit_body_right").show();
			$("#right_main_user").hide();
			$("#right_main_power").hide();
			$("#department_sname").trigger("focus").trigger("select");
			var department_info_data = {
				"number" : "",
				"name" : "",
				"simple_name" : "",
				"sell" : ""
			};
			$("#c" + id).data("department_info", department_info_data);
		} else {
			$('<ul><li><a href="#" class="leve" propertis="new_department" id="' + id + '" temid="' + id + '" parentid="' + $parent_id.children("a").attr("id") + '" ></a></li></ul>').appendTo($parent_id);
			var list_img = '<img src="/img/department/1.gif"/>';
			$parent_id.prepend(list_img);
			$parent_id.children("a").dblclick();
			$("#" + id).html("&nbsp;");
			$("#" + id).css("border", "1px dashed #ccc");
			$("#" + id).click();
			$("#department_sname").trigger("focus").trigger("select");
			$("#department_number").attr("currentid", "c" + id);
			$("#department_id").attr("currentid", "c" + id);
			$("#department_sname").attr("currentid", "c" + id);
			$("#department_sell").attr("currentid", "c" + id);
			$("#department_label span").removeClass("department_top_select");
			$("#department_top_select1").addClass("department_top_select");
			$("#department_edit_body_right").show();
			$("#right_main_user").hide();
			$("#right_main_power").hide();
			$("#department_sname").trigger("focus").trigger("select");
			var department_info_data = {
				"number" : "",
				"name" : "",
				"simple_name" : "",
				"sell" : ""
			};
			$("#c" + id).data("department_info", department_info_data);
		}
	}
};

//删除部门--------------------------------------------------------------------------------------------------------------------------------
function delete_siblings_department($parent_id, id, department_del, existsdata) {
	if (existsdata != "1" || id.substr(0, 4) == "cnew") {
		if ($parent_id.siblings("li").length <= 0) {
			$parent_id.parent().parent().children("img").remove();
			$parent_id.parent().remove();
			$("#department_sname").attr("currentid", "");
			$("#department_number").val("");
			$("#department_id").val("");
			$("#department_sname").val("");
			$("#department_sell").val("");
			$("#add_user_button input").attr("disabled", true);
			if ($("#department_edit_body_left").html() == "") {
				$("<ul id='list_companey'></ul>").appendTo($("#department_edit_body_left"));
			};

		} else {
			$parent_id.remove();
			$("#department_sname").attr("currentid", "");
			$("#department_number").val("");
			$("#department_id").val("");
			$("#department_sname").val("");
			$("#department_sell").val("");
			$("#add_user_button input").attr("disabled", true);
			if ($("#department_edit_body_left").html() == "") {
				$("<ul id='list_companey'></ul>").appendTo($("#department_edit_body_left"));
			};
		}
		if (existsdata == "0") {
			var del_dp = [];
			if ($("#department_edit_body_left").data("del") != null) {
				del_dp = $("#department_edit_body_left").data("del");
			}
			del_dp[del_dp.length] = id.substr(1);
			$("#department_edit_body_left").data("del", del_dp);
		}
		$("#power_name").html("");
		$("#manager_power").empty();
		$("#right_main_user_sub").empty();
	} else {
		alert("此部门有用户，不能被删除！");
	}

}
//用户信息列表-----------------------------------------------------------------------------------------------------------------------------
function user_info_list(array) {
	$("#right_main_user_sub").empty();
	var real_name = "";
	var phone = "";
	var gh = "";
	var addr = "";
	var bind_tf = "";
	var isuser_tf = "";
	var isuser_close = "";
	if (array != null) {
		var user_list_sub = "";
		$.each(array, function (i) {
			if (array[i].id == "new" || array[i].ExistsData == "0") {
				var del = '<td><input type="button" id="user_del" value="删除"/></td>';
			} else {
				var del = '<td></td>'
			};
			array[i].bind == "1" ? bind_tf = 'checked = "checked"' : bind_tf = "";
			array[i].isadmin == "1" ? isadmin = 'checked="checked"' : isadmin = "";
			array[i].isuser == "1" ? isuser_tf = 'checked = "checked"' : isuser_tf = "";
			array[i].isuser == "1" ? isuser_close = "" : isuser_close = 'disabled';
			array[i].real_name == null || array[i].real_name == "null" ? real_name = "" : real_name = array[i].real_name;
			array[i].phone == null || array[i].phone == "null" ? phone = "" : phone = array[i].phone;
			array[i].gh == null || array[i].gh == "null" ? gh = "" : gh = array[i].gh;
			array[i].addr == null || array[i].addr == "null" ? addr = "" : addr = array[i].addr;
			var user_list_sub = '<tr style="font-size:12px;" align="center"><td width="50" height="20">' + array[i].id + '</td><td width="100" height="20"><input name="login" type="text" value="' + array[i].name + '" style="width:90px; height:15px;" /></td><td width="50"><input type="button" name="password" password="0" value="初始化" style="width:50px; height:15px;" /></td><td width="100"><input type="text" value="' + real_name + '" style="width:90px; height:15px;" /></td><td width="100"><input type="text" value="' + phone + '" style="width:90px; height:15px;" /></td><td width="200"><input type="type" value="' + gh + '"  style="width:200px; height:15px;" /></td><td width="50" id="power_user">设置</td><td width="50"><input type="checkbox" ' + bind_tf + '  disabled="disabled"/></td><td width="120"><input type="text" value="' + addr + '" style="width:90px; height:15px;"  disabled="disabled"/></td><td width="50"><input type="checkbox" ' + isadmin + ' /></td><td width="50"><input type="checkbox" ' + isuser_close + ' ' + isuser_tf + ' class="isuser" /></td>' + del + '</tr>';
			$("#right_main_user_sub").append(user_list_sub);
		});
		user_list_sub = user_list_sub + '<tr style="font-size:12px;" align="center"><td width="50" height="20">new</td><td width="100" height="20"><input name="login" type="text" style="width:90px; height:15px;" /></td><td width="50"><input type="button" name="password" password="0" value="初始化" style="width:50px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="200"><input type="type"  style="width:200px; height:15px;"/></td><td width="50" id="power_user">设置</td><td width="50"><input type="checkbox"  disabled="disabled"/></td><td width="120"><input type="text" style="width:90px; height:15px;"  disabled="disabled"/></td><td width="50"><input type="checkbox" /></td><td width="50"><input type="checkbox" disabled="disabled" /></td><td><input type="button" id="user_del" value="删除"/></td></tr>';
		$("#right_main_user_sub").append(user_list_sub);
	} else {
		var user_list_sub = '<tr style="font-size:12px;" align="center"><td width="80" height="20">new</td><td width="100" height="20"><input name="login" type="text" style="width:90px; height:15px;" /></td><td width="50"><input type="button" name="password" password="0" value="初始化" style="width:50px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="100"><input type="text" style="width:90px; height:15px;" /></td><td width="200"><input type="type"  style="width:200px; height:15px;"/></td><td width="50" id="power_user">设置</td><td width="50"><input type="checkbox"  disabled="disabled"/></td><td width="120"><input type="text" style="width:90px; height:15px;"  disabled="disabled"/></td><td width="50"><input type="checkbox" /></td><td width="50"><input type="checkbox" disabled="disabled" /></td><td><input type="button" id="user_del" value="删除"/></td></tr>';
		$("#right_main_user_sub").append(user_list_sub);
	}
}
//权限列表---------------------------------------------------------------------------------------------------------------------------------
function power_list(place, data, parentid) { //容器，当前部门拥有的权限，当前部门的父部门
	var display_data = [];
	if (data != null) {
		$.each(data, function (i) {
			display_data[i] = parseInt(data[i].id);
		});
	};
	var hide_power = $("#left_scroll").data("hide_power");
	var parentid = parentid.attr("parentid");
	var data_list;
	if (parentid == "") {
		data_list = $("#department_edit_body_left").data("all_power");
	} else {
		data_list = $("#" + parentid).data("power_manager");
	}
	var p_length;
	var f = 4;
	var colo_power_number;
	var col_power = "";
	if (data_list == null) {
		col_power_number = 0;
	} else {
		p_length = data_list.length;
		col_power_number = Math.ceil(p_length / f);
	}
	for (var i = 0; i < col_power_number; i++) {
		$("<tr></tr>").appendTo($("#" + place));
	}
	var c_number = 0;
	var data_list_id = [];
	$.each(data_list, function (i) {
		data_list_id[i] = data_list[i].id;
	})
	$.each(data_list, function (i, n) {
		if (i < f) {
			if (department_power_validator(display_data, data_list_id[i]) >= 0) {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' power_name = '" + n.name + "' checked='checked' style='width:20px;'/></td><td  width='180'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			} else {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' power_name = '" + n.name + "' style='width:20px;'/></td><td  width='180'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			}
		} else {
			f += 4;
			c_number += 1;
			if (department_power_validator(display_data, data_list_id[i]) >= 0) {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' power_name = '" + n.name + "' checked='checked' style='width:20px;'/></td><td  width='180'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			} else {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' power_name = '" + n.name + "' style='width:20px;'/></td><td  width='180'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			}
		}

	});
}
//权限校验---------------------------------------------------------------------------------------------------------------------------------
function power_validate(power_manager, power_change, $this) { //权限存储缓存，提交权限数组，当前对象
	var abc = new Array();
	var f = 0;
	$.each(power_manager, function (i) {
		if (power_manager[i].state == "1") {
			abc[f] = power_manager[i].id;
			f += 1;
		}
	});
	if ($this.data("power_prototype").length != abc.length) {
		if ($.inArray($("#department_sname").attr("currentid"), power_change) == -1) {
			power_change[power_change.length] = $("#department_sname").attr("currentid");
		}
	} else {
		var abc_tf = false;
		$.each(abc, function (i, n) {
			if ($.inArray(n, $this.data("power_prototype")) == -1) {
				abc_tf = true;
				if ($.inArray($("#department_sname").attr("currentid"), power_change) == -1) {
					power_change[power_change.length] = $("#department_sname").attr("currentid");
				}
			}
		});
		if (!abc_tf) {
			if ($.inArray($("#department_sname").attr("currentid"), power_change) != -1) {
				power_change.splice($.inArray($("#department_sname").attr("currentid"), power_change), 1);
			}
		}
	}
}
//用户权限列表函数-----------------------------------------------------------------------------------------------------------------------------
function edit_power_list(place, data, number, state, id, tr) { //dom对象，父部门给予的权限，每行显示的列，用户拥有的权限数组,当前部门ID，当前用户所在行
	$("#" + place).empty();
	$("#user_power_title_edit").attr("depart_id", id); //存入当部门id
	$("#user_power_title_edit").attr("depart_tr", tr); //存入当前用户所在行数
	var p_length = data.length;
	var f = number;
	var col_power_number = Math.ceil(p_length / f);
	var col_power = "";
	for (var i = 0; i < col_power_number; i++) {
		$("<tr></tr>").appendTo($("#" + place));
	}
	var c_number = 0;
	$.each(data, function (i, n) {
		if (i < f) {
			if (department_power_validator(state, n.id) >= 0) {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' checked='checked'  style='width:20px;'/></td><td  width='170'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			} else {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' style='width:20px;'/></td><td  width='170'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			}
		} else {
			f += number;
			c_number += 1;
			if (department_power_validator(state, n.id) >= 0) {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' checked='checked' style='width:20px;'/></td><td  width='170'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			} else {
				col_power = $("<td height='40' width='20' align='center'><input name='items' alt='" + n.id + "' type='checkbox' style='width:20px;'/></td><td  width='170'><label class='edit_items'>" + n.name + "</label></td>");
				col_power.appendTo($("#" + place + " tr:eq(" + c_number + ")"));
			}
		}
	});
}
//部门权限列表显示判断函数----------------------------------------------------------------------------------------------------------------------------
function department_power_validator(current_power, parent_power) { //当前部门的权限数组（判断是否显示），父部门的权限id（列表显示）
	if (current_power != null) {
		return $.inArray(parseInt(parent_power), current_power);
	} else {
		return -1;
	}
}
//--------------------------------
function del_validator_power(clone_id, validator_id, sub_power_array) { //当前取消的ID，子部门权限数组
	var array = []
	$.each(sub_power_array, function (i) {
		array[i] = parseInt(sub_power_array[i].id);
	});
	var del_num = $.inArray(parseInt(validator_id), array);
	if (del_num >= 0) {
		sub_power_array.splice(del_num, 1);
		$("#" + clone_id).data("power_manager", sub_power_array);
	}
}
//-------------------------------
//用于保存事件的参数传递-------------------------------------------------

function menu_flash() {
	ajax_load_point("loadImg_left_list", "left_scroll");
	var aa;
	aa += 1;
	var frist_power;
	var first_id = {
		"id" : "",
		"power" : []
	};
	$.ajax({
		url : "/sale/department/list",
		dataType : "JSON",
		type : "post",
		statusCode : {
			302 : function () {
				alert('用户超时，请重新登陆！');
			}
		},
		data : {
			"aa" : aa
		},
		success : function (data) {
			$("#department_edit").attr("disabled", false);
			$("#loadImg_left_list").hide();
			var list_menu = data;
			var $menu_object = $("#list_companey");
			var list_img = '<img src="/img/department/1.gif"/>';
			list_menu_function(list_menu, $menu_object);
			$(".leve").parent().has("ul").prepend(list_img);
			$("#left_scroll_top").removeData("user_del");
			var user_del = [];
			$("#left_scroll_top").data("user_del", user_del);
		}
	});
}
function accept_menu_flash() {
	ajax_load_point("loadImg_left_list", "left_scroll");
	var aa;
	aa += 1;
	var frist_power;
	var first_id = {
		"id" : "",
		"power" : []
	};
	$.ajax({
		url : "/sale/department/list",
		dataType : "JSON",
		type : "post",
		statusCode : {
			302 : function () {
				alert('用户超时，请重新登陆！');
			}
		},
		data : {
			"aa" : aa
		},
		success : function (data) {

			$("#loadImg_left_list").hide();
			var list_menu = data;
			var $menu_object = $("#list_companey");
			var list_img = '<img src="/img/department/1.gif"/>';
			list_menu_function(list_menu, $menu_object);
			$(".leve").parent().has("ul").prepend(list_img);
			power_flash(1);
		}
	});
}
function check_user(uid, name) {
	$.ajax({
		url : "/sale/department/checkUser",
		type : "post",
		statusCode : {
			302 : function () {
				alert('用户超时，请重新登陆！');
			}
		},
		data : {
			"uid" : uid,
			"loginname" : name
		},
		success : function (data) {
			if (data == 1) {
				alert(name + "用户名已被占用，请更换其它用户名！")
			}
		}
	});
}

function power_flash(param) {
	var aa = "1";
	aa += 1;
	var frist_power = [];
	$.ajax({
		url : "/sale/department/power",
		dataType : "JSON",
		type : "post",
		data : "aa=" + aa,
		statusCode : {
			302 : function () {
				alert('用户超时，请重新登陆！');
			}
		},
		success : function (data) {
			frist_power = data;
			$("#department_edit_body_left").removeData("hide_power");
			$("#department_edit_body_left").data("hide_power", frist_power);
			if (param == 1) {
				department_del = [];
				$("#department_edit_body_left").data("del", department_del);
				scren_point($("#department_edit_box"));
				$("#department_edit_body_left").empty();
				$("#department_number").val("");
				$("#department_id").val("");
				$("#department_sname").val("");
				$("#department_sell").val("");
				$("#list_companey").clone(true).appendTo($("#department_edit_body_left"));
				$("#department_sname").attr("currentid", "");
				$("#power_name").html("");
				$("#manager_power").empty();
				$("#right_main_user_sub tr:not(:first)").empty();
				$("#right_main_user_sub").empty();
				power_change = []//定义权限更改的部门存储的数组
				$(".department_edit_body_left a").css("color", "#006");
				$("#department_edit_body_left a").css("background-color", "#EFF1FE");
				$("#department_edit_body_left a").each(function () {
					$this = $(this);
					if ($this.attr("parentid") != "") {
						$this.attr("parentid", "c" + $this.attr("parentid"))
					}
					var id = $this.attr("id");
					$this.attr("id", "c" + id);
					//把每个部门的权限读取出来并放入到相应的部门缓存中！
				});
				var frist_power = $("#department_edit_body_left").data("hide_power");
				for (ind in frist_power) {
					$("#c" + ind).data("power_manager", frist_power[ind]);
				}
				$("#department_button_accept").attr("disabled", true);
			}
		}
	});
}

function move_user() {
	$.ajax({
		url : "/sale/department/listUser",
		type : "post",
		dataType : "JSON",
		success : function (data) {
			$("#user_content").data("listUser", data);
		}
	});
}

function check_data_cancle() { //检测数据是否有更改，取消时提示
	var check_t = "false";
	$("#department_edit_body_left a[state='modify']").each(function (i) {
		check_t = "true";
		return false;
	});
	$("#department_edit_body_left a[propertis='new_department']").each(function (i) {
		check_t = "true";
		return false;
	});
	if ($("#department_edit_body_left").data("del") != null) {
		if ($("#department_edit_body_left").data("del").length > 0) {
			check_t = "true";
		}
	}
	return check_t;
}
$(function(){
	var partnerTree,partnerNodes,departmentTree,departmentNodes,departmentIdA,departmentIdB
	var setting = {	// 设置
		check: {
			enable: true
		  , chkboxType: {"Y":"", "N":""}
		}
	  , data: {
			simpleData: {
				enable: true
			  , idKey: 'id'
			  , pIdKey: 'parentid'
			  , rootPId: ''
			}
		}
	};
	var ptnTree = {
		'name'	: 'partner'
	  , 'obj'	: partnerTree
	  , 'val'	: partnerNodes
	  , 'set'	: setting
	  , 'old'	: ''
	}
	var dptTree = {
		'name'	: 'department'
	  , 'obj'	: departmentTree
	  , 'val'	: departmentNodes
	  , 'set'	: setting
	  , 'old'	: ''
	}
	
	initEvt();
	initAct();
	
	function initAct(){
		if($('#departmentId').length < 1){
			return false;
		}
		departmentIdA = $('#departmentId');
		if($('#partnertype').val() == 'admin'){
			departmentIdB = $('#branchDepartmentID');
		}else{
			departmentIdB = $('#departmentId');
		}
		if($('#partnerid').val() != 0 && $('#partnerid').val() != undefined){
			getDepartment(ptnTree);
		}
		getDepartment(dptTree);
		
	}
	
	function initEvt(){
		// 客户改变事件
		$('#partnerid').live('change',function(){
			getDepartment(ptnTree);
		});
		// 客户部门点击隐藏背景事件
		$('#ptnTreeBg').live('click',function(){
			if(ptnTree.obj){
				departmentIdA.val(ptnTree.old);
				setStrings(ptnTree);
				hideTree();
			}
		})
		// 管理部门点击隐藏背景事件
		$('#dptTreeBg').live('click',function(){
			if(dptTree.obj){
				departmentIdB.val(dptTree.old);
				setStrings(dptTree);
				hideTree();
			}
		})
		// 客户部门保存按钮事件
		$('[name="ptnTreeAct"]').click(function(){
			setValue(ptnTree);
		})
		// 管理部门保存按钮事件
		$('[name="dptTreeAct"]').click(function(){
			setValue(dptTree);
		})
		// 客户输入框点击事件
		$('#selectInput').live('click',function(){
			if($('#partnerid').val() == 0){
				return false;
			}
			if($('#ptnTree').html() != ''){
				ptnTree.old = departmentIdA.val();
				setStrings(ptnTree);
				$('#ptnTreeBox').show();
				$('#ptnTreeBox').css({
					'position' : 'absolute',
					'top' : $('#selectInput').offset().top + 19,
					'left' : $('#selectInput').offset().left - 1,
					'z-index':'100'
				})
				$('#ptnTreeBox').before('<div id="ptnTreeBg" class="treeBg"></div>');
				$('#ptnTreeBg').css({
					'height':$(document).height(),
					'width':$(document).width(),
					'z-index':'100'
				});
			}
			$('#dptTreeBox').hide();
		});
		// 管理输入框点击事件
		$('#treeInput').click(function(){
			if($('#dptTree').html() != ''){
				dptTree.old = departmentIdB.val();
				setStrings(dptTree);
				$('#dptTreeBox').show();
				$('#dptTreeBox').css({
					'position' : 'absolute',
					'top' : $('#treeInput').offset().top + 19,
					'left' : $('#treeInput').offset().left,
					'z-index':'100'
				})
				$('#dptTreeBox').before('<div id="dptTreeBg" class="treeBg"></div>');
				$('#dptTreeBg').css({
					'height':$(document).height(),
					'width':$(document).width(),
					'z-index':'100'
				});
			}
			$('#ptnTreeBox').hide();
		})
		$('#selectInput').blur(function(){
			hideTree();
		})
		
		
		// 查询按钮事件
		$('#doSubmit').click(function(){
			document.conditionform.action="";
			document.conditionform.submit();
		})
	}
	// 获取当前客户下属部门
	function getDepartment(tree){
		switch(tree.name){
			case 'department' :
				$.ajax({
					url:'/admin/query/listClientDepartment',
					type:'post',
					dataType:'json',
					data:'clientID='+$('#partnerid').val(),
					success:function(clientJson){
						console.log("success" + clientJson);
						if(clientJson.length == 0){
							hideTree();
							$('#ptnTree').html('');
							$('[name="ptnTreeCancel"]').click();
							return false;
						}
						tree.val = clientJson;
						tree.obj = $.fn.zTree.init($("#ptnTree"),setting, tree.val);
						setStrings(tree);
						setValue(tree);
					}
				})
				break;
			case 'partner' :
				tree.val = getDepartmentJson();
				tree.obj = $.fn.zTree.init($("#dptTree"),setting, tree.val);
				setStrings(tree);
				setValue(tree);
				break;
		}
	}
	// 返回选中的id和name字符串
	function getStrings(tree){
		if(tree.obj){
			var nodes = tree.obj.getCheckedNodes(true);
			var idArr = [],nameArr = [];
			for(ind in nodes){
				if(nodes[ind].getCheckStatus().checked){
					idArr.push(nodes[ind].id);
					nameArr.push(nodes[ind].name);
				}
			}
			return {'ids':idArr.toString(),'names':nameArr.toString()}
		}
	}
	// 根据字符串设置树节点选中状态
	function setStrings(tree){
		switch(tree.name){
			case 'partner' :
				var nodeStr = departmentIdA.val();
				break;
			case 'department' :
				var nodeStr = departmentIdB.val();
				break;
		}
		if(nodeStr != ''){
			var nodeArr = nodeStr.split(',');
			tree.obj.checkAllNodes(false);
			for(ind in nodeArr){
				tree.obj.checkNode(tree.obj.getNodeByParam("id", nodeArr[ind], null), true, true);
			}
		}else{
			tree.obj.checkAllNodes(false);
		}
	}
	// 保存输入框文字
	function setInput(){
		if(getStrings(dptTree).names == ''){
			$('#treeInput').val('默认全选');
		}else{
			$('#treeInput').val(getStrings(dptTree).names);
		}
	}
	function setValue(tree){
		switch(tree.name){
			case 'partner' :
				departmentIdA.val(getStrings(tree).ids);
				break;
			case 'department' :
				departmentIdB.val(getStrings(tree).ids);
				setInput();
				break;
		}
		hideTree();
	}
	// 隐藏树
	function hideTree(){
		$('#ptnTreeBox').hide();
		$('#dptTreeBox').hide();
		$('.treeBg').remove();
	}
	
})
@charset "utf-8";
html,body{
    -webkit-font-smoothing: antialiased; /*chrome、safari*/
    -moz-osx-font-smoothing: grayscale;/*firefox*/
    font: 400 14px/20px Arial,sans-serif;
    color: #5f6368;
    letter-spacing: .01428571em;
    margin: 0;
    -webkit-font-feature-settings: 'liga';
}
ul{list-style:none;margin:0px;padding:0px;}
a{
    outline-style: none;
    text-decoration: none;
    color: #0080FF;
}
a.needcallback{
    /* 弹出框里的链接文字 */
    cursor: pointer;
    color: #0080FF;
}
a:hover{
    color: #FFB402;
}
table{
    border-collapse:collapse;
    border-spacing:0;
    border:none;
}
td{
    padding: 0;
    border: none;
}
input::-ms-clear { display: none; }
input::-webkit-contacts-auto-fill-button,
input::-webkit-credentials-auto-fill-button {
    visibility: hidden;
    pointer-events: none;
    position: absolute;
    right: 0;
}
select{height:24px;line-height:24px;outline:none;}
input[type=text],input[type=password],input[type=number]{box-sizing:border-box;height:24px;line-height:24px;}
input[type=checkbox],input[type=radio]{margin: 0;vertical-align: -1px;margin-right: 4px;}
input[type=radio]{position: relative;top: -1px;vertical-align: middle;width: 14px;}
.clearFormat:hover,.clearFormat:focus{background-image:url(
data:image/png;base64,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
);background-repeat:no-repeat;background-position:100% 50%;padding-right:20px !important;}
.clearFormatHover:hover,.clearFormatHover:focus{background-image:url(
data:image/png;base64,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
);cursor:pointer;}

/* 通用类名start-------------------------------------------------- */
.clearfix:after {
    clear: both;
    content: ".";
    display: block;
    height: 0;
    visibility: hidden;
}
.fleft{
    float: left;
}
.fright{
    float: right;
}
/* 通用类名start-------------------------------------------------- */


/* 谷歌提供的图标库start-------------------------------------------------- */
@font-face {
    font-family: 'Material Icons Extended';
    font-style: normal;
    font-weight: 400;
    src: url(../other/MaterialIconsExtended.eot); /* For IE6-8 */
    src: local('Material Icons Extended'),
    local('Material Icons Extended'),
    url(../other/MaterialIconsExtended.woff2) format('woff2'),
    url(../other/MaterialIconsExtended.woff) format('woff');
}
@font-face {
    font-family: 'Material Icons Outlined';
    font-style: normal;
    font-weight: 400;
    src: local('Material Icons Outlined'),
    local('Material Icons Outlined'),
    url(../other/MaterialIconsOutlined.ttf) format('ttf'),
    url(../other/MaterialIconsOutlined.woff) format('woff');
}
.materialIcons,.materialIconsOutlined{
    font-family: 'Material Icons Extended';
    letter-spacing: 0;

    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;

    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;

    /* Support for IE. */
    font-feature-settings: 'liga';
}
.materialIconsOutlined{
    font-family: 'Material Icons Outlined';
}
/* 谷歌提供的图标库end-------------------------------------------------- */
/* dateTime还有其他一部分的输入信息，可能会需要禁用输入法start */
input[dateTime], .imeMode{
    -ms-ime-mode: disabled;
    ime-mode:disabled;
}
/* dateTime还有其他一部分的输入信息，可能会需要禁用输入法end */

/* 通用的做菜单栏start-------------------------------------------------- */
.col_side {
    background:#f8f9fa;
    width:235px;
    overflow-y: scroll;
    position: absolute;
    top: 65px;
    left: 0;
    bottom: 0;
    z-index:1;
    padding:10px 5px 20px 0;
}
.col_side::-webkit-scrollbar {
    transition-duration: 1s;
    background-color: #f8f9fa;
    width: 16px;
    height: 0;
}
.col_side::-webkit-scrollbar-thumb {
    background-color: transparent;
}
html::-webkit-scrollbar {
    transition-duration: 1s;
    background-color: #f8f9fa;
    width: 2px;
    height: 1px;
}
html::-webkit-scrollbar-track {
    border-radius: 1px;
}
html::-webkit-scrollbar-thumb {
    box-shadow: 0 0 10px 10px #1a73e8;
    border-radius: 1px;
    background-color: #1a73e8;
    transition: background-color linear 1s;
}

.col_side .menu{height:40px;line-height:40px; font-weight: 500; position:relative;border-left:5px solid transparent; z-index:2;cursor: pointer;overflow: hidden;border-top-right-radius: 20px;border-bottom-right-radius: 20px;margin-right: 10px;}
.col_side .menu:hover,.col_side .menu.over {background-color:#e5e5e5;}
.col_side .mainMenu.mainLinked{background-color: #e8eaed;}
.col_side .menu:hover a{color:#3c4043;}
.col_side .menu.linked{background-color: #e8f0fe;border-left-color: #1a73e8;}
.col_side .menu.linked a{color: #1a73e8;}
.col_side .subordinateMenu{display: none}
.col_side .subordinateMenu .menu{height:32px;line-height:32px;border-top-right-radius: 16px;border-bottom-right-radius: 16px;}
.col_side .mainMenu .mainMenuIcon{display: inline-block;background: url(../imgs/nav_closed.gif) no-repeat left center;height: 10px;width: 1em;}
.col_side .mainMenu.selected .mainMenuIcon{background-image: url(../imgs/nav_opened.gif);}
.col_side > .menu .fa{display: inline-block; margin-right: 16px;height: 24px;line-height: 20px;width: 20px;text-align: center;}
.col_side a{display:block;color: rgba(0,0,0,0.54);font-size: 12px;text-decoration:none;padding-left:30px;}
.col_side .subordinateMenu a{padding-left:54px;}
.col_side .subordinateMenu .mainMenu a{padding-left:42px;}
.col_side .subordinateMenu .subordinateMenu a{padding-left:66px;}
.col_side .subordinateMenu .subordinateMenu .mainMenu a{padding-left:54px;}
.col_side .subordinateMenu .subordinateMenu .subordinateMenu a{padding-left:80px;}
.col_side .subordinateMenu .subordinateMenu .subordinateMenu .mainMenu a{padding-left:66px;}


.col_scrollbar {
    transition-duration: 0.5s;
    z-index: 2;
    width: 1px;
    position: absolute;
    top: 65px;
    left: 225px;
    bottom: 0;
    background-color: #1a73e8;
    opacity: 0;
}
/*.col_side:hover + .col_manipulate {*/
/*	opacity: 1;*/
/*}*/
.col_scrollbar_thumb {
    transition: top 1s;
    z-index: 3;
    /*transition-duration: 1s;*/
    background-color: #f8f9fa;
    position: absolute;
    width: 9px;
    height: 25px;
    /*opacity: 1;*/
    border: solid 1px #ffb408;
    border-radius: 58px;
    left: -5px;
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select:none;
    -khtml-user-select:none;
    -webkit-user-select:none;
    -ms-user-select:none;
    user-select:none;
}
.col_scrollbar_opa {
    width: 100%;
    height: 100%;
    z-index: 1;
    position: fixed;
    top: 0;
    left: 0;
    display: none;
}
.col_scrollbar_track {
    z-index: 1;
    width: 18px;
    position: absolute;
    top: 65px;
    left: 223px;
    bottom: 0;
    background-color: #f8f9fa;
}
/* 通用的做菜单栏end--------------------------------------------------    */

/* 新式的输入框和下拉框样式start ------------------------------------------- */
/* 所需样本参照common.js的相关样例 */
.widthMeasuring{
    height: 0;
    letter-spacing: .2px;
    font-size: 14px;
    margin: 0;
    padding: 0;
    border: none;
    box-shadow: none;
    position: absolute;
    opacity: 0;
    z-index: -1;
    white-space: nowrap;
}
/* 主要是为了两边对齐 */
.flexFieldContainer{
    display: -webkit-flex;
    display: flex;
    display: -ms-flexbox;
}
.flexFieldContainer > .fieldContainer{
    width: 0\9;
    -ms-flex: auto;
    margin-left: 10px;
    margin-right: 10px;
}
.margin0.flexFieldContainer > .fieldContainer{
    margin-left: 0;
    margin-right: 0;
}
.flexFieldContainer > .fieldContainer:first-child{
    margin-left: 0;
}
.flexFieldContainer > .fieldContainer:last-child{
    margin-right: 0;
}
/* 两个框共享一个下划线，一般是前面选择后面填写start */
.combinedFieldContainer{
    width: 100%;
    position: relative;
}
.combinedFieldContainer .fieldContainer .underline{
    left: 0;
    right: 0;
    position: absolute;
}
.combinedFieldContainer .fieldContainer .field:focus ~ .underline{
    z-index: 1;
}
.combinedFieldContainer .textWidth{
    white-space: nowrap;
    position: absolute;
    opacity: 0;
    height: 0;
}
.flexFieldContainer.combinedFieldContainer .fieldContainer{
    margin-right: 0;
    margin-left: 0;
}
/* 两个框共享一个下划线，一般是前面选择后面填写end */
/* 在同一个combinedFieldContainer内的fieldContainer，如果都没填写的话，就显示第一个输入框，如果有填充的话，再显示所有输入框start */
/* cascadeFieldContainer仅控制显隐问题，不涉及间隔和长度等问题，这个在各个其他的class进行实现 */
.cascadeFieldContainer > .cascadeFieldItem:not(:first-child)
{
    display: none;
}
.cascadeFieldContainer.cascadeFilled > .cascadeFieldItem:not(:first-child) {
    display: table-cell;
}
/* 在同一个combinedFieldContainer内的fieldContainer，如果都没填写的话，就显示第一个输入框，如果有填充的话，再显示所有输入框end */

.fieldContainer{
    width: 100%;
    margin: 15px 0 10px;
    display: inline-block;
}

.fieldContainer .field{
    border: none;
    outline: none;
    box-shadow: none;
    padding: 0;
    width: 100%;
    font-size: 14px;
    letter-spacing: .2px;
    color: #5f6368;
    margin: 0; /* button自带margin，所以需要清除掉 */
}
/* 测定input或者select占用的实际宽度 */
.fieldContainer .fieldAdaptive,.fieldContainer .fieldAdaptiveWrapper{
    letter-spacing: .2px;
    height: 0px;
    overflow: hidden;
    white-space: nowrap;
    margin: 0;
    padding: 0;
}
.fieldContainer .fieldAdaptiveWrapper{
    width: 0;
    transition: width .2s;
}
.fieldContainer .fieldAdaptiveWrapper~input.field:focus{
    width: calc(100% + 24px);
    background-color: transparent;
}
.fieldContainer .fieldAdaptive{
    position: absolute;
}
.fieldContainer .field:focus{
    border: none;
    outline: none;
}
.fieldContainer.inputSelect{
    position: relative;
}
.fieldContainer .field[type=number]::-webkit-outer-spin-button,
.fieldContainer .field[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
.fieldContainer .field[type=number]{
    -moz-appearance: textfield;
}
.fieldContainer.inputSelect input{
    position: absolute;
    padding-left: 0px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyNCIgd2lkdGg9IjIwcHgiIGhlaWdodD0iMjRweCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDAwIj48cGF0aCBkPSJNMSAxMkw3IDE4TDEzIDEyIi8+PC9zdmc+') no-repeat right;
    /*background-size: 15px 5px;*/
    opacity: 0;
    z-index: 1;
}
.fieldContainer.inputSelect.noDropdown input{
    background-image: none;
}

.fieldContainer.inputSelect:hover input,.fieldContainer.inputSelect input:focus{
    opacity: 1;
}
.fieldContainer.inputSelect:hover select,.fieldContainer.inputSelect input:focus~select{
    opacity: 0;
}


.fieldContainer select.field:invalid{
    color: #9e9e9e;
}
/* 兼容IE的写法，如果把这个和上面的合并，会导致IE中上面的全部失效 */
.fieldContainer .field::placeholder{
    color: #9e9e9e;
}
.fieldContainer .field:-ms-input-placeholder
{
    color: #9e9e9e;
}
.fieldContainer select.field{
    padding-left: 0px;
    background-color: transparent;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyNCIgd2lkdGg9IjIwcHgiIGhlaWdodD0iMjRweCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDAwIj48cGF0aCBkPSJNMSAxMkw3IDE4TDEzIDEyIi8+PC9zdmc+') no-repeat right;
    /*background-size: 15px 5px;*/
    appearance:none;
    -webkit-appearance: none;
    -moz-appearance:none;
}
@-moz-document url-prefix(){
    .fieldContainer select.field{
        text-indent: -2px;
    }
}

.fieldContainer select.field::-ms-expand {
    display: none;
}

.fieldContainer.noDropdown select.field{
    background-image: none;
}
.combinedFieldContainer .fieldContainer{
    transition: all .2s;
    height: 27px; /* combinedField的下划线脱离了文档流，所以少了3像素的高度，需要把这3像素的高度加回来 */
}
.combinedFieldContainer .fieldContainer select.field{
    transition: all .2s;
    background-image: none;
}
.combinedFieldContainer:hover .fieldContainer select.field,.combinedFieldContainer:hover .fieldContainer select.field:invalid~.placeholder{
    color: #1a73e8;
}

.fieldContainer .placeholder{
    position: absolute;
    margin-top: -22px;
    color: #9e9e9e;
    font-size: 14px;
    line-height: normal;
    pointer-events: none;
    transition: all .2s;
    transform-origin: top left;
    letter-spacing: .2px;
}
.fieldContainer .placeholder.transitionDuration0{
    transition-duration: 0ms;
}

.fieldContainer .field:focus + .placeholder,
.fieldContainer .field:valid + .placeholder,
.fieldContainer .field[filled="true"] + .placeholder,
.fieldContainer .radioContainer ~ .placeholder,
.fieldContainer .field:focus + .placeholderWrapper .placeholder,
.fieldContainer .field[filled="true"] + .placeholderWrapper .placeholder,
.fieldContainer .field:valid + .placeholderWrapper .placeholder{
    transform: scale(0.75) translateY(-20px);
}
.fieldContainer .field:focus + .placeholder,
.fieldContainer .field:focus + .placeholderWrapper .placeholder{
    color: #4285f4;
}

.fieldContainer.required .underline1{
    position: relative;
    float: left;
    width: 10px;
    height: 0px;
}
.fieldContainer.required .underline1:before{
    content: " ";
    position: absolute;
    width: 10px;
    height: 1px;
    /*background-color: red;*/
    right: 0;
}
.fieldContainer.required .field:focus ~ .underline .underline1:before{
    height: 2px;
    -webkit-animation: quantumWizPaperInputAddHeight .3s cubic-bezier(0.4,0,0.2,1);
    animation: quantumWizPaperInputAddHeight .3s cubic-bezier(0.4,0,0.2,1);
}
.fieldContainer .underline{
    margin-top:2px;
    height:1px;
    background-color: #e0e0e0;
    position: relative;
}

.fieldContainer .field.errorField,
.fieldContainer .field.errorField:invalid,
.fieldContainer .field.errorField ~ .placeholder{
    color: red;
}
/* 兼容IE的写法，如果把这个和上面的合并，会导致IE中上面的全部失效 */
.fieldContainer .field.errorField::placeholder{
    color: red;
}
.fieldContainer .field.errorField:-ms-input-placeholder{
    color: red;
}

/*.fieldContainer .field.errorField ~.underline,.fieldContainer .field.errorField ~.underline:after{
    background-color: red;
}
.fieldContainer .field.emptyField ~.underline,.fieldContainer .field.emptyField ~.underline:after{
    background-color: red;
}*/
.fieldContainer .field.errorMessage ~.underline,.fieldContainer .field.errorMessage ~.underline:after{
    background-color: #ffb408;
}

/* dateTime在readonly后自制光标start */
#dateTimeShowWidth{
    position: absolute;
    width: 0!important;
    height: 0!important;
    margin: 0!important;
    padding: 0!important;
    text-indent: 0!important;
    z-index: -1;
    border: none;
    outline: none;
    pointer-events: none;
}
.showDateTimePointer{
    position: absolute;
    height: 15px;
    width: 1px;
    background-color: #666;
    margin-top: 4px;
    animation:dateTimeCursorBlink 1.2s linear infinite;
    display: none;
    pointer-events: none;
}
/* dateTime在readonly后自制光标end */
.fieldContainer .field ~ .underline:after{
    content: " ";
    background-color: #4285f4;
    height: 2px;
    /*margin-top:-1px;*/
    width:0;
    display: block;
    overflow: hidden;
    /*-webkit-animation: quantumWizPaperInputRemoveUnderline .3s cubic-bezier(0.4,0,0.2,1);
    animation: quantumWizPaperInputRemoveUnderline .3s cubic-bezier(0.4,0,0.2,1);*/
}

.fieldContainer .field:focus ~ .underline:after{
    -webkit-animation: quantumWizPaperInputAddUnderline .3s cubic-bezier(0.4,0,0.2,1);
    animation: quantumWizPaperInputAddUnderline .3s cubic-bezier(0.4,0,0.2,1);
    width:100%;
}
.fieldContainer .errorTips.field ~ .underline:before{
    content: " ";
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: red;
    animation: errortipsWizPaperInputAddUnderline .6s;
}
@keyframes errortipsWizPaperInputAddUnderline{
    59% {
        margin-left: 0
    }

    60%,80% {
        margin-left: 5px
    }

    70%,90% {
        margin-left: -5px
    }
}
.fieldContainer .underline .showmessages{
    transform: scale(0.75);
    color: red;
    transform-origin: top left;
    position: absolute;
    /*height: 0;*/
    /*animation: errorShow 2.5s;*/
    overflow: hidden;
}
@keyframes errorShow {
    0%{height: 0}
    20%{height: 25px;}
    80%{height: 25px;}
    100%{height: 0;display: none;}
}
@keyframes dateTimeCursorBlink{0%{opacity: 1}50%{opacity: 1}51%{opacity: 0}100%{opacity: 0}}
@keyframes quantumWizPaperInputAddHeight{0%{height: 1px}to{height: 2px}}
@-webkit-keyframes quantumWizPaperInputAddHeight{0%{height: 1px}to{height: 2px}}
@keyframes quantumWizPaperInputRemoveUnderline{0%{width:100%;opacity:1}to{width:100%;opacity:0}}
@-webkit-keyframes quantumWizPaperInputRemoveUnderline{0%{width:100%;opacity:1}to{width:100%;opacity:0}}
@keyframes quantumWizPaperInputAddUnderline{0%{width:0;margin-left:50%;}to{width:100%;margin-left:0;}}
@-webkit-keyframes quantumWizPaperInputAddUnderline{0%{width:0;margin-left:50%;}to{width:100%;margin-left:0;}}
/* 下拉框下拉的样式 */
.fieldContainer.standardHeight,.radioContainer.standardHeight,.fieldContainer .standardHeight{
    /* input输入框目前的标准高度是：行高24px，下边距3px */
    line-height: 24px;
}
.fieldContainer select option{
    display: none;
}
.fieldContainer .options{
    position: relative;
    height: 0;
}
.fieldContainer .options .topDirection{
    bottom: 28px;
    border-radius:8px 8px 0 0;
}
.fieldContainer ul{
    position: absolute;
    z-index: 999;
    left: 0;
    max-width: 100%;
    /*right: 0;*/
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 0 0 8px 8px;
    padding: 8px 0;
    max-height: 300px;
    overflow: auto;
    display: none;
    box-shadow: 0 2px 4px #ccc;
}
.fieldContainer.unlimitedWidth ul{
    min-width: 175px;
    right: auto;
    max-width: unset;
}
.fieldContainer.fullWidth ul{
    right: 0;
}
.fieldContainer ul li{
    overflow: hidden;
    padding: 7px 22px;
    line-height: 23px;
}
.fieldContainer ul li[likeTitle]{
    border-bottom: 1px solid #ddd;
}

.fieldContainer ul li:hover{
    background-color: #f1f1f1;
}
.fieldContainer ul li[likeTitle]:hover{
    background-color: #fff;
}


.fieldContainer li .optionShow{
    color:#222;
    cursor: default;
    /*font-size: 16px;*/
    position: relative;
    font-size: 13px;
    text-align: left;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
}
.fieldContainer ul li[likeTitle] .optionShow{
    color: #1a73e8;
}
.fieldContainer li .optionShow .radioContainer{
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}
.fieldContainer ul li[likeTitle] .radioContainer{
    display: none;
}
.fieldContainer li .optionShow .optionContent{
    margin-left: 16px;
}
.fieldContainer li[likeTitle] .optionShow .optionContent{
    margin-left: 0;
    text-decoration: underline;
    cursor: pointer;
}
.fieldContainer li .optionMainTitle{
    margin-bottom: -3px;
}
.fieldContainer li .titleTips{
    font-size: 10px;
    margin-left: 5px;
    color: #9e9e9e;
}
.fieldContainer li .optionSubTitle{
    line-height: 16px;
    font-size: 12px;
    color: #5f6368;
}
/* checkbox的统一样式start */
.fieldContainer.checkboxContainer{
    margin: 0;
    width: 15px;
    height: 15px;
    line-height: 15px;
    text-indent: -2px;
}
.fieldContainer .field[type="checkbox"]{
    display: none;
}
.fieldContainer .checkboxShow{
    color: #666;
}

.fieldContainer .checkboxShow:after{
    content: "check_box_outline_blank";
    font-family: "Material Icons Outlined";
    letter-spacing: 0;
    display: block;
    font-size: 19px;
    text-align: center;
    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;
    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;
    /* Support for IE. */
    font-feature-settings: 'liga';
}

.fieldContainer .field:disabled ~ .checkboxShow{
    color: #b8b8b8;
}
.fieldContainer .field:checked ~ .checkboxShow,
.fieldContainer .field:indeterminate ~ .checkboxShow{
    color: #1a73e8;
}
.fieldContainer .field.error ~ .checkboxShow,
.fieldContainer.styleThin .field.error ~ .checkboxShow {
    color: red;
}
.fieldContainer .field:checked ~ .checkboxShow:after{
    content: "check_box";
}
.fieldContainer .field:indeterminate ~ .checkboxShow:after{
    content: "indeterminate_check_box";
}
.fieldContainer.styleThin{
    width: auto;
}
.fieldContainer.styleThin .checkboxShow{
    color: #bbb;
}
.fieldContainer.styleThin .checkboxShow:before{
    content: " ";
    display: block;
    border: 1px solid currentColor;
    width: 12px;
    height: 12px;
    position: absolute;
    border-radius: 1px;
    transition: border .2s;
}
.fieldContainer.styleThin .checkboxShow:after{
    visibility: hidden;
    font-size: 19px;
    text-indent: -2px;
    height: 14px;
    line-height: 14px;
}
.fieldContainer.styleThin .field:disabled ~ .checkboxShow{
    color: #ddd;
}
.fieldContainer.styleThin .field:checked ~ .checkboxShow:after{
    visibility: visible;
    content: "check";
    transform: scale(.7) translateX(-1px);
}

/* checkbox的统一样式end */
/* 仅仅只有黑色三角的下拉，不显示下拉的类容start */
.fieldContainer.onlyTriangle{
    width: 14px;
    margin: 0;
}
.fieldContainer.onlyTriangle .field{
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    position: absolute;
}
.fieldContainer .shapeTriangle{
    color: #666;
}
.fieldContainer .shapeTriangle:after{
    content: "expand_more";
    font-family: "Material Icons Extended";
    letter-spacing: 0;
    display: block;
    height: 20px;
    line-height: 20px;
    font-size: 26px;
    cursor: pointer;
    text-align: center;
    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;

    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;

    /* Support for IE. */
    font-feature-settings: 'liga';
}
.fieldContainer .field.checked~.shapeTriangle{
    color: #1a73e8;
}
.fieldContainer .field:disabled~.shapeTriangle{
    color: #b8b8b8;
}
/* 仅仅只有黑色三角的下拉，不显示下拉的类容end */
/* radio整体需要向下移动，因为要和线对齐，不是字对齐 */
.radioContainer.fieldContainer{
    width: auto;
    margin: 0;
}
.radioContainer .radio,.radioContainer input[type=radio]{
    display: none;
}
.radioContainer .radioShow{
    width: 11px;
    height: 11px;
    border-radius: 50%;
    border:1px solid #666;
    display: inline-block;
    position: relative;
    top: 1px;
    margin-right: 3px;
}
.radioContainer .radio:checked ~ .radioShow, .radioContainer input[type=radio]:checked ~ .radioShow{
    border-color: #1a73e8;
}
.radioContainer .radio:checked ~ .radioShow:before, .radioContainer input[type=radio]:checked ~ .radioShow:before{
    content: " ";
    display: block;
    width: 7px;
    height: 7px;
    top: 2px;
    left: 2px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #1a73e8;
    position: absolute;
}
/* radio字的颜色变化 */
.radioContainer .title{
    color: #b8b8b8;
}
.radioContainer .radio:checked ~ .title, .radioContainer input[type=radio]:checked ~ .title{
    color: #5f6368;
}
/* radio字的颜色变化 */
/* 带有title的input进行特殊处理start */
.tableContainer{
    display: table;
}
.tableContainer .tableCellContainer{
    display: table-cell;
    width: 0;
}
.tableContainer .tableCellContainer:last-child{
    width: 100%;
}
.titleContainer{
    width: 100%;
    margin:15px 0 10px;
    position: relative;
}
.titleContainer .fieldTitle{
    color:#5f6368
}
.titleContainer .fieldTitle, .titleContainer .fieldTitleBackup,
.titleContainer .placeholder{
    height: 24px;
    line-height: 24px;
    white-space: nowrap;
}
.titleContainer .fieldTitleBackup{
    color: transparent;
    /*display: inline-block;
    transition: all .2s;
    width: 100%;
    flex: 0;*/
}
.titleContainer .placeholder{
    top: 0;
    margin-top: 0;
    display: inline-block;
}
.placeholderWrapper{
    position: absolute;
    left: 0;
    top: 0;
}
.fieldContainer .field:focus + .placeholderWrapper .fieldTitleBackup,
.fieldContainer .field:valid + .placeholderWrapper .fieldTitleBackup,
.fieldContainer .field[filled="true"] + .placeholderWrapper .fieldTitleBackup{
    width: 0;
}
.placeholderWrapper.placeholderTable{
    display: table;
}
.placeholderTable .placeholderCell{
    display: table-cell;
}
.titleContainer .underline{
    left: 0;
    right: 0;
    position: absolute;
    bottom: -2px;
}

/* 带有title的input进行特殊处理end   */
/* 新式的输入框和下拉框样式end ------------------------------------------- */

/* 列表式内容样式start------------------------------------------------------------------------------------- */
.table_msg {border:none;border-radius: 3px;padding-right:10px;width: 90%;margin: 0 auto;padding: 0 2em;}
.table_msg table {background-color: #FFFFFF;border-collapse: collapse;border-radius: 3px;text-align: center;width: 100%;}
.table_msg .borderhead th{border: 1px solid #D3D3D3;line-height: 32px;}

.table_msg .tbhead tr{border-top:none;}
.thead .table_cell,.tbhead th{line-height: 42px;font-size:14px;font-family:microsoft yahei;color:#777;}
.table_cell {font-style: normal;font-weight: 400;padding: 0;}
.thead .table_cell:first-child {border-left-width: 0;}
.table_msg thead th {padding-left: 2px;padding-right: 2px;text-align: left;vertical-align:middle;font-weight:400;}
.table_msg thead tr{border-top:none;}
.table_msg tbody .tr_on{background-color:#fefefe;border-top:1px dashed #ddd;}
.table_msg tbody .first_tr{border-top: 1px solid #f0f0f0;white-space: nowrap;}
.table_msg tbody .first_tr:first-child{border: none}
.table_msg tbody .first_tr.selected{border-top-color: #f8f9fa;background-color: #f9f9f9;}
.table_msg tbody .first_tr:hover,.table_msg .first_tr_on{background-color:#f5f5f5;}
.table_msg tr {border-top: 1px solid #dcdcdc;vertical-align:middle;font-size:12px;}
.no_pad table tr {border-top: none;}
.first_tr_last {border-bottom:1px solid #ccc;}
.table_msg a{color:#0066ff;margin:0 3px;}
.table_msg tbody td {height: 18px;line-height: 18px;padding: 12px 2px; text-align: left;color:#888;}
.table_msg tbody .tcenter,.table_msg thead .tcenter{text-align:center;}
.table_msg .tbody a{color: #2E7DC6;margin:0 5px;}
.table_msg .moneyTxt{text-align:right;padding-right:8px;}/* 金额又靠齐保留2位小数 */
/* 列表式内容样式end------------------------------------------------------------------------------------- */

/* 弹出层样式start---------------------------- */
.info_edit_box{width:600px;position:absolute;background-color:#fff;border:1px solid #ccc;left:220px;z-index:8;display:none;/* box-shadow:0 0 5px #ccc; */}
.info_edit_head{background-color: #E7E7E7;background-image: linear-gradient(to bottom, #F2F2F2 0px, #E0E0E0 100%); border-bottom: 1px solid #C2C2C2;height:35px;line-height:35px;padding:0px 15px;}
.info_edit_title{float:left;font-weight:800;font-size:14px;}
div.info_edit_close{font-size:18px;float:right;cursor:pointer;font-weight:800;height:35px;}
.info_edit_body{padding:15px;}
.info_edit_label{height:35px;text-align:left;border-bottom:1px solid #ccc;position:relative;z-index:0;}
.info_edit_label span { position:relative;z-index:1;margin-left:1px;top:10px;height:24px;line-height:24px;display:inline-block;padding-left:15px;padding-right:15px;cursor:pointer;border:1px solid #ccc; background-color:#ECEEFF;}
.info_edit_label .info_edit_select {border-bottom:1px solid #fff;background-color:#FFF;}
.info_edit_box tr{height:1em;line-height:1em;}
.info_edit_box td{height:1em;line-height:1em;padding:5px 0px;vertical-align:middle;}
.beneRows td{padding:0;}
.info_edit_body table{width:100%;}
.info_edit_body thead{border-bottom:1px solid #ccc;}
.info_edit_body input:text,.info_edit_body select{width:200px;}
.info_edit_body textarea{width:400px;}
.info_edit_body a:hover{text-decoration: underline;}
.info_edit_foot{background-color:#F0F0EE;height:40px;line-height:40px;text-align:center;border-top: 1px solid #CCCCCC;}
.info_edit_foot input{margin:0 10px;}
.info_edit_save,.info_edit_cancel{cursor: pointer;}
/* 弹出层样式end--------------------------------------------------------------------------------------- */
/* 保单查询后的操作按钮，收银台的支付方式的菜单列表样式start------------------- */
.listMenuHeader{
    height: 44px;
    line-height: 44px;
    padding-left: 24px;
    background-color: #f8f9fa;
}
.listMenuHeader.empty{
    height: 32px;
}
.listMenuHeader .listMenuName{
    color: #999;
    font-size: 12px;
    line-height: 20px;
    position: relative;
    top: 7px;
    font-weight: 800;
}
.listMenuBody{
    background-color: #fff;
}
.listSubMenu{
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    padding-left: 48px;
}
.listSubMenuBox{
    height: 44px;
    line-height: 44px;
    border-top: 1px solid #ddd;
    margin-top: -1px;
    color: #3b7bea;
}

/* 收银台微信点击支付宝时提示页面的样式start */
.alipayTips{
    position:fixed;
    top:0;
    left:0;
    bottom:0;
    right:0;
    background-color:rgba(0,0,0,0.65);
    z-index: 999;
    display:none;
}
.alipayTips img{
    float:right;
    margin-right:0.2rem;
    margin-top:0.2rem;
}
.alipayTips .alipayTipsBody{
    position:absolute;
    top:0.72rem;
    right:0.6rem;
    color:#fff;
    text-align:left;
    font-size:0.14rem;
}
.alipayTips .alipayTipsOpen{
    padding:0.02rem 0.08rem;
    margin:0 0.05rem;
    border-radius:0.04rem;
    background-image: linear-gradient(to bottom,#ccc 0%,#666 15%,#111 100%);
    display:inline-block;
    height:0.18rem;
    line-height:0.18rem;
}
.alipayTips .androidImg,.alipayTips .iphoneImg{
    display:inline-block;
    width:0.15rem;
    height:0.15rem;
    background:url(../imgs/mobile/browseIco.png) no-repeat;
    vertical-align: -3px;
    margin-right:0.05rem;

}
.alipayTips .iphoneImg{
    background-position:0 -25px;
}
/* 收银台微信点击支付宝时提示页面的样式end */
/* 保单查询后的操作按钮，收银台的支付方式的菜单列表样式end------------------- */
/*-- ----遮罩层样式----  */
.overlayContainer{position:fixed;z-index:99;left:0;top:0;width:100%;height: 100%;display: none;}
.overlayContainer iframe{border: none;}
.overlayOpa {background-color: #000;opacity: 0.32;position: absolute;width: 100%;height: 100%;}
.overlayContent{position: absolute;left: 200px;top: 0;bottom: 0;right: 0;width: calc(100% - 200px);background-color: #fff;}
@media screen and (max-width: 1024px){
    .overlayContent{
        width: 100%;
    }
}
.overlayContainer.fullWidth .overlayContent{left: 0;width: 100%;}
.overlayContentHead{
    position: absolute;
    width: 100%;
    height: 63px;
    line-height: 64px;
    background-color: #fff;
    border-bottom: 1px solid #dadce0;
    z-index: 3;
}

.overlayClose, .overlayClose:hover{
    position: absolute;
    top:50%;
    left: 20px;
    margin-top: -20px;
    margin-left: 0;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50%;
    text-align: center;
    color: #80868b;
    font-size: 24px;
    font-family: "Material Icons Extended";
    letter-spacing: 0;
    cursor: pointer;
    padding: 0;
    background-color: transparent;
    background-image: none;
    box-shadow: none;
    border: none;
    outline: none;
    transition: color,background-color .5s;
    /* Support for all WebKit browsers. */
    -webkit-font-smoothing: antialiased;
    /* Support for Safari and Chrome. */
    text-rendering: optimizeLegibility;

    /* Support for Firefox. */
    -moz-osx-font-smoothing: grayscale;

    /* Support for IE. */
    font-feature-settings: 'liga';
}
.overlayClose:hover{
    color: #000;
    background-color: #ddd;
}
.overlayEditContainer{
    position: absolute;
    top: 0;
    right: 30px;
}
.overlayContentHead .overlayMenuButton{
    /*border: 1px solid #1a73e8;
    background-color: #1a73e8;*/
    border:none;
    outline: none;
    background-color: #000066;
    background-image: none;
    color: #fff;
    height: 36px;
    line-height: 36px;
    margin-right: 22px;
    padding: 0 24px 0 24px;
    -webkit-transition: 280ms cubic-bezier(0.4,0,0.2,1),box-shadow 280ms cubic-bezier(0.4,0,0.2,1);
    transition: 280ms cubic-bezier(0.4,0,0.2,1),box-shadow 280ms cubic-bezier(0.4,0,0.2,1);
    -webkit-transition-property: color,border,background-color,box-shadow;
    transition-property:color,border,background-color,box-shadow;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;

}

/*.overlayContentHead .overlayMenuButton:hover{
    background-color: #1b67cc;
    border-color: #1b67cc;
}
.overlayContentHead .overlayMenuButton:active{
    background-color: #1c5bb0;
    border-color: #1c5bb0;
}*/
.overlayContentHead .overlayMenuButton:disabled{
    background-color: #808080;
    border-color: #808080;
}

.overlayMenuButton.closeandsave{
    font-size: 20px;
    color: #006;
    padding: 0 16px 0 16px;
    margin-right: 10px;
}
.overlayMenuButton.deleallbene{
    font-size: 20px;
    color: red;
    padding: 0 16px 0 16px;
    -webkit-transition: 2000ms cubic-bezier(0.4,0,0.2,1),background-color 1500ms cubic-bezier(0.4,0,0.2,1);
    transition: 2000ms cubic-bezier(0.4,0,0.2,1),background-color 1500ms cubic-bezier(0.4,0,0.2,1);
}
.overlayMenuButton.nextStepButton{
    font-size: 26px;
    color: #006;
    padding: 0 10px 0 16px;
}

.overlayMenuButton.deleallbene:hover{
    color: #fff;
    background-color: red;
    -webkit-box-shadow: 0 3px 1px -2px #ccc, 0 2px 2px 0 #dbdbdb, 0 1px 5px 0 #dbdbdb;
    box-shadow: 0 3px 1px -2px #ccc, 0 2px 2px 0 #dbdbdb, 0 1px 5px 0 #dbdbdb;
}

.overlayMenuButton.nextStepButton, .overlayMenuButton.closeandsave, .overlayMenuButton.deleallbene{
    position: relative;
    height: 32px;
    line-height: 32px;
    vertical-align: middle;
    background-color: #fff;
}
.overlayMenuButton.nextStepButton.prevStepButton{
    transform: scaleX(-1);
}
.overlayMenuButton.nextStepButton .forwardLine{
    position: absolute;
    right: 13px;
    top: 10px;
    bottom: 10px;
    width: 2px;
    background-color: #006;
    transition:background-color 280ms cubic-bezier(0.4,0,0.2,1),box-shadow 280ms cubic-bezier(0.4,0,0.2,1);
}
.overlayMenuButton.nextStepButton:hover .forwardLine{
    background-color: #fff;
}

.overlayMenuButton.nextStepButton:hover, .overlayMenuButton.closeandsave:hover{
    color: #fff;
    background-color: #006;
    -webkit-box-shadow: 0 3px 1px -2px #ccc, 0 2px 2px 0 #dbdbdb, 0 1px 5px 0 #dbdbdb;
    box-shadow: 0 3px 1px -2px #ccc, 0 2px 2px 0 #dbdbdb, 0 1px 5px 0 #dbdbdb;
}

.nextStepButtonBlue{
    line-height: 32px;
    background-color: #006;
    background-image: none;
    color: #fff;
    padding: 0 10px 0 13px;
    box-shadow: none;
    border-radius: 3px;
    border: none;
    outline: none;
    cursor: pointer;
    transition: background-color .2s linear;
}
.nextStepButtonBlue:hover{
    background-color: #44448f;
    border: none;
}
.nextStepButtonBlue:active{
    background-color: #000055;
}

.overlayTitleBox{
    float: left;
    height: 40px;
    font-size: 22px;
    line-height: 40px;
    margin-top: 12px;
    margin-left: 70px;
    color: #ff8e07;
}
.overlaySubtitle {
    height: 16px;
    line-height: 16px;
    color: #5f6368;
}
.overlayTitle{
    line-height: 28px;
    font-family: Helvetica, Arial,sans-serif;
}
.overlaymainTitle {
    color: #222;
    font-size: 22px;
    line-height: 28px;
}
.overlaySummary {
    color: #5f6368;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: .3px;
    margin: 0 8px;
}
.scrollShadow {
    -webkit-transition: box-shadow .1s ease-in-out;
    transition: box-shadow .1s ease-in-out;
}
.overlayCategoryMenus {
    margin-top: 62px;
    height: 2px;
    background-color: transparent;
    position: relative;
    z-index: 1;
}
.noneCategoryMenus .overlayCategoryMenus{
    height: 2px;
}
.scrollShadowOn {
    -webkit-box-shadow: 0 0 10px silver;
    box-shadow: 0 0 10px silver;
}
/* 分页标签样式start----------------------------------------------- */
.matTabLabelContainer{
    padding-left: 8px;
    height: 48px;
    line-height: 48px;
    color: #202124;
    border-bottom: 1px solid #dbdbdb;
    position: relative;
}
.matTabLabelContainer .matTabLabels{
    float: left;
}
.matTabLabelContainer .matTabLabel{
    position: relative;
    cursor: pointer;
    height: 48px;
    margin: 0;
    padding: 0 16px;
    font-size: 14px;
    float: left;
    overflow: hidden;
    transition: color .2s;
    user-select: none;
}
.matTabLabelContainer.font16  .matTabLabel{
    font-size: 16px;
}
.matTabLabelContainer .matTabLabelContent{
    pointer-events: none;
    position: relative;
}
.matTabLabelContainer .matTabLabel.matTabLabelActive{
    color: #4285f4;
}
.matTabLabelContainer .matTabLabel.matTabLabelActive .matTabLabelContent:after {
    content: " ";
    border-radius: 3px 3px 0 0;
    height: 3px;
    position: absolute;
    bottom: 0px;
    background-color: #4285f4;
    width: 100%;
    left: 0;
}
.matTabLabelContainer .matTabLabel.matTabLabelAnimating .matTabLabelContent:after{
    display: none;
}
.matTabLabelContainer .matRippleElement{
    position: absolute;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    pointer-events: none;
    -webkit-transition: opacity,-webkit-transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,-webkit-transform 0ms cubic-bezier(0,0,.2,1);
    -o-transition: opacity,-o-transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,transform 0ms cubic-bezier(0,0,.2,1),-webkit-transform 0ms cubic-bezier(0,0,.2,1),-o-transform 0ms cubic-bezier(0,0,.2,1);
    -webkit-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    background-color: #4285f41f;
}
.matTabLabelContainer .matInkBar{
    pointer-events: none;
    -webkit-border-radius: 3px 3px 0 0;
    border-radius: 3px 3px 0 0;
    height: 3px;
    position: absolute;
    bottom: 0px;
    background-color: #4285f4;
    transition: width,left .2s;
}

/* 分页标签内容页start---------- */
.matTabBodyWrapper {
    position: relative;
    display: flex;
    display: -ms-flexbox;
    transition: height 500ms cubic-bezier(0.35, 0, 0.25, 1);
}
.matTabBody {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    display: none;
    flex-basis: 100%;
    transition: all 500ms cubic-bezier(0.35, 0, 0.25, 1);
    transform:translate3d(100%, 0px, 0px);
}
.matTabBody.matTabBodyActive {
    z-index: 1;
    flex-grow: 1;
    display: block;
    position: static;
    transform: translate3d(0px, 0px, 0px);
}
/* 分页标签内容页end---------- */
/* 分页标签样式end----------------------------------------------- */

/* 区块标签样式start---------------------------------------------- */
.matCardGroup{
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #dadce0;
    margin: 32px auto;
}
.matCardGroup .matCardTitle{
    font-size:16px;
    height: 55px;
    line-height: 55px;
    border-bottom: 1px solid #dadce0;;
    padding: 0 24px;
    overflow: hidden;
}
.matCardGroup .matCardTitle .leftTitle{
    float: left;
    position: relative;
}
.matCardGroup .matCardTitle .leftTitle .leftTitleDescription{
    font-size: 12px;
    color: #9e9e9e;
    margin-left: 5px;
}
.matCardGroup .matCardTitle .matCardTitleTip{
    font-size: 12px;
    color: #9e9e9e;
    opacity: 0;
    transition: opacity .3s;
    position: absolute;
    white-space: nowrap;
    left: 0;
    line-height: 12px;
    transform-origin: left top;
    bottom: 8px;
}
.matCardGroup.closed .matCardTitle .matCardTitleTip{
    opacity: 1;
}
.matCardGroup .matCardTitle .matCardExpand{
    float: right;
    text-align: center;
    cursor: pointer;
}
.matCardGroup .matCardTitle .matCardExpand .fa{
    transition: all .3s;
    color: #b8b8b8;
}
.matCardGroup .matCardTitle:hover .matCardExpand .fa{
    color: #0080FF;
}
.matCardGroup.closed .matCardExpand .fa{
    transform: rotate(-90deg);
}
/* 位于标签页右上角的菜单，如果没带这个，就是位于左上角了 */
.matCardGroup .rightMenus.matCardMenus{
    float: right;
    margin-right: 24px;
}
.matCardGroup .matCardMenu,.matCardGroup .matCardMenuTip{
    color: #b8b8b8;
    margin-left: 16px;
}
.matCardGroup .matCardMenuTip .tip{
    font-size: 12px;
    transform: scale(.75);
}
.matCardGroup:hover .matCardMenu{
    color: #0080FF;
}
.matCardGroup .matCardMenuTip .matCardMenu{
    margin-left: 0;
}


.matCardGroup .matCardContent{
    padding: 30px 24px;
}

.matCardGroup .matCardContent.matTabBodyWrapper{
    padding: 0;
}
.matCardGroup .matCardContent.matTabBodyWrapper .matTabBody{
    padding: 30px 24px;
    width: 100%;
    box-sizing: border-box;
}
/* 区块标签样式end------------------------------------------------ */

@keyframes loadingAnimate {
    0% {left: -50%;}
    40% {left: 150%;}
    40.1%{visibility: hidden}
    40.2%{left: -50%;}
    40.3%{visibility: visible;}
    60% {left: 150%;}
    60.1%{visibility: hidden}
    60.2%{left: -50%;}
    60.3%{visibility: visible;}
    100%{left: -50%;}
}
.overlayContentBody {
    position: absolute;
    top: 64px;
    width: 100%;
    bottom: 0;
    overflow-y: auto;
    background-color: #f1f3f4;
}
.noneCategoryMenus .overlayContentBody{
    top: 62px;
}

.categoryContainer {
    display: none;
}

/* 划出版弹出窗口end-------------------------------------------- */

/* 关于列表式横排输入框的相关样式start-------------------------- */
.listTable tr td{
    padding: 0px;
    vertical-align: middle;
    white-space: nowrap;
}

/* 关于列表式横排输入框的相关样式end----------------------------- */

/* 各种按钮上的点击的时候波浪特效start---------------------- */
.animationRipple{
    position: relative;
    overflow: hidden;
}
.matRippleElement{
    position: absolute;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    pointer-events: none;
    -webkit-transition: opacity,-webkit-transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,-webkit-transform 0ms cubic-bezier(0,0,.2,1);
    -o-transition: opacity,-o-transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,transform 0ms cubic-bezier(0,0,.2,1);
    transition: opacity,transform 0ms cubic-bezier(0,0,.2,1),-webkit-transform 0ms cubic-bezier(0,0,.2,1),-o-transform 0ms cubic-bezier(0,0,.2,1);
    -webkit-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    background-color: #4285f41f;
}
/* 各种按钮上的点击的时候波浪特效end---------------------- */

/* 搜索框样式start---------------------- */
.overlaySearch {
    position: absolute;
    top: 4px;
    left: 30%;
    margin: 8px auto;
    width: 500px;
    height: 40px;
    line-height: 40px;
    transition: box-shadow .2s;
    border-radius: 5px 5px 0 0;
    background-color: rgb(241, 243, 244);
}
.overlaySearch:before{
    color: #80868b;
    content: "search";
    font-family: "Material Icons Extended";
    font-size: 24px;
    height: 40px;
    line-height: 40px;
    margin: 0 16px;
    width: 24px;
    z-index: 0;
    position: absolute;
    font-feature-settings: 'liga';
}
.overlaySearchSubmit {
    color: #80868b;
    width: 24px;
    height: 40px;
    font-size: 24px;
    line-height: 40px;
    margin: 0 12px;
    left: 0;
    position: absolute;
    cursor: pointer;
    text-align: center;
    background-color: transparent;
    background-image: none;
    box-shadow: none;
    border: none;
    outline: none;
}
.overlaySearchClear {
    color: #80868b;
    width: 40px;
    height: 40px;
    font-size: 24px;
    line-height: 40px;
    right: 10px;
    padding: 0;
    position: absolute;
    cursor: pointer;
    text-align: center;
    opacity: 0;
    display: inline-block;
    transition: opacity .2s;
    background-color: transparent;
    background-image: none;
    box-shadow: none;
    border: none;
    outline: none;
}
.overlaySearch:focus, .overlaySearch:hover {
    background-color: #fff;
    box-shadow: 0px 2px 4px -1px #ccc, 0px 4px 5px 0px #dbdbdb, 0px 1px 10px 0px #dbdbdb;
}
.overlaySearch .overlaySearchInput {
    width: 460px;
    height: 40px;
    color: #5f6368;
    line-height: 40px;
    border: none;
    background-color: transparent;
    font-size: 16px;
    margin-left: 56px;
    position: absolute;
    left: 0;
    outline: none;
}
.overlaySearchInput::placeholder {
    color: #cfcfcf;
}
.overlaySearch.focus .overlaySearchClear, .overlaySearch:hover .overlaySearchClear {
    opacity: 1;
}
/* 搜索框样式end---------------------- */


/* table样式start */
/* 九宫格式表格 start -------------------------------------------
   每个td的宽度如果不指定的话，完全的平均分配表格宽度
   td之间的间隔为80px(两个相邻的td各自有40px的内边距)
 */
/* 九宫格类的表格：宽度100%，不会动态添加一行，每列宽度固定占有多少百分比或者像素，多行显示一类信息 */
.squaredTable{
    table-layout: fixed;
}
.squaredTable td{
    padding: 0 40px;
}
.squaredTable td:first-child{
    padding-left: 0;
}
.squaredTable td:last-child{
    padding-right: 0;
}
/* 九宫格式表格 end -------------------------------------------*/

/* 填入内容的列表式表格 start --------------------------------------
 由表头和内容两部分组成，表头可能会有标题和操作菜单
 表头上的操作按钮均可以在这里写一份，以作为公用的按钮
 <div class="listTableEditor">
    <div class="listTableEditorHeader">
        <div class="listTableEditorMenus">
            <div class="listTableEditorMenu">
                <label class="fieldContainer checkboxContainer">
                    <input type="checkbox" class="field tableEditorSelect">
                    <div class="checkboxShow"></div>
                </label>
            <div>
        </div>
    </div>
    <table class="listTable">
        <tr class="tableEditorRow">
            <td>
                <label class="fieldContainer styleThin">
                    <input type="checkbox" class="field tableEditorRowSelect" />
                    <div class="checkboxShow"></div>
                </label>
            </td>
        </tr>
    </table>
 </div>
*/
.listTableEditor .listTableEditorHeader{
    height: 50px;
    line-height: 50px;
    vertical-align: top;
}
.listTableEditor .listTableEditorHeader.bottomLine{
    border-bottom: 1px solid #dadce0;
}
.listTableEditor .listTableEditorMenus{
    height: 24px;
    line-height: 24px;
    padding: 13px 0;
}
.listTableEditor .listTableEditorMenu{
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin-left: 16px;
    color: #b8b8b8;
    float: left;
    text-align: left;
}
.listTableEditor.showTableTitle .listTableEditorMenus{
    position: absolute;
}
.listTableEditor.showTableTitle .listTableEditorMenu{
    display: none;
}
.listTableEditor .listTableEditorHeader.moreMenus td:not(:first-child){
    visibility: hidden;
}

/* listTableEditor中菜单上的每个操作按钮的样式start-- */

.listTableEditor .listTableEditorMenu.listTableSelectedCount{
    float: right;
    width: auto;
    white-space: nowrap;
    cursor: auto;
    letter-spacing: 0.00625em;
}
.listTableEditor.showTableTitle .checkAll,
.listTableEditor.showTableTitle .moreMenus .listTableEditorMenu{
    display: block;
}
.listTableEditor .listTableEditorMenu .checkboxShow,
.listTableEditor .listTableEditorMenu .shapeTriangle,
.listTableEditor .listTableEditorMenu .tableEditorSelect:disabled ~ .checkboxShow,
.listTableEditor .listTableEditorMenu .tableEditorSelect:disabled ~ .shapeTriangle{
    color: #b8b8b8;
}
.listTableEditor:hover .listTableEditorMenu,
.listTableEditor:hover .listTableEditorMenu .checkboxShow,
.listTableEditor:hover .listTableEditorMenu .shapeTriangle{
    color: #1a73e8;
}
.listTableEditor .listTableEditorMenu.checkAll{
    margin-left: 0px;
    padding-top: 3px;
}
.listTableEditor .listTableEditorMenu.selectAll{
    margin-left: -6px;
    margin-right: 4px;
    padding-top: 2px;
}
.listTableEditor .listTableEditorMenu{
    font-size: 15px;
}
/*.listTableEditor .listTableEditorMenu.delete svg{*/
/*    width: 19px;*/
/*    height: 19px;*/
/*    vertical-align: -2px;*/
/*    fill: currentColor;*/
/*}*/
/*.listTableEditor .listTableEditorMenu.merge{*/
/*    display: none;*/
/*    font-size: 21px;*/
/*}*/
/* listTableEditor中菜单上的每个操作按钮的样式end-- */
.listTableEditor table td:first-child{
    width: 20px;
}
.listTableEditor .listTableRepeatRow .tableEditorRowSelect ~ .checkboxShow{
    color: #ff7b08;
}
.listTableEditor .listTableRepeatRow .duplicateField{
    color: #ff7b08;
}
/* 填入内容的列表式表格 end -------------------------------------- */
/* table样式end */

.dataTooltip {
    transition: transform .2s;
    transform-origin:bottom left;
    display:none;
    padding: 5px;
    position: absolute;
    z-index:999;
    min-width: 400px;
    min-height: 34px;
}
.dataTooltip .dataTooltipMain {
    border: 1px solid #e0e0e0;
    box-shadow: 0 10px 30px -30px black, 0 0 12px -10px black;
    position: relative;
    background-color: #fdfdfd;
    z-index:2;
    min-height: 34px;
    color: #666666;
}
.dataTooltip .dataTooltipOption {
    width: 23px;
    height: 22px;
    font-size: 20px;
    text-align: center;
    padding-top: 2px;
    padding-right: 1px;
    position: absolute;
    right: 10px;
    bottom: 10px;
    z-index: 3;
    color: #949494;
    cursor: pointer;
}
.dataTooltip .dataTooltipOption:hover {
    background-color: #e2e2e2;
}
.dataTooltip .dataTooltipMain hr {
    background-color: #e6e6e6;
    height: 1px;
}
.dataTooltip .dataTooltipMain > *:not(hr) {
    margin: 7px 10px;
}
/* 日历与时间混搭的控件 */
.calendarTime .times{width:80px;text-align:center;}
.calendarTime .times:disabled{background-color:#fff;color:#aaa;}
.calendarTime .calendarTrigger{display:inline-block;width:42px;height:32px;vertical-align:-9px;margin:0;background:url(data:image/png;base64,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) no-repeat 50% 9px;}
.calendarTime .calendarTrigger:hover{cursor:pointer;}


/* mobile相关的样式，可能以后会和pc端的分开start--------------------- */
@media screen and (max-width: 600px){
    /* 移动端全局的一些样式start */
    html{font-size: 100px;}
    /* 移动端全局的一些样式end */
    /* 头部样式start */
    .pageHeader {
        background-color: #000066;
        height: 0.6rem;
        line-height: 0.6rem;
        z-index: 2;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        text-align: center;
        color: #fff;
        font-weight: bold;
    }
    .pageHeader .fa-angle-left{
        font-size: 0.36rem;
    }
    .pageHeader .goback {
        color: #fff;
        display: block;
        height: 0.6rem;
        line-height: 0.6rem;
        float: left;
        padding: 6px 0 0 0.18rem;
        text-align: left;
        position: absolute;
    }
    /* 头部样式end */

    /* searchPage:即单个输入框独立展开的弹出界面---start */
    .searchPage{
        position: fixed;
        top:0;
        left: 0;
        bottom: 0;
        width: 100%;
        z-index: 9999;
        background-color: #eeeef3;
        display: none;
        overflow: auto;
    }
    .searchPage .searchHeader {
        background-color: #000066;
        height: 0.6rem;
        line-height: 0.6rem;
        text-align: center;
        color: #fff;
        font-weight: bold;
    }
    .searchPage .searchClose{
        position: absolute;
        width: 0.5rem;
        text-align: left;
        padding-left: 0.18rem;
        color:#ffb408;
        font-size: 36px;
        vertical-align: top;
    }
    .searchClose .fa-angle-left{
        vertical-align: 2px;
    }

    .searchPage .searchConfirm{
        position: absolute;
        width: 0.7rem;
        text-align: right;
        padding-right: 0.16rem;
        color:#ffb408;
        right: 0;
        top:0;
        border: none;
        background-color: transparent;
        height: 0.6rem;
        line-height: 0.6rem;
        font-weight: bold;
    }
    .searchPage .searchBody{
        margin: 32px 0 290px;
        padding-left: 30px;
        background-color: #fff;
        border-top: 1px solid #e0e0e0;
        border-bottom: 1px solid #e0e0e0;
        line-height: 0;
    }
    .searchPage .searchBody .fieldContainer{
        margin: 10px 0 5px;
    }
    .searchPage .searchBody textarea{
        line-height: 24px;
        resize: none;
    }
    .searchPage .calculateWrapper{
        height: 0;
    }
    .searchPage .calculateFieldRows{
        width: 100%;
        border: none;
        height: 0;
        font-size: 14px;
        letter-spacing: .2px;
    }
    .searchPage .searchBody .underline{
        height: 1px;
        top: 6px;
    }
    .searchPage .searchBody .underline:after{
        display: none;
    }
    .searchPage .fieldContainer .options{
        height: auto;
    }
    .searchPage .searchBody ul{
        margin-top: 6px;
        border: none;
        border-radius: 0;
        box-shadow: none;
        width: 100%;
        padding: 0;
        max-height: none;
        min-width: 0!important;
        right: 0;
        position: static;
    }
    .searchPage .searchBody li{
        padding-left: 0;
        border-top: 1px solid #e0e0e0;
    }
    /*.searchPage .searchBody li:before{
        content: " ";
        position: absolute;
        height: 3px;
        margin-top: 36px;
        background-color: #fff;
        width: 22px;
    }*/
    .searchPage .searchBody li:first-child{
        border-top-width: 0;
    }
    .searchPage .searchBody .radioContainer{
        display: none;
    }
    /* searchPage:即单个输入框独立展开的弹出界面---end */

    /* 手机端的输入框start------------------------------  */
    .wrapBox {
        overflow: hidden;
        width: 93%;
        height: 32px;
        line-height: 32px;
        border-bottom: 1px solid #ddd;
        padding: 0px 0 5px 0;
        margin: 5px 0 0 30px;
    }
    .wrapBox_link {
        overflow: hidden;
        position: relative;
        height: auto;
        border-bottom: 1px solid #ddd;
        padding: 8px 26px 6px 0;
        margin: 0 0 -1px 30px;
    }
    .wrapBox_link .enteringArrow{
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
    }
    .wrapBox_link .mainCeil{
        line-height: 0.2rem;
        font-size: 0.14rem;
        min-width: 52%;
        color: #5f6368;
    }
    .wrapBox_link .leftCeil{
        display: inline-block;
        min-width: 52%;
    }
    .wrapBox_link .subCeil{
        font-size: 0.12rem;
        line-height: 0.18rem;
        color: #999;
        display: inline-block;
        white-space: nowrap;
    }
    .wrapBox_link .insuredPrice{
        width: 45%;
        text-align: right;
    }
    .wrapBox_link input,.wrapBox_link select{
        border:none;
        height:16px;
        line-height:16px;
        width:100%;
        font-size: 0.12rem;
        color: #999;
    }
    .wrapBox input{
        width: 90%;
        border:none;
        height:27px;
        line-height:27px;
        color:#5f6368;
        padding: 0;
        -webkit-appearance: none;
    }
    .wrapBox span{
        outline: none;
        color: #5f6368;
    }
    .wrapBox select{
        background-color: #fff;padding: 0;
        border:none;
        height:32px;
        line-height:32px;
        width:90%;
        color: #5f6368;
    }

    .wrapBox input::placeholder,.wrapBox select::placeholder{
        color: #9e9e9e;
    }
    .wrapBox input:disabled,.wrapBox select:disabled{
        background-color: #fff;
        color: #aaa;
    }
    .calendarTime #effectiveDate,.calendarTime #expiryDate{
        width:100px !important;
    }
    .wrapBox input:hover,.wrapBox input:focus{
        border:none;
    }
    .wrapBox .errorColor,.wrapBox .errorColor::placeholder,.errorColor{
        color: red;
    }
    .submitRow{
        height: 44px;
        line-height: 44px;
        background-color: #fff;
        border-top: 1px solid #e0e0e0;
        border-bottom: 1px solid #ddd;
        text-align: center;
        color:#0080ff;
        margin-bottom: 32px;
    }
    /* 手机端的输入框end------------------------------  */
    /* mobile上错误提示变种一下 */
    .fieldContainer .field.errorField,
    .fieldContainer .field.errorField::placeholder,
    .fieldContainer .fieldShow.errorField,
    .fieldContainer .fieldShow.errorField.filled{
        color: red;
    }
    .fieldContainer .field.errorField ~.underline, .fieldContainer .field.errorField ~.underline:after{
        background-color: #e0e0e0;
    }
    /* 手机端select弹出新的页面的样式start--------------*/
    .selectOpa {
        position: fixed;
        background-color: #000;
        top: 0;
        left: 0;
        right: 0;
        z-index: 99;
        opacity: 0.3;
        height: 300%;
    }

    .selectBox {
        height: 100%;
        position: fixed;
        width: 100%;
        /*left: 5%;*/
        padding: 0;
        background-color: #f5f5f5;
        top: 0px;
        z-index: 100;
        color: #777;
        -webkit-overflow-scrolling : touch;
    }

    .selectBox .selectMain {
        margin-top: 0.6rem;
        overflow-y: auto;
        background-color: #eeeef3;
        max-height: 100%;
    }

    .selectBox .selectUl {
        margin-bottom: 100px;
        margin-top: 32px;
        border-bottom: 1px solid #ddd;
        border-top: 1px solid #ddd;
        /*box-shadow: 0 0 0 1px red;*/
        background-color: #fff;
        padding: 0;
        list-style-type: none;
    }

    .selectBox .selectUl li {
        margin-left: 0.3rem;
        font: normal 14px Hei, Microsoft Yahei, SimHei, sans-serif;
        height: 42px;
        line-height: 42px;
        border-bottom: 1px solid #ddd;
    }
    .selectBox .selectUl li:last-child{
        border-bottom: none;
    }

    .selectBox .selectUl .selectedOption {
        color: #0080ff;
    }

    .selectMain .selectUl .selectedOption:before {
        content: " ";
        position: absolute;
        display: block;
        width: 6px;
        height: 12px;
        border: 2px solid;
        border-left: none;
        border-top: none;
        transform: rotate(45deg);
        margin-left: -17px;
        margin-top: 12px;
    }

    .selectBox .selectFoot {
        margin: 20px 20px;
        height: 32px;
        line-height: 32px;
    }

    .selectBox .selectFoot span {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        border: 1px solid #ccc;
        width: 45%;
        text-align: center;
    }

    .selectBox .selectCancel {
        float: right;
        border: none;
    }

    .selectBox .selectConfirm {
        float: left;
        border-color: #0080ff;
        color: #0080ff;
    }

    .selectBox  .liSelect{
        margin-right: 1em;
        float: right;
        display: none
    }
    .selectBox .header-title{text-align:center}
    /* 手机端select弹出新的页面的样式end--------------*/

    /* policyApplication相关页面挪移过来，然后一步步得删除无用和重复信息，并且把仅和policyApplication相关的信息还回去start */
    * {margin: 0;padding: 0;outline: none;-webkit-tap-highlight-color: rgba(0, 0, 0, 0);}
    html {font-size: 100px;-webkit-text-size-adjust: 100%;-ms-text-size-adjust: 100%;text-size-adjust: 100%;}
    body {font: normal 0.14rem Microsoft Yahei, SimHei, sans-serif;color: #000;-webkit-text-adjust: 100%;-ms-text-size-adjust: 100%;margin:0;padding:0;}
    input, select,textarea {font: normal 0.14rem Microsoft Yahei, SimHei, sans-serif;-webkit-appearance:none;}
    input[type="checkbox"] {-webkit-appearance:checkbox;}
    select{background-size:15px 5px;}
    select[disabled] {background-image: none;}
    ul,ol,li{list-style-type:none;margin:0;padding:0;}
    .clr666 {color: #666;}
    .clraaa {color: #aaa;}
    .tright {text-align: right;}
    .clearfix:after{content:" ";display:block;width:0;height:0;padding:0;margin:0;overflow:hidden;clear:both;}

    /* 全局样式end */
    /* 头部样式start */
    .policyHead {background-color: rgb(0, 0, 102);height: 0.6rem;line-height: 0.6rem;z-index: 2;position: absolute;top: 0;left: 0;right: 0;text-align: center;color: #fff;font-weight: bold;}
    .editHead{display:none;}
    .logInfo, .rtnInfo {position: absolute;color: #fff;text-decoration: none;}
    .rtnInfo {left: 20px;font-size: 0.16rem;}
    .logInfo {right: 0px;width: 2.5em;height: 2em;background-color: #031C71;}

    /* 录单页面需要的常用联系人样式开始 */
    .enteringArrow{
        color: #ccc;
        font-size: 0.25rem;
        float: right;
        margin-right: 0.7em;
        text-align: center;
        line-height: 32px;
    }
    .contactsBox{
        position: fixed;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        background-color: #fff;
        display: none;
        z-index: 999;
        -webkit-overflow-scrolling : touch;
    }
    .contactsBox input{
        border:none;
        background:transparent;
    }
    .contactsBox input:before{
        font-size:0.17rem;
        font-family: FontAwesome;
        content: "\f096";
        display:block;
        background-color:#fff;
    }
    .contactsBox input:checked:before{
        content: "\f14a";
    }
    .confirmContacts{
        position: absolute;
        top: 0;
        width: 4em;
        right: 0em;
        font-size: 0.16rem;
        color: #ffb408;
    }
    .contactsMainWrap{
        position:absolute;
        top:70px;
        bottom:0;
        overflow:auto;
        width:100%;
        padding: 0 10px;
    }

    .contactsList{
        display:block;
        margin-left:0.12rem;
        border-bottom:1px solid #ccc;
        position: relative;
    }
    .contactsLeft{
        float: left;
        width: 0.24rem;
        line-height: 0.36rem;
    }
    .contactsLeft input:after{

    }
    .contactsRight{
        float: left;
        line-height: 0.16rem;
        font-size: 0.14rem;
        color: #666;
        padding: 0.03rem 0;
    }
    .contactsIdentity{
        color:#999;
        font-size:0.12rem;
    }
    /* 录单页面需要的常用联系人样式结束 */
    /* 投保步骤需要的样式start */
    .policyInfo,.receiptInfo,.beneficiaryTable{
        background-color: #fff;
        border-collapse: collapse;
        margin: auto;
        width: 100%; /* background-color:#f5f5f5; */
        overflow: hidden;
    }

    .policyInfo .tright {
        color: #aaa;
    }

    .policyInfo td {
        padding: 5px 0px;
    }

    .policyInfo .detailInfo {
        padding: 0;
    }

    .titleInfo {
        background-color: rgb(238,238,243);
        padding: 0em 0.09rem 0.0rem 0.9em;
        color: #999;
        border-bottom: 1px solid #ddd;
        font-size: 0.13rem;
        height: 32px;
        line-height: 32px;
    }
    .plusOrminus{
        line-height: 32px;
        width: 6%;
        float: left;
    }
    .plusOrminus span{
        margin-left: 0.08rem;
        /*margin-top: 15px;*/
        /*float: left;*/
    }
    .policyInfo .nextStepBtn, .nextStepBtn {
        margin-top: 15px;
        float: right;
        color: white;
        border: none;
        background: url("../imgs/insure/nextbtn_bg1.png") no-repeat left top;
        width: 132px;
        height: 30px;
        padding:0;
        padding-left: 30px;
        text-align: center;
        cursor: pointer;
        border-radius: 2px;
        margin-right:0.08rem;
    }
    .policyInfo tr{
        /*border-bottom: 1px solid #f5f5f5;*/
    }
    .insureStep1 .policyInfo td{
        padding:0;
        background-color: rgba(255,255,255,0);
    }

    .deleteBox{
        border:none;
        border-bottom:1px dashed #ccc;
        width:90%;
        margin:10px auto 20px;
        height:32px;
    }

    /* 国家选择start */
    .hotCountry{margin-bottom: 20px;}
    .countryOpa{
        position: fixed;
        background-color: #000;
        top: 0;
        left: 0;
        right: 0;
        z-index: 99;
        opacity: 0.3;
        height: 300%;
        display:none;
    }
    .countryBox {
        position: fixed;
        width: 100%;
        left:0;
        bottom:0;
        padding: 0;
        background-color: #fff;
        top:0;
        z-index: 100;
        color: #777;
        display:none;
    }
    .countryHead{
        height:0.46rem;
        line-height:0.46rem;
        font-size:0.16rem;
        border-bottom:1px solid #7f7e7f;
    }
    .countryHead .editCancel,.countryHead .editConfirm{
        position:absolute;
        color:#007aff;
    }
    .countryHead .editCancel{
        left:0.15rem;
    }
    .countryHead .editConfirm{
        right:0.15rem;
        font-weight:800;
    }
    .countryBox .search{
        background-color:#c9c9ce;
        padding:0.1rem 0.08rem;
    }
    .countryBox .search .searchCountry{
        width:100%;
        height:0.36rem;
        border:none;
        border-radius:0.05rem;
    }

    .countryListMain{
        position:absolute;
        top:1.04rem;
        bottom:0;
        left:0rem;
        right:0.15rem;
        overflow-y: auto;
        background-color:#fff;
        z-index:2;
        -webkit-overflow-scrolling:touch;
    }
    .countryListMain .countryListBox{
        position:relative;
    }
    .countryListBox .countryDetail{
        text-indent: 15px;
        height: 0.42rem;
        line-height: 0.42rem;
        border-bottom: 1px solid #ddd;
        padding-left: 0.03rem;
        overflow: hidden;
        padding-right: 0.05rem;
        margin-left:0.15rem;
        font-size:0.15rem;
        position:relative;
    }
    .countryListBox .phoneticize,.phoneticizeTitle{
        height: 0.24rem;
        line-height: 0.24rem;
        margin-top: -1px;
        background-color: #fff;
        padding-left: 0.18rem;
        font-size: 0.18rem;
        color: #000;
    }
    .phoneticizeTitle{
        background-color:#eee;
        position:absolute;
        top:1.04rem;
        left:0;
        right:0.15rem;
        z-index:3;
        padding-left:0.18rem;
    }
    .countryListMain .nationalFlag{
        display:inline-block;
        width:0.2rem;
        margin-right:0.05rem;
    }
    .countryListMain .nationalFlag img{
        max-height:0.15rem;
        max-width:0.2rem;
    }
    .countryListMain .unselectable .nationalFlag img{
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -ms-filter: grayscale(100%);
        -o-filter: grayscale(100%);
        filter: grayscale(100%);
        filter: gray;
    }
    .countryListBox .countryDetail.unselectable{
        text-decoration:line-through;
        color:#ccc;
    }

    .countryListBox  .countryDetail.countrySelected:after{
        content: '';
        position:absolute;
        top:0.01rem;
        right:0.03rem;
        background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAKCAMAAACzB5/1AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAylpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NjFCQTA2M0ZDMDdBMTFFN0ExMjBFODZBQjM3QkY0RTQiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NjFCQTA2M0VDMDdBMTFFN0ExMjBFODZBQjM3QkY0RTQiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuZGlkOmExYjUyMjU5LTJlYzgtY2E0OC04NmU5LWQ1ZmI5ZDI0ZWYzNiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDphMWI1MjI1OS0yZWM4LWNhNDgtODZlOS1kNWZiOWQyNGVmMzYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6oxuaxAAAAWlBMVEVChfTY5v2FsfiLtPiCrvhJivTK3fyArfh2pveIsvjX5f2dwPn9/v/P4Px6qffb6P1IifTL3vxvovdxo/d1pvf4+//z9/5MjPVNjPXT4/xFh/RsoPb7/P////+HZ/wWAAAAHnRSTlP//////////////////////////////////////wDsGBxeAAAAUklEQVR42kTNRw7AIAADwSWB9N6b///NBKEIX6zxxSjGHDVR/c4UWQ3cK88WdF7MjSgZvVoovmKBTMqg8yvKwRjIFagE50j0UymkipS1/9srwAB9mgtyVC4ZWwAAAABJRU5ErkJggg==) no-repeat 50%;
        width:0.2rem;
        height:0.42rem;
    }
    .phoneticizeList{
        position:absolute;
        top:1.04rem;
        left:0;
        right:0;
        bottom:0;
        z-index:2;
    }
    .phoneticizeListBox div{
        color:#0277ff;
        text-align:right;
        font-size:0.09rem;
        height:0.12rem;
        line-height:0.12rem;
    }
    .phoneticizeListBox div span{
        display:inline-block;
        width:0.15rem;
        text-align:center;
    }
    .countrySelected{
        color:#0080ff;
    }
    /* 国家选择end */

    .forWhich{

    }
    .forWhich span{
        float:left;
        width:40%;
        margin-left:7%;
        text-align:center;
        height:0.3rem;
        line-height:0.3rem;
        border:1px solid #ccc;
        border-radius:0.03rem;
        color:#333;
    }
    .forWhich .forOther{
        float:right;
        margin-left:0;
        margin-right:7%;
    }
    .forWhich .forWhichOn{
        border-color:#0080ff;
    }

    .avgPriceTd{
        padding:0 10%;
        font-size:0.14rem;
    }
    .rightEdit{
        float: right;
        height: 22px;
        width: 22px;
        background: url(../imgs/mobile/insurantedit.png) no-repeat 0 -39px;
        margin-right:10%;
    }
    .rightEdit.handle {
        background-position:0 0;
        margin-right:5%;
    }
    .rightEdit.par_handle {
        background-position:0 0;
        margin-right:5%;
    }
    .thirdPartyLast{
        display:none;
    }
    .edittips{
        float:right;
        color:#ffb408;;
        font-size:0.14rem;
        margin-top:2px;
        border: 1px solid #ffb408;
        margin-right: 38%;
        border-radius: 3px;
    }
    .editcontacts{
        position:absolute;
        right: 0;
        width: 50px;
        height: 40px;
        line-height: 40px;
        /*float:right;*/
        color:#ffb408;;
        font-size:0.14rem;
        /*margin-top:14px;*/
        /*margin-right: 33px;*/
    }

    .showNotes,.showDeclare{
        color:#0080ff;
    }
    .showPlanInfo{
        float: right;
        color:#0080ff;
        margin-right: 2em;
        width: 39%;
    }
    .declareTitle {
        font-weight: 800;
        text-align: center;
        font-size: 0.2rem;
        text-indent: 0;
    }

    .declareEditBox{
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding:0 1em;
        background-color: #fff;
    }
    .declareClose,.declareCloses{
        position: absolute;
        right: 0em;
        width: 4em;
        color:#ffb408;
        top: 0
    }
    #insureClause .mainDeclare{font-size:0.14rem;}
    #insureClause .clauseTitle{
        border-top:1px solid #ddd;
        padding:0.1rem 0;
        line-height:0.14rem;
    }
    #insureClause .clauseContent{
        padding:0.1rem 0;
        color:#666;
        display:none;
        border-top:1px solid #f5f5f5;
        background-color: #fafafa;
        overflow: hidden;
    }
    #insureClause .clauseContent p{
        margin:0;
        padding:0;
    }
    /* 保障范围和价格详情细化 */
    /*#insureMessage table{
        border-collapse: collapse;
        border:1px solid gray;
        width:100%;
        color:#333;
        font-size:0.15rem;
        text-align:center;
    }

    #insureMessage thead{
        background-color:#e7e7e7;
        font-weight: bolder;
    }

    #insureMessage td{
        border:1px solid gray;
    }*/

    .totalPremiumTable td{
        border-bottom:1px dashed #ccc;
        padding:5px 0;
        color:#757575;
    }
    .totalPremiumTable .payfeee,.totalPremiumTable .crefee,.totalPremiumTable .expfee{
        padding-right:0.08rem;
    }
    .totalPremiumTable input{
        color:#757575;
        border:1px solid #ccc;
        border-radius:2px;
    }
    .premiumTitle td{
        color:#aaa;
    }
    .newCInfo td{
        padding:0px;
    }
    /* 投保步骤需要的样式end */

    /* 复选框统一化 */
    .checkBoxMain {
        display: inline-block;
        height: 14px;
        width: 14px;
        overflow: hidden;
        border: 1px solid #aaa;
        border-radius: 2px;
        background-color: #ffffff;
        vertical-align: -1px;
        margin: 0px
    }

    .checkBoxMain.checkOn {
        background-color: #6095ff;
        background-image: url(../imgs/mobile/checkbox.png);
        background-position: 50% 50%;
        border-color: #6095ff;
    }

    .checkBoxMain input {
        visibility: hidden;
    }
    .ScoreBox{height: 165px;}
    .ScoreBox .Scorediv {
        height: 35px;
    }
    .rangeTip{
        width: 15%;
        background-color: rgb(255, 255, 255);
        border: 1px solid rgb(204, 204, 204);
        padding: 4px 0 0px 0;
        z-index: 1;
        display: block;
        border-radius: 3px;
        text-align: center;
        margin-left: -7.5%;
    }
    .totalFee span{font-weight: 600;font-size: 0.17rem;line-height: 24px;}

    .PUBeneficiaryTr,#policyExtendPlanBlock,.detailedListhidden,.opahidden{display: none;}
    .clearFormat:hover,.clearFormat:focus{background:none!important; }

    .benePercentBox{display:inline-block;margin-right:0;width:80px;}
    .benePercentBox input{width:60px;}
    .delBene,.addBenePer{color:#0080ff;}
    #beneficiaryBox .declareFoot{margin-top:20px;}
    #beneficiaryBox .declareFoot span{
        width: 40%;height: 30px;line-height: 30px;border: 1px solid #0080ff;
        display: inline-block;text-align: center;border-radius: 2px;}
    /*.liabilityInfos{*/
    /*display: none;*/
    /*}*/
    /*.carNumber{position:relative;}*/
    #div_1,#div_3,#div_5,#div_7,#div_9,#div_11{float: right;width: 42.5px;height: 24.5px;border-radius: 50px;position: relative;margin: 5px 0;}
    #div_2,#div_4,#div_6,#div_8,#div_10,#div_12{width: 24px;height: 24px;border-radius: 48px;position: absolute;background: white;box-shadow: 0px 1px 2px rgba(0,0,0,0.4);}
    .open1,.open3,.open5,.open7,.open9,.open11{background: #006;}
    .open2,.open4,.open6,.open8,.open10,.open12{top: 0px;right: 0px;}
    .close1,.close3,.close5,.close7,.close9,.close11{background: rgba(255,255,255,0.4);border:1px solid rgba(0,0,0,0.15);border-left: transparent;}
    .close2,.close4,.close6,.close8,.close10,.close12{left: 0px;top: 0px;border:0px solid rgba(0,0,0,0.1);}
    .cancelContacts {color:#ffb408;display:block;height:100%;line-height:4.5em;float:left;margin-left:0.18rem;text-align:left;position:absolute;}

    .mainDeclares{position: absolute;left: 0;top: 0.4rem;bottom: 0;right: 0;overflow: auto;padding: 2em;}
    .mainDeclare{
        background-color: rgb(238,238,243);height: auto;position: absolute;left: 0;top: 60px;
        bottom: 0;right: 0;overflow: auto;z-index: 9;line-height: 25px;font-size: 0.15rem;
        -webkit-overflow-scrolling : touch;
    }
    /* 点击后左滑删除的整体控件start */
    .movelist{
        position: relative;
        margin-left: 0px;
        margin-right: 0px;
        transition: .5s;
    }
    #rowRemoveOpa{
        /* 当弹出了行的删除或者编辑按钮后，不允许做出编辑菜单外的任何操作，所以需要一个弹出层遮挡住屏幕 */
        position: fixed;
        top:0;
        left: 0;
        bottom:0;
        right: 0;
        z-index: 9997;
    }
    .movelist.addmovelist{
        margin-left: -0.72rem;
        margin-right: 0.72rem;
    }
    .movelist .plusOrminuss{
        height: 100%;
        position: absolute;
    }
    .movelist .deleterows{
        font-size: 0.2rem;
        line-height: 0.3rem;
        color: #ffb408;
        margin-left: 0.05rem;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }
    .movelist .detelbtn{
        position: absolute;
        width: 72px;
        right: -72px;
        top: 0;
        bottom: 0;
        line-height: 100%;
        background: #ffb408;
        text-align: center;
    }
    .addmovelist.movelist .detelbtn{
        /* 编辑操作的弹出层数要高于遮罩层，不然会导致行编辑按钮根本点不了的情况 */
        z-index: 9999;
    }
    .movelist .detelbtn span{
        height: 0.2rem;
        line-height: 0.2rem;
        width: 100%;
        left: 0;
        position: absolute;
        top: 50%;
        transform: translateY(-0.1rem);
    }
    /* 点击后左滑删除的整体控件end */
    .Maincontent{overflow: auto;z-index: 1;top: 0;left: 0;bottom: 0;right: 0;background-color: rgb(238,238,243);}
    .divideline{background-color: rgb(238,238,243);border-bottom: 1px solid #ddd;height: 44px;border-top: 1px solid #ddd;}
    .companyName,.goback{/*color:#ffb408;*/color:#fff;display:block;height:0.6rem;line-height:0.6rem;float:left;padding:6px 0 0 0.18rem;text-align:left;position:absolute;}
    .expenseBtn{z-index: 1;height: 0.6rem;width: 100%;background-color: #ddd;bottom: 0;position: fixed;}
    .dd{font-size: 0.13rem;line-height: 30px;width: 27%;float: left;height: 4em;}
    .dd span{margin-left: 1em;font-size: 0.13rem;line-height: 30px;float: left;height: 2em;}
    .detailedList{top:0;bottom: 0.6rem;position: fixed;width: 100%;z-index:3;height: auto;overflow: auto;-webkit-overflow-scrolling : touch;}
    .detailedList .listtable{background-color: #fff;border-collapse: collapse;margin: auto;width: 100%;height: auto;overflow: auto;}
    .detailedList .detaileddiv{width: 100%;height: auto;overflow: auto;bottom: 0;position: absolute;max-height: 100%;}
    .expenseListopa{position:fixed;width:100%; top:0;left:0;background-color:#000;opacity:0.3;z-index:2;  bottom:0.6rem;}
    .detailedPlus_tr table{text-align: center;font-size: 0.12rem;width: 100%; line-height: 30px;}
    .expenseList{z-index: 1;}
    .contactsMain{margin-bottom: 35px;}
    .borderNone{border:none;}
    .calendarTime #effectiveDate:disabled,.calendarTime #expiryDate:disabled{color: #666;opacity: 1;-webkit-opacity: 1;-webkit-text-fill-color: #666;}
    .calendarTime input{-webkit-user-select:none;}
    .titleInfos {
        border-bottom: 1px solid #ddd;
        margin-top: -1px;
        border-top: 1px solid #ddd;
        background-color: rgb(238,238,243);
        padding: 20px 0.09rem 0.0rem 0.9em;
        color: #999;
        font-size: 0.13rem;
        height: 32px;
    }
    .arrowheight{margin-top: -46px;}
    #PUBeneficiaryTempletelist{display:none}
    .PUBeneficiaryButton{-webkit-user-select:none;}
    .unselectable{display:none;text-decoration: line-through;color: #ccc;}
    .planDetail{
        height: 0.42rem;line-height: 0.42rem;border-bottom: 1px solid #ddd;padding-left: 0.03rem;
        padding-right: 0.05rem;margin-left: 0.3rem;font-size: 0.15rem;position: relative;
    }
    .selectedPlan{color: #0080ff;}
    .selectedPlan:after{
        content: '';position: absolute;top: 0.01rem;left: -22px;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAKCAMAAACzB5/1AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAylpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNS1jMDE0IDc5LjE1MTQ4MSwgMjAxMy8wMy8xMy0xMjowOToxNSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6NjFCQTA2M0ZDMDdBMTFFN0ExMjBFODZBQjM3QkY0RTQiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6NjFCQTA2M0VDMDdBMTFFN0ExMjBFODZBQjM3QkY0RTQiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuZGlkOmExYjUyMjU5LTJlYzgtY2E0OC04NmU5LWQ1ZmI5ZDI0ZWYzNiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDphMWI1MjI1OS0yZWM4LWNhNDgtODZlOS1kNWZiOWQyNGVmMzYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz6oxuaxAAAAWlBMVEVChfTY5v2FsfiLtPiCrvhJivTK3fyArfh2pveIsvjX5f2dwPn9/v/P4Px6qffb6P1IifTL3vxvovdxo/d1pvf4+//z9/5MjPVNjPXT4/xFh/RsoPb7/P////+HZ/wWAAAAHnRSTlP//////////////////////////////////////wDsGBxeAAAAUklEQVR42kTNRw7AIAADwSWB9N6b///NBKEIX6zxxSjGHDVR/c4UWQ3cK88WdF7MjSgZvVoovmKBTMqg8yvKwRjIFagE50j0UymkipS1/9srwAB9mgtyVC4ZWwAAAABJRU5ErkJggg==) no-repeat 50%;
        width: 0.2rem;height: 0.42rem;
    }
    .item{margin-left: 0px;display: block;white-space: nowrap;overflow: hidden;}
    .commenting{
        float: right;color: #0080ff;font-size: 15px;padding: 0 20px 0 10px;line-height: 0.32rem;
        position: absolute;right: 0;top: 0;background-color: #fff;
    }
    .commenting img{width: 19px;margin-top: 12px;height: 19px;}
    #proAndpla{width: 82%;position: absolute;display: block;white-space: nowrap;overflow: hidden;line-height: 0.35rem;}
    .declareBox {position: fixed;width: 100%;left: 0;bottom: 0;right:0;padding: 0;background-color: #fff;top: 0;z-index: 100;display: none;-webkit-overflow-scrolling: touch;}
    .fa-angle-left{font-size:36px!important;}
    .mainContent{background: rgb(238,238,243);position: absolute;top: 0;bottom: 0;left: 0;width:100%;overflow: auto;}
    .buyBtn {background: url($cPath/imgs/insure/nextbtn_bg1.png) no-repeat;display: inline-block;width: 1.32rem;height: 0.3rem;line-height: 0.3rem;text-align: left;
        text-indent: 0.5rem;color: #fff;font-size: 0.152rem;float: right;margin-right:0.15rem;margin:0.1rem;}
    .buyBtnBackground{height:0.65rem;}
    .buyBtnFixBackground{display:none;position:fixed;width:100%;height:0.5rem;left:0;bottom:0;background-color:#efeff4;box-shadow:0 -1px 2px rgba(0,0,0,0.1);z-index:10;}

    .insuredNameCNSpan{display:inline-block;height: 20px;}
    .contactsListHide,.hideBySearch{display:none;}
    #countrySelect2::-webkit-input-placeholder{color:#666;}
    input.invalid::-webkit-input-placeholder{color:red!important;}
    input.invalidIn{color:red!important;}
    /* policyApplication相关页面挪移过来，然后一步步得删除无用和重复信息，并且把仅和policyApplication相关的信息还回去end */
}
/* mobile相关的样式，可能以后会和pc端的分开end----------------------- */

/* textarea 控制台样式光标 start----------------------------------------------- */
@keyframes blink {0%, 50%{opacity: 1;} 50.1%, 100%{opacity: 0;}}
@-webkit-keyframes blink {0%, 50%{opacity: 1;} 50.1%, 100%{opacity: 0;}}
.consoleStyle {
    position: relative;
}
.consoleStyle textarea {
    caret-color: transparent;
    font-family: courier;
}
.consoleCursor {
    position: absolute;
    font-size: 14px;
    background-color: #4285f4;
    color: white;
    vertical-align: bottom;
    line-height: 16px;
    animation: blink 1s infinite linear;
    pointer-events: none;
    margin-top: 4px;
    font-family: courier;
}
.consoleCursor.blur {
    background-color: transparent;
    color: transparent;
    line-height: 14px;
    font-size: 12px;
    border: 1px solid #4285f4;
    animation: none;
}
/* textarea 控制台样式光标 end----------------------------------------------- */
/* Vue.js 相关的样式start----------------------*/
.vueLoading[v-cloak]{
    display: none;
}
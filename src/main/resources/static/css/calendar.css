.tableborder {
    background: none repeat scroll 0 0 white;
    border: 1px solid #86b9d6;
}
.tableborder .header {
    background: none repeat scroll 0 0 #c2deed;
    color: #154ba0;
    font: bold 12px Arial,Tahoma !important;
    height: 25px;
    padding-left: 10px;
}
.tableborder .header td {
    padding-left: 10px;
	font: bold 12px <PERSON>l,Tahoma !important;
}
.tableborder .header {
    background: none repeat scroll 0 0 #c2deed;
    color: #154ba0;
    font: bold 12px Arial,Tahoma !important;
    height: 25px;
    padding-left: 10px;
	vertical-align:middle;
}
#year, #month {
    padding-right: 10px;
}
.tableborder .header a {
    color: #154ba0;
    text-decoration: none;
    font: bold 12px Arial,Tahoma !important;
}
.tableborder .category {
    background-color: #ffffd9;
    color: #92a05a;
    font: 12px Arial,Tahoma !important;
    height: 20px;
}
.tableborder .category td {
    border-bottom: 1px solid #dedeb8;
    color: #92a05a;
    font: 12px <PERSON>l,<PERSON><PERSON><PERSON> !important;
}

.expire, .expire a:link, .expire a:visited {
    color: #999999;
    text-decoration: none;
}

.today, .today a:link, .today a:visited {
    color: #00bb00;
    text-decoration: none;
}
.altbg2 a {
    font: 11px Arial,Tahoma;
}
.default, .default a:link, .default a:visited {
    color: #333333;
    text-decoration: none;
}
.zmo, .zmo a {
    color: #009900;
    text-decoration: none;
}
.altbg2 a {
    font: 11px Arial,Tahoma;
}
.checked, .checked a:link, .checked a:visited {
    color: #ff0000;
    text-decoration: none;
}
#calendar_year {
    background: none repeat scroll 0 0 #ffffff;
    display: none;
    line-height: 130%;
    position: absolute;
    z-index: 21;
}
#calendar_year .col {
    background: none repeat scroll 0 0 #ffffff;
    border: 1px solid #86b9d6;
    float: left;
    margin-left: 1px;
    padding: 4px;
}#calendar_year .col span {
    color: #666666;
    text-decoration: none;
	font-size:12px;
	cursor: pointer;
}
.checked, .checked a:link, .checked a:visited {
    color: #ff0000;
    text-decoration: none;
}
#calendar_month {
    background: none repeat scroll 0 0 #ffffff;
    border: 1px solid #86b9d6;
    display: none;
    line-height: 130%;
    padding: 4px;
    position: absolute;
    z-index: 22;
}
#calendar_month span {
    color: #666666;
    text-decoration: none;
	font-size:12px;
}
.checked, .checked a:link, .checked a:visited {
    color: #ff0000;
    text-decoration: none;
}

#calendar #hour,#calendar #minute{height:1.2em;line-height:1.2em;text-align:center;width:1.5em;letter-spacing:1px;}

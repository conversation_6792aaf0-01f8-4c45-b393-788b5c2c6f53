html, body {
 margin:0;
 padding:0;
 height:100%;
}
table { table-layout:fixed;}

#list_companey { list-style:none;
                 margin:0;
				 padding:0;
				 margin-top:5px;
				 margin-left:0px;
				 font-family:Arial, Helvetica, sans-serif;
				 font-size:14px;
				 }
#list_companey li { margin-top:5px;
					width:160px;
				  }
				  
#list_companey ul { list-style:none;
                    display:none;}
#list_companey img { margin-right:5px;
                     cursor:pointer;}
					
#left_scroll { overflow-y:auto;
               padding-left:10px;
			   height:552px;
			   }
#left_scroll_main { background-color:#E0E8FC;}			   
#left_scroll_top { overflow:hidden;
                   border-bottom:1px solid #FFF;
                   padding:10px 20px;
				   margin-bottom:10px;} 			   
			   
#list_companey a { text-decoration:none;
                   color:#006;}

#list_companey a:hover { color:red;}
	   
.tab_menu { margin:0px;
            margin-top:12px;
			margin-left:37px;
            padding:0px;
            width:800px;
            height:30px;
			position:absolute;
			}
			
.tab_menu li{ margin-left:10px;
                 padding-top:11px;
				 cursor:pointer;
				 list-style:none;
				 text-align:center;
				 background-color:#DFDFFF;
                 width:200px;
                 height:30px;
                 border:1px solid #ccc;
				 border-bottom:none;
				 position:relative;
				 z-index:-2;
				 }
				 
.tab_menu li:hover { background-color:#FFFFFF;}
				 
.tab_menu .selected { z-index:2;
                         border-bottom:none;
                         background-color:#FFFFFF;}
				 
.tab_box div { position:absolute;
               top:45px;
               width:800px;
               height:420px;
		       margin-top:11px;
		       margin-left:50px;
			   padding:10px;
		       background-color:#FFFFFF;
		       border:1px solid #ccc;
		       z-index:-1;
               }
.tab_box > div { overflow:auto;}
.tab_box div input { width:400px;
                     height:25px;
					 line-height:25px;}		   
.hide { display:none;}

#user { margin:0px;
        padding:0px;
		border:none;
		height:390px;}
		
#power td{ border-bottom:2px solid #666;}

.power_content td { border-bottom:1px solid #ccc;
                   }

#power_button input { width:60px;
                      height:25px;
					  line-height:15px;
					  text-align:center;}
					  
#user_content { position:relative;
                z-index:10;
                top:0px;
                height:340px;
				margin:0;
				padding:0;
				border:none;
				overflow:auto;}
				
#user_content tabel { border-collapse:collapse;
					  }
				
#user_content input { height:16px;
                      line-height:16px;
                      text-align:center;
                      margin:0px;
					  padding:0px;
					  }
#user_content .check { width:40px;}
#user_cound { margin-right:40px;
              font-size:14px;}
#user_edit { margin-right:300px;
             font-size:14px;}
			 
.user_info { border:1px solid #ccc;}

#user_alert { position:absolute;
              z-index:10;
              width:300px;
              height:150px;
			  display:none;
			  filter: alpha(opacity=90); 
			  opacity:0.9; 
			  background-color:#F6F6F6;

			  border:2px solid #CCC;}
#user_alert_title { width:300px;
                    height:30px;
					line-height:30px;
					text-align:center;
                    border-bottom:2px solid #ccc;
					background-color:#999;}
#user_alert_body { width:300px;
                   height:120px;
                   }
#user_opacity { position:absolute;
                z-index:9;
				top:0px;
				left:0px;
                width:100%;
                height:100%;
				filter: alpha(opacity=0); 
			    opacity:0;
				background-color:#09C;
				display:none;
				}
				
#user_power { position:absolute;
              z-index:10;
			  width:600px;
			  height:500px;
			  border:2px solid #ccc;
			  background-color:#F6F6F6;
			  display:none;
			  }
			  
#user_power_title { width:600px;
                    height:30px;
					line-height:30px;
					text-align:center;
					border-bottom:2px solid #ccc;
					background-color:#999;
					}
					
#user_power_body { width:600px;
                   height:470px;}
				   
.tab_box span { margin-right:20px;
                margin-left:10px;
				cursor:pointer;}

.items { cursor:pointer;}

#select_power { cursor:default;}

#select_bottom { border-bottom:1px solid #ccc;}

#power_bottom { border-top:1px solid #ccc;
                height:30px;}
#user_power_1 { height:30px;
                line-height:30px;
				text-align:left;
				font-size:14px;
                border-bottom:1px solid #ccc;}
#user_power_2 { height:390px;
                overflow:auto;}
#user_power_3 { height:30px;
                padding-top:5px;
				font-size:12px;
				line-height:30px;
				text-align:right;
                border-top:1px solid #ccc;}
#user_power_1 span { margin-left:10px;
                     cursor:pointer;
					 }
.user_items { cursor:pointer;}
#department_info { margin-top:50px;}
#department_edit_box { position:absolute;
                       z-index:10;
                       width:1000px;
                       height:535px;
					   display:none;
					   background-color:#FFF;
					   border:4px solid #ccc;}
#department_edit_top { height:30px;
					   color:#FFF;
					   line-height:30px;
                       border-bottom:1px solid #ccc;
                       background-color:#96C1FC;}
#department_edit_body { height:470px;}
#department_edit_left_scroll_main { background-color:#EFF1FE;}
#department_edit_body_left { overflow:auto;
                             height:432px;
							 padding-top:10px;
							 padding-left:10px;}
#department_edit_body_right { height:420px;
                              width:790px;}
#department_button { width:230px;
                     height:420px;
					 background-color:#EFF1FE;
					 border-right:1px solid #ccc;}
#department_input { height:420px;
                    width:790;
					}
#department_input input { line-height:25px;
                          text-align:left;}
#department_right_bottom { height:33px;
                           line-height:25px;
						   text-align:center;
						   background-color:#96c1fc;
						   border-top:1px solid #ccc;}
#department_label { height:35px;
                    list-style:35px;
					text-align:center;
					border-bottom:1px solid #ccc;}
#department_label span { position:relative;
                         z-index:-1;
                         top:10px;
                         width:150px;
                         height:20px;
					     font-family:Arial, Helvetica, sans-serif;
						 font-size:12px;
						 padding-top:3px;
						 padding-bottom:9px;
						 padding-left:5px;
						 padding-right:5px;
						 cursor:pointer;
						 border:1px solid #ccc;
						 border-bottom:none;
						 background-color:#ECEEFF;
							}
#department_label .department_top_select {
	                     position:relative;
						 z-index:1;
                         top:10px;
                         width:150px;
                         height:20px;
					     font-family:Arial, Helvetica, sans-serif;
						 font-size:12px;
						 padding-top:3px;
						 padding-bottom:9px;
						 padding-left:5px;
						 padding-right:5px;
						 cursor:pointer;
						 border:1px solid #ccc;
						 border-bottom:none;
						 background-color:#FFF;
	}
#scroll_right { overflow-x:auto;
                overflow-y:hidden;
                height:470px;}											  
#right_main_user{ height:420px;
                  }
#right_main_power{ height:420px;}
#current_department { font-family:Arial, Helvetica, sans-serif;
                      font-size:12px;
					  color:#03C;}
#function_button { overflow:hidden;}
#department_serch { overflow:hidden; 
                    margin:4px;
					}
#department_serch > input { line-height:20px;
                            text-align:left;
							background-color:#EFF1FE;
							border:1px solid #ccc;
							color:#ccc;}
#right_main_user input { line-height:15px;
                         text-align:center;
						 border:1px solid #ACA8FB;}
#right_main_power { font-size:12px;
                    margin:5px;
					overflow:hidden;}
#right_main_power div { margin-bottom:10px;
                        margin-top:5px;}
#right_main_power a { margin-left:20px;
                      color:#06C;
					  cursor:pointer;}
#right_main_power span { float:right;
                         margin-right:20px;}
#right_main_power b { margin-left:10px;}
#right_main_power fieldset { margin-top:5px;}
#power_user { cursor:pointer;}
#right_main_user_sub { overflow-y:auto;}
# SMTP邮件配置示例
email:
  sender:
    type: smtp  # 可选值: smtp, sendcloud

# SMTP服务器配置
smtp:
  host: smtp.exmail.qq.com  # SMTP服务器地址
  port: 587                 # SMTP端口
  username: <EMAIL>  # SMTP用户名
  password: your-password   # SMTP密码
  from: <EMAIL>      # 发件人邮箱
  from-name: 美亚保险_旅行险        # 发件人名称
  auth: true                # 是否启用认证
  starttls-enable: true     # 是否启用STARTTLS
  ssl-enable: false         # 是否启用SSL
  connection-timeout: 30000 # 连接超时时间（毫秒）
  timeout: 30000           # 读取超时时间（毫秒）
  write-timeout: 30000     # 写入超时时间（毫秒）

# 常见SMTP服务器配置示例：

# 腾讯企业邮箱
# smtp:
#   host: smtp.exmail.qq.com
#   port: 587
#   starttls-enable: true
#   ssl-enable: false

# 阿里云企业邮箱
# smtp:
#   host: smtp.mxhichina.com
#   port: 587
#   starttls-enable: true
#   ssl-enable: false

# Gmail
# smtp:
#   host: smtp.gmail.com
#   port: 587
#   starttls-enable: true
#   ssl-enable: false

# Outlook/Hotmail
# smtp:
#   host: smtp-mail.outlook.com
#   port: 587
#   starttls-enable: true
#   ssl-enable: false

# 网易企业邮箱
# smtp:
#   host: smtp.ym.163.com
#   port: 587
#   starttls-enable: true
#   ssl-enable: false

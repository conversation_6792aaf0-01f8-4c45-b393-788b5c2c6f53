package com.wenhq.external.hnair;

/**
 * 航连错误代码枚举类
 */
public enum AUErrorCode {
    /*************航连接口标准定义代码***************/
    SUCCESS(0, "成功"),
    NOTEXIST(901, "未找到符合条件的保单"),

    /*******以下为TravelYe自定义扩展错误码*****/
    SUCCESS2(1, "成功"),
    KEYNOTMATCH(2, "报文数据验证错误"),
    INVALIDPARAMETERS(3, "无效的请求参数"),
    INVALIDXML(4, "XML解析错误"),
    INVALIDTRANSTYPE(5, "不支持的交易类型"),
    INVALIDOPERATEDATE(6, "交易操作时间错误"),
    IPNOTALLOWED(7, "IP地址不在允许访问的范围"),
    CANCELEDPOLICY(8, "保单已经取消"),
    INPROGRESS(9, "交易正在进行中"),
    INVALIDAPPLICATION(10, "无效的投保请求"),
    INVALIDPRODUCT(11, "产品未授权"),
    WRONGPREMIUM(12,"保费金额错误"),
    CONNECTKERNEL(13, "不能连接到核心出单服务器"),
    DUPLICATEPOLICY(14, "系统中已存在相似保单"),
    OTHERS(99, "其他错误,请连系系统管理员");

    /**
     * 错误代码
     */
    private int code;
    /**
     * 错误名称
     */
    private String name;

    /**
     * 枚举购造方法
     * @param code
     * @param name
     */
    AUErrorCode(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AUErrorCode getErrorCode(int code) {
        for ( AUErrorCode errorCode : AUErrorCode.values() ) {
            if ( code == errorCode.getCode() ) {
                return errorCode;
            }
        }
        return AUErrorCode.OTHERS;
    }

    /**
     * 获取错误代码
     */
    public int getCode() {
        return this.code;
    }

    /**
     * 获取错误名称
     * @return
     */
    public String getName() {
        return this.name;
    }

    @Override
    public String toString() {
        return this.code + "-" + this.name;
    }
}


package com.wenhq.external.hnair;

/**
 * 航联响应类
 */
public class AUResponse {
    //构造方法, 默认认置回复'成功'状态
    public AUResponse() {
        this.errorCode = AUErrorCode.SUCCESS;
    }

    /*********************以下字段为构造响应报文使用****************/
    /**
     * 返回代码
     */
    private AUErrorCode errorCode;
    public AUErrorCode getErrorCode() {
        return errorCode;
    }
    public void setErrorCode(AUErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * 错误描述
     */
    private String errorMessage;
    public String getErrorMessage() {
        return errorMessage;
    }
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * 出单/退保对应的保单号
     */
    private String policyNo;
    public String getPolicyNo() {
        return policyNo;
    }
    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    /**
     * 电子保单下载地址(无'http://'或'https://'前缀)
     */
    private String ePolicyUrl;
    public String getEPolicyUrl() {
        return ePolicyUrl;
    }
    public void setEPolicyUrl(String ePolicyUrl) {
        this.ePolicyUrl = ePolicyUrl;
    }

    /*******************以下为系统内部使用********************/
    /**
     * 保单流水号
     */
    private long policyId;
    public long getPolicyId() {
        return policyId;
    }
    public void setPolicyId(long policyId) {
        this.policyId = policyId;
    }

    /**
     * 是否重复请求
     */
    private boolean isDuplicated;
    public boolean getIsDuplicated() {
        return isDuplicated;
    }
    public void setIsDuplicated(boolean isDuplicated) {
        this.isDuplicated = isDuplicated;
    }

    /**
     * 响应报文xml
     */
    private String xml;
    public String getXml() {
        return xml;
    }
    public void setXml(String xml) {
        this.xml = xml;
    }
}


package com.travelye.inInterceptor;


import org.apache.cxf.interceptor.AbstractInDatabindingInterceptor;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.apache.cxf.phase.Phase;
import org.springframework.beans.factory.annotation.Value;

public class WsInterceptor extends AbstractInDatabindingInterceptor {

    private String url;

    public WsInterceptor(String url){
        super(Phase.USER_STREAM);
        this.url = url;
    }

    @Override
    public void handleMessage(Message message) throws Fault {
        message.put("org.apache.cxf.request.url", url);
    }
}

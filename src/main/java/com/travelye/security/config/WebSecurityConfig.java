//package com.travelye.security.config;
//
//
//import com.travelye.config.WebLogFilter;
//import com.travelye.security.filter.JwtTokenInterceptor;
//import com.travelye.security.okta.OktaUserDetailsService;
//import com.travelye.security.formLogin.MyPasswordEncoder;
//import com.travelye.security.filter.TYSecuritySessionFilter;
//import com.travelye.security.handler.FormLoginAuthenticationFailureHandler;
//import com.travelye.security.handler.LoginSuccessHandler;
//import com.travelye.security.formLogin.UserLoginServiceImpl;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.boot.web.servlet.FilterRegistrationBean;
//
//import org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository;
//
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
//import static org.springframework.security.web.util.matcher.AntPathRequestMatcher.antMatcher;
//
//
//@Slf4j
//public class WebSecurityConfig {
//
//    public static class WebSecurity {
//        private final String[] FILTER_URLS = {
//                "/china",
//                "/sale/",
//                "/admin/",
//                "/china/*",
//                "/sale/*",
//                "/admin/*"
//        };
//
//        public FilterRegistrationBean<TYSecuritySessionFilter> tySecuritySessionFilter() {
//            FilterRegistrationBean<TYSecuritySessionFilter> bean = new FilterRegistrationBean<>();
//            bean.setFilter(new TYSecuritySessionFilter());
//            bean.addUrlPatterns(FILTER_URLS);
//            return bean;
//        }
//
//        public FilterRegistrationBean<WebLogFilter> webLogFilter() {
//            FilterRegistrationBean<WebLogFilter> bean = new FilterRegistrationBean<>();
//            bean.setFilter(new WebLogFilter());
//            bean.addUrlPatterns(FILTER_URLS);
//            bean.addUrlPatterns("/*");
//            return bean;
//        }
//    }
//
//    public static class WebAppConfigurer implements WebMvcConfigurer {
//        @Override
//        public void addInterceptors(InterceptorRegistry registry) {
//            registry.addInterceptor(new JwtTokenInterceptor())
//                    .excludePathPatterns("/user/login")
//                    .addPathPatterns("/user/*");
//        }
//    }
//
//    public static class TravelYeSecurityConfiguration {
//        private final String[] PATH_RELEASE = {
//                "/formLogin",
//                "/external/**",
//                "/user/**",
//                "/index",
//                "/static/**/**",
//                "/js/**", "/css/**", "/img/**",
//                "/login/saml2/sso/**/**",
//                "/saml/doLogin/**",
//                "/captcha.jpg",
//                "/ws",
//                "/ws/**"
//        };
//        private final UserLoginServiceImpl userLoginService;
//        private final MyPasswordEncoder myPasswordEncoder;
//        private final FormLoginAuthenticationFailureHandler formLoginAuthenticationFailureHandler;
//        private final LoginSuccessHandler loginSuccessHandler;
//        private final OktaUserDetailsService oktaUserDetailsService;
//        private final RelyingPartyRegistrationRepository relyingPartyRegistrationRepository;
//
//        public TravelYeSecurityConfiguration(UserLoginServiceImpl userLoginService, MyPasswordEncoder myPasswordEncoder, FormLoginAuthenticationFailureHandler formLoginAuthenticationFailureHandler, LoginSuccessHandler loginSuccessHandler, OktaUserDetailsService oktaUserDetailsService, RelyingPartyRegistrationRepository relyingPartyRegistrationRepository) {
//            this.userLoginService = userLoginService;
//            this.myPasswordEncoder = myPasswordEncoder;
//            this.formLoginAuthenticationFailureHandler = formLoginAuthenticationFailureHandler;
//            this.loginSuccessHandler = loginSuccessHandler;
//            this.oktaUserDetailsService = oktaUserDetailsService;
//            this.relyingPartyRegistrationRepository = relyingPartyRegistrationRepository;
//        }
//
//    }
//}

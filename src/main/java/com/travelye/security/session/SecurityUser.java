package com.travelye.security.session;

import com.travelye.system.Action;
import com.travelye.system.Menu;
import com.travelye.vo.Partner;
import com.travelye.vo.Product;
import com.travelye.vo.Users;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

@Data
public class SecurityUser implements UserDetails {
    private final boolean accountNonExpired;
    private final boolean enabled;
    List<Product> productList;              //代理商授权产品
    private String loginName;
    private String username;
    private String realName;
    private String password;
    private String macAddress;
    private String agencyType;
    private long agencyId;
    private long branch;
    private String agencyName;
    private long uid;
    private long departmentId;
    private String isPayment;
    private Partner agency;
    private Partner client;
    private List<Menu> mainMenuList;        // 该用户可使用的菜单
    private List<Menu> subMenuList;        // 该用户可使用的菜单
    private List<Action> actionList;        // 该用户可使用的权限
    private int status;                // 状态
    private int bindMAC;//是否需要检查MAC地址
    private int admin;//是否管理员
    private List<GrantedAuthority> authorities;
    private boolean accountNonLocked;
    private boolean credentialsNonExpired;
    private long accountID;      //账户id
    private String contact;        //联系方式
    private boolean firstLogin; //用户在初始化密码后首次登陆系统
    private String partnerName; //合作伙伴名称
    private List<Users> relatedUsersList; //通过email获取所有对应user后放在这里，用于用户切换


    public SecurityUser() {
        this.authorities = new ArrayList<>();
        this.accountNonExpired = true;
        this.accountNonLocked = true;
        this.credentialsNonExpired = true;
        this.enabled = true;
    }

    /**
     * @return 根据用户类型，返回用户登录的模块
     * china--中国总公司;admin--保险分公司；sale--销售商；saleclient--销售商下客户
     */
    public String getModule() {
        if ("saleclient".equalsIgnoreCase(this.getAgencyType())) {
            return "sale";
        } else {
            return this.getAgencyType().toLowerCase();
        }
    }

    public Product getProduct(long productid) {
        for (Product product : productList) {
            if (product.getProductid() == productid) {
                return product;
            }
        }
        return null;
    }

    public void roles(String... roles) {
        List<GrantedAuthority> authorities = new ArrayList<>(roles.length);
        for (String role : roles) {
            Assert.isTrue(!role.startsWith("ROLE_"),
                    () -> role + " cannot start with ROLE_ (it is automatically added)");
            authorities.add(new SimpleGrantedAuthority("ROLE_" + role));
        }
        this.authorities = authorities;
    }
}

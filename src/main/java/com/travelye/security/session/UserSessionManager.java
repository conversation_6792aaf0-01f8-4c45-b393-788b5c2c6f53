package com.travelye.security.session;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import org.springframework.security.core.context.SecurityContextHolder;


/**
 * <AUTHOR>
 */
public class UserSessionManager {
    // User对象在Session中的标示
    public final static String SESSION_USER_KEY = "SESSION_USER_KEY";
    private final static Logger log = LoggerFactory.getLogger(UserSessionManager.class);

    /**
     * @param request
     * @return 获得已经登录的用户
     */
    public static SecurityUser getSecurityLoginUser(HttpServletRequest request) {
//        HttpSession ssn = request.getSession(false);
//        SecurityUser user = null;
//
//        if (ssn != null) {
//            user = (SecurityUser) ssn.getAttribute(SESSION_USER_KEY);
//            return user;
//        }
//
//        return user;

        return (SecurityUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
    }

    public static SecurityUser getSecurityLoginUser() {
//        HttpSession ssn = request.getSession(false);
//        SecurityUser user = null;
//
//        if (ssn != null) {
//            user = (SecurityUser) ssn.getAttribute(SESSION_USER_KEY);
//            return user;
//        }
//
//        return user;
        if (SecurityContextHolder.getContext() != null
                && SecurityContextHolder.getContext().getAuthentication() != null
                && SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof SecurityUser securityUser
        ) {
            return securityUser;
        } else {
            return null;
        }

    }


    /**
     * 用户退出
     *
     * @param req
     */
    public static void logoutUser(HttpServletRequest req) {

        HttpSession ssn = req.getSession(false);

        if (ssn != null) {
            Object o = req.getSession().getAttribute(SESSION_USER_KEY);
            ssn.removeAttribute(SESSION_USER_KEY);
        }

    }

    public static void loginSecurityUser(HttpServletRequest req, SecurityUser user) {
        updateSecurityLoginUser(req, user);
    }

    public static void updateSecurityLoginUser(HttpServletRequest request, SecurityUser securityUser) {
        HttpSession ssn = request.getSession();
        if (ssn != null) {
            ssn.setAttribute(SESSION_USER_KEY, securityUser);
        }
    }


}

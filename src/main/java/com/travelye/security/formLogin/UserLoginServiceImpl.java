package com.travelye.security.formLogin;

import com.travelye.security.common.CommonSecurityUserDetailService;
import com.travelye.security.session.SecurityUser;
import com.travelye.user.UserService;
import com.travelye.vo.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

@Component("userLoginService")
public class UserLoginServiceImpl extends CommonSecurityUserDetailService implements UserDetailsService {

    @Autowired
    UserService userService;

    @Override
    public SecurityUser loadUserByUsername(String username) throws UsernameNotFoundException {
        Users user = userService.getUser(username);
        if (user != null && user.isOkta()) {
            throw new InternalAuthenticationServiceException("请使用Okta登录！");
        }
        if (user == null || user.getStatus() != 1) {
            throw new InternalAuthenticationServiceException("无效的用户！");
        }

        SecurityUser securityUser = new SecurityUser();

        setAgencyInformation(securityUser, user, true);

        return securityUser;
    }
}

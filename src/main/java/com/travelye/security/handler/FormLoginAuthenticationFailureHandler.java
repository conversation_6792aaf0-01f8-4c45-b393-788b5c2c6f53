package com.travelye.security.handler;

import com.travelye.user.UserService;
import com.travelye.vo.Users;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@Component
@Slf4j
public class FormLoginAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private final UserService userService;

    public FormLoginAuthenticationFailureHandler(UserService userService) {
        this.userService = userService;
    }

    @Override
    public void onAuthenticationFailure(HttpServletRequest request,
                                        HttpServletResponse response,
                                        AuthenticationException exception)
            throws IOException, ServletException {
        log.warn("登录失败", exception);
        if (exception instanceof BadCredentialsException) {
            String loginName = request.getParameter("username");
            Users user = userService.getUser(loginName);
            if (user != null) {
                userService.addLoginFailureCount(user);
            }
        }
        request.setAttribute("message", exception.getMessage());
        request.getRequestDispatcher("/formLogin?error=true").forward(request, response);

    }


}

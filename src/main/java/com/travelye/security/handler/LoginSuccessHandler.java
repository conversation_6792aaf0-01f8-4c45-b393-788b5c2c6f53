package com.travelye.security.handler;

import java.io.IOException;
import java.time.LocalDateTime;

import com.travelye.security.okta.TYSaml2Authentication;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import com.travelye.security.session.SecurityUser;
import com.travelye.user.UserService;
import com.travelye.vo.ClientInfo;
import com.travelye.vo.UserLoginHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class LoginSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();

    private final UserService userService;

    public LoginSuccessHandler(UserService userService) {
        this.userService = userService;
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws ServletException, IOException {
        SecurityUser securityUser = null;
        if (authentication instanceof TYSaml2Authentication) {
            securityUser = ((TYSaml2Authentication) authentication).getUserDetails();
        }else if(authentication instanceof UsernamePasswordAuthenticationToken){
            securityUser = (SecurityUser) authentication.getPrincipal();
        }
        if (securityUser != null) {
            userService.resetLoginFailureCount(securityUser.getUid());
            userService.loginSuccess(securityUser.getUid());
            insertClientInfo(request);
            log.info("登录成功");
        } else {
            throw new ServletException();
        }

        String targetUrl = "saleclient".equalsIgnoreCase(securityUser.getAgencyType())
                ? "/sale/index"
                : "/" + securityUser.getAgencyType().toLowerCase() + "/index";
        UserLoginHistory userLoginHistory = userService.getUserLoginHistory(securityUser.getUid());
        if (LocalDateTime.now().isAfter(userLoginHistory.getLastLoginTime().plusDays(30))) {
            targetUrl = "saleclient".equalsIgnoreCase(securityUser.getAgencyType())
                    ? "/sale/editpass"
                    : "/" + securityUser.getAgencyType().toLowerCase() + "/editpass";
        }

        redirectStrategy.sendRedirect(request, response, targetUrl);
    }

    private void insertClientInfo(HttpServletRequest request) {
        ClientInfo clientInfo = new ClientInfo();

        clientInfo.setScreenInfo(request.getParameter("resolution"));
        clientInfo.setClientIp(getClientIpAddr(request));
        clientInfo.setSource("login");
        clientInfo.setUserAgent(request.getHeader("User-Agent"));

        userService.insertClientInfo(clientInfo);
    }

    public String getClientIpAddr(HttpServletRequest request) {

        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip != null && ip.length() > 30) {
            ip = ip.substring(0, 30);
        }
        return ip;
    }

}

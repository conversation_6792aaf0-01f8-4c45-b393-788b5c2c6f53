package com.travelye.security.jwt.controller;

import com.travelye.base.ActionSupport;
import com.travelye.security.jwt.UserVo;
import com.travelye.security.jwt.utils.JwtUtils;
import com.travelye.security.jwt.utils.R;
import com.travelye.user.UserService;
import com.travelye.vo.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR> C.R.
 * @date 2023/08/16 10:15
 **/
@Controller
public class JwtController extends ActionSupport {

    @Autowired
    private UserService userService;

    @PostMapping("/user/login")
    @ResponseBody
    public R login(String userName, String password) {
        Users user = userService.getUser(userName);
        if (user == null) {
            return R.error("用户不存在");
        }
        if (!user.getPassword().equals(password)) {
            return R.error("密码错误");
        }


        return R.ok()
                .put("userName", user.getLoginname())
                .put("token", JwtUtils.generateToken(user.getLoginname()));
    }

}

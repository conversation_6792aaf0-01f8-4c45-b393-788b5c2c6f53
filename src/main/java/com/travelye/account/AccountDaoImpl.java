package com.travelye.account;

import com.travelye.base.BaseJdbcDaoImpl;
import com.travelye.base.QueryResult;
import com.travelye.base.SjisConstant;
import com.travelye.base.tools.CommonUtil;
import com.travelye.base.tools.JdbcUtil;
import com.travelye.security.session.SecurityUser;
import com.travelye.vo.Account;
import com.travelye.vo.AccountList;
import com.travelye.vo.Partner;
import com.travelye.vo.Users;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AccountDaoImpl  extends BaseJdbcDaoImpl implements AccountDao{

	public void saveAccount(Account model){
		String sql = null;
		if (model.getAccountID() ==0){
			sql ="Insert into tblAccount(accountname,balance,credits,frozen,createdate ,status ,grade ,agencyid,parentAccountid"
				+ " )values(" 
				+ JdbcUtil.getHtmlStringValue(model.getAccountName()) + "," 
				+ JdbcUtil.getNumberValue(model.getBalance()) + "," 
				+ JdbcUtil.getNumberValue(model.getCredits()) + "," 
				+ JdbcUtil.getNumberValue(model.getFrozen()) + "," 
				
				+ JdbcUtil.getDateTime(model.getCreateDate()) + "," 
				+ JdbcUtil.getHtmlStringValue(model.getStatus()) + "," 
				+ JdbcUtil.getHtmlStringValue(model.getGrade()) + "," 
				+ JdbcUtil.getNumberValue(model.getAgencyID())  + "," 
				+ JdbcUtil.getNumberValue(model.getParentAccountID()) 
				+ ")";
		}else{
			sql ="update tblAccount set accountname = " + JdbcUtil.getHtmlStringValue(model.getAccountName()) 
				+ ",credits = " + JdbcUtil.getNumberValue(model.getCredits()) 
				+ ",status = " + JdbcUtil.getHtmlStringValue(model.getStatus()) 
				+ ",agencyid = " + JdbcUtil.getNumberValue(model.getAgencyID()) 
				+ " where accountID=" + model.getAccountID();
		}
		this.execute(sql);
	}
	
	/**给账户增加金额，减少时fee<0
	 * @param accountID
	 * @param fee
	 */
	public void addAccountBalance(long accountID,Double fee){
		String sql ="update tblAccount set balance = balance + " + fee 
		+ " where accountID=" + accountID;
		this.execute(sql);
	}
	
	/**冻结或解冻
	 * AccountList 扣除保费时，fee在数据库的值为负数，但传进来的model的fee值为正数，因此冻结时 冻结金额=原冻结金额 + fee
	 * @param model
	 */
	public void frozen(AccountList model){
		String sql=null;
		if (model.getAction() ==AccountList.AcountAction.frozen){
			sql ="update tblAccount set frozen = frozen + " + model.getFee() 
			+ " where accountID=" + model.getAccountID();
			this.execute(sql);
			model.setStatus("2");
			model.setFee(0 - model.getFee());//扣除保费时，先设置为负数，然后再设置为正数
			this.addAccountList(model);
			model.setFee(0 - model.getFee());  //扣除保费时，先设置为负数，然后再设置为正数
			model.setAccountListID(this.getPrimaryKey()); //设置自增长id
		}else if (model.getAction() ==AccountList.AcountAction.unfrozen_valid){
			sql ="update tblAccount set frozen = frozen - " + model.getFee() + " ,balance =balance - " + model.getFee()
			+ " where accountID=" + model.getAccountID();
			this.execute(sql);
			
			sql ="update tblAccountList l set status =1" + " ,balance =(select balance from tblAccount a where a.accountID=l.accountID),remark=" +  JdbcUtil.getHtmlStringValue(model.getRemark())
			+ " where accountListID=" + model.getAccountListID();
			this.execute(sql);			
		}else if (model.getAction() ==AccountList.AcountAction.unfrozen_invalid){
			sql ="update tblAccount set frozen = frozen - " + model.getFee()
			+ " where accountID=" + model.getAccountID();
			this.execute(sql);
			
			sql ="update tblAccountList set status =0"
			+ " where accountListID=" + model.getAccountListID();
			this.execute(sql);			
		}
	}

	/** 增加账户明细
	 * @param aList
	 */
	public void addAccountList(AccountList model){
		String sql ="Insert Into tblAccountList(accountID,createTime,uid,balance,fee,remark,feetype,feeNO,status)values(" 
			+ JdbcUtil.getNumberValue(model.getAccountID()) + "," 
			+ JdbcUtil.getDateTime(model.getCreateTime()) + "," 
			+ JdbcUtil.getNumberValue(model.getUid()) + "," 
			+ JdbcUtil.getNumberValue(model.getBalance()) + "," 
			+ JdbcUtil.getNumberValue(model.getFee()) + "," 
			+ JdbcUtil.getHtmlStringValue(model.getRemark()) + "," 
			+ JdbcUtil.getHtmlStringValue(model.getFeeType()) + "," 
			+ JdbcUtil.getHtmlStringValue(model.getFeeNO()) + "," 
			+ JdbcUtil.getHtmlStringValue(model.getStatus()) 
			+ ")";
		this.execute(sql);
	}
	
	/**查询账户明细
	 * @param condition
	 * @param userSession
	 * @param start
	 * @param count
	 * @return
	 */
	public QueryResult queryAccountList(Condition condition,SecurityUser userSession, int start, int count) {
		String sql="select al.*,u.loginName as operator" +
				" from tblaccountList al JOIN tblaccount ac ON al.accountid = ac.accountID" +
				" JOIN tbluser u ON al.uid = u.uid" ;
		if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_admin)){
			sql = sql + " JOIN tblagency a ON a.AgencyID = ac.agencyID" ;
			sql =sql + " and a.branch=" + userSession.getAgencyId();
		}else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_sale)){
			sql =sql + " and ac.agencyID=" + userSession.getAgencyId();
		}else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_saleclient)){
			sql = sql + " JOIN tblagency a ON a.parentAgencyID = ac.agencyID" ;
			sql =sql + " and a.AgencyID=" + userSession.getAgencyId();
		}
		sql =sql + " where  al.status=1 ";
		if (!CommonUtil.isBlank(condition.getStartDate())){
			sql =sql + " and al.createTime >=" + JdbcUtil.getStringValue(condition.getStartDate());
		}
		if (!CommonUtil.isBlank(condition.getEndDate())){
			sql =sql + " and al.createTime <" + JdbcUtil.getStringValue(condition.getEndDate() + " 23:59:59");
		}
		if (condition.getAccountID() !=0){
			sql =sql + " and ac.accountID=" + condition.getAccountID();
		}
		sql =sql + " order by al.accountListID desc";
		return this.find(sql, start, count,AccountList.class);
	}
	
	/** 列出分公司下一级代理的账户
	 * @param branchID
	 * @return
	 */
	public QueryResult listAccountByBranch(Condition condition,long branchID, int start, int count){
		String sql="SELECT ac.*,a.agencyName FROM tblaccount ac ,tblagency a" +
				"  WHERE ac.agencyid=a.agencyID AND ac.grade=1 AND a.agencyType='sale'" +
				" AND a.branch=" + branchID;
		if (condition.getAgencyID() !=0){
			sql =sql + " and ac.agencyID=" + condition.getAgencyID();
		}
		return this.find(sql, start, count,Account.class);
	}
	
	/**分公司的一级代理商
	 * @param branchID
	 * @return
	 */
	public List<Partner> listAgencyByBranch(long branchID){
		String sql="SELECT a.* FROM tblagency a  WHERE  a.agencyType='sale'" +
		" AND a.branch=" + branchID;
		return this.findObject(sql, Partner.class);
	}

	@Override
	public Account getAccount(long accountID) {
		String sql="select * from tblaccount where accountID =" + accountID;
		List<Account> list =this.findObject(sql, Account.class);
		if (list !=null && list.size()>0){
			return list.get(0);
		}
		return null;
	}
		
	/** 列出代理商所有的账户
	 * @param branchID
	 * @return
	 */
	public List<Account> listAccountByAgency(long agencyID){
		String sql="SELECT ac.* FROM tblaccount ac ,tblagency a  WHERE ac.agencyid=a.agencyID" +
				" AND a.agencyID=" + agencyID + 
				" order by ac.grade";
		return this.findObject(sql, Account.class);
	}

	/**列出代理商的所有用户,包含二级代理商的用户
	 * @param agencyID
	 * @return
	 */
	public List<Users> listUserByAgency(long agencyID){
		String sql="SELECT u.uid,u.realname,u.loginname,IF(ac.accountName IS NULL,'',ac.ACCOUNTNAME) as accountName" +
				" FROM tblagency a join tbluser u on u.agencyid=a.agencyID" +
				" left join tblaccount ac on u.accountID=ac.accountID " +
				" WHERE (a.agencyID=" + agencyID + " or a.parentAgencyID =" + agencyID + ")";
		return this.findObject(sql, Users.class);
	}
	
	/** 修改用户的 账户ID
	 * @param uids 多个用户ID用逗号分隔,23,444,33,55
	 * @param accountID
	 */
	public void updateUserAccount(String uids,long accountID){
		if (uids ==null) return;
		String sql ="update tbluser set accountID = " + accountID 
		+ " where uid in (" + uids + ")";
		this.execute(sql);
	}	
	
	/** 用户是否 是代理商的用户
	 * 所有都是等于0，否则返回不是该代理商的用户数量
	 * @param uids
	 * @param agencyID
	 * @return
	 */
	public int userNotInAgency(String uids,long agencyID){
		String sql="SELECT count(*) FROM tbluser u,tblagency a" +
				" WHERE u.agencyid=a.agencyID AND u.uid IN (" + uids + ")" +
				" AND !(a.agencyID=" + agencyID + " or a.parentAgencyID =" + agencyID + ")";
		return this.findObjectSize(sql);
		
	}
	
	/** 列出账户下的用户
	 * @param accountID
	 * @return
	 */
	public List<Users> listUserByAccount(long accountID){
		if (accountID ==0) return null;
		String sql="SELECT u.uid,u.realname,u.loginname FROM tbluser u " +
				" where u.accountID =" + accountID;
		return this.findObject(sql, Users.class);
	}
}

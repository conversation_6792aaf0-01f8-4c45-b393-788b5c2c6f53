package com.travelye.account;

import com.travelye.security.session.SecurityUser;
import com.travelye.sjis.ajax.json.JSONException;
import com.travelye.base.QueryResult;
import com.travelye.vo.Account;
import com.travelye.vo.AccountList;
import com.travelye.vo.Partner;
import com.travelye.vo.Users;

import java.util.List;

public interface AccountService {
	public void saveAccount(Account model);
	/**
	 * @param jsonStr
	 * @param agencyID,等于0时为分公司保存一级账户，否则为代理商保存二级账户
	 * @return
	 * @throws JSONException
	 */
	public void saveBatchAccount(String jsonStr,long agencyID,SecurityUser userSession) throws JSONException;
	/** 保存用户的账户
	 * @param jsonStr
	 * @param accountID
	 * @param agencyID
	 * @throws JSONException
	 */
	public void saveUserAccount(String jsonStr,long accountID,long agencyID) throws JSONException;

	public void saveRecharge(AccountList model, SecurityUser userSession) ;
	public Account getAccount(long accountID);
	public QueryResult listAccountByBranch(Condition condition,long branchID, int start, int count);
	public List<Partner> listAgencyByBranch(long branchID);
	/**查询账户明细
	 * @param condition
	 * @param userSession
	 * @param start
	 * @param count
	 * @return
	 */
	public QueryResult queryAccountList(Condition condition,SecurityUser userSession, int start, int count) ;
	
	/** 列出代理商所有的账户
	 * @param branchID
	 * @return
	 */
	public List<Account> listAccountByAgency(long agencyID);
	/**列出代理商的所有用户,包含二级代理商的用户
	 * @param agencyID
	 * @return
	 */
	public List<Users> listUserByAgency(long agencyID);
	/** 列出账户下的用户
	 * @param accountID
	 * @return
	 */
	public List<Users> listUserByAccount(long accountID);

	/**扣除保费
	 * @param model
	 */
	public void saveMinFee(AccountList model);
	/**冻结
	 * @param model
	 * @return 返回null，表示冻结成功，否则返回错误提示信息
	 */
	public String saveFrozenFee(AccountList model);
	/** 解冻 预付款
	 * 根据model.getAction 
	 * @param model
	 * @return 返回null，表示解冻成功，否则返回错误提示信息
	 */
	public String saveUnFrozenFee(AccountList model);

}

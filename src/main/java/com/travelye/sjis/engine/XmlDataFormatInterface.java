package com.travelye.sjis.engine;

import com.travelye.vo.Policy;
import com.travelye.vo.Policyuser;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 构建保单各种xml文件（申请、删除、修改、取消）
 *
 * <AUTHOR>
 */
public interface XmlDataFormatInterface {

    /**
     * 生成向WAATS申请提交新保单的XML字符串
     *
     * @param policy     保单信息
     * @param insuredLst 被保险人名单
     * @return 符合WAATS要求的XML字符串
     * @date 2011-05-24
     */
    public String constructApplyPolicy(Policy policy, List<Policyuser> insuredList, String transactionType);

    /**
     * 生成向WAATS申请查询保单的XML字符串
     *
     * @param policy 保单信息
     * @return 符合WAATS查询要求的XML字符串
     * <AUTHOR>
     * @date 2011-05-24
     */
    public String constructQueryPolicy(Policy policy);

    /**
     * 生成向WAATS申请取消保单的XML字符串
     *
     * @param policy
     * @return
     * <AUTHOR>
     * @date 2011-05-24
     */
    public String constructCancelPolicy(Policy policy);

    public String constructNoWattsApplyPolicy(Policy policy, List<Policyuser> insuredLst, String policyChange, LocalDateTime transaction);

}

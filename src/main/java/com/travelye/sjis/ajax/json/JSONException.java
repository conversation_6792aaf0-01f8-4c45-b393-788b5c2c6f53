package com.travelye.sjis.ajax.json;

/**
 * The JSONException is thrown by the JSON.org classes then things are amiss.
 * <AUTHOR>
 * @version 2
 */
@SuppressWarnings("serial")
public class JSONException extends Exception {
    private Throwable cause;

    /**
     * Constructs a JSONException with an explanatory message.
     * @param message Detail about the reason for the exception.
     */
    public JSONException(String message) {
        super(message);
    }

    public JSONException(Throwable t) {
        super(t.getMessage());
        this.cause = t;
    }

    public Throwable getCause() {
        return this.cause;
    }
}

package com.travelye.external;

import com.travelye.base.MessageException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

/**交易对象
 * <AUTHOR>
 *
 */
public class Transaction {

	public static String NSELL="NSell";                   //新单
	public static String CANCELLATION="Cancellation";     //取消保单
	public static String POLICY="Policy";     			  //查询保单
	public static String NQUOTE="NQuote";				  //报价
	public static String GENERATEDOC="GenerateDoc";		  //获取pdf	
	public static String ENDORSEMENT="Endorsement";		  //获取德国批注的pdf
	public static String ENDORSEMENTESV="EndorsementESV";		  //获取德国批注的pdf
	public static String INVOICE="Invoice";		          //发票信息	
	
	private long id;        //对象主键
	private long agencyUserId ;  //代理商使用的用户id
	private String transactionId;
	private String transactionType;//NSell表示新单购买,NQuote询价,Cancellation表示取消，Policy表示查询保单,GenerateDoc生成pdf
	private String uniqueTransactionChk;
	private String iATACntryCd;
	private String gDSCode;
	private String agencyPCC;
	private String agencyCode;
	private String originatingCity;  //出发地
	private String furthestCity;     //目的地
	
	private String gDSProductCode;
	private String transactionApplDateStr;
	private String inceptionDateStr;
	private String expirationDateStr;
	private String flightNo ;   //航班号
	private String invoiceTag;  //是否要发票
	private String invoice;  //发票
	private String comment;  //备注 
	private String department;  //客户部门 
	private String clientUser;  //客户用户 
	private boolean hasExtend; //是否有扩展信息
	private String policyHolder;  //投保人姓名 
	private String isRed;  			//是否使用红章,1使用红章，打印背景图，否则不适用，打印背景图
	
	private String policyNumber;
	
	private long policyId;          //投保单的id
	private LocalDateTime requesttime;       //服务器接收时间
	private String lineNo;           //线路团号
	private String attn;             //经办人，用于渠道中青旅传它系统的销售人员
	private String remark;          //保单备注

	private String feeNo;           //支付流水号  见费出单的渠道线上支付时（即feeType = 1 时）必传
	private int  feeType;           //支付类型   1：见费出单的渠道线上支付

	private String callbackUrl;     //异步回调地址；暂时只支持在线支付，日后可扩展支持接口出单成功的通知
	private String redirectUrl;     //支付跳转地址

	public Transaction(){
		this.requesttime =LocalDateTime.now();
	}
	
	public LocalDateTime getRequesttime() {
		return requesttime;
	}

	public void setRequesttime(LocalDateTime requesttime) {
		this.requesttime = requesttime;
	}

	public long getPolicyId() {
		return policyId;
	}

	public void setPolicyId(long policyId) {
		this.policyId = policyId;
	}

	private List<Insured> insuredList;  //被保人列表
	
	
	public LocalDateTime getTransactionApplDate(){
		try {
			return LocalDateTime.parse(this.getTransactionApplDateStr(), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH));
		} catch (Exception e) {
			throw new MessageException("120");
		}
	}
	
	public LocalDateTime getInceptionDate(){
		try {
			return LocalDateTime.parse(this.getInceptionDateStr(), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH));
		} catch (Exception e) {
			throw new MessageException("113");

		}
	}
	
	public LocalDateTime getExpirationDate(){
		try {
			return LocalDateTime.parse(this.getExpirationDateStr(), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH));
		} catch (Exception e) {
			throw new MessageException("117");

		}
	}

	public long getId() {
		return id;
	}

	public String getTransactionApplDateStr() {
		return transactionApplDateStr;
	}

	public void setTransactionApplDateStr(String transactionApplDateStr) {
		this.transactionApplDateStr = transactionApplDateStr;
	}

	public String getInceptionDateStr() {
		return inceptionDateStr;
	}

	public void setInceptionDateStr(String inceptionDateStr) {
		this.inceptionDateStr = inceptionDateStr;
	}

	public String getExpirationDateStr() {
		return expirationDateStr;
	}

	public void setExpirationDateStr(String expirationDateStr) {
		this.expirationDateStr = expirationDateStr;
	}

	public void setId(long id) {
		this.id = id;
	}


	public long getAgencyUserId() {
		return agencyUserId;
	}

	public void setAgencyUserId(long agencyUserId) {
		this.agencyUserId = agencyUserId;
	}

	public String getTransactionId() {
		return transactionId;
	}

	public void setTransactionId(String transactionId) {
		this.transactionId = transactionId;
	}

	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	public String getIATACntryCd() {
		return iATACntryCd;
	}

	public void setIATACntryCd(String cntryCd) {
		iATACntryCd = cntryCd;
	}

	public String getGDSCode() {
		return gDSCode;
	}

	public void setGDSCode(String code) {
		gDSCode = code;
	}

	public String getAgencyPCC() {
		return agencyPCC;
	}

	public void setAgencyPCC(String agencyPCC) {
		this.agencyPCC = agencyPCC;
	}

	public String getAgencyCode() {
		return agencyCode;
	}

	public void setAgencyCode(String agencyCode) {
		this.agencyCode = agencyCode;
	}

	public String getGDSProductCode() {
		return gDSProductCode;
	}

	public void setGDSProductCode(String productCode) {
		gDSProductCode = productCode;
	}


	public List<Insured> getInsuredList() {
		return insuredList;
	}

	public void setInsuredList(List<Insured> insuredList) {
		this.insuredList = insuredList;
	}

	public String getUniqueTransactionChk() {
		return uniqueTransactionChk;
	}

	public void setUniqueTransactionChk(String uniqueTransactionChk) {
		this.uniqueTransactionChk = uniqueTransactionChk;
	}

	public String getPolicyNumber() {
		return policyNumber;
	}

	public void setPolicyNumber(String policyNumber) {
		this.policyNumber = policyNumber;
	}

	public String getOriginatingCity() {
		return originatingCity;
	}

	public void setOriginatingCity(String originatingCity) {
		this.originatingCity = originatingCity;
	}

	public String getFurthestCity() {
		return furthestCity;
	}

	public void setFurthestCity(String furthestCity) {
		this.furthestCity = furthestCity;
	}

	public String getFlightNo() {
		return flightNo;
	}

	public void setFlightNo(String flightNo) {
		this.flightNo = flightNo;
	}

	public String getIsRed() {
		return isRed;
	}

	public void setIsRed(String isRed) {
		this.isRed = isRed;
	}

	public String getInvoiceTag() {
		return invoiceTag;
	}

	public void setInvoiceTag(String invoiceTag) {
		this.invoiceTag = invoiceTag;
	}

	public String getInvoice() {
		return invoice;
	}

	public void setInvoice(String invoice) {
		this.invoice = invoice;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
		if (comment !=null && !"".equals(comment)){
			this.hasExtend =true;
		}
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
		if (department !=null && !"".equals(department)){
			this.hasExtend =true;
		}
	}

	public String getClientUser() {
		return clientUser;
	}

	public void setClientUser(String clientUser) {
		this.clientUser = clientUser;
		if (clientUser !=null && !"".equals(clientUser)){
			this.hasExtend =true;
		}
	}

	public boolean getHasExtend() {
		return hasExtend;
	}

	public void setHasExtend(boolean hasExtend) {
		this.hasExtend = hasExtend;
	}

	public String getPolicyHolder() {
		return policyHolder;
	}

	public void setPolicyHolder(String policyHolder) {
		this.policyHolder = policyHolder;
	}

	public String getLineNo() {
		return lineNo;
	}

	public void setLineNo(String lineNo) {
		this.lineNo = lineNo;
	}

	public String getAttn() {
		return attn;
	}

	public void setAttn(String attn) {
		this.attn = attn;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getFeeNo() {
		return feeNo;
	}

	public void setFeeNo(String feeNo) {
		this.feeNo = feeNo;
	}

	public int getFeeType() {
		return feeType;
	}

	public void setFeeType(int feeType) {
		this.feeType = feeType;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getRedirectUrl(){
		return redirectUrl;
	}

	public void setRedirectUrl(String redirectUrl) {
		this.redirectUrl = redirectUrl;
	}
}

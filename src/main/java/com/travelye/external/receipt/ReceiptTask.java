package com.travelye.external.receipt;

import java.time.LocalDateTime;

public class ReceiptTask {
    public static final String RECEIPT_TASK_APPLY = "apply";
    public static final String RECEIPT_TASK_NOTIFY = "notify";

    private long receiptId;
    private int retryCount;
    private LocalDateTime nextRunTime;
    private String taskType;

    public long getReceiptId() {
        return receiptId;
    }

    public void setReceiptId(long receiptId) {
        this.receiptId = receiptId;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public LocalDateTime getNextRunTime() {
        return nextRunTime;
    }

    public void setNextRunTime(LocalDateTime nextRunTime) {
        this.nextRunTime = nextRunTime;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }
}

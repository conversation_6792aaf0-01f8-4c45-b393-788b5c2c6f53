package com.travelye.external.eTravel;

import com.travelye.external.LibertyErrorCode;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.math.NumberUtils;


import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DecimalFormat;

public class XmlToResponse {
    private final static Logger log = LoggerFactory.getLogger(XmlToResponse.class);
    private static DecimalFormat df_00 = new DecimalFormat("0.00");

    public static String toLibertyResponse(String libertyResponse, Transaction transaction) {
        return "liberty";
    }

    public static String toLibertyErrorResponse(String requestXML, String errorCode) {
        try {
            String errorMessage = "";
            if (!errorCode.isEmpty() && NumberUtils.isCreatable(errorCode)) {
                LibertyErrorCode errorEnum = LibertyErrorCode.valueOfCode(Integer.parseInt(errorCode));
                if (errorEnum == null) {
                    errorCode = "-1";
                    errorMessage = "SYSTEM ERROR. PLEASE, TRY AGAIN LATER";
                } else {
                    errorMessage = errorEnum.getErrorMessage();
                }
            } else if (errorCode.contains("已存在相似保单")) {
                errorMessage = errorCode;
                errorCode = "278";
            } else {
                errorMessage = errorCode;
                errorCode = "999";
            }


            log.error(errorCode + errorMessage);
            String parseXml = requestXML.substring(requestXML.indexOf("<TINS_XML_DATA"));
            Document document = DocumentHelper.parseText(parseXml);
            Element header = (Element) document.selectSingleNode("/TINS_XML_DATA/Header");
            header.addElement("ErrorCode").setText("0");
            header.addElement("ErrorMessage").setText("OK");

            Element segment = (Element) document.selectSingleNode("/TINS_XML_DATA/Segment");
            segment.addElement("ErrorCode").setText(errorCode);
            segment.addElement("ErrorMessage").setText(errorMessage);
            return document.asXML().replace("\n", "").replace("\r", "");
        } catch (Exception e) {
            log.error("", e);
        }

        return "";
    }

    /**
     * 按照waats的格式 连接字符串
     *
     * @param xml
     * @param finishCode
     * @return
     */
    public static String getWaatsString(String xml, String finishCode) {
        StringBuffer buffer = new StringBuffer("<startwaats>");
        if ("0".equals(finishCode)) {
            buffer.append("\n").append(finishCode);
            buffer.append("\n").append("OK");
        } else if ("1".equals(finishCode)) {
            buffer.append("\n").append(finishCode);
            buffer.append("\n").append("超时");
        } else if ("2".equals(finishCode)) {
            buffer.append("\n").append(finishCode);
            buffer.append("\n").append("错误的GDS");
        } else if ("3".equals(finishCode)) {
            buffer.append("\n").append(finishCode);
            buffer.append("\n").append("错误的信息类型");
        } else if ("4".equals(finishCode)) {
            buffer.append("\n").append(finishCode);
            buffer.append("\n").append("系统没有响应");
        } else {
            buffer.append("\n").append("5");
            buffer.append("\n").append("空信息");
        }
        buffer.append("\n").append(xml);
        buffer.append("\n<endwaats>");
        return buffer.toString();
    }

    public static String pdfToXml(String xml, byte[] pdf) {
        try {
            String parseXml = xml.substring(xml.indexOf("<TINS_XML_DATA"));
            Document document = DocumentHelper.parseText(parseXml);
            Element header = (Element) document.selectSingleNode("/TINS_XML_DATA/Header");
            header.addElement("ErrorCode").setText("0");
            header.addElement("ErrorMessage").setText("OK");
            if (pdf != null) {
                Element segment = (Element) document.selectSingleNode("/TINS_XML_DATA/Segment");
                segment.addElement("ErrorCode").setText("0");
                segment.addElement("ErrorMessage").setText("OK");
//				Element policyOut =segment.addElement("PolicyOut");
                Base64 base64 = new Base64();
                byte[] enbytes = base64.encode(pdf);
                segment.addElement("PolicyDoc").setText(new String(enbytes));
            }
            return document.asXML();
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

}

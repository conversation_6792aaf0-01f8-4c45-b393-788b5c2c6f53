package com.travelye.external.eTravel;

import com.travelye.base.ActionSupport;
import com.travelye.base.MessageException;
import com.travelye.external.concurrent.Queue;
import com.travelye.external.eTravel.service.ETravelService;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.dom4j.DocumentHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 旅行险 对接接口
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/external")
@Slf4j
public class ETravelAction extends ActionSupport {
    @Autowired
    private ETravelService eTravelService;

    /**
     * 请求的主方法
     *
     * @return
     */
    @RequestMapping({"/eTravel.action", "/policyPDF.action"})
    @ResponseBody
    public String travel() {
        String messageText = request.getParameter("MessageText");

        log.info("接口请求的交易数据: url={}://{}{},messageText--{}", request.getScheme(), request.getServerName(), request.getRequestURI(), messageText);


        String responseMessage = null;
        Transaction transaction = null;
        try {

            if (messageText == null || messageText.isBlank()) {
                try {
                    messageText = getMessageTextFromInputStream(request.getInputStream());
                } catch (IOException e) {
                    throw new MessageException("", "5");
                }
            }

            if (messageText == null || messageText.isBlank()) {
                throw new MessageException("", "5");
            } else {
                //把报文解析成对象
                transaction = XmlToTransaction.xmlToTransaction(messageText);

                if (transaction == null) {
                    throw new MessageException("", "5");
                }
                transaction.setRequestXML(messageText);
                transaction.setRequestTime(LocalDateTime.now());
                log.info("接口请求的交易数据:agencyUserId--" + transaction.getAgencyUserId() + "TransactionId:" + transaction.getSegment().getTransactionId() + ",requestXML--" + messageText);


            }
        } catch (MessageException m) {
            if (m.getErrorCode().equals("5")) {
                responseMessage = XmlToResponse.getWaatsString("", "3");
            } else {
                responseMessage = XmlToResponse.toLibertyErrorResponse(messageText, m.getMessage());
            }
        } catch (Exception e) {
            log.info("ETravel API Exception: ", e);
            responseMessage = XmlToResponse.getWaatsString("", "3");
        }

        if (responseMessage == null) {
            try {
                switch (transaction.getSegment().getTransactionType().toUpperCase()) {
                    case Transaction.NSELL:
                        transaction = eTravelService.NSell(transaction);
                        break;
                    case (Transaction.NQUOTE):
                        transaction = eTravelService.NQuote(transaction);
                        break;
                    case (Transaction.CANCELLATION):
                        transaction = eTravelService.Cancellation(transaction);
                        break;
                    case (Transaction.POLICY):
                        break;
                    case (Transaction.GENERATEDOC):
                        transaction = eTravelService.GenerateDoc(transaction);
                        break;
                }
                responseMessage = transaction.getResponseXML();
                eTravelService.saveExternal(transaction.getRequestXML(), responseMessage, transaction, transaction.isSuccessFlag() ? "1" : "0");
            } catch (MessageException e) {
                log.error("ETravel MessageException: ", e);
                responseMessage = waatsXMLTag(XmlToResponse.toLibertyErrorResponse(messageText, e.getMessage()), false);
                eTravelService.saveExternal(transaction.getRequestXML(), responseMessage, transaction, "0");
            } catch (Exception e) {
                log.error("ETravel Exception: ", e);
                responseMessage = waatsXMLTag(XmlToResponse.toLibertyErrorResponse(messageText, "系统异常，请稍候重试。"), true);
                eTravelService.saveExternal(transaction.getRequestXML(), responseMessage, transaction, "0");
            } finally {
//                AgencyQueue.remove(transaction.getAgencyUserId() + "#U#" + transaction.getSegment().getTransactionType());
                Queue.remove(transaction.getAgencyUserId() + "#T#" + transaction.getSegment().getTransactionType(), transaction.getSegment().getTransactionId());
            }
        }

        request.setAttribute("message", responseMessage);
        return responseMessage;
    }

    private String waatsXMLTag(String responseXML, boolean systemError) {
        StringBuffer buffer = new StringBuffer("<startwaats>");
        if (!systemError) {
            buffer.append("\n0");
            buffer.append("\nOK");
        } else {
            buffer.append("\n-1");
            buffer.append("\n系统没有响应");
        }
        buffer.append("\n").append(responseXML);
        buffer.append("\n<endwaats>");
        return buffer.toString();
    }

    public String getMessageTextFromInputStream(InputStream inputStream) throws IOException {
        String inputStreamAsString = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        log.info("-----------inputStreamAsString:" + inputStreamAsString);
        boolean isXml = true;
        try {
            DocumentHelper.parseText(inputStreamAsString);
        } catch (Exception e) {
            isXml = false;
        }
        if (isXml) {
            return URLDecoder.decode(inputStreamAsString, StandardCharsets.UTF_8);
        }
        String[] resultList = inputStreamAsString.split("&");
        List<String> result = Arrays.stream(resultList).filter(s -> s.contains("MessageText=")).collect(Collectors.toList());
        String messageText = result.size() == 0 ? "" : result.get(0).replace("MessageText=", "");
        return URLDecoder.decode(messageText, StandardCharsets.UTF_8);
    }


}

package com.travelye.external.down.sinosoft.service;

import org.springframework.stereotype.Service;

@Service
public class SinosoftServiceImpl implements SinosoftService {

    /**
     * 查询报价单/保单/批单请求的状态，以及对应的报价单/保单/批单信息
     *
     * @param businessType 请求类型
     *                     <ul>
     *                         <li>01 - 报价</li>
     *                         <li>02 - 保单</li>
     *                         <li>03 - 批改报价</li>
     *                         <li>04 - 批单</li>
     *                     </ul>
     * @param businessNo   业务单号
     *                     <ul>
     *                         <li>businessType=01，传报价请求交易流水号</li>
     *                         <li>businessType=02，传报价单号</li>
     *                         <li>businessType=03，传批改交易流水号</li>
     *                         <li>businessType=04，传批改报价单号</li>
     *                     </ul>
     */
    public String getAsyncResult(String businessType, String businessNo) {
        return null;
    }
}

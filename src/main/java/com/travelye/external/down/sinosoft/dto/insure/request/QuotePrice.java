package com.travelye.external.down.sinosoft.dto.insure.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date --
 **/
@Data
@Builder
public class QuotePrice {

    /**
     * 报价类型 0-快速报价 1-正式报价
     */
    private String quoteType;

    /**
     * 投保公共信息
     */
    private Main main;

    /**
     * 投保人信息
     */
    private List<AppliClient> appliClient;

    /**
     * 销售信息
     */
    private Sales sales;

    /**
     * 风险辅助信息
     */
    private RiskInfo riskInfo;

    /**
     * 保障信息
     */
    private Coverage coverage;

    /**
     * 保单限额/免赔信息
     */
    private List<LimitList> limitList;
}

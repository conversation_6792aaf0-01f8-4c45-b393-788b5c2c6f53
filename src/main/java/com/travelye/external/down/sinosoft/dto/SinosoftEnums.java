package com.travelye.external.down.sinosoft.dto;

import lombok.Getter;

/**
 * <AUTHOR> C.R.
 * @date 2023/06/22 23:40
 **/
public class SinosoftEnums {

    /**
     * 境内外标志
     */
    public enum NationFlag {
        DOMESTIC("0"), OVERSEAS("1");

        private final String value;

        NationFlag(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum Currency {
        RMB("RMB");

        private final String value;

        Currency(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    @Getter
    public enum IdTypes {
        /**
         * 身份证
         */
        NID("111"),
        /**
         * 台湾居民往来大陆通行证，Mainland Travel Permit for Taiwan Residents
         */
        MPT("511"),
        /**
         * 港澳居民来往内地通行证，Mainland Travel Permit for Hong Kong and Macao Residents
         */
        MPHK("516"),
        /**
         * 出生证，Birth Certificate
         */
        BC("117"),
        /**
         * 军官证，Military  Certificate
         */
        MID("114"),
        /**
         * 台湾通行证，Taiwan Travel Permit for Mainland Compatriots
         */
        TPM("517"),
        /**
         * 港澳通行证，Hong Kong and Macao Travel Permit for Mainland Compatriots
         */
        HKPM("515"),
        /**
         * 其他证件，Other Identification
         */
        OID("990"),
        /**
         * 中国护照，China Passport
         */
        CPP("E05"),
        /**
         * 外国护照，Foreign Passport
         */
        FPP("E01"),
        /**
         * 营业执照，Business License
         */
        BL("3"),
        /**
         * 统一社会信用代码，Unified Social Credit Identifier
         */
        USCI("10");


        private final String value;

        IdTypes(String value) {
            this.value = value;
        }

    }

    @Getter
    public enum Gender {
        /**
         * 未知
         */
        UNKNOWN("0"),
        MALE("1"),
        FEMALE("2"),
        /**
         * 其他性别
         */
        OTHER("9");

        private final String value;

        Gender(String value) {
            this.value = value;
        }

    }

    public enum ComCode {
        HEADER_QUARTERS("729000"),
        SH("729010"),
        GD("729020"),
        GD_YXFWB("729021"),
        GD_FSZG("729022"),
        GD_DG("729023"),
        GD_ZS("729024"),
        GD_HZ("729025"),
        SZ("729030"),
        BJ("729040"),
        JS("729050"),
        JS_SZ("729052"),
        ZJ("729060"),
        HY("729070");

        private final String value;

        ComCode(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    public enum AgentType {
        DIRECT_BUSINESS("0101"),
        PROFESSIONAL_AGENT("0201"),
        PERSONAL_AGENT("0202"),
        PART_TIME_AGENT("0203"),
        BROKER_COMPANY("0301"),
        INTERNET_ENTERPRISE_AGENT("0401"),
        OTHER("9901");

        private final String value;

        AgentType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public enum InsuredRelation {
        POLICY_HOLDER("00"),
        SPOUSE("01"),
        PARENTS("02"),
        CHILDREN("03"),
        SIBLINGS("05"),
        EMPLOYER("06"),
        EMPLOYEE("07"),
        GRANDPARENTS("08"),
        GRANDCHILDREN("09"),
        GUARDIAN("10"),
        WARD("11"),
        FRIEND("12"),
        UNKNOWN("98"),
        OTHER("99");

        private final String value;

        InsuredRelation(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    @Getter
    public enum Branch {

        COMPANY("729000","美亚财产保险有限公司"),
        HEAD_OFFICE("729080","美亚财产保险有限公司总公司"),
        SHANGHAI_BRANCH("729010","美亚财产保险有限公司上海分公司"),
        GUANGDONG_BRANCH("729020","美亚财产保险有限公司广东分公司"),
        YUEXIU_MARKETING("729021","美亚财产保险有限公司广东分公司越秀营销服务部"),
        FOSHAN_SUB_BRANCH("729022","美亚财产保险有限公司广东分公司佛山支公司"),
        DONGGUAN_MARKETING("729023","美亚财产保险有限公司广东分公司东莞营销服务部"),
        ZHONGSHAN_MARKETING("729024","美亚财产保险有限公司广东分公司中山营销服务部"),
        HUIZHOU_MARKETING("729025","美亚财产保险有限公司广东分公司惠州营销服务部"),
        SHENZHEN_BRANCH("729030","美亚财产保险有限公司深圳分公司"),
        BEIJING_BRANCH("729040","美亚财产保险有限公司北京分公司"),
        JIANGSU_BRANCH("729050","美亚财产保险有限公司江苏分公司"),
        SUZHOU_BUSINESS("729052","美亚财产保险有限公司江苏分公司苏州营业部"),
        ZHEJIANG_BRANCH("729060","美亚财产保险有限公司浙江分公司"),
        SHIPPING_INSURANCE_CENTER("729070","美亚财产保险有限公司航运保险运营中心");

        private final String code;
        private final String name;

        Branch(String code,String name) {
            this.code = code;
            this.name = name;
        }

    }


}

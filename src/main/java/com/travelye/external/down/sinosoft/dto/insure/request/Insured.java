package com.travelye.external.down.sinosoft.dto.insure.request;

import com.travelye.external.down.sinosoft.dto.SinosoftEnums;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 被保险人信息
 *
 * <AUTHOR>
 * @date 2023/06/16 01:19
 */
@Data
@Builder
public class Insured {

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 出生日期 yyyy-MM-dd
     */
    private String birthday;

    /**
     * 生效终止日期
     */
    private LocalDate endDate;

    /**
     * 证件类型
     *
     * @see SinosoftEnums.IdTypes
     */
    private String identifyType;

    /**
     * 证件号码
     */
    private String identifyNumber;

    /**
     * 证件有效期
     */
    private LocalDate identifyPeriod;

    /**
     * 姓名
     */
    private String insuredName;

    /**
     * 英文名
     */
    private String insuredEName;

    /**
     * 保障方案序号，被保险人对应标的信息的序号
     */
    private String itemNo;

    /**
     * 出发日期 yyyy-MM-dd
     */
    private String originDate;

    /**
     * 总含税保费（保单币种）
     */
    private Double premium;

    /**
     * 备注
     */
    private String remark;

    /**
     * 序号，从1开始
     */
    private Integer serialNo;

    /**
     * 性别 0-未知 1-男 2-女 9-未说明
     * @see SinosoftEnums.Gender#getValue()
     */
    private String sex;

    /**
     * 生效起始日期
     */
    private LocalDate startDate;

    // 客户地址
    private String insuredAddress;
    // 民族
    private String nation;
    // 国家代码
    private String country;
    // 省
    private String province;
    // 市
    private String city;
    // 县/区
    private String region;
    // 手机
    private String mobile;
    // 邮箱
    private String email;
    // 个人凭证地址
    private String cerfiAddress;
    // 年度收入，个人保单单张保费>RMB 10k时必填
    private BigDecimal annualIncome;
    // 工作单位，个人保单单张保费>RMB 10k时必填
    private String company;
    // 是否有社保
    private String socialSecurityFlag;
    // 与主被保险人关系
    private String relationToInsured;
    // 与投保人关系 00-本人 01-配偶 02-父母 03-子女 05-兄弟姐妹 06-雇主 07-雇员 08-祖/外祖父母 09-祖孙/外祖孙 10-监护人 11-被监护人 12-朋友 98-未知 99-其他
    private SinosoftEnums.InsuredRelation relationToAppnt;
    // 职业代码，意外险必填
    private String occupationCode;
    // 受益方式
    private String benefitModeCode;
    // 受益分配方式
    private String benefitOrder;
    // 第二证件类型
    private String secondIdType;
    // 第二证件号码
    private String secondIdNum;
    // 证件有效起期
    private LocalDate certiStartDate;
    // 证件有效止期
    private LocalDate certiEndDate;
    // 是否境外客户
    private String isAbroadCustomer;
    // 身份证号码
    private String idCardNo;
    // 职业大分类
    private String occupationGrade;
    // 职业中分类
    private String occupationType;
    // 受益人
    private List<Beneficiary> beneficiary;

}

package com.travelye.external.down.edge.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record QuoteResponse(
        String code,
        String message,
        String quoteId,
        Plan plan,
        Product product
) {
    public record Plan(
            String planCode,
            String planDesc,
            String originalPremium,
            String discountAmount,
            String premium,
            String taxAmount,
            List<Coverage> coverages
    ) {
    }

    public record Product(
            List<Insured> insureds
    ) {
    }

    public record Insured(
            String firstName,
            String lastName,
            String gender,
            String dateOfBirth,
            Plan plan
    ) {
    }

    public record Coverage(
            String coverageCode,
            String coverageDesc,
            String premium,
            String sumInsured
    ) {
    }
}

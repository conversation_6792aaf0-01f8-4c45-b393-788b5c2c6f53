package com.travelye.external.down.edge.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class QuoteRequest {

    public String quoteDate;
    public Integer productCode;
    public String effectiveDate;
    public String expiryDate;
    public Integer agencyPCC;
    public String companyCode;
    public String salesChannel;
    public Campaign campaign;
    public Plan plan;
    public Product product;
    public List<DestinationCountry> destinationCountry;
    public FlightDetails flightDetails;
    public Partner partner;

    public record Campaign(
            String campaignCode
    ) {
    }

    public record Plan(
            String planCode
    ) {
    }

    public record Product(
            String travelType,
            String travelGroupType,
            List<DestinationCountry> destinationCountry,
            FlightDetails flightDetails,
            List<Insured> insureds
    ) {
    }

    public record DestinationCountry(
            String isoCode
    ) {
    }

    public record FlightDetails(
            String flightNumber
    ) {
    }

    public record Insured(
            String firstName,
            String lastName,
            String gender,
            String dateOfBirth
    ) {
    }

    public record Partner(
            String partnerCode
    ) {
    }
}

package com.travelye.external.down.edge.dto;

import com.travelye.external.down.edge.util.EdgeMessageUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 保险请求对象的主类，包含投保相关所有信息。
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationRequest {

    /**
     * 支付信息
     */
    private final Payment payment = new Payment();
    /**
     * 询价单号
     */
    private String quoteId;
    /**
     * 递交时间（北京时间），格式为 YYYY-MM-DDTHH:mm:ss
     */
    private String offerDate;
    /**
     * 产品代码（Zurich分配的产品代码）
     */
    private int productCode;
    /**
     * 保单生效时间，格式为 YYYY-MM-DDTHH:mm:ss
     */
    private String effectiveDate;
    /**
     * 保单到期时间，格式为 YYYY-MM-DDTHH:mm:ss
     */
    private String expiryDate;
    /**
     * 优惠方案（可选）
     */
    private Campaign campaign;
    /**
     * 方案信息
     */
    private Plan plan;
    /**
     * 保单持有人信息
     */
    private Individual policyHolder;
    /**
     * 产品信息
     */
    private Product product;


    private Integer agencyPCC;
    private String companyCode;
    /**
     * 原GDS Code
     */
    private String salesChannel;

    /**
     * 合作伙伴信息
     */
    private Partner partner;


    @Data
    public static class Campaign {
        /**
         * 优惠方案代码（可选），最大长度20
         */
        private String campaignCode;
    }

    @Data
    @AllArgsConstructor
    public static class Plan {
        /**
         * 方案代码
         */
        private String planCode;

        /**
         * 方案保费
         */
        private double premium;
    }


    @Data
    @AllArgsConstructor
    public static class Phone {
        private String countryCode;
        private String mobile;
    }

    @Data
    public static class Identification {
        private String idType;   // 证件类型（最大长度3）
        private String idNumber; // 证件号码（最大长度50）

        public Identification(String idType, String idNumber) {
            this.idType = EdgeMessageUtil.toIdType(idType);
            this.idNumber = idNumber;
        }

    }

    @Data
    @AllArgsConstructor
    public static class Product {
        private String travelType;       // 旅游类型（SINGLE / ANNUAL）
        private String travelGroupType;  // 团体旅游类型（INDIVIDUAL / GROUP）
        private List<Destination> destinationCountry; // 旅游国家 ISO-3
        private FlightDetails flightDetails; // 航班信息（可选）
        private List<Individual> insureds;
    }

    @Data
    @AllArgsConstructor
    public static class Destination {
        private String isoCode;
    }

    @Data
    static class FlightDetails {
        private String flightNumber; // 航班号
    }

    @Data
    public static class Payment {
        private final int paymentMethod = 11; // 支付方式，默认11
    }

    @Data
    @AllArgsConstructor
    public static class Partner {
        private String partnerCode; // 合作伙伴代码（如HK-TRAVEL）
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Individual {
        private String firstName; // 名（最大长度20）
        private String lastName;  // 姓（最大长度20）
        private String altFirstName; // 英文名（最大长度20）
        private String altLastName; //英文姓（最大长度20）
        private List<Identification> identification; // 证件信息
        private Phone phone; // 手机号（最大长度15）
        private String gender; // 性别（FEMALE / MALE）
        private String dateOfBirth; // 出生日期（YYYY-MM-DD）
        private String emailId; // 邮箱
        private String relationship; // 与投保人关系（SELF, SPOUSE, PARENT等）
        private String nationality; // ISO-2
        private Address address;
        private String groupName; //工作单位名称
        private String occupationCode; //职业代码
        private String annualIncome; //年收入
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Address {
        private String province;
        private String city;
        private String county;
        private String detail;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Beneficiary {
        List<Identification> identification;
        private String beneficiaryType;
        private String beneficiaryName;
        private String percentOfBenefit;
        private String beneficiaryGender;
        private String beneficiaryDateOfBirth;
        private String beneficiaryRelation;
    }
}

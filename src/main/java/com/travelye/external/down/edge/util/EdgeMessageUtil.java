package com.travelye.external.down.edge.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.base.MessageException;
import com.travelye.base.tools.DateUtil;
import com.travelye.external.down.edge.dto.*;
import com.travelye.external.down.edge.dto.ApplicationRequest.*;
import com.travelye.vo.Policy;
import com.travelye.vo.Policyuser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class EdgeMessageUtil {

    private final static String EDGE_DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    public static String quoteMessage(Policy policy) throws JsonProcessingException {
        List<Policyuser> tyInsuredList = policy.getPolicyuserList().stream().filter(i -> i.getIsInsuredFlag() > 0).toList();
        List<QuoteRequest.Insured> edgeInsuredList = tyInsuredList.stream().map(EdgeMessageUtil::toEdgeQuoteInsured).toList();
        QuoteRequest.Product product = new QuoteRequest.Product(
                "SINGLE",
                "INDIVIDUAL",
                Optional.ofNullable(policy.getFurthestCity())
                        .map(s -> Arrays.asList(s.split(",")))
                        .orElseGet(Collections::emptyList)
                        .stream()
                        .map(QuoteRequest.DestinationCountry::new)
                        .collect(Collectors.toList()),
                null,
                edgeInsuredList);

        return JsonMapperUtils.objectToJson(
                QuoteRequest.builder()
                        .quoteDate(formatDateTime(LocalDateTime.now()))
                        .productCode(Integer.parseInt(policy.getProductCode()))
                        .effectiveDate(formatDateTime(policy.getEffectiveDate()))
                        .expiryDate(formatDateTime(policy.getExpiryDate()))
                        .agencyPCC(Integer.parseInt(policy.getAgencyPCC()))
                        .companyCode(policy.getIATACntryCd())
                        .salesChannel(policy.getGDSCode())
                        .plan(new QuoteRequest.Plan(policy.getPlanCode()))
                        .product(product)
                        .partner(new QuoteRequest.Partner("HK-TRAVEL"))
                        .build());
    }

    public static String insureMessage(String offerId) throws MessageException, JsonProcessingException {
        return JsonMapperUtils.objectToJson(
                new InsureRequest(
                        formatDateTime(LocalDateTime.now()),
                        offerId,
                        new InsureRequest.Payment(UUID.randomUUID().toString(), 99)
                )
        );
    }

    public static String applicationMessage(Policy policy, QuoteResponse quoteResponse) throws JsonProcessingException {
        Policyuser applicant = policy.getPolicyuserList().stream().filter(i -> i.getIsInsuredFlag() < 2).findAny().get();
        List<Policyuser> tyInsuredList = policy.getPolicyuserList().stream().filter(i -> i.getIsInsuredFlag() > 0).toList();
        List<Individual> edgeInsuredList = tyInsuredList.stream().map(i -> {
            Individual edgeIndividual = toEdgeIndividual(i);
            edgeIndividual.setRelationship(toRelationShip(i.getRelationship()));
            return edgeIndividual;
        }).toList();

        List<Destination> destinationList = Optional.ofNullable(policy.getFurthestCity())
                .map(s -> Arrays.asList(s.split(",")))
                .orElseGet(Collections::emptyList)
                .stream()
                .map(Destination::new)
                .collect(Collectors.toList());

        ApplicationRequest insureRequest = ApplicationRequest.builder()
                .quoteId(quoteResponse.quoteId())
                .offerDate(formatDateTime(LocalDateTime.now()))
                .effectiveDate(formatDateTime(policy.getEffectiveDate()))
                .expiryDate(formatDateTime(policy.getExpiryDate()))
                .plan(new Plan(policy.getPlanCode(), policy.getTotalPremium()))
                .product(new Product("SINGLE", "INDIVIDUAL", destinationList, null, edgeInsuredList))
                .productCode(Integer.parseInt(policy.getProductCode()))
                .policyHolder(toEdgeIndividual(applicant))
                .partner(new Partner("HK-TRAVEL"))
                .build();

        return JsonMapperUtils.objectToJson(insureRequest);
    }

    public static String cancelMessage(Policy policy) throws JsonProcessingException {
        return JsonMapperUtils.objectToJson(
                CancelRequest.builder()
                        .policyNumber(policy.getPolicyNo())
                        .endorsement(new CancelRequest.Endorsement("NO_REASON", formatDateTime(policy.getSurrenderEffDate() != null ? policy.getSurrenderEffDate() : policy.getEffectiveDate())))
                        .build()
        );
    }

    private static String toGender(String tyGender) {
        return switch (tyGender) {
            case "男" -> "MALE";
            case "女" -> "FEMALE";
            default -> "";
        };
    }

    public static String toIdType(String tyIdType) {
        return switch (tyIdType) {
            case "身份证" -> "111";
            case "临时居民身份证" -> "112";
            case "中国人民解放军军官证" -> "114";
            case "中国人民武装警察部队警官证" -> "115";
            case "普通护照" -> "414";
            case "往来港澳通行证" -> "513";
            case "外国人永久居留身份证" -> "553";
            default -> "990";
        };
    }

    private static String toRelationShip(String tyRelationShip) {
        return switch (tyRelationShip) {
            case "本人 Policyholder" -> "00";
            case "配偶 Spouse" -> "01";
            case "父母 Parents" -> "02";
            case "子女 Child" -> "03";
            case "兄弟姐妹" -> "05";
            case "雇主" -> "06";
            case "雇员" -> "07";
            case "祖父母、外祖父母" -> "08";
            case "祖孙、外祖孙" -> "09";
            case "监护人" -> "10";
            case "被监护人" -> "11";
            case "朋友" -> "12";
            case "未知" -> "98";
            default -> "99";
        };
    }

    private static QuoteRequest.Insured toEdgeQuoteInsured(Policyuser policyuser) {
        return new QuoteRequest.Insured(
                policyuser.getInsuredFullName(),
                policyuser.getInsuredFullName(),
                toGender(policyuser.getGend()),
                DateUtil.format(policyuser.getBirthday()));
    }

    private static Individual toEdgeIndividual(Policyuser policyuser) {
        return Individual.builder()
                .firstName(policyuser.getInsuredFullName())
                .lastName(policyuser.getInsuredFullName())
                .gender(toGender(policyuser.getGend()))
                .emailId(policyuser.getEmailAddr())
                .phone(new Phone("+86", policyuser.getHomePhoneNo()))
                .dateOfBirth(DateUtil.format(policyuser.getBirthday()))
                .identification(toIdentificationList(policyuser))
                .address(new Address(policyuser.getProvince(), policyuser.getCity(), policyuser.getDistrict(), null))
                .build();

    }

    private static List<Identification> toIdentificationList(Policyuser policyuser) {
        List<Identification> identificationList = new ArrayList<>();
        identificationList.add(new Identification(toIdType(policyuser.getInsuredIdNoType()), policyuser.getInsuredIdNo()));
        if (!"居民身份证".equals(policyuser.getInsuredIdNoType()) && StringUtils.isNotBlank(policyuser.getIdentificationID())) {
            identificationList.add(new Identification("111", policyuser.getIdentificationID()));
        }
        return identificationList;
    }

    private static String formatDateTime(LocalDateTime localDateTime) {
        return DateUtil.customFormat(localDateTime, EDGE_DATE_TIME_FORMAT);
    }

}

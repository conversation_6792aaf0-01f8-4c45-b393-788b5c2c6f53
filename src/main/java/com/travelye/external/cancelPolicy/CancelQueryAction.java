package com.travelye.external.cancelPolicy;

import com.travelye.base.ActionSupport;
import com.travelye.base.tools.DateUtil;
import com.travelye.external.data.ExternalService;
import com.travelye.external.health.HealthPolicy;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by zzl on 2017/2/6.
 */
public class CancelQueryAction extends ActionSupport {

    private static final long serialVersionUID = -7079670822081697079L;
    private final static Logger log = LoggerFactory.getLogger(CancelQueryAction.class);
    private HttpServletResponse response;
    private ExternalService externalService;

    public void setExternalService(ExternalService externalService) {
        this.externalService = externalService;
    }

    /**
     * 请求的主方法
     *
     * @return
     */
    public String cancelQuery() {

        log.info("前一天退保记录查询,method=" + request.getMethod() + ",ip=" + this.getClientIpAddr() + ",url=" + request.getScheme() + "://" + request.getServerName() + request.getRequestURI());
        FileInputStream stream = null;

        try {

            String yesterday = request.getParameter("date");

            if (StringUtils.isNotBlank(yesterday)) {
                Pattern pattern_days = Pattern.compile("^[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}");
                if (!pattern_days.matcher(yesterday).matches()) {
                    request.setAttribute("message", "时间格式不正确，格式须为：yyyy-MM-dd（2017-02-15）");
                    return "success";
                }
            } else {
                yesterday = LocalDateTime.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            LocalDateTime lastday = DateUtil.getLocalDateTime(yesterday);

            LocalDateTime tmp = lastday.plusDays(1);

            String fileName = "CancelledPolicies_" + tmp.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".csv";


            //表头
            LinkedHashMap head = new LinkedHashMap();
            head.put("1", "Product Name");
            head.put("2", "Product Code");
            head.put("3", "Plan Name");
            head.put("4", "Plan Code");
            head.put("5", "Policy Number");

            head.put("6", "Endorsement Sequence No.");

            head.put("7", "Policy Status");

            head.put("8", "Total Premium Amount");
            head.put("9", "Policy Holder");
            head.put("10", "Insured Name");
            head.put("11", "InsuredId");
            head.put("12", "Premium per Insured");
            head.put("13", "Certificate No.");
            head.put("14", "Insured Count");


            head.put("15", "Cancellation Date and Time");
            head.put("16", "Inception Date and Time");
            head.put("17", "Expiration Date and Time");
            head.put("18", "Branch Name");
            head.put("19", "Branch Code");
            head.put("20", "Agency Name");
            head.put("21", "Agency PCC Code");
            head.put("22", "Agency Code");
            head.put("23", "GDS Code");


            List<HealthPolicy> list = externalService.CancelPolicy(yesterday);

            List exportData = new ArrayList<Map>();


            for (HealthPolicy policy : list) {
                Map row1 = new LinkedHashMap<String, String>();

                row1.put("1", policy.getProductName());
                row1.put("2", policy.getProductCode());
                row1.put("3", policy.getPlanName());
                row1.put("4", policy.getPlanCode());
                row1.put("5", policy.getPolicyNo());

                row1.put("6", "");
                if ("3".equals(policy.getStatus())) {
                    row1.put("7", "CANCELLED");
                } else if ("4".equals(policy.getStatus())) {
                    row1.put("7", "Pending");
                } else if ("5".equals(policy.getStatus())) {
                    row1.put("7", "Order");
                } else {
                    row1.put("7", "ENDORSED");
                }
                row1.put("8", policy.getTotalPremium());
                row1.put("9", policy.getPolicyholder());
                row1.put("10", policy.getInsuredName());
                row1.put("11", policy.getInsuredID());
                row1.put("12", policy.getPremiumPer());
                row1.put("13", policy.getInsuredIdNo());
                row1.put("14", "1");


                row1.put("15", DateUtil.formatyyyyMMddHHmmss(policy.getCancelTime()));
                row1.put("16", DateUtil.formatyyyyMMddHHmmss(policy.getEffectiveDate()));
                row1.put("17", DateUtil.formatyyyyMMddHHmmss(policy.getExpiryDate()));
                row1.put("18", policy.getBranchName());
                row1.put("19", policy.getBranch());
                row1.put("20", policy.getAgencyName());
                row1.put("21", policy.getAgencyPCC());
                row1.put("22", policy.getAgencyCode());
                row1.put("23", policy.getGDSCode());
                exportData.add(row1);
            }

            File csvFile = createCSVFile(exportData, head, fileName);

            response.setContentType("application/x-download");
            response.setHeader("Content-disposition", "attachment; filename=" + fileName);
            response.setCharacterEncoding("UTF-8");

            ServletOutputStream out = response.getOutputStream();
            //BOM头信息，WINDOWS自带的记事本等软件，在保存一个以UTF-8编码的文件时会在文件开始的地方插入三个不可见的字符（0xEF 0xBB 0xBF，即BOM）。它是一串隐藏的字符，用于让记事本等编辑器识别这个文件是否以UTF-8编码
            out.write(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF});

            stream = new FileInputStream(csvFile);

            int len = 0;
            byte[] buffer = new byte[1024];
            while ((len = stream.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }

        } catch (Exception e) {
            log.error("", e);
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (Exception e) {
                    log.error("", e);
                }
            }
        }

        return "success";
    }


    private File createCSVFile(List exportData, LinkedHashMap map, String fileName) {
        File csvFile = null;
        BufferedWriter csvFileOutputStream = null;
        try {
            //定义文件名格式并创建
            csvFile = File.createTempFile(fileName, ".csv");
            // UTF-8使正确读取分隔符","
            csvFileOutputStream = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(
                    csvFile), "UTF-8"), 1024);
            for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator.hasNext(); ) {
                Map.Entry propertyEntry = (Map.Entry) propertyIterator.next();
                csvFileOutputStream.write((String) propertyEntry.getValue() != null ? new String(
                        ((String) propertyEntry.getValue()).getBytes("GBK"), "GBK") : "");
                if (propertyIterator.hasNext()) {
                    csvFileOutputStream.write(",");
                }
            }
            csvFileOutputStream.newLine();
            // 写入文件内容
            for (Iterator iterator = exportData.iterator(); iterator.hasNext(); ) {
                Object row = (Object) iterator.next();
                for (Iterator propertyIterator = map.entrySet().iterator(); propertyIterator
                        .hasNext(); ) {
                    Map.Entry propertyEntry = (Map.Entry) propertyIterator
                            .next();
                    Object object = BeanUtils.getProperty(row, (propertyEntry.getKey() != null ? (String) propertyEntry.getKey() : ""));
                    /*  log.info("插入数据为："+object);*/
                    csvFileOutputStream.write(String.valueOf(object == null ? "" : object));
                    if (propertyIterator.hasNext()) {
                        csvFileOutputStream.write(",");
                    }
                }
                if (iterator.hasNext()) {
                    csvFileOutputStream.write("\r\n");
                }
            }
            csvFileOutputStream.flush();
        } catch (Exception e) {
            log.error("生成取消保单的csv文件失败：", e);
        } finally {
            try {
                csvFileOutputStream.close();
            } catch (IOException e) {
                log.error("关闭csv文件输出流失败。。。", e);
            }
        }
        return csvFile;
    }
}

package com.travelye.external.ctrip;

import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.base.tools.PropUtil;
import com.travelye.external.data.ExternalService;
import com.travelye.policy.PolicyDao;
import com.travelye.rabbitmaq.RabbitMessagePublisher;
import com.travelye.sjis.engine.HttpUtil;
import com.travelye.vo.Policy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DailyJob {

    private final ExternalService externalService;

    private final PolicyDao policyDao;

    private final RabbitMessagePublisher rabbitMessagePublisher;

    public DailyJob(ExternalService externalService, PolicyDao policyDao, RabbitMessagePublisher rabbitMessagePublisher) {
        this.externalService = externalService;
        this.policyDao = policyDao;
        this.rabbitMessagePublisher = rabbitMessagePublisher;
    }

    private static List<CtripReconcileData> reconcile(List<CtripReconcileData> list) {
        if (CollectionUtils.isEmpty(list)) {
            // 没有出单数据就不处理了
            log.info("CTRIP RECONCILE - no data");
            return null;
        }

        // 每次请求30单
        int cycles = (int) Math.ceil((double) list.size() / 30);

        List<String> failedPolicyIdList = new ArrayList<>();

        for (int i = 0; i < cycles; i++) {
            List<CtripReconcileData> data;
            if (i == cycles - 1) {
                data = list.subList(i * 30, list.size());
            } else {
                data = list.subList(i * 30, (i + 1) * 30);
            }

            try {
                String dataJson = JsonMapperUtils.objectToJson(data);
                log.info("data:{}", dataJson);

                CtripReconcileRequest request = CtripReconcileRequest.builder()
                        .merchantNo(PropUtil.getString("ctrip.reconcile.merchant_no"))
                        .requestTime(LocalDateTime.now())
                        .body(CtripAesUtil.encrypt(dataJson, PropUtil.getString("ctrip.reconcile.aes_key")))
                        .build();

                for (int j = 0; j < 3; j++) {
                    HttpResponse<CtripReconcileRequest> response = HttpUtil.post(PropUtil.getString("ctrip.reconcile.url"), request, HttpUtil.BODY_TYPE_JSON, null, CtripReconcileRequest.class);
                    if (response.statusCode() != 200 || response.body().isError()) {
                        // http code不是200 或是 返回异常，就重发一次
                        continue;
                    }

                    // 失败的数据
                    if (CollectionUtils.isNotEmpty(response.body().getItem())) {
                        List<String> failedPolicyIdThisCycle = response.body().getItem().stream().filter(d -> d.isError() || d.isFail()).map(CtripReconcileData::getItemId).collect(Collectors.toList());
                        // 记录下来，完事重发
                        if (CollectionUtils.isNotEmpty(failedPolicyIdThisCycle)) {
                            failedPolicyIdList.addAll(failedPolicyIdThisCycle);
                        }
                    }

                    break;
                }

            } catch (Exception e) {
                log.error("ctrip reconcile error", e);
                failedPolicyIdList.addAll(data.stream().map(CtripReconcileData::getItemId).collect(Collectors.toList()));
            }
        }

        return list.stream().filter(d -> failedPolicyIdList.contains(d.getItemId())).collect(Collectors.toList());
    }

    /**
     * 每日携程对账
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void dailyReconcileJob() {
        // start和end都为null，即昨天一天的数据
        dailyReconcile(null, null);
    }


    /**
     * 对账方法
     * @param start - 账单起始时间，默认为昨天凌晨0点
     * @param end - 账单终止日期，默认为起始时间 + 1day
     */
    public void dailyReconcile(LocalDateTime start, LocalDateTime end) {
        long uid = Long.parseLong(PropUtil.getString("ctrip.reconcile.uid"));
        if (start == null) {
            start = LocalDateTime.now().minusDays(1).with(LocalTime.MIDNIGHT);
        }
        if (end == null) {
            // 默认查询区间为一天
            end = start.plusDays(1);
        }
        List<CtripReconcileData> list = externalService.ctripDailyReconcile(uid, start, end);

        List<CtripReconcileData> failedData = reconcile(list);

        if (CollectionUtils.isNotEmpty(failedData)) {
            // 失败数据列表不为空，记录在失败文件中
            externalService.saveCtripFailedData(failedData);
        }
    }

    /**
     * 每日携程对账回溯处理 - 即处理目前存在的失败记录
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void dailyCtripFailedDataReconcile() {
        log.info("ctrip reconcile failed data daily job begin");
        List<CtripReconcileData> failedData = externalService.getCtripFailedData();

        List<CtripReconcileData> stillFailedData = reconcile(failedData);

        List<Long> policyIdList;
        if (CollectionUtils.isNotEmpty(stillFailedData)) {
            policyIdList = stillFailedData.stream().map(CtripReconcileData::getItemId).map(Long::parseLong).collect(Collectors.toList());
        } else {
            policyIdList = List.of(0L);
        }

        // 删除已经成功的数据
        externalService.deleteCtripSuccessData(policyIdList);
        log.info("ctrip reconcile failed data daily job end");
    }

    public void iseeFix(int min, int max) {
        String sql = "select * from tblpolicy" +
                " where inssueDate > '2024-02-27 00:00:00'" +
                " and inssueDate < '2024-04-11 00:00:00'" +
                " and iseebiz is not null" +
                " and status = 1" +
                " limit " + min + "," + max + ";";

        List<Policy> policyList = policyDao.findObject(sql, Policy.class);

        List<String> iseeBizList = policyList.stream().map(Policy::getIseebiz).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        iseeBizList.forEach(iseeBiz -> {
                    List<Long> policyIdList = policyList.stream().filter(p -> iseeBiz.equals(p.getIseebiz())).map(Policy::getPolicyID).collect(Collectors.toList());
                    rabbitMessagePublisher.iseeDelay(policyIdList);
                }
        );
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    static class CtripReconcileRequest {
        public static final String RESULT_CODE_SUCCESS = "1";
        public static final String RESULT_CODE_FAIL = "2";
        public static final String RESULT_CODE_ERROR = "3";

        private LocalDateTime requestTime;
        private String merchantNo;
        private String body;

        // 响应相关
        /**
         * 响应结果
         */
        private String resultCode;
        /**
         * 响应描述
         */
        private String resultMsg;
        /**
         * 数据清单
         */
        private List<CtripReconcileData> item;

        public boolean isSuccess() {
            return RESULT_CODE_SUCCESS.equals(resultCode);
        }

        public boolean isFail() {
            return RESULT_CODE_FAIL.equals(resultCode);
        }

        public boolean isError() {
            return RESULT_CODE_ERROR.equals(resultCode);
        }
    }
}

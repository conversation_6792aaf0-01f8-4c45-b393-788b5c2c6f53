package com.travelye.external.ctrip;

import com.travelye.base.ActionSupport;
import com.travelye.email.AsyncEmailService;
import com.travelye.email.EmailServiceImpl;
import com.travelye.external.Beneficiary;
import com.travelye.partner.DepartmentService;
import com.travelye.partner.PartnerService;
import com.travelye.policy.PolicyService;
import com.travelye.base.MessageException;
import com.travelye.base.SjisConstant;
import com.travelye.base.tools.AESOperator;
import com.travelye.base.tools.CommonUtil;
import com.travelye.base.tools.PolicyUtil;
import com.travelye.external.ErrorCode;
import com.travelye.external.concurrent.Queue;
import com.travelye.external.data.External;
import com.travelye.external.data.ExternalPartner;
import com.travelye.external.data.ExternalService;
import com.travelye.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import jakarta.servlet.ServletOutputStream;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.SocketException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 携程 对接接口
 *
 * <AUTHOR>
 */
@Controller
@Slf4j
@RequestMapping("/external")
public class CTripTravelAction extends ActionSupport {
    private static Map<String, Integer> downMap = new HashMap<>();  //控制携程当日保单下载次数

    @Autowired
    private ExternalService externalService;
    @Autowired
    private PolicyService policyService;
    @Autowired
    private PartnerService partnerService;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private AsyncEmailService asyncEmailService;


    /**
     * 请求的主方法
     * ResultStatus	ResultMsg
     * 00	出/退保成功
     * 80	出/退保异常，后台处理异常。（用于合作方在程序处理过程中发生异常。）
     * 81	出/退保失败。（用于携程上送的数据校验失败，后跟具体的失败原因）
     *
     * @return
     */
    @RequestMapping("/ctrip.action")
    @ResponseBody
    public String travel() {
        StringBuffer sb = new StringBuffer();
        String message = null;
        String transactionType = "parseerror";
        Transaction transaction = null;
        String messageText = null;
        try {
            String method = request.getMethod();
            if ("POST".equalsIgnoreCase(method)) {
                //获取HTTP请求的输入流
                InputStream is;
                is = request.getInputStream();
                //已HTTP请求输入流建立一个BufferedReader对象
                BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
                //读取HTTP请求内容
                String buffer = null;
                while ((buffer = br.readLine()) != null) {
                    //在页面中显示读取到的请求参数
                    sb.append(buffer);
                }
                br.close();
                messageText = sb.toString();
            }
            log.info("接口请求的交易数据:method=" + method + ",ip=" + this.getClientIpAddr() + ",url=" + request.getScheme() + "://" + request.getServerName() + request.getRequestURI() + ",messageText--" + messageText);
            transaction = XmlToTransaction.xmlToTransaction(messageText);
            transactionType = transaction.getTransactionType();
            log.info("TransactionId:" + transaction.getTransactionId() + ",TransactionType:" + transaction.getTransactionType());
            Queue.add(transaction.getAgencyUserId() + "#T#" + transaction.getTransactionType(), transaction.getTransactionId(), 10);
        } catch (MessageException m) {
            log.error("", m);
            if (("系统正在处理中".equals(m.getMessage()))) {
                message = XmlToResponse.errorToXml(messageText, "80", ErrorCode.getMessage(m.getMessage()), transactionType);
            } else {
                message = XmlToResponse.errorToXml(messageText, "81", ErrorCode.getMessage(m.getMessage()), transactionType);
            }
            saveExternalError(message, transaction, messageText);
        } catch (Exception e) {
            log.error("", e);
            message = XmlToResponse.errorToXml(messageText, "80", "系统错误", transactionType);
            saveExternalError(message, transaction, messageText);
        }
        if (message == null) {
            try {
                if (!transaction.getServerTransSignature().equals(transaction.getClientTransSignature())) {
                    log.warn("系统签名:" + transaction.getServerTransSignature() + ",用户签名:" + transaction.getClientTransSignature());
                    throw new MessageException("701");
                }
                if (Transaction.CANCELLATION.equals(transaction.getTransactionType())) {
                    message = cancellcation(messageText, transaction);
                } else if (Transaction.NSELL.equals(transaction.getTransactionType())) {
                    message = nsell(messageText, transaction);
                } else if (Transaction.EDIT.equals(transaction.getTransactionType())) {
                    message = edit(messageText, transaction);
                } else {
                    message = XmlToResponse.errorToXml(messageText, null, "无效的交易类型", transaction.getTransactionType());
                    saveExternalError(message, transaction, messageText);
                }
            } catch (MessageException m) {
                log.error("携程对接错误代码：" + m.getErrorCode(), m);
                if (CommonUtil.isBlank(m.getErrorCode())
                        || MessageException.ErrorCode.invalideRequest.toString().equals(m.getErrorCode())
                        || m.getErrorCode().equals("182")                      //渠道对接人员/销售人员subcode为空
                        || m.getErrorCode().equals("184")) {                    //被险人在黑名单中，返回错误则非异常，避免对方重复尝试
                    message = XmlToResponse.errorToXml(messageText, "81", ErrorCode.getMessage(m.getMessage()), transactionType);
                } else {
                    message = XmlToResponse.errorToXml(messageText, "80", ErrorCode.getMessage(m.getMessage()), transactionType);
                }
                saveExternalError(message, transaction, messageText);
            } catch (Exception e) {
                log.error("", e);
                message = XmlToResponse.errorToXml(messageText, "80", "系统错误", transactionType);
                saveExternalError(message, transaction, messageText);
            } finally {
                Queue.remove(transaction.getAgencyUserId() + "#T#" + transaction.getTransactionType(), transaction.getTransactionId());
            }
        }
        request.setAttribute("message", message);
        return message;
    }

    private void saveExternalError(String message, Transaction transaction,
                                   String messageText) {
        try {
            if (transaction == null) {//解析错误时，该对象为空
                transaction = new Transaction();
                transaction.setTransactionType("parseerror");
            }
            saveExternal(messageText, message, transaction, transaction.getPolicyId(), "0");
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 购买保单
     *
     * @param messageText
     * @param transaction
     * @return
     * @throws Exception
     */
    private String nsell(String messageText, Transaction transaction) throws Exception {
        //不能去掉，同时还检查了代理商的代码是否正确
        ExternalPartner partner = getAgency(transaction);
        Policy policy = null;
        External external = this.externalService.getExternal(transaction.getAgencyUserId(),
                transaction.getTransactionId(), transaction.getTransactionType());
        if (external != null && "1".equals(external.getResult())) {//第二次发送时，上次已经返回成功
            String result = external.getResponse();
            saveExternal(messageText, result, transaction, external.getPolicyid(), "1");
            return result;
        } else {
            Product product = null;
            List<Policyuser> userlist = null;
            if (external == null) {
                Plan plan = this.getPlan(transaction);
                product = policyService.getProduct(plan.getProductid());
                Partnerproduct pp = getPartnerProduct(partner.getAgencyID(), product.getProductid());
                policy = this.getNSellPolicy(transaction, product, plan, partner);
                userlist = this.getNsellPolicyUser(transaction, policy);
                //检验被保险人证件号是否重复
                String checkUserResult = policyService.checkUserId(userlist);
                if (!"0".equals(checkUserResult)) {
                    throw new MessageException("被保险人证件号码重复,重复证件号是:" + checkUserResult);
                }
                policy.setPolicyNo(pp.getMasterPolicyID());
                policy.setCommissionRate(pp.getCommissionRate());

                //重复投保校验
                String result = policyService.checkDuplicatePolicy(policy, userlist, "EXTERNAL");
                if (!"0".equals(result)) {
                    throw new MessageException(result);
                }
                policyService.savePolicy(policy, userlist, plan);
            } else {
                policy = this.policyService.getPolicy(external.getPolicyid());
                userlist = policyService.getPolicyUserList(policy.getPolicyID());
            }
            transaction.setPolicyId(policy.getPolicyID());
            /**
             * 如果是需要发送给WAATS的产品，向WAATS发送数据
             */
            if (policyService.sendWaats(policy) && "4".equals(policy.getStatus())) {
                try {
                    log.debug(transaction.getTransactionId() + "发送waats时间---" + (new Date()));
                    policyService.resubmitWTTSPolicy(policy, userlist);
                    log.debug(transaction.getTransactionId() + "waats返回时间---" + (new Date()));
                } catch (Exception e) {
                    log.error("保单发送异常:" + policy.getPolicyID(), e);
                    policyService.saveWAATSMessage(policy);
                    throw e;
                }
                policyService.saveTansation(policy, userlist);
            }
            try {
                asyncEmailService.sendPolicyEmailAsync(policy, "", EmailServiceImpl.RECIPIENT_TYPE_ALL);
            } catch (Exception e) {
                log.info("接口发送保单异常:", e);
            }
            String host = request.getScheme() + "://" + request.getServerName();
            String result = XmlToResponse.nsellToXml(messageText, policy, userlist, host);
            saveExternal(messageText, result, transaction, policy.getPolicyID(), "1");
            return result;
        }
    }

    /**
     * 取消保单
     *
     * @param messageText
     * @param transaction
     * @return
     */
    private String cancellcation(String messageText, Transaction transaction) {
        String cancellResponse = null;
        //不能去掉，同时还检查了代理商的代码是否正确
        ExternalPartner partner = getAgency(transaction);
        Policy policy = this.getCancelPolicy(transaction, partner);
        if ("1".equals(policy.getStatus()) || "2".equals(policy.getStatus())) {
            LocalDateTime now = LocalDateTime.now();
            if (now.isAfter(policy.getEffectiveDate())) {
                throw new MessageException("保单只能在生效时间前取消");
            }
            policy.setUid(transaction.getAgencyUserId());
            policy.setPremium(0);
            policy.setFee(0);
            policyService.deletePolicy(policy);
            policy.setStatus("3");
            cancellResponse = XmlToResponse.cancellToXml(messageText, policy);
        } else if ("3".equals(policy.getStatus())) {
            External external = this.externalService.getExternal(transaction.getAgencyUserId(),
                    transaction.getTransactionId(), transaction.getTransactionType());
            if (external != null && "1".equals(external.getResult())) {
                cancellResponse = external.getResponse();
            } else {//已退保保单的退保交易号与本次不匹配
                throw new MessageException("602");
            }
        }
        saveExternal(messageText, cancellResponse, transaction, policy.getPolicyID(), "1");
        return cancellResponse;
    }

    /**
     * 修改保单
     * 只修改团险，不需要发送给保险公司
     *
     * @param messageText
     * @param transaction
     * @return
     * @throws Exception
     */
    private String edit(String messageText, Transaction transaction) throws Exception {
        //不能去掉，同时还检查了代理商的代码是否正确
        ExternalPartner partner = getAgency(transaction);
        if (transaction.getTransactionId() == null || "".equals(transaction.getTransactionId().trim())) {
            throw new MessageException("交易号不能为空.");
        }
        if (transaction.getTransactionOldId() == null || "".equals(transaction.getTransactionOldId().trim())) {
            throw new MessageException("原交易号不能为空.");
        }
        String result = null;
        //查找改修改交易 是否第一次接收到，第二次时直接返回，不再重复处理业务
        External external = this.externalService.getExternal(transaction.getAgencyUserId(),
                transaction.getTransactionId(), transaction.getTransactionType());
        if (external != null && "1".equals(external.getResult())) {
            result = external.getResponse();
            saveExternal(messageText, result, transaction, external.getPolicyid(), "1");
        } else {
            Policy policy = getExistPolicy(transaction);
            List<Policyuser> userlist = null;
            //保单有效时，才需要修改保单
            if (policy == null || (!"1".equals(policy.getStatus()) && !"2".equals(policy.getStatus()))) {
                throw new MessageException("原保单无效.");
            }
            LocalDateTime now = LocalDateTime.now();
            if (now.isAfter(policy.getEffectiveDate())) {
                throw new MessageException("保单只能在生效时间前批改");
            }
            if (policy.getIsWaats() == 1) {
                throw new MessageException("Waats个险保单不能修改.");
            }
            Plan plan = policyService.getPlan(policy.getPlanID());
            List<Policyuser> oldUserList = policyService.getPolicyUserList(policy.getPolicyID());
            userlist = this.getEditPolicyUser(transaction, oldUserList, policy);
            double premium = Double.valueOf(transaction.getPremium());
            policy.setCtripPremium(premium);
            policy.setLasttime(now);
            policyService.savePolicy(policy, userlist, plan);

            asyncEmailService.sendPolicyEmailAsync(policy, "", EmailServiceImpl.RECIPIENT_TYPE_ALL);
            String host = request.getScheme() + "://" + request.getServerName();
            result = XmlToResponse.editToXml(messageText, policy, userlist, host);
            saveExternal(messageText, result, transaction, policy.getPolicyID(), "1");
        }
        return result;
    }

    /**
     * 保存接收和返回的数据
     *
     * @param messageText
     * @param message
     * @param transaction
     * @param
     */
    private void saveExternal(String messageText,
                              String message, Transaction transaction, long policyid, String result) {
        try {
            log.info("接口返回的交易数据:agencyUserId--" + transaction.getAgencyUserId() + ",TransactionId:" + transaction.getTransactionId() + ",message--" + message);
            External external = new External();
            external.setAgencytransactionid(transaction.getTransactionId());
            external.setAgencyuserid(transaction.getAgencyUserId());
            external.setTransactiontype(transaction.getTransactionType());
            external.setRequest(messageText);
            external.setResponse(message);
            external.setPolicyid(policyid);
            external.setRequesttime(transaction.getRequesttime());
            external.setResult(result);
            this.externalService.save(external);
        } catch (Exception e) {
            log.error("", e);
        }
    }

    /**
     * 从交易中查询当前的保单是否已存在
     * 除了新单购买外，其它的业务操作必须是购买保单的用户
     *
     * @param transaction
     * @return
     */
    private Policy getExistPolicy(Transaction transaction) {
        Policy policy = null;
        External external = this.externalService.getExternal(transaction.getAgencyUserId(),
                transaction.getTransactionOldId(), Transaction.NSELL);
        if (external == null || !"1".equals(external.getResult())) {
            return null;
        }
        if (Transaction.CANCELLATION.equals(transaction.getTransactionType())) {
            policy = policyService.getPolicy(external.getPolicyid());
            if (policy != null) {
                //如果不是同一投保人，不能进行查询保单，保单号错误，团险的凭证号有误
                if (policy.getUid() != transaction.getAgencyUserId()
                        || (policy.getIsGroup() == 0 && !policy.getPolicyNo().equals(transaction.getPolicyNumber()))
                        || (policy.getIsGroup() == 1 && !String.valueOf(policy.getPolicyID()).equals(transaction.getPolicyNumber()))
                ) {
                    policy = null;
                }
            }
        } else if (Transaction.EDIT.equals(transaction.getTransactionType())) {
            policy = policyService.getPolicy(external.getPolicyid());
            if (policy != null) {
                //如果不是同一投保人,个险保单，不能进行修改保单
                if (policy.getUid() != transaction.getAgencyUserId()) {
                    policy = null;
                }
            }
        }
        return policy;
    }

    private Partnerproduct getPartnerProduct(long agencyId, long productId) {
        Partnerproduct pp = partnerService.getPartnerProduct(agencyId, productId);
        if (pp == null) {
            throw new MessageException("9");
        }
        return pp;
    }

    private Plan getPlan(Transaction transaction) {
        String productCode = transaction.getGDSProductCode(); //产品代码
        String subplanCode = transaction.getGDSPlanCode(); //计划代码
        //携程产品代码，再做一次映射
        if (StringUtils.isNotBlank(productCode)) {
            String systemProCode = externalService.getSystemCode(productCode, 0);
            if (StringUtils.isNotBlank(systemProCode)) {
                productCode = systemProCode;
            }
        }
        if (StringUtils.isNotBlank(subplanCode)) {
            String systemPlanCode = externalService.getSystemCode(subplanCode, 1);
            if (StringUtils.isNotBlank(systemPlanCode)) {
                subplanCode = systemPlanCode;
            }
        }
        Plan plan = this.externalService.getPlan(productCode, subplanCode);
        if (plan == null) {
            throw new MessageException("9");
        }
        return plan;
    }

    /**
     * 获取新单的数据
     *
     * @param transaction
     * @param product
     * @param plan
     * @param partner
     * @return
     */
    private Policy getNSellPolicy(Transaction transaction, Product product, Plan plan, ExternalPartner partner) {
        if (product == null || plan == null) {
            throw new MessageException("9");
        }
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(transaction.getInceptionDate())) {
            throw new MessageException("113");
        }
        if (now.isAfter(transaction.getExpirationDate())) {
            throw new MessageException("117");
        }
        if (transaction.getInceptionDate().isAfter(transaction.getExpirationDate())) {
            throw new MessageException("117");
        }
        double premium = Double.parseDouble(transaction.getPremium());
        if (premium < 0.001) {
            throw new MessageException("保单保费有误.");
        }
        Policy policy = new Policy();
        policy.setCtripPremium(premium);
        policy.setApplicationSignStatus(Policy.SIGN_NO_NEED_CONFIRMED);
        policy.setBranch(partner.getBranch());
        policy.setAgencyID(partner.getAgencyID());
        policy.setClientID(partner.getClientID());
        policy.setUid(transaction.getAgencyUserId());

        policy.setEffectiveDate(transaction.getInceptionDate());
        policy.setExpiryDate(transaction.getExpirationDate());
        if (("TJS2015".equalsIgnoreCase(product.getProductcode())            //新宝岛游踪至少承保15天
                || "TJS-CTRIP".equalsIgnoreCase(product.getProductcode()))    //携游天下台湾计划至少承保15天
                && policy.getPolicyDays() < 15) {
            policy.setExpiryDate(transaction.getInceptionDate().plusDays(15).minusSeconds(1));
        }
        policy.setClientip(getClientIpAddr());
        policy.setInssueDate(transaction.getTransactionApplDate());
        policy.setLasttime(now);
        policy.setFurthestCity(transaction.getFurthestCity());
        policy.setOriginatingCity(transaction.getOriginatingCity());
        policy.setLineno(transaction.getLineNO());
        policy.setFlightNo(transaction.getFlightNo());

        policy.setProductID(product.getProductid());
        policy.setPlanID(plan.getPlanid());
        setProductAndPlan(policy, product, plan);

        //setAgency
        setAgency(policy, partner.getPartner());

        //检查团险投保人
        if (policy.getIsGroup() == 1) {  //携程团险指定领队为投保人，携程自己不能作为投保人
            for (Insured insured : transaction.getInsuredList()) {
                if ("0".equals(insured.getIsInsuredFlag())) {
                    if (insured.getLastNm() == null || insured.getLastNm().equals("")) {
                        throw new MessageException("投保人姓名不能为空.");
                    }
                    policy.setPolicyholder(insured.getLastNm());
                    break;
                }
            }
        }

        String agencyCode = null;          //保单所应使用的agencyCode

        //setUserAndDepartment
        Users user = this.externalService.getUser(transaction.getAgencyUserId());
        policy.setDepartmentID(user.getDepartmentID());
        policy.setAttn(user.getRealName());
        policy.setAttnContact(user.getContact());
        policy.setQualificationNO(user.getQualificationNO());
        policy.setAgent(user.getLoginname());
        //如果为一级渠道，检果对接用户的subCode并设置为保单所使用的agencyCode
        if (partner.getAgencyType().equalsIgnoreCase(SjisConstant.module_sale)) {
            agencyCode = user.getSubCode();
        }

        if (!CommonUtil.isBlank(user.getSubCode())) {
            policy.setAgencyCode(user.getSubCode());
        }

        Users salesMan = this.externalService.getUser(partner.getSalesManID());
        if (salesMan != null) {
            policy.setEmployeeID(salesMan.getEmployeeID());
            policy.setSalesMan(salesMan.getRealName());
            policy.setSalesContact(salesMan.getContact());
            policy.setSalesManID(salesMan.getUid());
        }
        Department department = departmentService.getDepartment(policy.getSalesDepartmentID());
        if (department != null) {
            policy.setDepartment(department.getDepartmentName());
            policy.setSalesLocation(department.getSaleLocation());
        }

        if (partner.getAgencyType().equalsIgnoreCase(SjisConstant.module_saleclient)) {        //二级渠道
            Users agencySalesMan = this.externalService.getUser(partner.getAgencySalesManID());
            if (agencySalesMan != null) {
                policy.setAgencySalesManID(agencySalesMan.getUid());
                policy.setAgencySalesDepartmentID(agencySalesMan.getDepartmentID());

                agencyCode = agencySalesMan.getSubCode();
            }
        } else {
            policy.setAgencySalesManID(policy.getUid());
            policy.setAgencySalesDepartmentID(policy.getDepartmentID());
        }
        policy.setSalesType(SjisConstant.saleType_ecommercesale);
//		policy.setLineno();

        //检查并设置保单的agencyCode
        if (!CommonUtil.isBlank(agencyCode)) {
            policy.setAgencyCode(agencyCode);
        } else {
            throw new MessageException("渠道对接/销售人员未配置subCode,请联系业务人员", "182");
        }

        return policy;
    }

    /**
     * 返回代理商信息
     *
     * @param transaction
     * @return
     */
    private ExternalPartner getAgency(Transaction transaction) {
        ExternalPartner partner = this.externalService.getPartner(transaction.getAgencyUserId());
        if (partner == null) {
            throw new MessageException("3");
        }
        LocalDateTime now = LocalDateTime.now();
        //检查代理商的有效日期和状态
        if (partner.getStatus() != 1
                || partner.getEffectiveDate() == null || (partner.getEffectiveDate() != null && now.isBefore(partner.getEffectiveDate()))
                || (partner.getExpiryDate() != null && now.isAfter(partner.getExpiryDate()))
        ) {
            throw new MessageException("3");
        }
        //如果是代理商的客户
        if (partner.getAgencyType().equalsIgnoreCase(SjisConstant.module_saleclient)) {
            ExternalPartner old = partner;
            partner = externalService.getPartnerByID(partner.getParentAgencyID());
            if (partner == null || partner.getStatus() != 1 || now.isBefore(partner.getEffectiveDate()) || now.isAfter(partner.getExpiryDate())) {
                throw new MessageException("3");
            }
            partner.setClientID(old.getAgencyID());
            partner.setAgencyType(old.getAgencyType());
            partner.setAgencySalesManID(old.getAgencySalesManID());
            partner.setAgencySalesDepartmentID(old.getAgencySalesDepartmentID());
        } else {
            partner.setClientID(partner.getAgencyID());
        }
        return partner;
    }

    /**
     * id  的下载次数是否在允许的范围内
     *
     * @param id
     */
    private void checkDownCounts(String id) {
        Calendar now = Calendar.getInstance();
        int day = now.get(Calendar.DAY_OF_MONTH);
        synchronized (downMap) {
            Object o = downMap.get("today");
            int today = 0;
            if (o != null) {
                today = (Integer) o;
            }
            if (today != day) {
                downMap.clear();
                downMap.put("today", day);
            }
            Object c = downMap.get(id);
            if (c != null) {
                int counts = (Integer) c;
                log.info(id + "携程保单当日下载次数为：" + (counts + 1));
                downMap.put(id, counts + 1);
                if (counts == 2) {
                    log.info("downMap：" + downMap.toString());
                }
                if (counts > 20) {
                    throw new MessageException("保单下载次数超限");
                }
            } else {
                downMap.put(id, 1);
            }
        }
    }

    /**
     * 下载 保单
     *
     * @return
     */
    @RequestMapping("/ctripdown.action")
    @ResponseBody
    public String down() {
        String message = "无效的保单";
        byte[] pdf = null;
        try {
            String uidStr = request.getParameter("uid");//下载单个用户的保单
            log.info("下载携程保单:ip=" + this.getClientIpAddr() + ",uidStr--" + uidStr);
            Policy policy = null;
            if (CommonUtil.isBlank(uidStr)) {
                String pid = request.getParameter("pid"); //下载整张保单
                log.info("下载携程保单:pid=" + pid);
                pid = AESOperator.getInstance().decrypt(pid);
                if (pid == null || "".equals(pid.trim())) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                checkDownCounts(pid);
                String[] arr = pid.split(",");
                if (arr.length < 2) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                String policyidStr = arr[0];
                String agencyID = arr[1];
                policy = policyService.getPolicy(Long.valueOf(policyidStr));
                if (policy == null || !("1".equals(policy.getStatus()) || "2".equals(policy.getStatus())) || policy.getAgencyID() != Long.valueOf(agencyID)) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                pdf = policyService.getOssPolicyPdf(policy);
            } else {
                uidStr = AESOperator.getInstance().decrypt(uidStr);
                if (uidStr == null || "".equals(uidStr.trim())) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                checkDownCounts(uidStr);
                String[] arr = uidStr.split(",");
                if (arr.length < 2) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                String insuredIDStr = arr[0];
                String agencyID = arr[1];
                Policyuser policyuser = policyService.getPolicyUser(Long.parseLong(insuredIDStr));
                if (policyuser == null) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                policy = policyService.getPolicy(policyuser.getPolicyid());
                if (policy == null || !("1".equals(policy.getStatus()) || "2".equals(policy.getStatus())) || policy.getAgencyID() != Long.parseLong(agencyID)) {
                    request.setAttribute("message", "无效的保单");
                    return message;
                }
                pdf = policyService.getOssPolicyPdf(policy);
            }
            response.setContentType("application/x-download");
            response.setHeader("Content-disposition", "attachment; filename=" + policy.getPolicyNo() + ".pdf");
            response.setCharacterEncoding("UTF-8");
            ServletOutputStream out = response.getOutputStream();
            BufferedOutputStream bos = new BufferedOutputStream(out);
            bos.write(pdf, 0, pdf.length);
            bos.close();
        } catch (MessageException e) {
            if (!"保单下载次数超限".equals(e.getMessage())) {
                log.error("", e);
            }
            request.setAttribute("message", e.getMessage());
            message = e.getMessage();
            return message;
        } catch (Exception e) {
            if (!(e.getCause() instanceof SocketException)) {
                log.warn("下载保单时报错，错误信息：", e);
            }
        }
        return null;
    }


    /**
     * 获取新单投被保人信息
     *
     * @param transaction
     * @return
     */
    private List<Policyuser> getNsellPolicyUser(Transaction transaction, Policy policy) {
        LocalDateTime activeDate = policy.getEffectiveDate();
        List<Policyuser> list = new ArrayList<Policyuser>();
        for (Insured insured : transaction.getInsuredList()) {
            if (!(policy.getIsGroup() == 1 && "0".equals(insured.getIsInsuredFlag()))) {
                Policyuser user = new Policyuser();
                setPolicyUser(activeDate, insured, user);
                list.add(user);
            }
        }
        return list;
    }

    /**
     * 获取修改时 投被保人信息
     *
     * @param transaction
     * @return
     */
    private List<Policyuser> getEditPolicyUser(Transaction transaction, List<Policyuser> oldUserList, Policy policy) {
        LocalDateTime activeDate = policy.getEffectiveDate();
        for (Insured insured : transaction.getInsuredList()) {
            if ("99".equals(insured.getChangeType())) { //删除
                boolean blnFound = false;
                if (!CommonUtil.isNumeric(insured.getInsuredID())) {
                    throw new MessageException("删除被保险人时，InsuredNo必须是数字.");
                }
                long insuredID = Long.parseLong(insured.getInsuredID());
                for (Policyuser user : oldUserList) {
                    if (user.getInsuredID() == insuredID && (user.getPolicyStatus() == 1 || user.getPolicyStatus() == 2)) {
                        user.setModifyFlag(1);
                        user.setPolicyStatus(3);
                        blnFound = true;
                    }
                }
                if (!blnFound) {
                    throw new MessageException("删除被保险人时，InsuredNo[" + insured.getInsuredID() + "]找不到.");
                }
            } else if ("01".equals(insured.getChangeType())) {  //修改被保险人
                boolean blnFound = false;
                if (!CommonUtil.isNumeric(insured.getInsuredID())) {
                    throw new MessageException("修改被保险人时，InsuredNo必须是数字.");
                }
                long insuredID = Long.valueOf(insured.getInsuredID());
                for (Policyuser user : oldUserList) {
                    if (user.getInsuredID() == insuredID && (user.getPolicyStatus() == 1 || user.getPolicyStatus() == 2)) {
                        if (!user.getInsuredFullName().equals(insured.getLastNm()) && !user.getInsuredIdNo().equals(insured.getInsuredIdNo())) {
                            throw new MessageException("修改被保险人时，InsuredNo[" + insured.getInsuredID() + "]名字证件不能同时修改.");
                        }
                        setPolicyUser(activeDate, insured, user);
                        user.setModifyFlag(1);
                        user.setPolicyStatus(2);
                        blnFound = true;
                    }
                }
                if (!blnFound) {
                    throw new MessageException("修改被保险人时，InsuredNo[" + insured.getInsuredID() + "]找不到.");
                }
            } else if (!(policy.getIsGroup() == 1 && "0".equals(insured.getIsInsuredFlag()))) {
                Policyuser user = new Policyuser();
                setPolicyUser(activeDate, insured, user);
                if (policy.getPolicyholder().equalsIgnoreCase(user.getInsuredFullName())) {
                    user.setRelationship("本人 Policyholder");
                }
                oldUserList.add(user);
            }
        }
        return oldUserList;
    }

    /**
     * 设置被保险人信息
     *
     * @param activeDate
     * @param insured
     * @param user
     */
    private void setPolicyUser(LocalDateTime activeDate, Insured insured, Policyuser user) {
        user.setInsuredname(insured.getLastNm());
        user.setBirthday(insured.getBirthDt());
        user.setInsuredIdNoType(insured.getInsuredIdNoType());
        user.setInsuredIdNo(insured.getInsuredIdNo());
        user.setHomePhoneNo(insured.getHomePhoneNo());
        user.setEmailAddr(insured.getEmailAddr());
        user.setRelationship(insured.getRelationShip());
        user.setDeathBeneficiary(getBeneficiary(insured.getBeneficiaryList()));
        user.setSeq(insured.getSeq());
        user.setPolicyStatus(1);
        user.setIsInsuredFlag(Integer.parseInt(insured.getIsInsuredFlag()));
        if (!"".equals(insured.getBirthDtStr())) {
            user.setTypeCD(PolicyUtil.getWAATSInsureTypeCD(activeDate, insured.getBirthDt()));
        }
    }

    /**
     * @param transaction
     * @return 取消保单时 的保单对象
     */
    private Policy getCancelPolicy(Transaction transaction, ExternalPartner partner) {
        Policy policy = getExistPolicy(transaction);
        if (policy == null) {
            throw new MessageException("137");
        }
        policy.setIATACntryCd(partner.getIATACntryCd());
        policy.setGDSCode(partner.getGDSCode());
        policy.setSourceId(partner.getGDSCode());
        policy.setAgencyCode(partner.getAgencyCode());
        return policy;
    }

    private void setAgency(Policy policy, Partner agency) {
        if (!agency.getAgencyType().equals(SjisConstant.module_admin)) {
            policy.setSalesManID(agency.getSalesManID());
            policy.setSalesDepartmentID(agency.getSalesDepartmentID());
        }
        policy.setGDSCode(agency.getGDSCode());
        policy.setSourceId(agency.getSourceId());
        policy.setAgencyCode(agency.getAgencyCode());
        policy.setIATACntryCd(agency.getIATACntryCd());


        policy.setPolicyholder(agency.getAgencyName());
        if (SjisConstant.module_admin.equals(agency.getAgencyType())) { //分公司直接销售
            policy.setSalesChannel(SjisConstant.saleChannel_directsale);
        } else {
            policy.setSalesChannel(agency.getSalesChannel());
        }
        policy.setAgency(agency.getAgencyName());
        policy.setAgencyAddress(agency.getAddress());
        policy.setAgencyContact(agency.getTelphone());
        if (CommonUtil.isBlank(policy.getIsSendEmail())) {
            if (CommonUtil.isBlank(agency.getIsSendEmail())) {
                //当 agency不是分公司时，使用分公司的 邮件发送参数
                if (!agency.getAgencyType().equals(SjisConstant.module_admin)) {
                    Partner branch = partnerService.getPartner(agency.getBranch());
                    policy.setIsSendEmail(branch.getIsSendEmail());
                }
            } else {
                policy.setIsSendEmail(agency.getIsSendEmail());
            }
        }

        //需要 代理商设置机构代码的(waats产品，分公司直销，上海的个险产品)
        //agencypcc 统一都存
        AgencyPCC pcc = partnerService.getEffectiveAgencyPcc(agency.getAgencyID());
        if (policy.getIsWaats() == 1) {
            if (pcc != null) {
                policy.setAgencyPCC(pcc.getAgencyPCC());
            } else if (policyService.mustAgencyPcc(policy)) {
                throw new MessageException("机构代码无效或已过期，请向分公司申请.");
            }
        } else {
            if (pcc != null) {
                policy.setAgencyPCC(pcc.getAgencyPCC());
            }
        }

        //获取有效的s3b code
        S3bCode s3bCode = null;
        //美亚直销,取销售人员的s3b code
        if (SjisConstant.module_admin.equals(agency.getAgencyType()) && "1".equals(policy.getDirectBranchSale())) {
            s3bCode = partnerService.getEffectiveS3bCode(policy.getSalesManID(), S3bCode.CODE_TYPE_USER);
        } else {
            s3bCode = partnerService.getEffectiveS3bCode(agency.getAgencyID(), S3bCode.CODE_TYPE_AGENCY);
        }
        if (s3bCode != null && !CommonUtil.isBlank(s3bCode.getS3bCode())) {
            policy.setS3bCode(s3bCode.getS3bCode());
        } else {
            policy.setS3bCode("");
        }
    }

    private Policy setProductAndPlan(Policy policy, Product product, Plan plan) {
        policy.setProductCode(product.getProductcode());
        policy.setProductNum(product.getProductNum());//@modified by hs on 2018-03-14
        policy.setIsGroup(product.getIsGroup());
        policy.setIsStandard(product.getIsStandard());

        Partnerproduct partnerproduct = partnerService.getPartnerProduct(policy.getAgencyID(), product.getProductid());
        policy.setIsWaats(Integer.valueOf(partnerproduct.getSendAdressType()));
        policy.setSendUrlID(Long.valueOf(partnerproduct.getSendAdressType()));

        policy.setPlanCode(plan.getPlancode());
        //全年计划时,失效时间直接加一年
        if (plan.getMinday() == 365) {
            policy.setExpiryDate(policy.getEffectiveDate().plusYears(1).minusSeconds(1));

        }
        return policy;
    }

    private String getBeneficiary(List<com.travelye.external.Beneficiary> list) {
        StringBuffer buffer = new StringBuffer();
        if (list == null || list.size() == 0) {
            buffer.append("法定Legal Heir");
        } else {
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) {
                    buffer.append(",");
                }
                Beneficiary b = list.get(i);
                if (b.getFirstNm() != null) {
                    buffer.append(b.getFirstNm());
                }
                buffer.append(b.getLastNm()).append(":");
                String idType = b.getBeneficiaryIdType().trim();
                //如果需要，转换证件类型为核心所使用的标准格式: 1-身份证; 2-护照; 3-其他
                if (!idType.matches("[136]")) {
                    idType = PolicyUtil.convertWAATSIDNoType(idType);
                }
                buffer.append(idType).append(":");
                buffer.append(b.getBeneficiaryIdNo()).append(":");
                buffer.append(b.getRelation()).append(":");
                buffer.append(b.getPercentage());
            }
        }
        return buffer.toString();
    }
}

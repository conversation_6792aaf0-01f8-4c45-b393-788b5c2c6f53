package com.travelye.external.data;

import com.travelye.external.ctrip.CtripReconcileData;
import com.travelye.external.health.HealthPolicy;
import com.travelye.external.queryPolicy.PolicyQueryResult;
import com.travelye.vo.*;

import java.time.LocalDateTime;
import java.util.List;


public interface ExternalDao {
    public void save(External model);

    public External getExternal(long agencyuserid, String agencytransactionid, String transactiontype);

    public Plan getPlan(String productCode, String subplanCode);

    Plan getAgencyPlan(String productCode, String subPlanCode, long agencyId);

    /**
     * 根据计划的渠道对接编码，获取计划
     *
     * @param subplanCode
     * @return
     */
    public Plan getPlanByPlanCode(String subplanCode);

    public ExternalPartner getPartner(long agencyUserId);

    public Policy getPolicyByNum(String policyNo);

    public List<Policy> getPolicyByNumAndId(String policyNo, long policyiD);

    public Users getUser(long uid);

    public ExternalPartner getPartnerByID(long partnerID);

    /**
     * 查询健康险 平台需要的非waats保单
     *
     * @param transaction
     * @return
     */
    public List<HealthPolicy> healthPolicy(com.travelye.external.health.Transaction transaction);

    /**
     * 查询健康险 平台需要的非waats保单的被保险人
     *
     * @param transaction
     * @return
     */
    public List<Policyuser> healthPolicyUser(com.travelye.external.health.Transaction transaction);

    /**
     * 携程对账的ftp数据
     */
    List<CtripReconcileData> ctripDailyReconcile(long uid, LocalDateTime start, LocalDateTime end);

    public String getResponse(long externalID);

    /**
     * 查询第一个对接对象
     *
     * @param agencyuserid
     * @param agencytransactionid
     * @param transactiontype
     * @return
     */
    public External getFirstExternal(long agencyuserid, String agencytransactionid, String transactiontype);

    /**
     * 返回请求内容相同的对接对象
     *
     * @param agencyuserid
     * @param agencytransactionid
     * @param transactiontype
     * @param requestXml
     * @return
     */
    public External getExternal(long agencyuserid, String agencytransactionid, String transactiontype, String requestXml);

    /**
     * 导出sql的数据到csv文件
     *
     * @param sql
     */
    public List<String> exportCsv(String sql);


    /**
     * 查询一定时间段内的取消保单，包含被保人信息
     *
     * @param dateStr
     * @return
     */

    public List<HealthPolicy> CancelPolicy(String dateStr);

    /*
    efapiao保单查询接口白名单
     */
    public boolean isInEfapiaoWhiteList(String ip, String inter);

    /**
     * 携程/航联的产品/计划代码做一次映射
     *
     * @param sourceCode
     * @param type
     * @return
     */
    String getSystemCode(String sourceCode, int type);

    /**
     * 根据产品计划的系统代码，获取短编码
     *
     * @param systemCode
     * @param type
     * @return
     */
    String getMapCode(String systemCode, int type);


	/*
	携程ftp对账，查询保单的原始被保险人数（无论是否修改）
	 */

    public int getInsuredNumsByPolicyId(long policyId);

    /**
     * 根据投保单ID,获取渠道请求
     *
     * @param policyId
     * @return
     */
    public External getExternalByPolicyId(long policyId);


    /**
     * 异步通知回调
     *
     * @param back
     * @return
     */
    public void saveExternalCallBack(IcExternalBack back);

    /**
     * 获取回调通知待发送列表
     *
     * @param dateTime
     * @return
     */
    public List<IcExternalBack> getCallbackNeededList(String dateTime);

    /**
     * 看看这个保单是不是要回调啊~
     *
     * @param policyId
     * @return
     */
    IcExternalBack getCallbackNeededByPolicyId(long policyId);


    /**
     * 获取保单出单时的回调内容（奇葩的外分接口，注销回调也要出单时交易流水号）
     *
     * @param policyId
     * @return
     */
    IcExternalBack getNsellCallbackByPolicyId(long policyId);


    /**
     * 已出单成功的保单，但回调结果不一致的数目
     */
    String getPolicyProcessFinishNeeded();

    /**
     * 已出单成功的保单，更新回调结果
     *
     * @param policyIdList
     * @return
     */
    int savePolicyProcessFinished(String policyIdList);

    /**
     * 根据保险人证件号码查询保单号码
     *
     * @param idNo 待查询证件号码列表
     * @return 查询到的保单列表
     */
    List<PolicyQueryResult> getPolicyByIdNo(String idNo);

    /**
     * 从白名单表中查询匹配(ip, interface）记录的数量
     *
     * @param ip 外部接口调用者IP埴
     * @return int 符合条件记录的数量
     * @interfaceName 外部接口
     */
    int getWhiteListRecord(String ip, String interfaceName);

    /**
     * 获取海南航空对接用户
     */
    Users getHNAirUser();

    /**
     * 根据配置名获取配置value
     *
     * @param configName
     * @return
     */
    String getEditableConfig(String configName);

    List<Policy> getPolicyByExternalIndex(long agencyUserId, String externalIndex);

    /**
     * 获取指定的接口记录---不检查policyID
     *
     * @param agencyuserid
     * @param agencytransactionid
     * @param transactiontype
     * @return
     */
    External getExternalEx(long agencyuserid, String agencytransactionid, String transactiontype);

    Plan getAgencyPlanByPlanCode(String planCode, String productNum, String benefitCd, long agencyId, String sqlParam);

    /**
     * 根据产品计划代码，获取有效产品
     *
     * @param productNum - 产品代码
     * @param planCode   - 计划代码
     * @param benefitCd  - 扩展/可选计划代码
     * @return 带plan信息的product
     */
    Plan getPlanByCode(String productNum, String planCode, String benefitCd);

    long saveApiCallback(ApiCallback apiCallback);

    ApiCallback getApiCallbackByModule(String module, String moduleId, String requestType, long agencyUserId);

    List<ApiCallback> getApiCallbackSendList();

    ApiCallback getApiCallbackByTransactionId(String module, String moduleId, String requestType, long agencyUserId);

    void saveCtripFailedData(List<CtripReconcileData> ctripFailedData);

    List<CtripReconcileData> getCtripFailedData();

    void deleteCtripSuccessData(List<Long> policyIdList);
}

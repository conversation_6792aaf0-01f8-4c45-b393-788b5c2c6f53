package com.travelye.external.data;

import com.travelye.vo.IcExternalBack;

import java.util.List;

public interface ExternalBackService {
    void saveExternalCallBack(IcExternalBack back);

    void savePolicyProcessFinished();

    List<IcExternalBack> getCallbackNeededList(String dateTime);

    void saveAndSendCallback(IcExternalBack externalBack);

    IcExternalBack getCallbackNeededByPolicyId(long policyId);

    IcExternalBack getNsellCallbackByPolicyId(long policyId);
}

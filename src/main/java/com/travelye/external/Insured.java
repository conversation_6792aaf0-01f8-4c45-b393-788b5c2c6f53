package com.travelye.external;

import com.travelye.base.MessageException;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/** 被保险人
 * <AUTHOR>
 *
 */
public class Insured {

	private long insuredID;		// 用户ID
	private List<String> planCode;	// 计划代码，WATTS指定
	private String firstNm;	// 被保险人姓名
	private String lastNm;	// 被保险人姓名
	private String birthDtStr;		// 被保险人生日
	//被保险人类型  成人（18-80岁）=IND 儿童（1-17岁）=CHD
	private String insuredTypCd;		// 
	private String insuredIdNoType;	// 证件类型
	private String insuredIdNo;	// 证件号码
	//如果是保单持有人，非被保险人，IsInsuredFlag=0
    //如果是保单持有人，且是被保险人，IsInsuredFlag=1
	//如果不是保单持有人，只是被保险人，InInsuredFlag=2
	private String isInsuredFlag;		
	private String homePhoneNo;	// 被保险人电话
	private String relationShip; //被保人与投保人的关系
	private String emailAddr;        //email地址
	private String addressInfo;		//地址
	private Map<String,String> ratingFactorMap;
	
	private List<Beneficiary> beneficiaryList;  //受益人列表

	//反洗钱项目新增字段
	private String nationalityCd;	//国家代码
	private String occupation;		//职业描述
	private String occupationCd;	//职业代码
	private String annualIncome;	//年收入
	private String companyName;		//工作单位
	private String identificationID;//身份证号码
	private LocalDate identificationIDExpiry;//身份证有效期
	private String countryName;
	private String province;
	private String city;
	private String genderCd;//性别 0-男 1-女

	public String getCountryName() {
		return countryName;
	}

	public void setCountryName(String countryName) {
		this.countryName = countryName;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}



	public String getNationalityCd() {
		return nationalityCd;
	}

	public void setNationalityCd(String nationalityCd) {
		this.nationalityCd = nationalityCd;
	}

	public String getOccupation() {
		return occupation;
	}

	public void setOccupation(String occupation) {
		this.occupation = occupation;
	}

	public String getOccupationCd() {
		return occupationCd;
	}

	public void setOccupationCd(String occupationCd) {
		this.occupationCd = occupationCd;
	}

	public String getAnnualIncome() {
		return annualIncome;
	}

	public void setAnnualIncome(String annualIncome) {
		this.annualIncome = annualIncome;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getIdentificationID() {
		return identificationID;
	}

	public void setIdentificationID(String identificationID) {
		this.identificationID = identificationID;
	}

	public LocalDate getBirthDt(){
		try {
			return LocalDate.parse(this.getBirthDtStr(), DateTimeFormatter.ofPattern("MM/dd/yyyy"));
		} catch (Exception e) {
			throw new MessageException("15");
		}
	}
	
	/** 增加受益人
	 * @param beneficiary
	 */
	public void addBeneficiary(Beneficiary beneficiary){
		if (beneficiaryList ==null) beneficiaryList=new ArrayList<Beneficiary>();
		beneficiaryList.add(beneficiary);
	}
	
	public void addPlanCode(String planCode){
		if (this.planCode ==null) this.planCode =new ArrayList<String>();
		this.planCode.add(planCode);
	}

	public long getInsuredID() {
		return insuredID;
	}

	public void setInsuredID(long insuredID) {
		this.insuredID = insuredID;
	}

	public List<String> getPlanCode() {
		return planCode;
	}

	public void setPlanCode(List<String> planCode) {
		this.planCode = planCode;
	}

	public String getFirstNm() {
		return firstNm;
	}

	public void setFirstNm(String firstNm) {
		this.firstNm = firstNm;
	}

	public String getLastNm() {
		return lastNm;
	}

	public void setLastNm(String lastNm) {
		this.lastNm = lastNm;
	}


	public String getBirthDtStr() {
		return birthDtStr;
	}

	public void setBirthDtStr(String birthDtStr) {
		this.birthDtStr = birthDtStr;
	}

	public String getInsuredTypCd() {
		return insuredTypCd;
	}

	public void setInsuredTypCd(String insuredTypCd) {
		this.insuredTypCd = insuredTypCd;
	}

	public String getInsuredIdNoType() {
		return insuredIdNoType;
	}

	public void setInsuredIdNoType(String insuredIdNoType) {
		this.insuredIdNoType = insuredIdNoType;
	}

	public String getInsuredIdNo() {
		return insuredIdNo;
	}

	public void setInsuredIdNo(String insuredIdNo) {
		this.insuredIdNo = insuredIdNo;
	}

	public String getIsInsuredFlag() {
		return isInsuredFlag;
	}

	public void setIsInsuredFlag(String isInsuredFlag) {
		this.isInsuredFlag = isInsuredFlag;
	}

	public String getHomePhoneNo() {
		return homePhoneNo;
	}

	public void setHomePhoneNo(String homePhoneNo) {
		this.homePhoneNo = homePhoneNo;
	}

	public List getBeneficiaryList() {
		return beneficiaryList;
	}

	public void setBeneficiaryList(List beneficiaryList) {
		this.beneficiaryList = beneficiaryList;
	}

	public String getRelationShip() {
		return relationShip;
	}

	public void setRelationShip(String relationShip) {
		this.relationShip = relationShip;
	}

	public String getEmailAddr() {
		return emailAddr;
	}

	public void setEmailAddr(String emailAddr) {
		this.emailAddr = emailAddr;
	}

	public Map<String, String> getRatingFactorMap() {
		return ratingFactorMap;
	}

	public void setRatingFactorMap(Map<String, String> ratingFactorMap) {
		this.ratingFactorMap = ratingFactorMap;
	}

	public LocalDate getIdentificationIDExpiry() {
		return identificationIDExpiry;
	}

	public void setIdentificationIDExpiry(LocalDate identificationIDExpiry) {
		this.identificationIDExpiry = identificationIDExpiry;
	}

	public String getAddressInfo() {
		return addressInfo;
	}

	public void setAddressInfo(String addressInfo) {
		this.addressInfo = addressInfo;
	}

	public String getGenderCd() {
		return genderCd;
	}

	public void setGenderCd(String genderCd) {
		this.genderCd = genderCd;
	}
}

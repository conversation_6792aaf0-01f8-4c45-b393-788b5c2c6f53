package com.travelye.external.isee;

import com.google.common.hash.Hashing;
import com.travelye.base.MessageException;
import com.travelye.base.tools.DateUtil;
import com.travelye.base.tools.JSONUtils;
import com.travelye.base.tools.PropUtil;
import com.travelye.external.ihub.IhubUtils;
import com.travelye.vo.Policy;
import com.travelye.vo.Policyuser;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/15 15:50
 **/
@Slf4j
@Component
public class IseeUtils {

    private static final String key;
    private static final String url;
    private static final String orgNo;
    private static final String companyId;

    static {
        key = PropUtil.getString("isee.key");
        url = PropUtil.getString("isee.url");
        orgNo = PropUtil.getString("isee.orgNo");
        companyId = PropUtil.getString("isee.companyId");
    }


    public static void request(Map<String, Object> params) throws Exception {
        request(requestBody(params));
    }

    public static void request(List<Policy> policyList, String productName, List<Policyuser> policyuserList) throws Exception {
        request(requestBody(policyList, productName, policyuserList));
    }

    public static void request(String requestBody) throws Exception {
        String env = PropUtil.getString("env");
        if (!"PROD".equals(env)) {
            try (CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
                HttpPost httpPost = new HttpPost(url);

                String iseeGwSign = sign(key, requestBody);
                httpPost.setHeader("ISEE_GW_SIGN", iseeGwSign);

                log.info("请求isee：签名-{} 报文-{}", iseeGwSign, requestBody);
                StringEntity entity = new StringEntity(requestBody, ContentType.create("text/plain", "utf-8"));
                httpPost.setEntity(entity);

                CloseableHttpResponse response = httpClient.execute(httpPost);
                if (response.getCode() == 200) {
                    String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                    log.info("请求isee返回结果：{}", result);
                }

            } catch (Exception e) {
                log.error("请求isee异常", e);
            }
        } else {
            try {
                String response = IhubUtils.post(requestBody, "json", Map.of("ISEE_GW_SIGN", sign(key, requestBody)), PropUtil.getString("isee.url"));
                Map<String, Object> resultMap = JSONUtils.readJson2Map(response);
                if (!"0".equals(resultMap.get("responseCode"))) {
                    throw new MessageException("Ihub返回失败:" + resultMap.get("responseMsg"));
                }
            } catch (Exception e) {
                log.error("请求ihub异常", e);
                throw e;
            }
        }
    }

    public static String requestBody(List<Policy> policyList, String productName, List<Policyuser> policyuserList) {
        Map<String, Object> body = new HashMap<>();
        body.put("companyId", companyId);
        body.put("requestType", "01");
        body.put("UUID", UUID.randomUUID());
        body.put("sendTime", DateUtil.dateToStr3(LocalDateTime.now()));

        Map<String, Object> request = new HashMap<>();
        request.put("iseeBiz", policyList.get(0).getIseebiz());
        request.put("orgNo", orgNo);
        request.put("campaignName", productName);
        request.put("applyTime", System.currentTimeMillis());
        request.put("underwritingTime", Timestamp.valueOf(policyList.get(0).getInssueDate()));
        request.put("orderNo", policyList.get(0).getOrderId());

        Map<String, Object> extraInfo = new HashMap<>();
        extraInfo.put("关联保单号", policyList.stream().map(Policy::getPolicyNo).map(String::valueOf).collect(Collectors.toList()));
        request.put("extraInfo", extraInfo);

        List<Map<String, Object>> customerList = policyuserList.stream().map(u -> {
                    Map<String, Object> customer = new HashMap<>();
                    customer.put("customType", u.getIsInsuredFlag() != 2 ? 1 : 2);
                    customer.put("customName", u.getInsuredFullName());
                    customer.put("customIdType", idType(u.getInsuredIdNoType()));
                    customer.put("customIdNo", u.getInsuredIdNo());
                    customer.put("customMobile", u.getHomePhoneNo());
                    return customer;
                }
        ).collect(Collectors.toList());

        request.put("customer", customerList);

        body.put("request", request);
        return JSONUtils.toJson(body);
    }

    public static String requestBody(Map<String, Object> signParams) {
        Map<String, Object> body = new HashMap<>();
        body.put("companyId", companyId);
        body.put("requestType", "01");
        body.put("UUID", UUID.randomUUID());
        body.put("sendTime", DateUtil.dateToStr3(LocalDateTime.now()));

        Map<String, Object> request = new HashMap<>();
        request.put("iseeBiz", signParams.get("iseebiz"));
        request.put("orgNo", orgNo);
        request.put("campaignName", signParams.get("campaignName"));
        request.put("applyTime", System.currentTimeMillis());
        request.put("underwritingTime", System.currentTimeMillis());
        request.put("orderNo", signParams.get("orderNo"));

////        Map<String, Object> extraInfo = new HashMap<>();
////        extraInfo.put("关联订单号", policyList.stream().map(Policy::getPolicyID).map(String::valueOf).collect(Collectors.toList()));
//
//        request.put("extraInfo", extraInfo);
        Map<String, Object> customerMap = new HashMap<>();
        Map<String, String> customer = (Map<String, String>) signParams.get("customer");
        customerMap.put("customType", 1);
        customerMap.put("customName", customer.get("name"));
        customerMap.put("customIdType", idType(customer.get("idType")));
        customerMap.put("customIdNo", customer.get("idNo"));

        request.put("customer", Collections.singletonList(customerMap));

        body.put("request", request);
        return JSONUtils.toJson(body);
    }

    public static String sign(String key, String requestBody) throws NoSuchAlgorithmException, InvalidKeyException, UnsupportedEncodingException {
//        byte[] base64Body = Base64.getEncoder().encode(requestBody.getBytes(StandardCharsets.UTF_8));
//        Mac sha246HMAC = Mac.getInstance("HmacSHA256");
//        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
//        sha246HMAC.init(secretKey);
//        byte[] bytes = sha246HMAC.doFinal(base64Body);
//        StringBuilder sb = new StringBuilder();
//        for (byte b : bytes) {
//            sb.append(Integer.toHexString((b & 0xFF) | 0x100), 1, 3);
//        }
//        return sb.toString();
        String base64Body = Base64.getEncoder().encodeToString(requestBody.getBytes(StandardCharsets.UTF_8));
        return Hashing.hmacSha256(key.getBytes(StandardCharsets.UTF_8)).hashString(base64Body, StandardCharsets.UTF_8).toString();
    }

    private static String idType(String idType) {
        switch (idType) {
            case "身份证":
                return "111";
            case "护照":
                return "414";
            default:
                return "990";
        }
    }
}

package com.travelye.external.wesure.utils;

import java.nio.charset.StandardCharsets;
import java.security.Key;

import com.travelye.external.wesure.exception.WesureException;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * 对称加密算法AES加解密工具类
 */
public class MessageUtils {

    public static final String AES_ALGORITHM = "AES";
    public static final String CRYPT_MODEL = "AES/ECB/PKCS5Padding";
    public static final String CHARSET_UTF8 = "UTF-8";

    /**
     * 加密
     *
     * @param data         明文
     * @param secretKeyStr 密钥
     * @return
     * @throws Exception
     */
    public static String encrypt(String data, String secretKeyStr) throws Exception {
        if (data == null || secretKeyStr == null) {
            throw new IllegalArgumentException();
        }
        // KEY转换
        Key key = new SecretKeySpec(Base64.decodeBase64(secretKeyStr), AES_ALGORITHM);
        // 加密
        Cipher cipher = Cipher.getInstance(CRYPT_MODEL);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] result = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(result);
    }

    /**
     * 解密
     *
     * @param cryptedStr   密文
     * @param secretKeyStr 密钥
     * @return
     * @throws Exception
     */
    public static String decrypt(String cryptedStr, String secretKeyStr) {
        try {

            if (cryptedStr == null || secretKeyStr == null) {
                throw new IllegalArgumentException();
            }
            // KEY转换
            Key key = new SecretKeySpec(Base64.decodeBase64(secretKeyStr), AES_ALGORITHM);
            // 解密
            Cipher cipher = Cipher.getInstance(CRYPT_MODEL);
            cipher.init(Cipher.DECRYPT_MODE, key);
            byte[] result = cipher.doFinal(Base64.decodeBase64(cryptedStr));
            return new String(result, StandardCharsets.UTF_8);
        }catch (Exception e){
            throw new WesureException(WesureErrorCodeEnum.MESSAGE_ERROR);
        }
    }

}

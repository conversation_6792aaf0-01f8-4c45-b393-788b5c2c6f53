package com.travelye.external.wesure.dto.cancel.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-12-24 21:55
 **/
@Data
public class CancelResponse {
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime statusChangeDate;
    /**
     * 退费金额，单位分
     */
    private Integer surrenderValue;
    /**
     * 保单状态，目前固定为 2 - 生效前退保
     */
    private Integer policyStatus;

}

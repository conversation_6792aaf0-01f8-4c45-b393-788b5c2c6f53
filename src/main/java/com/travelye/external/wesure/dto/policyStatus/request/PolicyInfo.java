package com.travelye.external.wesure.dto.policyStatus.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022-12-29 09:31
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PolicyInfo {
    private String policyNo;
    private Integer status;
    private Integer premium;
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime statusTime;
}

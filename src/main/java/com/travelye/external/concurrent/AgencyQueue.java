package com.travelye.external.concurrent;

import com.travelye.base.MessageException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.util.Hashtable;
import java.util.Map;

/**
 * 接口对接处理队列(渠道agencyUserId)，任务在队列中时等待上次相同agencyUserId的请求执行完毕后再继续执行
 * 防止并发的事务中相同渠道的账户数据库操作锁死
 *
 * <AUTHOR>
 */
public class AgencyQueue {
    private final static Logger log = LoggerFactory.getLogger(AgencyQueue.class);
    private static final Map<String, String> map = new Hashtable<String, String>();

    /**
     * 默认查询1200次，即等待2分，还在队列中就直接返回不再等待。
     *
     * @param agencyUserId
     */
    public static void add(String agencyUserId) {
        AgencyQueue.add(agencyUserId, 1200);
    }

    public static void add(String agencyUserId, int count) {
        if (doAdd(agencyUserId, count) != null) {
            throw new MessageException("系统正在处理中");
        }
        log.info("队列中添加---" + agencyUserId);
    }

    public static void remove(String agencyUserId) {
        log.info("队列中删除---" + agencyUserId);
        map.remove(agencyUserId);
    }

    /**
     * 不等待，直接加入队列
     *
     * @param agencyUserId
     * @return
     */
    public static boolean addFast(String agencyUserId) {
        String q = agencyUserId;
        Object o = map.put(q, "0");
        if (o == null) {
            log.info("队列中添加fast---" + q);
        } else {
            log.info("队列中已包含---" + q);
        }
        return o == null;
    }

    /**
     * 每隔100ms查询一次，是否任务还在队列中
     *
     * @param id
     * @param count 查询次数
     * @return
     */
    private static Object doAdd(String id, int count) {
        Object o = null;
        for (int i = 0; i <= count; i++) {
            o = map.put(id, "0");
            if (o == null) {
                break;
            } else {
                try {
                    if (i > 0) {
                        log.info("add==" + id + "---" + Thread.currentThread().getId() + "=" + i);
                    }
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                }
            }
        }
        return o;
    }
}

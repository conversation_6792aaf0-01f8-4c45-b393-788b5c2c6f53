package com.travelye.blacklist;

import com.travelye.base.QueryResult;
import com.travelye.vo.BlacklistUser;
import com.travelye.vo.Policyuser;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * Created by zzl on 2016/12/27.
 */
public interface BlacklistService {

    /**
     * 保存黑名单
     * @param user
     */
    void save(BlacklistUser user);

    /**
     * 移除黑名单
     * @param ID
     */
    void delete(int ID);


    /**
     * 根据ID获取黑名单用户
     * @param id
     * @return
     */
    BlacklistUser getBlistUserById(int id);

    /*
      根据证件号码，证件类型，查找黑名单
     */

    List<BlacklistUser> getBlistUserByNoAndType(String IDNo , String  IDType);

    /**
     * 查询黑名单
     * @param condition
     * @param start
     * @param count
     * @return
     */
    QueryResult queryBlacklist(Condition4blacklist condition, int start, int count);


    /**
     * 保存上传Execl的黑名单用户
     * @param file
     * @param uid    操作人员
     */
    void saveBlacklistUsers(File file,String excelfileFileName,long uid) throws Exception;


    /**
     * 获取黑名单过滤配置项项
     * @return
     */

    List<Map<String,Object>> getBlackListFilterConfig();

    /**
     * 更新黑名单过滤配置项
     * @param configName
     * @param configVlue
     */

    void updateConfig(String configName,String configVlue);


    /**
     * 被保险人黑名单检查
     * @param insuredList
     * @return
     */
    public boolean isInsuredsAllowed(List<Policyuser> insuredList);
}

package com.travelye.blacklist;

import com.travelye.base.QueryResult;
import com.travelye.vo.BlacklistUser;

import java.util.List;
import java.util.Map;

/**
 * Created by zzl on 2016/12/27.
 */
public interface BlacklistDao {
    /**
     * 插入黑名单
     * @param user
     */

    void insert(BlacklistUser user);

    /**
     * 修改黑名单人员信息
     * @param user
     */
    void update(BlacklistUser user);

    /**
     * 根据ID获取黑名单用户
     * @param id
     * @return
     */
    BlacklistUser getBlistUserById(int id);

    /**
     * 移除黑名单
     * @param blacklistUserID
     */
    void delete(int blacklistUserID);

    /*
      根据证件号码，证件类型，查找黑名单
     */

    List<BlacklistUser> getBlistUserByNoAndType(String IDNo , String  IDType);


    /**
     * 查询黑名单
     * @param condition
     * @param start
     * @param count
     * @return
     */
    QueryResult queryBlacklist(Condition4blacklist condition, int start, int count);


    /**
     * 保存上传Execl的多名黑名单用户
     */
    void saveBlacklistUsers(List<BlacklistUser> userList);

    /**
     * 获取黑名单过滤配置项项
     * @return
     */

    List<Map<String,Object>> getBlackListFilterConfig();


    /**
     * 更新黑名单过滤配置项
     * @param configName
     * @param configVlue
     */

    void updateConfig(String configName,String configVlue);


    List<BlacklistUser> getBlistUserByBlacklistConfig(Map<String,Object> map,List<String> keylist,Map<String,Object> policyuserMap);

}

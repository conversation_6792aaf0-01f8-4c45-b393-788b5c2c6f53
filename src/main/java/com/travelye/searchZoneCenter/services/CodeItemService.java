package com.travelye.searchZoneCenter.services;



import com.travelye.vo.CodeItem;

import java.util.List;

/**
 * Created by KAFA5 on 2016/7/27.
 */
public interface CodeItemService {

    List<CodeItem> getProvinces();//获取所有的省以及直辖市

    /**
     * 获取所有有效的省以及直辖市
     *
     * @return
     */
    List<CodeItem> getValidProvinces();

    List<CodeItem> getGradeOneJobs();//获得所有的第一级工作名称

    List<CodeItem> getGradeOneJobsOnCustomize1(String Customize1);//根据风险获得第一级工作

    List<CodeItem> getGradeTwoJobsOnCustomize1(String levelCode, String Customize1);//根据风险和第一级的分级码获得第二级工作

    List<CodeItem> getCountriesAndAreas();//获得所有的国家以及地区

    List<CodeItem> getFirstLevelCodeItemsOnCodeSetId(int codeSetId);//根据CodeSet获取到CodeItem的第一级目录

    List<CodeItem> get1stLvEnabledItemsBySetCode(String codeSetCode);//根据CodeSet获取到CodeItem的第一级目录

    List<CodeItem> getCitiesOnLevelCode(String LevelCode);//获取所有的直辖市以及省下一级别的城市

    List<CodeItem> getTownsOnLevelCode(String LevelCode);//获得所有的区县

    List<CodeItem> getGradeTwoJobsOnLevelCode(String LevelCode);//根据第一级工作获得所有的第二级工作名称

    List<CodeItem> getGradeThreeJobsOnLevelCode(String LevelCode, String Customize1);//根据第二级工作以及工作等级获得所有的第三级工作名称

    CodeItem getDivisionOnLevelCode(String LevelCode);//根据编号精确获得一个省区县


    CodeItem getCountryOnCodeItemId(String CodeItemId);//根据编号精确获得一个区县

    CodeItem getAreaOnCodeItemId(String CodeItemId);//根据编号精确获得一个区县

    CodeItem getGradeThreeJobOnLevelCode(String LevelCode);//根据编号精确获得第三级工作

    CodeItem getCountryOrArea(String CodeItemId);//根据分级码精确获得一个国家或者地区

    CodeItem getCodeItem(int CodeItemId);//根据主键精确获得一个CodeItem对象

    CodeItem getJobOnlevelCode(String levelCode);//根据分级码获得一个具体的Job

    void updateCodeItem(CodeItem codeItem);//更新信息

    void saveCodeItem(CodeItem codeItem);//插入一条数据

    List<CodeItem> getItemBySetCode(String codeSetCode);

    List<CodeItem> getItemBySetCode(String codeSetCode, int islowest);

    List<CodeItem> getNextLevelCodeItemsOnCodeItemId(int codeItemId);/*根据主键获得下一级的CodeItem集合*/

    long getMaxPrimaryKey();//获取表格中的最大主键数

    String getMaxLevelCodeInSameLevel(CodeItem codeItem);//获取同级最大分级码的值

    String getMaxLevelCodeInNextLevel(CodeItem codeItem);//获取下级最大分级码的值

    String getMaxCode();//获得表格中的最大Code

    CodeItem getFirstCodeItemInNextLevel(int codeSetId);//根据主键获得下一级的第一个CodeItem

    CodeItem getCodeItemOnLevelCodeAndCodeSetId(String levelCode, int CodeSetId);//根据分级码还有CodeSetId获得一个精确的CodeSet

    List<CodeItem> threeGradeJobsOnLevelCodeAndCustomize1(String levelCode, String Customize1);//根据风险和上级的二级码获得第三级目录

    List<CodeItem> oneGradeJobsOnCustomize1(String Customize1);//根据风险获得第第一级目录

    List<CodeItem> twoGradeJobsOnLevelCodeAndCustomize1(String levelCode, String Customize1);//根据风险和上级的分级码获得第二级目录

    CodeItem getCodeItemByCodeAndCodeSetId(int CodeSetId, String CODE);//根据CODE还有CodeSetId获得CodeItem

    CodeItem getCodeItemByCodeAndCodeSetCode(String CodeSetCode, String CODE);//根据CODE还有CodeSetCode获得CodeItem

    CodeItem getCodeItemEnabledByCodeAndCodeSetCode(String CodeSetCode, String code);//根据CODE还有CodeSetCode获得有效的CodeItem

    List<CodeItem> getSuperCodeItemsOnCode(String Code, String CodeSetCode);//根据Code获得该单位的所有上级工作信息

    String getCodeItemsNamesOnCode(String Code, String CodeSetCode);

    /*
   根据CodeSetCode，祖先元素的LevelCode，以及级别差，获取所有末级元素
    */
    List<CodeItem> getLowestItemsBySetCodeAndAncestorLevCode(String CodeSetCode, String LevelCode, int levelNum);

}

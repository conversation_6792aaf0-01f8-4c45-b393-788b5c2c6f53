package com.travelye.policy.ruleEngine;

import lombok.Data;

/**
 * 规则作用域，当前需要区分是field还是method
 *
 * <AUTHOR> C.R.
 * @date 2023/07/31 22:01
 **/
@Data
public class Scope {
    public static final String TYPE_FIELD = "field";
    public static final String TYPE_METHOD = "method";

    public static final String CLAZZ_POLICY = "Policy";
    public static final String CLAZZ_USER = "Policyuser";


    /**
     * 作用域所在的类
     */
    private String clazz;

    /**
     * 作用域名称，对应field或者method的名称
     */
    private String name;

    /**
     * 作用域类型
     *
     * @see Scope#TYPE_FIELD
     * @see Scope#TYPE_METHOD
     */
    private String type;
}

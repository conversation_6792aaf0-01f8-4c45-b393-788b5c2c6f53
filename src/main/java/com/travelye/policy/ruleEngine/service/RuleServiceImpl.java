package com.travelye.policy.ruleEngine.service;

import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.partner.PartnerService;
import com.travelye.policy.PolicyDao;
import com.travelye.policy.ruleEngine.Condition;
import com.travelye.policy.ruleEngine.RuleResult;
import com.travelye.policy.ruleEngine.RuleUtils;
import com.travelye.policy.ruleEngine.Scope;
import com.travelye.vo.EditableConfig;
import com.travelye.vo.Partner;
import com.travelye.vo.Policy;
import com.travelye.vo.Policyuser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> C.R.
 * @date 2023/07/27 21:16
 **/
@Service
@Slf4j
public class RuleServiceImpl implements RuleService {


    private final PolicyDao policyDao;
    private final PartnerService partnerService;

    public RuleServiceImpl(PolicyDao policyDao, PartnerService partnerService) {
        this.policyDao = policyDao;
        this.partnerService = partnerService;
    }

    @Override
    public RuleResult underwriting(Policy policy, List<Policyuser> userList) {
        boolean ok = true;
        List<String> errorMsgList = new ArrayList<>();
        // 公共核保校验条件
        EditableConfig commonRuleJson = policyDao.getConfigByName("commonRuleJson");
        if (commonRuleJson != null && StringUtils.isNotBlank(commonRuleJson.getConfigValue())) {
            RuleResult ruleResult = executeRule(commonRuleJson.getConfigValue(), policy, userList);
            ok = ruleResult.isOk();
            if (!ruleResult.isOk()) {
                errorMsgList.add(ruleResult.getMessage());
            }
        }
        // 渠道的核保条件
        Partner agency = partnerService.getPartner(policy.getAgencyID());
        if (StringUtils.isNotBlank(agency.getRuleJson())) {
            RuleResult ruleResult = executeRule(agency.getRuleJson(), policy, userList);
            ok = (ok && ruleResult.isOk());
            if (!ruleResult.isOk()) {
                errorMsgList.add(ruleResult.getMessage());
            }
        }
        return ok ? new RuleResult().ok() : new RuleResult().fail(String.join("｜", errorMsgList));
    }

    @Override
    public RuleResult executeRule(String ruleJson, Policy policy, List<Policyuser> userList) {
        List<Condition> conditionList = null;
        boolean ok = true;
        List<String> messageList = new ArrayList<>();
        try {
            conditionList = (List<Condition>) JsonMapperUtils.jsonToObject(ruleJson, Condition.class, List.class);
        } catch (Exception e) {
            log.error("规则JSON解析失败，不进行规则校验", e);
            return new RuleResult().ok();
        }

        if (CollectionUtils.isEmpty(conditionList)) {
            return new RuleResult().ok();
        }

        for (Condition condition : conditionList) {
            Scope scope = condition.getScope();
            if (Scope.CLAZZ_POLICY.equals(scope.getClazz())) {
                if (CollectionUtils.isNotEmpty(condition.getConstraintConditionList())) {
                    boolean meetConstraint = true;
                    // 所有限制条件都得符合
                    for (Condition constraintCondition : condition.getConstraintConditionList()) {
                        if (Scope.CLAZZ_POLICY.equals(constraintCondition.getScope().getClazz())) {
                            meetConstraint = (meetConstraint && RuleUtils.underwrite(policy, constraintCondition).isOk());
                        }
                        if (Scope.CLAZZ_USER.equals(constraintCondition.getScope().getClazz())) {
                            for (Policyuser user : userList) {
                                meetConstraint = (meetConstraint && RuleUtils.underwrite(user, constraintCondition).isOk());
                            }
                        }
                    }
                    if (!meetConstraint) {
                        continue;
                    }
                }
                RuleResult policyRuleResult = RuleUtils.underwrite(policy, condition);
                ok = (ok && policyRuleResult.isOk());
                if (!policyRuleResult.isOk()) {
                    messageList.add(policyRuleResult.getMessage());
                }
            }
            if (Scope.CLAZZ_USER.equals(scope.getClazz())) {
                for (Policyuser user : userList) {
                    if (CollectionUtils.isNotEmpty(condition.getConstraintConditionList())) {
                        boolean meetConstraint = true;
                        // 所有限制条件都得符合
                        for (Condition constraintCondition : condition.getConstraintConditionList()) {
                            if (Scope.CLAZZ_POLICY.equals(constraintCondition.getScope().getClazz())) {
                                meetConstraint = (meetConstraint && RuleUtils.underwrite(policy, constraintCondition).isOk());
                            }
                            if (Scope.CLAZZ_USER.equals(constraintCondition.getScope().getClazz())) {
                                meetConstraint = (meetConstraint && RuleUtils.underwrite(user, constraintCondition).isOk());
                            }
                        }
                        if (!meetConstraint) {
                            continue;
                        }
                    }
                    RuleResult userRuleResult = RuleUtils.underwrite(user, condition);
                    ok = (ok && userRuleResult.isOk());
                    if (!userRuleResult.isOk()) {
                        messageList.add(userRuleResult.getMessage());
                    }
                }
            }
        }
        return new RuleResult(ok, String.join("｜", messageList));
    }
}

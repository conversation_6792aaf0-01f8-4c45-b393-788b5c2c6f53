/**
 * 包名：com.wenhq.policy<br>
 * 文件：Condition.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-25 上午10:20:06<br>
 * <p>
 * Current revision $Revision: 1.3 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: Condition.java,v $
 * Revision 1.3  2010/11/03 00:27:16  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.2  2010/09/15 04:02:03  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.1  2010/08/26 04:11:54  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.policy;

import com.travelye.base.SjisConstant;
import com.travelye.security.session.SecurityUser;
import lombok.Data;

/**
 * <AUTHOR>
 *保单的搜索条件
 */
@Data
public class Condition implements ConditionFace {

    private long branch;            // 分公司ID
    private long partnerid;            // 代理商ID
    private long userID;            // 操作者ID
    private String policyID;        // 保险凭证号
    private String policyNo;        // 保单号
    private String isStandard;        // 是否是标准产品
    private String issueBeginDate;        // 开始签单日期
    private String issueEndDate;        // 结束签单日期
    private String effectiveBeginDate;    // 开始生效日期
    private String effectiveEndDate;    // 结束生效日期
    private String lineno;            // 线路/团号
    private String insuredName;        // 被保险人姓名
    private String insuredIdNo;        // 被保险人证件号码
    private String status;            // 保单状态
    private String attn;            // 经办人
    private String policyStatus;    // 被保险人状态
    private String remark;    // 备注
    private String departmentId;    // 部门id，多个已逗号分割
    private String branchDepartmentID;    // 分公司部门id，多个已逗号分割
    private String webSubmit;    // 页面提交
    private String feeNo;    //支付流水号
    private String includeLiberty;  //是否包含liberty保单

    public boolean has(String o) {
        if (o == null || "".equals(o.trim())) {
            return false;
        } else
            return true;
    }


    /** 根据用户 返回查询字符串
     * @param userSession
     * @return
     */
    public String getConStr(ConditionFace condition, SecurityUser userSession) {
        StringBuffer conStr = new StringBuffer();
        //穷游用户，绿野的客户
        if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_saleclient)) {
            conStr.append(" and p.clientid=").append(userSession.getAgencyId());
            if (userSession.getAdmin() != 1) {
                conStr.append(" and ((p.uid=").append(userSession.getUid());
                conStr.append(" and p.departmentid=").append(userSession.getDepartmentId());
                conStr.append(") or (p.departmentid");
                conStr.append(" in (select p.departmentid");
                conStr.append(" from tbldepartment p,tbldepartment pa,tbluservisitdepartment v");
                conStr.append(" where p.departtype='saleclientdep' and v.DEPARTMENTID=pa.DEPARTMENTID ");
                conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and v.uid=");
                conStr.append(userSession.getUid());
                conStr.append(")))");
            }
            if (condition.has(condition.getDepartmentId())) {
                conStr.append(" and p.departmentid ");
                conStr.append(" in (select p.departmentid");
                conStr.append(" from tbldepartment p,tbldepartment pa");
                conStr.append(" where p.departtype='saleclientdep' ");
                conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and pa.DEPARTMENTID in (");
                conStr.append(condition.getDepartmentId());
                conStr.append("))");
            }
            //绿野的用户,查询是代理商的销售人员为条件，
            //绿野直接出单并销售人员是本人或授权部门
            //穷游出单并穷游的销售人员是本人或授权部门
        } else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_sale)) {
            conStr.append(" and p.agencyID =").append(userSession.getAgencyId());
            if (userSession.getAdmin() != 1) {
                conStr.append(" and ((p.agencysalesmanid=").append(userSession.getUid());
                conStr.append(" and p.agencysalesdepartmentid=").append(userSession.getDepartmentId());
                conStr.append(" and ad.departmentid=-1) or p.agencysalesdepartmentid =ad.departmentid)");
            }
        } else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_admin)) {
            conStr.append(" and p.branch=").append(userSession.getAgencyId());
            if (userSession.getAdmin() != 1) {//分公司销售人员是 本人或 授权部门,代理商指定的销售人员是本人或 授权部门
                conStr.append(" and ((p.salesManid=").append(userSession.getUid());
                conStr.append(" and p.salesdepartmentid=").append(userSession.getDepartmentId());
                conStr.append(" and ad.departmentid=-1) or p.salesdepartmentid =ad.departmentid)");
            }
            if (condition.has(condition.getBranchDepartmentID())) {
                conStr.append(" and p.salesdepartmentid ");
                conStr.append(" in (select p.departmentid");
                conStr.append(" from tbldepartment p,tbldepartment pa");
                conStr.append(" where p.departtype='admindep' ");
                conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and pa.DEPARTMENTID in (");
                conStr.append(condition.getBranchDepartmentID());
                conStr.append("))");
            }
        } else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_china)) {
        } else { //其它类型的用户不允许查询保单
            conStr.append(" and FALSE");
        }
        if (condition.has(condition.getDepartmentId())) {
            conStr.append(" and p.agencysalesdepartmentid ");
            conStr.append(" in (select p.departmentid");
            conStr.append(" from tbldepartment p,tbldepartment pa");
            conStr.append(" where p.departtype='saledep' ");
            conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and pa.DEPARTMENTID in (");
            conStr.append(condition.getDepartmentId());
            conStr.append("))");
        }
        return conStr.toString();
    }

    /** 返回关联的表，目前仅指 分公司的部门
     *  2015-07-31 增加到一级中介
     * @param userSession
     * @return
     */
    public String getTableStr(SecurityUser userSession) {
        StringBuffer conStr = new StringBuffer();
        if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_admin) && (userSession.getAdmin() != 1)) {
            conStr.append(" ,(SELECT -1 departmentid UNION select DISTINCT p.departmentid");
            conStr.append(" from tbldepartment p,tbldepartment pa,tbluservisitdepartment v");
            conStr.append(" where p.departtype='admindep' and v.DEPARTMENTID=pa.DEPARTMENTID ");
            conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and v.uid=");
            conStr.append(userSession.getUid());
            conStr.append(" ) as ad");
        } else if (userSession.getAgencyType().equalsIgnoreCase(SjisConstant.module_sale) && (userSession.getAdmin() != 1)) {
            conStr.append(" ,(SELECT -1 departmentid UNION select DISTINCT p.departmentid");
            conStr.append(" from tbldepartment p,tbldepartment pa,tbluservisitdepartment v");
            conStr.append(" where p.departtype='saledep' and v.DEPARTMENTID=pa.DEPARTMENTID ");
            conStr.append(" AND (p.PARENT LIKE CONCAT(pa.PARENT,pa.DEPARTMENTID,',%') OR p.DEPARTMENTID=pa.DEPARTMENTID) and v.uid=");
            conStr.append(userSession.getUid());
            conStr.append(" ) as ad");
        }
        return conStr.toString();
    }
}



package com.travelye.policy;

import com.travelye.base.tools.*;
import com.travelye.email.AsyncEmailService;
import com.travelye.email.EmailRecord;
import com.travelye.email.EmailService;
import com.travelye.email.EmailServiceImpl;
import com.travelye.external.concurrent.Queue;
import com.travelye.external.data.ExternalBackService;
import com.travelye.external.receipt.ReceiptRecord;
import com.travelye.external.receipt.ReceiptService;
import com.travelye.external.receipt.ReceiptTask;
import com.travelye.vo.*;


import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

/**
 * Created by zzl on 2017/4/21.
 */
@Component
public class TaskJob {

    private final static Logger log = LoggerFactory.getLogger(TaskJob.class);

    private final PolicyService policyService;
    private final ReceiptService receiptService;
    private final ExternalBackService externalBackService;
    private final PolicyDao policyDao;
    private final EmailService emailService;
    private final AsyncEmailService asyncEmailService;

    public TaskJob(PolicyService policyService, ReceiptService receiptService, ExternalBackService externalBackService, PolicyDao policyDao, EmailService emailService, AsyncEmailService asyncEmailService) {
        this.policyService = policyService;
        this.receiptService = receiptService;
        this.externalBackService = externalBackService;
        this.policyDao = policyDao;
        this.emailService = emailService;
        this.asyncEmailService = asyncEmailService;
    }

    /**
     * 定时发送保单处理结果通知
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void sendCallbackRequest() {
        try {

            //已出单成功的保单，更新通知列表的result
            externalBackService.savePolicyProcessFinished();

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.MINUTE, -2); //未成功通知的记录每隔至少两分钟发送一次

            List<IcExternalBack> sendList = externalBackService.getCallbackNeededList(sdf.format(cal.getTime()));
            for (IcExternalBack eb : sendList) {
                Queue.add("ExternalCallback", String.valueOf(eb.getId()), 20);
                try {
                    externalBackService.saveAndSendCallback(eb);
                } catch (Exception e) {
                    log.error("发送回调通知请求异常", e);
                } finally {
                    Queue.remove("ExternalCallback", eb.getId());
                }

            }
        } catch (Exception e) {
            log.error("定时发送保单处理结果通知,任务出错：", e);
        }
    }


    @Scheduled(cron = "0 0/1 * * * ?")
    public void runReceiptTasks() {

        try {
            List<ReceiptTask> taskList = receiptService.getReceiptTasks();
            if (CollectionUtils.isNotEmpty(taskList)) {
                log.info("发票定时任务: size=" + taskList.size());
                for (ReceiptTask task : taskList) {
                    ReceiptRecord record = receiptService.getReceiptRecord(task.getReceiptId());
                    if (record != null) {
                        if (ReceiptTask.RECEIPT_TASK_APPLY.equals(task.getTaskType())) {
                            receiptService.handleDownApply(record, task.getRetryCount());
                        } else if (ReceiptTask.RECEIPT_TASK_NOTIFY.equals(task.getTaskType())) {
                            receiptService.handleDownNotify(record, task.getRetryCount());
                        } else {
                            log.error("未知的发票定时任务类型：" + task.getTaskType());
                            receiptService.deleteReceiptTask(task.getReceiptId());
                        }
                    } else {
                        log.error("未找到相应的发票记录：receiptId=" + task.getReceiptId());
                        receiptService.deleteReceiptTask(task.getReceiptId());
                    }
                }
            }
        } catch (Exception e) {
            log.error("发票定时任务启动失败", e);
        }
    }

    /**
     * 每日凌晨两点，将昨天生成的日志文件上传到Ali OSS
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void uploadLogToAliOss() {
        try {
            String logPath = PropUtil.getString("logPath");
            File directory = new File(logPath);
            File[] files = directory.listFiles();
            if (files != null) {
                for (File f : files) {
                    LocalDateTime lastModified = LocalDateTime.ofInstant(Instant.ofEpochMilli(f.lastModified()),
                            ZoneId.systemDefault());
                    LocalDateTime today = LocalDateTime.now().with(LocalTime.MIDNIGHT);
                    LocalDateTime yesterday = today.minusDays(1);
                    if (lastModified.isBefore(today) && !lastModified.isBefore(yesterday)) {
                        String logDateStr = f.getName().split("\\.")[1];
                        LocalDate logDate = LocalDate.parse(logDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                        // 按月份将文件分类
                        String fileName = logDate.format(DateTimeFormatter.ofPattern("yyyy-MM")) + "/" + f.getName();
                        AliOSSUtil.uploadFile(f, PropUtil.getString("ali-cloud.oss.log-bucket"), fileName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("每日log上传Ali OSS失败", e);
        }
    }

    /**
     * 每日凌晨三点，查询需要数据回传的CPS渠道的有效保单状态，如果由有效变为退保，则回传退保数据
     */
//    @Scheduled(cron = "0 0 3 * * ?")
    public void cpsPolicyStatusCheck() {
        try {
            log.info("检查CPS保单状态开始");
            List<Policy> policyList = policyService.getValidCpsPolicyList();
            if (CollectionUtils.isNotEmpty(policyList)) {
                for (Policy policy : policyList) {
                    try {
//                        policyService.queryLibertyPolicy(policy);
                    } catch (Exception e) {
                        log.error("CPS保单" + policy.getPolicyNo() + "状态检查失败", e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("CPS保单状态检查失败", e);
        }
    }

    /**
     * 每隔5分钟查询失败的邮件列表，并尝试重新发送
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void retryEmail() {
        List<EmailRecord> failedEmailList = policyDao.getFailedEmailRecordList();
        if (CollectionUtils.isEmpty(failedEmailList)) {
            return;
        }

        failedEmailList.forEach(e -> {
            if (EmailRecord.EMAIL_TYPE_POLICY == e.getType() && StringUtils.isNotBlank(e.getRecipient())) {
                Map<String, Object> result = emailService.sendPolicyEmailSilent(policyDao.getPolicy(Long.parseLong(e.getModuleId())), e.getRecipient(), EmailServiceImpl.RECIPIENT_TYPE_ALL);
                if (0 == (int) result.get("code")) {
                    // 失败
                    policyDao.addFailedEmailRecordRetryCount(e);
                    // 如果这次发送之前已经两次失败了，这次又失败了，那就告警
                    asyncEmailService.sendAlertEmail("【TravelYe】电子保单邮件发送失败", "邮件发送失败达到上限，id : " + e.getModuleId());
                } else {
                    policyDao.deleteFailedEmailRecord(e.getId());
                }
            }
        });
    }
}

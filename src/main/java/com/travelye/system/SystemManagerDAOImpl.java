package com.travelye.system;

import com.travelye.base.BaseJdbcDaoImpl;
import com.travelye.base.QueryResult;
import com.travelye.base.tools.JdbcUtil;
import com.travelye.vo.Users;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public class SystemManagerDAOImpl extends BaseJdbcDaoImpl implements ISystemManagerDAO {

	/*************************************菜单管理*************************/
	/**
	 * 保存新增、修改的菜单
	 */
	public void saveMenu(Menu menu) {
		String sql = "";
		if (menu.getMenuID() == 0) {
			sql = "Insert Into tblMenu (menuName, fatherID, level,"
				+ "href, sequence) values ("
				+ JdbcUtil.getStringValue(menu.getMenuName()) + ","
				+ JdbcUtil.getNumberValue(menu.getFatherID()) + ","
				+ JdbcUtil.getNumberValue(menu.getLevel()) + ","
				+ JdbcUtil.getStringValue(menu.getHref()) + ","
				+ JdbcUtil.getNumberValue(menu.getSequence()) + ")";
		} else {
			sql = "Update tblMenu set menuName="
				+ JdbcUtil.getStringValue(menu.getMenuName())
				+ ",fatherID="
				+ JdbcUtil.getNumberValue(menu.getFatherID())
				+ ",level="
				+ JdbcUtil.getNumberValue(menu.getLevel())
				+ ",href="
				+ JdbcUtil.getStringValue(menu.getHref())
				+ ",sequence="
				+ JdbcUtil.getNumberValue(menu.getSequence())
				+ " Where menuID=" + menu.getMenuID();
		}
		this.execute(sql);	
	}
	/**
	 * 读取一个菜单信息
	 */
	public Menu getMenu(long menuID) {
		
		String sql = "SELECT * FROM tblMenu t WHERE t.menuID = " 
			+ menuID;
		
		List list=this.findObject(sql, Menu.class);
		Menu model = null;
		
		if(list != null && list.size() > 0)
		{
			model = (Menu)list.get(0); 
		}
		
		return model;	
	}
	/**
	 * 读取一级菜单列表
	 */
	public List<Menu> getLevelOneMenuList() {

		String sql = "SELECT menuID, menuName FROM tblMenu "
			+ "WHERE level=1 ORDER BY sequence";
		return this.findObject(sql, Menu.class);
	}
	/**
	 * 读取所有菜单列表
	 */
	public List<Menu> getMenuList(Condition condition) {
		
		StringBuffer b=new StringBuffer();
		b.append("SELECT *, (SELECT menuName FROM tblMenu WHERE menuID = a.fatherID)");
		b.append("AS fatherName FROM tblMenu a ");
		if (condition.getMenuID() != 0) {
			b.append(" WHERE fatherID = ").append(condition.getMenuID());
		}
		
		return this.findObject(b.toString(), Menu.class);	
	}
	

	/*******************************菜单权限管理******************************/
	/**
	 * 保存菜单权限
	 */
	public void saveMenuAction(MenuAction menuAction) {
		String sql = "";
		if (menuAction.getId() == 0) {
			sql = "Insert Into tblMenuAction (actionID, menuID) values ("
					+ JdbcUtil.getNumberValue(menuAction.getActionID()) + ","
					+ JdbcUtil.getNumberValue(menuAction.getMenuID()) + ")";
		} else {
			sql = "Update tblMenuAction set actionID="
				+ JdbcUtil.getNumberValue(menuAction.getActionID())
				+ ",MenuID="
				+ JdbcUtil.getNumberValue(menuAction.getMenuID())
				+ " Where ID = " + menuAction.getId();
		}
		this.execute(sql);		
	}
	/**
	 * 删除菜单权限
	 * @param groupID
	 */
	public void deleteMenuAction(long menuID) {
		String sql="delete from tblMenuAction where menuID=" + menuID;
		this.execute(sql);
	}
	/**
	 * 读取一个菜单所有的权限
	 * @param menuID
	 * @return
	 */
	public List<MenuAction> menuActionList(long menuID) {

		String sql = "SELECT actionID FROM tblMenuAction "
			+ "WHERE menuID = " + JdbcUtil.getNumberValue(menuID);
		return this.findObject(sql, MenuAction.class);
	}
	/**
	 * 读取一个用户所有菜单列表
	 * @param condition
	 * @return
	 */
	public List<Menu> userMainMenuList(long userID) {
		String sql = "SELECT DISTINCT m.* " +
			" FROM tblMenu m,tblMenuAction ma,tbluseraction ua" +
			" WHERE m.LEVEL = 1 AND m.menuID=ma.menuID AND ma.actionID=ua.actionID" +
			" AND ua.uID=" + userID + 
			" order by m.sequence";
		return this.findObject(sql, Menu.class);
	}
	
	/**
	 * 读取一个用户所有子菜单列表
	 * @param condition
	 * @return
	 */
	public List<Menu> userSubMenuList(long userID) {
		String sql = "SELECT DISTINCT m.* " +
		" FROM tblMenu m,tblMenuAction ma,tbluseraction ua" +
		" WHERE m.LEVEL = 2 AND m.menuID=ma.menuID AND ma.actionID=ua.actionID" +
		" AND ua.uID=" + userID +
		" order by m.sequence";
		return this.findObject(sql, Menu.class);	
	}
	/*******************************组管理**********************************/

	public void saveGroup(Group group) {
		String sql = "";
		if (group.getGroupID() == 0) {
			sql = "Insert Into tblGroup (groupName, groupInfo, masterID, createDate"
					+ ") values ("
					+ JdbcUtil.getStringValue(group.getGroupName()) + ","
					+ JdbcUtil.getStringValue(group.getGroupInfo()) + ","
					+ JdbcUtil.getNumberValue(group.getMasterID()) + ","
					+ JdbcUtil.getDateTime(group.getCreateDate()) + ")";
		} else {
			sql = "Update tblGroup set groupName="
				+ JdbcUtil.getStringValue(group.getGroupName())
				+ ",groupInfo="
				+ JdbcUtil.getStringValue(group.getGroupInfo())
				+ ",masterID="
				+ JdbcUtil.getNumberValue(group.getMasterID())
				+ " Where groupID = " + group.getGroupID();
		}
		this.execute(sql);		
	}

	public Group getGroup(long groupID) {
		
		String sql = "SELECT *, (SELECT userName FROM tblUser WHERE uID = a.masterID)"
			+ "AS masterName FROM tblGroup a  WHERE groupID=" + groupID;
		
		List list=this.findObject(sql, Group.class);
		
		Group model = null;
		
		if(list != null && list.size() > 0)
		{
			model = (Group)list.get(0); 
		}
		
		return model;
		
	}
	
	public List<Group> getGroupList() {

		StringBuffer b=new StringBuffer();
		b.append("SELECT *, (SELECT userName FROM tblUser WHERE uID = a.masterID)");
		b.append("AS masterName FROM tblGroup a ");
		
		return this.findObject(b.toString(), Group.class);	
	}
	
	/*******************************组权限管理******************************/

	public void saveGroupAction(GroupAction groupAction) {
		String sql = "";
		//if (groupAction.getId() == 0) {
			sql = "Insert Into tblGroupAction (actionID, groupID, masterID, "
					+ "createDate) values ("
					+ JdbcUtil.getNumberValue(groupAction.getActionID()) + ","
					+ JdbcUtil.getNumberValue(groupAction.getGroupID()) + ","
					+ JdbcUtil.getNumberValue(groupAction.getMasterID()) + ","
					+ JdbcUtil.getDateTime(groupAction.getCreateDate()) + ")";
		/*
		 } else {
		 
			sql = "Update tblActinGroup set action="
				+ JdbcUtil.getNumberValue(groupAction.getActionID())
				+ ",groupID="
				+ JdbcUtil.getNumberValue(groupAction.getGroupID())
				+ ",masterID="
				+ JdbcUtil.getNumberValue(groupAction.getMasterID())
				+ ",createDate="
				+ JdbcUtil.getDate(groupAction.getCreateDate())
				+ " Where ID = " + groupAction.getId();
		}*/
		this.execute(sql);		
	}

	public void deleteGroupAction(long groupID) {
		String sql="delete from tblGroupAction where groupID=" + groupID;
		this.execute(sql);
	}
	
	public List<GroupAction> getGroupActionList(long groupID) {
		StringBuffer b=new StringBuffer();
		b.append("SELECT *, (SELECT userName FROM tblUser WHERE uID = a.masterID) AS masterName");
		b.append(" FROM tblGroupAction a WHERE groupID=").append(groupID);
		
		return this.findObject(b.toString(), GroupAction.class);		
	}
	
	/**
	 * 
	 */
	public List<Action> userActionList(long userID) {		
		String sql = "SELECT a.* FROM tblAction a,tbluseraction ua" +
				" WHERE a.actionID=ua.actionID " +
				" and ua.uID=" + userID + "";
		return this.findObject(sql, Action.class);
	}
	
/*****************************************用户分组管理******************************************/
	
	public void saveUser(Users user) {
		String sql = "";
		if (user.getUid() ==0){
			sql ="Insert into tblUser(loginname,username,password,status,agencyID,createDate,"
				+ "bindMac, macaddr, admin"
				+ ",realName, contact, employeeID,salesLocation,department,qualificationNO"
				+ " )values(" 
				+ JdbcUtil.getStringValue(user.getLoginname()) + "," 
				+ JdbcUtil.getStringValue(user.getUsername()) + "," 
				+ JdbcUtil.getStringValue(user.getPassword()) + "," 
				+ JdbcUtil.getNumberValue(user.getStatus()) + "," 
				+ JdbcUtil.getNumberValue(user.getAgencyID()) + "," 
				+ JdbcUtil.getDateTime(user.getCreateDate()) + "," 
				+ JdbcUtil.getNumberValue(user.getBindMac()) + "," 
				+ JdbcUtil.getStringValue(user.getMacAddr())  + "," 
				+ JdbcUtil.getNumberValue(user.getAdmin())  + ","
				+ JdbcUtil.getStringValue(user.getRealName())  + "," 
				+ JdbcUtil.getStringValue(user.getContact())  + "," 
				+ JdbcUtil.getStringValue(user.getEmployeeID())  + "," 
				+ JdbcUtil.getStringValue(user.getSalesLocation())  + "," 
				+ JdbcUtil.getStringValue(user.getDepartment())  + "," 
				+ JdbcUtil.getStringValue(user.getQualificationNO()) 
				+ ")";
		}else{
			sql ="update tblUser set loginname = " + JdbcUtil.getStringValue(user.getLoginname()) 
				+ ",username = " + JdbcUtil.getStringValue(user.getUsername()) 
				+ ",password = " + JdbcUtil.getStringValue(user.getPassword()) 
				+ ",status = " + JdbcUtil.getNumberValue(user.getStatus()) 
				+ ",agencyID =" + JdbcUtil.getNumberValue(user.getAgencyID()) 
				+ ",bindmac = " + JdbcUtil.getNumberValue(user.getBindMac())
				+ ",macAddr = " + JdbcUtil.getStringValue(user.getMacAddr())
				+ ",admin = " + JdbcUtil.getNumberValue(user.getAdmin())
				+ ",realname=" + JdbcUtil.getStringValue(user.getRealName())   
				+ ",contact=" + JdbcUtil.getStringValue(user.getContact())   
				+ ",employeeid="  + JdbcUtil.getStringValue(user.getEmployeeID())  
				+ ",saleslocation=" + JdbcUtil.getStringValue(user.getSalesLocation())   
				+ ",department=" + JdbcUtil.getStringValue(user.getDepartment())   
				+ ",qualificationno=" + JdbcUtil.getStringValue(user.getQualificationNO()) 
				+ " where uid=" + user.getUid();
		}
		this.execute(sql);
	}
	

	/**
	 * 根据登录名读取用户信息
	 * @param loginName
	 * @return
	 */
	public Users getUser(String loginName) {
		
		String queryString="select * from tblUser t where t.loginname="+JdbcUtil.getStringValue(loginName);
		
		List list=this.findObject(queryString, Users.class);
		
		Users model=null;
		
		if(list!=null)
		{
		  if(list.size()>0){
			  model=(Users)list.get(0); 
		  }
		}
		return model;
	}
	
	/*******************************用户分组管理****************************/

	public void saveUserGroup(UserGroup userGroup) {
		String sql = "";
		if (userGroup.getId() == 0) {
			sql = "Insert Into tblUserGroup (userID, groupID, modifyID, "
					+ "createDate) values ("
					+ JdbcUtil.getNumberValue(userGroup.getUserID()) + ","
					+ JdbcUtil.getNumberValue(userGroup.getGroupID()) + ","
					+ JdbcUtil.getNumberValue(userGroup.getMasterID()) + ","
					+ JdbcUtil.getDateTime(userGroup.getCreateDate()) + ")";
		} else {
			sql = "Update tblUserGroup set userID="
				+ JdbcUtil.getNumberValue(userGroup.getUserID())
				+ ",groupID="
				+ JdbcUtil.getNumberValue(userGroup.getGroupID())
				+ ",modifyID="
				+ JdbcUtil.getNumberValue(userGroup.getMasterID())
				+ " Where ID = " + userGroup.getId();
		}
		this.execute(sql);		
	}
	
	public void deleteUserGroup(long userID) {
		String sql="delete from tblUserGroup where userID=" + userID;
		this.execute(sql);	
	}
	
	public List<UserGroup> getUserGroupList(long userID) {
		StringBuffer b=new StringBuffer();
		b.append("SELECT *, (SELECT userName FROM tblUser WHERE uID = a.userID) AS userName, ");
		b.append("(SELECT userName FROM tblUser WHERE uID = a.modifyID) AS modifyName  ");
		b.append("FROM tblUserGroup a  WHERE a.userID=").append(userID);
		
		return this.findObject(b.toString(), UserGroup.class);		
	}
	/*************************************权限管理*************************/
	/**
	 * 保存新增、修改的权限信息
	 */
	public void saveAction(Action action) {
		String sql = "";
		if (action.getActionID() == 0) {
			sql = "Insert Into tblAction (actionType, actionName, action,"
					+ "viewMode) values ("
					+ JdbcUtil.getStringValue(action.getActionType()) + ","
					+ JdbcUtil.getStringValue(action.getActionName()) + ","
					+ JdbcUtil.getStringValue(action.getAction()) + ","
					+ JdbcUtil.getStringValue(action.getViewMode()) + ")";
		} else {
			sql = "Update tblAction set actionName="
				+ JdbcUtil.getStringValue(action.getActionName())
				+ ", actionType = "
				+ JdbcUtil.getStringValue(action.getActionType())
				+ ", action = "
				+ JdbcUtil.getStringValue(action.getAction())
				+ ", viewMode = "
				+ JdbcUtil.getStringValue(action.getViewMode())
				+ " Where actionID = " + action.getActionID();
		}
		this.execute(sql);		
	}
	/**
	 * 根据权限ID读取权限信息
	 * @param menuID
	 * @return
	 */
	public Action getAction(long actionID) {

		String sql = "SELECT * FROM tblAction t WHERE t.actionID = " 
			+ actionID;
		
		List list=this.findObject(sql, Action.class);
		Action model = null;
		
		if(list != null && list.size() > 0)
		{
			model = (Action)list.get(0); 
		}
		
		return model;	
	}
	/**
	 * 读取权限列表
	 * @param condition
	 * @return
	 */
	public List<Action> getActionList() {
		StringBuffer b=new StringBuffer();
		b.append("SELECT * ");
		b.append("FROM tblAction a ");
		
		return this.findObject(b.toString(), Action.class);	
	}

	/*******************************批单分类管理****************************/

	public void saveEndorsmentType(EndorsmentType edorsmentType) {
		String sql = "";
		if (edorsmentType.getEndorsmentTypeID() == 0) {
			sql = "Insert Into tblEndorsmentType (endorsmentTypeName, endorsmentNote) values ("
					+ JdbcUtil.getStringValue(edorsmentType.getEndorsmentTypeName()) + ","
					+ JdbcUtil.getStringValue(edorsmentType.getEndorsmentNote()) + ")";
		} else {
			sql = "Update tblEndorsmentType set endorsmentTypeName="
				+ JdbcUtil.getStringValue(edorsmentType.getEndorsmentTypeName())
				+ ",endorsmentNote="
				+ JdbcUtil.getStringValue(edorsmentType.getEndorsmentNote())
				+ " Where endorsmentTypeID = " + edorsmentType.getEndorsmentTypeID();
		}
		this.execute(sql);		
	}
	
	public EndorsmentType getEndorsmentType(long endorsmentTypeID) {
		
		String sql = "SELECT * FROM tblEndorsmentType t WHERE t.endorsmentTypeID = " 
			+ endorsmentTypeID;
		
		List list=this.findObject(sql, EndorsmentType.class);
		EndorsmentType model = null;
		
		if(list != null && list.size() > 0)
		{
			model = (EndorsmentType)list.get(0); 
		}
		
		return model;	
	}
	
	public QueryResult getEndorsmentTypeList(int start,int count) {
		
		StringBuffer b=new StringBuffer();
		b.append("SELECT endorsmentTypeID, endorsmentTypeName, endorsmentNote FROM tblEndorsmentType ");
		
		return this.find(b.toString(), start, count, EndorsmentType.class);
	}

}

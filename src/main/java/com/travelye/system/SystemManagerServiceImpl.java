package com.travelye.system;

import com.travelye.base.QueryResult;
import com.travelye.vo.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SystemManagerServiceImpl implements ISystemManagerService {

    @Autowired
    private ISystemManagerDAO systemManagerDAO;

    /*****************************菜单管理*********************************/
    public void saveMenu(Menu menu, List<MenuAction> menuActionList) {

        systemManagerDAO.saveMenu(menu);

        if (menu.getMenuID() == 0) {
            menu.setMenuID(systemManagerDAO.getPrimaryKey());
        } else {
            systemManagerDAO.deleteMenuAction(menu.getMenuID());
        }

        for (int i = 0; i < menuActionList.size(); i++) {
            menuActionList.get(i).setMenuID(menu.getMenuID());
            systemManagerDAO.saveMenuAction(menuActionList.get(i));
        }
    }

    public Menu getMenu(long menuID) {

        return systemManagerDAO.getMenu(menuID);

    }

    public List<Menu> getLevelOneMenuList() {
        return systemManagerDAO.getLevelOneMenuList();
    }

    public List<Menu> getMenuList(Condition condition) {

        return systemManagerDAO.getMenuList(condition);

    }

    /**
     * 读取一个菜单所有的权限
     *
     * @param menuID
     * @return
     */
    public List<MenuAction> getMenuActionList(long menuID) {
        return systemManagerDAO.menuActionList(menuID);
    }

    public List<Menu> getUserMainMenuList(long userID) {
        return systemManagerDAO.userMainMenuList(userID);
    }

    public List<Menu> getUserSubMenuList(long userID) {
        return systemManagerDAO.userSubMenuList(userID);
    }

    /********************************权限管理******************************/

    public void saveAction(Action action) {

        systemManagerDAO.saveAction(action);
    }

    public Action getAction(long actionID) {

        return systemManagerDAO.getAction(actionID);
    }

    public List<Action> getActionList() {

        return systemManagerDAO.getActionList();
    }

    public List<Action> getUserActionList(long userID) {
        return systemManagerDAO.userActionList(userID);
    }

    /*******************************组管理**********************************/

    public void saveGroup(Group group, List<GroupAction> groupActionList) {

        if (group.getGroupID() == 0) {
            group.setCreateDate(LocalDateTime.now());
        }

        systemManagerDAO.saveGroup(group);

        if (group.getGroupID() == 0) {
            group.setGroupID(systemManagerDAO.getPrimaryKey());
        } else {
            systemManagerDAO.deleteGroupAction(group.getGroupID());
        }

        for (int i = 0; i < groupActionList.size(); i++) {
            groupActionList.get(i).setGroupID(group.getGroupID());
            systemManagerDAO.saveGroupAction(groupActionList.get(i));
        }

    }

    public Group getGroup(long groupID) {
        return systemManagerDAO.getGroup(groupID);
    }

    public List<Group> getGroupList() {
        return systemManagerDAO.getGroupList();
    }

    /*******************************组权限管理******************************/

    public void saveGroupAction(GroupAction groupAction) {

        groupAction.setCreateDate(LocalDateTime.now());
        systemManagerDAO.saveGroupAction(groupAction);
    }

    public void deleteGroupAction(long groupID) {
        systemManagerDAO.deleteGroupAction(groupID);
    }

    public List<GroupAction> getGroupActionList(long groupID) {
        return systemManagerDAO.getGroupActionList(groupID);
    }

    /*******************************用户管理****************************/

    public void saveUser(Users user, List<UserGroup> userGroupList) {

        if (user.getUid() == 0) {
            user.setCreateDate(LocalDateTime.now());
        }

        systemManagerDAO.saveUser(user);

        if (user.getUid() == 0) {
            user.setUid(systemManagerDAO.getPrimaryKey());
        } else {
            systemManagerDAO.deleteUserGroup(user.getUid());
        }

        for (int i = 0; i < userGroupList.size(); i++) {
            userGroupList.get(i).setUserID(user.getUid());
            systemManagerDAO.saveUserGroup(userGroupList.get(i));
        }
    }

    public Users getUser(String loginName) {
        return systemManagerDAO.getUser(loginName);
    }

    /*******************************用户分组管理****************************/

    public void saveUserGroup(UserGroup userGroup) {

        userGroup.setCreateDate(LocalDateTime.now());
        systemManagerDAO.saveUserGroup(userGroup);
    }

    public List<UserGroup> getUserGroupList(long userID) {
        return systemManagerDAO.getUserGroupList(userID);
    }

    /********************************批单类型管理***********************************/
    public EndorsmentType getEndorsmentType(long endorsmentTypeID) {

        return systemManagerDAO.getEndorsmentType(endorsmentTypeID);
    }

    public QueryResult getEndorsmentTypeList(int start, int count) {
        return systemManagerDAO.getEndorsmentTypeList(start, count);
    }

    public void saveEndorsmentType(EndorsmentType endorsmentType) {

        systemManagerDAO.saveEndorsmentType(endorsmentType);
    }

    public ISystemManagerDAO getSystemManagerDAO() {
        return systemManagerDAO;
    }

    public void setSystemManagerDAO(ISystemManagerDAO systemManagerDAO) {
        this.systemManagerDAO = systemManagerDAO;
    }

}

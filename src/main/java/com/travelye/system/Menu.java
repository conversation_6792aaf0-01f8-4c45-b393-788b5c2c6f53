package com.travelye.system;
/**
 * 动作的分栏，系统运行时，左侧菜单栏提供了几块不同的功能，每一块就是一个分栏，
 * 每添加一个分栏，该表中的记录就会增加一条,相对应的，
 * 左侧菜单栏中也会新增机一个栏。
 * 
 * <AUTHOR> 2011-07-23
 *
 */
public class Menu {
	private long menuID;		// 菜单ID
	private String menuName;	// 菜单名称
	private String fatherName;	// 父菜单名称
	private long fatherID;		// 父菜单ID
	private String href;		// 页面Url
	private int level;			// 菜单级别
	private int sequence;		// 菜单顺序
	
	public long getMenuID() {
		return menuID;
	}
	
	public void setMenuID(long menuID) {
		this.menuID = menuID;
	}
	
	public String getMenuName() {
		return menuName;
	}
	
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
	
	public String getFatherName() {
		return fatherName;
	}
	
	public void setFatherName(String fatherName) {
		this.fatherName = fatherName;
	}
	
	public long getFatherID() {
		return fatherID;
	}
	
	public void setFatherID(long fatherID) {
		this.fatherID = fatherID;
	}
	
	public String getHref() {
		return href;
	}

	public void setHref(String href) {
		this.href = href;
	}
	
	public int getLevel() {
		return level;
	}

	public void setLevel(int level) {
		this.level = level;
	}
	
	public int getSequence() {
		return sequence;
	}
	
	public void setSequence(int sequence) {
		this.sequence = sequence;
	}
}


package com.travelye.system;

import com.travelye.base.BaseDao;
import com.travelye.base.QueryResult;
import com.travelye.vo.Users;

import java.util.List;


public interface ISystemManagerDAO extends BaseDao {
	
	/*************************菜单管理***********************************/
	/**
	 * 保存菜单
	 * @param actionColumn
	 */
	public void saveMenu(Menu menu);
	/**
	 * 读取一个菜单
	 * @param menuID
	 * @return
	 */
	public Menu getMenu(long menuID);
	/**
	 * 读取一级菜单列表
	 * @return
	 */
	public List<Menu> getLevelOneMenuList();
	/**
	 * 读取所有菜单列表
	 * @param condition
	 * @return
	 */
	public List<Menu> getMenuList(Condition condition);
	/**
	 * 读取一个用户所有主菜单列表
	 * @param condition
	 * @return
	 */
	public List<Menu> userMainMenuList(long userID);
	/**
	 * 读取一个用户所有子菜单列表
	 * @param condition
	 * @return
	 */
	public List<Menu> userSubMenuList(long userID);
	/*******************************菜单权限管理******************************/
	/**
	 * 保存菜单权限
	 */
	public void saveMenuAction(MenuAction menuAction);
	/**
	 * 删除菜单权限
	 * @param groupID
	 */
	public void deleteMenuAction(long menuID);
	/**
	 * 读取一个菜单所有的权限
	 * @param menuID
	 * @return
	 */
	public List<MenuAction> menuActionList(long menuID);
	/*****************************权限Action操作**********************************/
	/**
	 * 保存增加、修改的权限
	 * @param action
	 */
	public void saveAction(Action action);
	/**
	 * 根据权限ID读取权限信息
	 * @param actionID
	 * @return
	 */
	public Action getAction(long actionID);
	/**
	 * 读取权限列表
	 * @param condition
	 * @return
	 */
	public List<Action> getActionList();
	/*******************************组管理**********************************/
	public void saveGroup(Group group);
	public Group getGroup(long groupID);
	public List<Group> getGroupList();
	/*******************************组权限管理******************************/
	public void saveGroupAction(GroupAction groupAction);
	public void deleteGroupAction(long groupID);
	public List<GroupAction> getGroupActionList(long groupID);
	
	/*******************************用户管理****************************/
	
	public void saveUser(Users user);
	
	/**
	 * 根据登录名读取用户信息
	 * @param loginName
	 * @return
	 */
	public Users getUser(String loginName);
	
	/**
	 * 得到一个用户的所有权限对象
	 * @param userID
	 * @return
	 */
	public List<Action> userActionList(long userID); 
	/*******************************用户分组管理****************************/
	public void saveUserGroup(UserGroup userGroup);
	public void deleteUserGroup(long userID);
	public List<UserGroup> getUserGroupList(long userID);
	/*******************************批单分类管理****************************/
	/**
	 * 保存新增、修改批单分类
	 * @param endorsmentType
	 */
	public void saveEndorsmentType(EndorsmentType endorsmentType);
	/**
	 * 读取一个批单分类
	 * @param endorsmentTypeID
	 * @return
	 */
	public EndorsmentType getEndorsmentType(long endorsmentTypeID);
	/**
	 * 读取批单分类列表
	 * @param start
	 * @param count
	 * @return
	 */
	public QueryResult getEndorsmentTypeList(int start,int count);
}

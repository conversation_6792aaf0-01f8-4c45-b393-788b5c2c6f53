/**
 * 包名：com.wenhq.user<br>
 * 文件：UserDaoImpl.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-1 下午08:04:57<br>
 * <p>
 * Current revision $Revision: 1.6 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: UserDaoImpl.java,v $
 * Revision 1.6  2010/08/24 11:26:07  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.5  2010/08/24 07:20:37  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.4  2010/08/22 11:03:15  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.3  2010/08/21 09:25:58  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.2  2010/08/14 02:46:44  wenc
 * *** empty log message ***
 * <p>
 * Revision 1.1  2010/08/10 06:35:36  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.user;

import com.travelye.base.BaseJdbcDaoImpl;
import com.travelye.base.QueryResult;
import com.travelye.base.tools.JdbcUtil;
import com.travelye.vo.ClientInfo;
import com.travelye.vo.UserLoginHistory;
import com.travelye.vo.Users;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class UserDaoImpl extends BaseJdbcDaoImpl implements UserDao {

    /* (non-Javadoc)
     * @see com.wenhq.user.UserDao#save(com.wenhq.vo.Users)
     * <AUTHOR>
     */
    @Override
    public void save(Users user) {
        String sql = "";
        if (user.getUid() == 0) {
            sql = "Insert into tblUser(loginname,username,password,status,agencyID,createDate,"
                    + "bindMac, macaddr, admin"
                    + ",realName, contact, employeeID,salesLocation,department,qualificationNO"
                    + ",ispayment,subCode,departmentid,userType" +
                    ")values("
                    + JdbcUtil.getStringValue(user.getLoginname()) + ","
                    + JdbcUtil.getStringValue(user.getUsername()) + ","
                    + JdbcUtil.getStringValue(user.getPassword()) + ","
                    + JdbcUtil.getNumberValue(user.getStatus()) + ","
                    + JdbcUtil.getNumberValue(user.getAgencyID()) + ","
                    + JdbcUtil.getDateTime(user.getCreateDate()) + ","
                    + JdbcUtil.getNumberValue(user.getBindMac()) + ","
                    + JdbcUtil.getStringValue(user.getMacAddr()) + ","
                    + JdbcUtil.getNumberValue(user.getAdmin()) + ","
                    + JdbcUtil.getStringValue(user.getRealName()) + ","
                    + JdbcUtil.getStringValue(user.getContact()) + ","
                    + JdbcUtil.getStringValue(user.getEmployeeID()) + ","
                    + JdbcUtil.getStringValue(user.getSalesLocation()) + ","
                    + JdbcUtil.getStringValue(user.getDepartment()) + ","
                    + JdbcUtil.getStringValue(user.getQualificationNO()) + ","
                    + JdbcUtil.getStringValue(user.getIsPayment()) + ","
                    + JdbcUtil.getStringValue(user.getSubCode()) + ","
                    + JdbcUtil.getNumberValue(user.getDepartmentID()) + ","
                    + JdbcUtil.getNumberValue(user.getUserType())
                    + ")";
        } else {
            sql = "update tblUser set loginname = " + JdbcUtil.getStringValue(user.getLoginname())
                    + ",username = " + JdbcUtil.getStringValue(user.getUsername())
                    + ",password = " + JdbcUtil.getStringValue(user.getPassword())
                    + ",status = " + JdbcUtil.getNumberValue(user.getStatus())
                    + ",agencyID =" + JdbcUtil.getNumberValue(user.getAgencyID())
                    + ",bindmac = " + JdbcUtil.getNumberValue(user.getBindMac())
                    + ",macAddr = " + JdbcUtil.getStringValue(user.getMacAddr())
                    + ",admin = " + JdbcUtil.getNumberValue(user.getAdmin())
                    + ",realname=" + JdbcUtil.getStringValue(user.getRealName())
                    + ",contact=" + JdbcUtil.getStringValue(user.getContact())
                    + ",employeeid=" + JdbcUtil.getStringValue(user.getEmployeeID())
                    + ",saleslocation=" + JdbcUtil.getStringValue(user.getSalesLocation())
                    + ",department=" + JdbcUtil.getStringValue(user.getDepartment())
                    + ",qualificationno=" + JdbcUtil.getStringValue(user.getQualificationNO())
                    + ",ispayment=" + JdbcUtil.getStringValue(user.getIsPayment())
                    + ",subCode=" + JdbcUtil.getStringValue(user.getSubCode())
                    + ",departmentid = " + JdbcUtil.getNumberValue(user.getDepartmentID())
                    + ",userType = " + JdbcUtil.getNumberValue(user.getUserType())
                    + " where uid=" + user.getUid();
        }
        this.execute(sql);
    }

    /* (non-Javadoc)
     * @see com.wenhq.user.UserDao#updateStatus(com.wenhq.vo.Users)
     * <AUTHOR>
     */
    public void updateStatus(Users user) {
        String sql = "update tblUser set status=" +
                user.getStatus() +
                " where uid=" + user.getUid();
        this.execute(sql);
    }

    // 初始化用户密码为:password
    public void resetPassword(long userID) {
        String sql = "update tblUser set PASSWORD='password' where uid=" + userID;
        this.execute(sql);
    }

    // 设置是否要求绑定网卡物理IP
    public void updateBindMac(long userID, int bindMacFlag) {

        String sql = "update tblUser set bindMac = " + bindMacFlag
                + " where uid=" + userID;
        this.execute(sql);
    }

    // 设置网卡物理IP
    public void updateMacAddr(Users user) {

        String sql = "update tblUser set macAddr = " + JdbcUtil.getStringValue(user.getMacAddr())
                + " where uid=" + user.getUid();
        this.execute(sql);
    }

    // 设置是否是管理员
    public void updateAdmin(Long userID, int admin) {

        String sql = "update tblUser set admin = " + admin
                + " where uid=" + userID;
        this.execute(sql);
    }

    /* (non-Javadoc)
     * @see com.wenhq.user.UserDao#getUser(long)
     * <AUTHOR>
     */
    public Users getUser(long uid) {
        String sql = "select * from tblUser t where t.uid=" + uid;
        List list = this.findObject(sql, Users.class);
        Users model = null;
        if (list != null) {
            if (list.size() > 0) {
                model = (Users) list.get(0);
            }
        }
        return model;
    }

    /* (non-Javadoc)
     * @see com.wenhq.user.UserDao#getUser(String)
     * <AUTHOR>
     */
    public Users getUser(String loginname) {
        String sql = "select * from tblUser t where t.loginname=" + JdbcUtil.getStringValue(loginname);
        List list = this.findObject(sql, Users.class);
        Users model = null;
        if (list != null) {
            if (list.size() > 0) {
                model = (Users) list.get(0);
            }
        }
        return model;
    }

    public Users getUserByEmail(String email) {
        String sql = "select * from tbluser u where u.email=" + JdbcUtil.getStringValue(email);
        List<Users> list = this.findObject(sql, Users.class);
        Users model = null;
        if (list != null && list.size() > 0) {
            model = list.get(0);
        }
        return model;
    }

    public List<Users> getUserListByEmail(String email) {
        String sql = "select * from tbluser u where u.email=" + JdbcUtil.getStringValue(email);
        return this.findObject(sql, Users.class);
    }


    public List<Users> getAgencyUserList(long agencyID) {
        StringBuffer b = new StringBuffer();
        b.append("SELECT a.UID, a.agencyID, a.username, a.loginName, a.password, a.status,");
        b.append(" a.createDate, c.agencyName, a.macaddr, a.admin, a.bindMac,a.realname");
        b.append(" ,a.contact,a.employeeid,a.saleslocation,a.department,a.qualificationno  ");
        b.append("FROM tblUser a, tblAgency c WHERE a.agencyID = c.agencyID and a.status=1 ");
        b.append(" and a.agencyID=").append(agencyID);
        b.append(" order by a.uID");
        return this.findObject(b.toString(), Users.class);
    }

    public QueryResult getUserList(Condition condition, int start, int count) {
        StringBuffer b = new StringBuffer();
        b.append("SELECT a.UID, a.agencyID, a.username, a.loginName, a.password, a.status,");
        b.append(" a.createDate, c.agencyName, a.macaddr, a.admin, a.bindMac  ");
        b.append("FROM tblUser a, tblAgency c WHERE a.agencyID = c.agencyID");
        b.append(" AND (a.agencyID IN (SELECT agencyID FROM tblAgency WHERE branch=");
        b.append(condition.getBranchID()).append(")");
        b.append(" OR a.agencyID = ").append(condition.getBranchID()).append(")");
        if (condition.getAgencyID() != 0) {
            b.append(" and a.agencyID=").append(condition.getAgencyID());
        }
        if (condition.has(condition.getStartDate())) {
            b.append(" and a.createDate>=").append(JdbcUtil.getStringValue(condition.getStartDate()));
        }
        if (condition.has(condition.getEndDate())) {
            b.append(" and a.createDate<'").append(condition.getEndDate()).append(" 23:59:59'");
        }
        b.append(" order by a.agencyID");
        return this.find(b.toString(), start, count, Users.class);
    }

    /**
     * 删除用户
     *
     * @param uid
     */
    public void delete(long uid) {
        String sql = "delete from tbluser where uid=" + uid;
        this.execute(sql);
    }


    /**
     * 获取渠道第一个有效用户（管理员优先），cps投保挂保单用
     *
     * @param agencyId
     * @return
     */
    public Users getFirstValidUserByAagencyId(long agencyId) {
        String sql = "select * from tblUser t where t.agencyId=" + JdbcUtil.getNumberValue(agencyId) + " and t.STATUS =1 order by admin desc,uid";
        List list = this.findObject(sql, Users.class);
        Users model = null;
        if (list != null) {
            if (list.size() > 0) {
                model = (Users) list.get(0);
            }
        }
        return model;
    }

    @Override
    public void insertClientInfo(ClientInfo clientInfo) {
        String sql = "INSERT INTO tblclientinfo(source, clientIP, screenInfo, useragent)"
                + " VALUES(" + JdbcUtil.getStringValue(clientInfo.getSource())
                + ", " + JdbcUtil.getStringValue(clientInfo.getClientIp())
                + ", " + JdbcUtil.getStringValue(clientInfo.getScreenInfo())
                + ", " + JdbcUtil.getStringValue(clientInfo.getUserAgent())
                + ")";

        this.execute(sql);
    }

    @Override
    public void loginSuccess(long uid) {
        String sql = "update user_login_history set lastLoginTime=" + JdbcUtil.getDateTime(LocalDateTime.now())
                + " where uid=" + JdbcUtil.getNumberValue(uid);
        this.execute(sql);
    }

    @Override
    public void addLoginFailureCount(Users user) {
        String sql = "update user_login_history set failureCount=failureCount+1"
                + " where uid=" + JdbcUtil.getNumberValue(user.getUid());
        this.execute(sql);
    }

    @Override
    public void resetLoginFailureCount(long uid) {
        String sql = "update user_login_history set failureCount=0"
                + " where uid=" + JdbcUtil.getNumberValue(uid);
        this.execute(sql);
    }

    @Override
    public void updatePassword(Users user) {
        // 更新密码
        String sql = "update tblUser set password=" + JdbcUtil.getStringValue(user.getPassword())
                + " where uid=" + JdbcUtil.getNumberValue(user.getUid());
        this.execute(sql);

        // 更新密码更新时间
        String sql1 = "update user_login_history set passwordUpdateTime = " + JdbcUtil.getDateTime(LocalDateTime.now())
                + " where uid=" + JdbcUtil.getNumberValue(user.getUid());
        this.execute(sql1);
    }

    @Override
    public UserLoginHistory getUserLoginHistory(long uid) {
        String sql = "select * from user_login_history where uid=" + JdbcUtil.getNumberValue(uid);
        List<UserLoginHistory> list = this.findObject(sql, UserLoginHistory.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public void insertUserLoginHistory(long uid) {
        String sql = "insert into user_login_history(uid)"
                + " values(" + JdbcUtil.getNumberValue(uid) + ")";
        this.execute(sql);
    }

}

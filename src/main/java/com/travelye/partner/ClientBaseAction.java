package com.travelye.partner;

import com.travelye.security.jwt.utils.JwtUtils;
import com.travelye.security.jwt.utils.R;
import com.travelye.base.ActionSupport;
import com.travelye.base.SjisConstant;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.system.Action;
import com.travelye.vo.*;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 客户关系管理 基础类
 * 子类有admin.ClientAction
 * sale.ClientAction
 *
 * <AUTHOR>
 */
@Component
abstract public class ClientBaseAction extends ActionSupport {
    @Autowired
    protected PartnerService partnerService;
    @Autowired
    protected ClientService clientService;
    @Autowired
    protected DepartmentService departmentService;

    public static ClientBaseAction clientBaseAction;

    @PostConstruct
    public void init() {
        clientBaseAction = this;
    }

    /**
     * 保存客户信息
     *
     * @return
     */
    abstract public Map<String, Object> save();

    /**
     * 修改客户
     *
     * @return
     */
    abstract public Map<String, Object> edit();

    /**
     * 客户列表
     *
     * @return
     */
    @RequestMapping("/{module}/client/list")
    @ResponseBody
    public List<Map<String, Object>> list(@PathVariable String module) {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        List<Partner> list = clientService.list(userSession.getAgencyId(), userSession.getAgencyType());
        List<Map<String, Object>> data = new ArrayList<>();
        for (Partner partner : list) {
            data.add(partner.toTreeMenu());
        }
        return data;
    }

    /**
     * agency 列表
     */
    @RequestMapping("/user/agency/list")
    @ResponseBody
    public R listAgency() {
        R r = new R();
        SecurityUser currentUser = JwtUtils.getCurrentUser();

        List<Partner> agencyList = clientService.list(currentUser.getAgencyId(), currentUser.getAgencyType());
        List<Map<String, Object>> agencyMapList = new ArrayList<>();
        agencyList.forEach(agency -> {
            agencyMapList.add(Map.of(
                    "id", agency.getAgencyID(),
                    "name", agency.getAgencyName(),
                    "abbrName", agency.getAbbrName(),
                    "branchName", agency.getBranchName(),
                    "status", agency.getStatus(),
                    "agencyType", agency.getSalesChannel()
            ));
        });
        return r.put("agencyList", agencyMapList);
    }

    /**
     * 获取渠道的用户、产品等信息
     */
    @RequestMapping("/user/agency/edit")
    @ResponseBody
    public R editAgency(Long agencyId) {
        R r = new R();
        SecurityUser currentUser = JwtUtils.getCurrentUser();
        if (agencyId == null || agencyId == 0) {
            return R.error("参数错误，请联系管理员");
        }
        try {
            Partner agency = clientService.getPartnerAndData(agencyId);
            if (agency == null || agency.getBranch() != currentUser.getAgencyId()) {
                return R.error("找不到对应的代理商");
            }

            // 用户列表
            List<Users> agencyUserList = clientService.listUsers(agencyId);
            agencyUserList.forEach(u -> u.setActionIDList(departmentService.listUserAction(u.getUid())));

            // 渠道的功能权限
            Department department = departmentService.getDepartmentByAgency(agencyId);
            List<Action> actionList = departmentService.listDepartmentAction(department.getDepartmentID());

            // 授权的产品列表
            List<Partnerproduct> agencyProductList = partnerService.listProduct(agencyId);

            // producer code 配置
            List<AgencyPCC> agencyPccList = partnerService.listAgencyPCC(agencyId);

            r.put("agency", agency);
            r.put("agencyUserList", agencyUserList);
            r.put("actionList", actionList);
            r.put("agencyProductList", agencyProductList);
            r.put("agencyPccList", agencyPccList);
            return r;
        } catch (Exception e) {
            return R.error("获取数据失败，请联系管理员");
        }
    }

    /**
     * 产品列表
     *
     * @return
     */
    @RequestMapping("/{module}/client/listProduct")
    @ResponseBody
    public List<Map<String, Object>> listProduct(@PathVariable String module) {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        // 读取客户可销售的产品
        List<Partnerproduct> productList = null;
        if (userSession.getAgencyType().equals(SjisConstant.module_admin)) {
            productList = clientService.getAllProduct(userSession.getAgencyId(), userSession.getBranch());
        } else {
            productList = partnerService.listProduct(userSession.getAgencyId());
        }
        List<Map<String, Object>> jsonProductList = new ArrayList<>();
        for (Partnerproduct partnerproduct : productList) {
            jsonProductList.add(partnerproduct.toEdit());
        }

        request.setAttribute("message", jsonProductList.toString());
        return jsonProductList;
    }


}

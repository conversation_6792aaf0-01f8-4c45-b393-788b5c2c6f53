package com.travelye.partner;

import com.travelye.base.QueryResult;
import com.travelye.security.session.SecurityUser;
import com.travelye.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PartnerServiceImpl implements PartnerService {
    @Autowired
    private PartnerDao partnerDao;
    @Autowired
    private DepartmentDao departmentDao;

    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.admin.partner.PartnerService#save(com.wenhq.vo.Partner)
     *      <AUTHOR>
     */
    public void save(Partner model) {
        partnerDao.save(model);
        if (model.getAgencyID() == 0) {
            model.setAgencyID(partnerDao.getPrimaryKey());

            Department depart = new Department();
            depart.setAgencyID(model.getAgencyID());
            depart.setDepartmentName(model.getAgencyName());
            depart.setStatus(model.getStatus());
            depart.setDepartType("sale");
            depart.setParent(model.getParentDepartment());
            departmentDao.saveDepartment(depart);
        } else {
            Department depart = departmentDao.getDepartmentByAgency(model.getAgencyID());
            depart.setDepartmentName(model.getAgencyName());
            depart.setStatus(model.getStatus());
            departmentDao.saveDepartment(depart);
        }
    }

    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.admin.partner.PartnerService#searchSport(java.lang.String,
     *      int, int) <AUTHOR>
     */
    public QueryResult list(int start, int count, long branch) {
        return partnerDao.list(start, count, branch);
    }

    public QueryResult getAgencyPCCList(int start, int count, long branchID, String agencyName) {
        return partnerDao.agencyPCCList(start, count, branchID, agencyName);
    }

    /**
     * @param dao the partnerDao to set
     */
    public void setPartnerDao(PartnerDao dao) {
        this.partnerDao = dao;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.partner.PartnerService#listProduct(long) <AUTHOR>
     */
    public List<Partnerproduct> listProduct(long partnerid) {
        return partnerDao.listProduct(partnerid);
    }



    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.partner.PartnerService#getPartner(long)
     * <AUTHOR>
     */
    public Partner getPartner(long partnerid) {
        if (partnerid == 0) return null;
        return partnerDao.getPartner(partnerid);
    }

    /**
     * 得到所有分公司
     */
    public List<Partner> getBranchList(long branchID) {
        return partnerDao.branchList(branchID);
    }


    /**
     * 得到公司所有partner
     */
    public List<Partner> getPartnerList(long branchID) {
        return partnerDao.partnerList(branchID);
    }

    public List<Partner> getAgencyListForUserManager(long branchID) {
        return partnerDao.agencyListForUserManager(branchID);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.partner.PartnerService#getUser(long) <AUTHOR>
     */
    public Users getUser(long partnerid) {
        return partnerDao.getUser(partnerid);
    }

    public Users getUserByAgencyCode(String agencyCode, long agencyId) {
        return partnerDao.getUserByAgencyCode(agencyCode, agencyId);
    }


    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.partner.PartnerService#getPartnerProduct(long,long)
     * <AUTHOR>
     */
    public Partnerproduct getPartnerProduct(long partnerID, long productID) {
        return partnerDao.getPartnerProduct(partnerID, productID);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.wenhq.partner.PartnerService#getAllProduct(long) <AUTHOR>
     */
    public List<Partnerproduct> getAllProduct(long branch, long agencyID, long branchID) {
        return partnerDao.getAllProduct(branch, agencyID, branchID);
    }

    /*
     * 该函数不合理，不能保留过去的授权产品记录和日期，以后完善
     * 思路是：保存一个客户的授权产品时，把该客户数据库中的授权
     * 产品记录全部删除，重新添加一边。
     * @date 2011-05-20
     * <AUTHOR>
     * @see com.wenhq.partner.PartnerService#savePartnerProduct(java.lang.String,
     *      java.lang.String[]) <AUTHOR>
     */
    public void savePartnerProduct(long partnerID, List<Partnerproduct> productInfo) {
        // 删除客户的所有授权产品
        partnerDao.deleteAllProduct(partnerID);
        // 重新添加客户的授权产品
        for (int i = 0; i < productInfo.size(); i++) {
            Partnerproduct model = productInfo.get(i);
            model.setAgencyID(partnerID);
            model.setMasterPolicyID(model.getMasterPolicyID().trim());
            partnerDao.addProduct(model);
        }

		/* 源代码
		List<Partnerproduct> oldList = this.listProduct(partnerid);
		if (oldList != null) {
			for (int i = 0; i < oldList.size(); i++) {
				boolean find = false;
				if (productInfo != null) {
					for (int j = 0; j < productInfo.size(); j++) {
						if (Long.valueOf(productInfo.get(j).getProductid()).equals(
								oldList.get(i).getProductid())) {
							find = true;
							break;
						}
					}
				}
				if (!find) {
					partnerDao.deleteProduct(oldList.get(i)
							.getPartnerproductid());
				}
			}
		}
		if (productInfo != null) {
			for (int i = 0; i < productInfo.size(); i++) {
				boolean find = false;
				for (int j = 0; j < oldList.size(); j++) {
					if (Long.valueOf(productInfo.get(j).getProductid()).equals(
							oldList.get(j).getProductid())) {
						find = true;
						break;
					}
				}
				if (!find) {

				}
			}
		}*/
    }

    public AgencyPCC getAgencyPcc(long agencyPccID) {
        return partnerDao.getAgencyPcc(agencyPccID);
    }

    public List<AgencyPCC> listAgencyPCC(long agencyID) {
        return partnerDao.listAgencyPCC(agencyID);
    }

    public void saveAgencyPcc(AgencyPCC model) {
        partnerDao.saveAgencyPcc(model);
    }

    public AgencyPCC getEffectiveAgencyPcc(long agencyID) {
        return partnerDao.getEffectiveAgencyPcc(agencyID);
    }

    public List<AgencyPCC> listEffectiveAgencyPcc(long agencyID) {
        return partnerDao.listEffectiveAgencyPcc(agencyID);
    }

    public void saveAgencyPcc(Partner model, List<AgencyPCC> list) {
        partnerDao.save(model);
        for (int i = 0; i < list.size(); i++) {
            partnerDao.saveAgencyPcc(list.get(i));
        }
    }

    /**
     * 公司的人员列表
     *
     * @param partnerid
     * @return
     */
    public List<Users> listUser(long partnerid) {
        return partnerDao.listUser(partnerid);
    }

    /**
     * 返回有效的代理商
     * 状态和合同期都有效
     *
     * @param start
     * @param count
     * @param branch
     * @return
     */
    public QueryResult listEffectiveAgency(int start, int count, long branch) {
        return partnerDao.listEffectiveAgency(start, count, branch);
    }

    public void setDepartmentDao(DepartmentDao departmentDao) {
        this.departmentDao = departmentDao;
    }

    /**
     * 当前用户授权访问的代理商
     *
     * @param userSession
     * @return
     */
    public List<Partner> listAuthAgency(SecurityUser userSession) {
        return partnerDao.listAuthAgency(userSession);
    }

    /**
     * 分公司销售人员列表
     *
     * @param userSession
     * @return
     */
    public List<Users> salesMan(SecurityUser userSession) {
        return partnerDao.salesMan(userSession);
    }

    /**
     * 代理商销售人员列表
     *
     * @param userSession
     * @return
     */
    public List<Users> agencySalesMan(SecurityUser userSession) {
        return partnerDao.agencySalesMan(userSession);
    }


    /*
    根据ＩＤ和类型，获取所有S3BCode
     */
    @Override
    public List<S3bCode> listS3bCodeById(long clientID, int type) {
        return partnerDao.listS3bCodeById(clientID, type);
    }

    /*
        新增保存或修改保存S3BCode
        */
    public void saveS3bCode(S3bCode s3b) {
        partnerDao.saveS3bCode(s3b);
    }

    /*获得有效的S3BCode,取生效期最近之s3bcode*/

    public S3bCode getEffectiveS3bCode(long associateID, int type) {
        return partnerDao.getEffectiveS3bCode(associateID, type);
    }


    /**
     * 得到公司所有渠道
     */
    public List<Partner> getAgencyList(long branchID) {
        return partnerDao.getAgencyList(branchID);
    }

    /**
     * Created by linchengdong on 2017-12-28 取AgencyPCC时将未生效的与失效的分开取，此处取失效的。
     */
    @Override
    public List<AgencyPCC> listExpiryAgencyPcc(long agencyID) {
        return partnerDao.listExpiryAgencyPcc(agencyID);
    }

    @Override
    public Partner getAgencyByAgencyPCC(String agencyPCC, String GDSCode) {
        return partnerDao.getAgencyByAgencyPCC(agencyPCC, GDSCode);
    }


}

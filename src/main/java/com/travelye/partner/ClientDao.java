package com.travelye.partner;

import com.travelye.base.BaseDao;
import com.travelye.system.Action;
import com.travelye.vo.Partner;
import com.travelye.vo.Partnerproduct;
import com.travelye.vo.SendUrl;
import com.travelye.vo.Users;

import java.util.List;

public interface ClientDao  extends BaseDao{
	/** 显示分公司列表
	 * @param branch
	 * @return
	 */
	public List<Partner> listBranch();
	/** 显示分公司的客户
	 * @param branch
	 * @return
	 */
	public List<Partner> listAgency(long branch) ;
	/** 显示代理商的客户
	 * @param branch
	 * @return
	 */
	public List<Partner> listClient(long agencyID);
	/** 显示产品授权之客户
	 * @param branch
	 * @return
	 */
	public List<Partner> proPartnerlist(long productid,long partnerID,String partnerType);
	/** 显示客户的所有用户
	 * @param clientID
	 * @return
	 */
	public List<Users> listUsers(long clientID);
	/** 显示代理商以及代理商客户 的所有用户
	 * @param clientID
	 * @return
	 */
	public List<Users> listAgencyAndClientUsers(long agencyID);
	/** 返回代理商下的所有 子代理商
	 * @param partnerid
	 * @return
	 */
	public List<Partner> listChildPartner(long partnerid);
	/**是否有保单
	 * @param partnerid
	 * @return
	 */
	public boolean clientExistsPolicy(long partnerid);
	
	/**删除客户
	 * @param clientID
	 */
	public void delete(long clientID);
	/** 删除客户的功能权限时，同时需要删除客户下所有用户的权限
	 */
	public void deleteClientUserAction(long clientID,long actionID);
	/** 返回分公司的所有产品
	 * @param branchID
	 * @param chinaID
	 * @return
	 */
	public List<Partnerproduct> getAllProduct(long branchID , long chinaID);
	/**分公司是否有保单
	 * @param branchID
	 * @return
	 */
	public boolean branchExistsPolicy(long branchID);
	/**
	 * 读取权限列表
	 * @return
	 */
	public List<Action> listAction() ;
	
	/**保存发送地址
	 * @param model
	 */
	public void saveSendUrl(SendUrl model);
	public SendUrl getSendUrl(long sendUrlID);
	public List<SendUrl> listSendUrl();
	public SendUrl getDefaulSendUrl();


	}

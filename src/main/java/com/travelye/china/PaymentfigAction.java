package com.travelye.china;


import com.travelye.sjis.ajax.json.JSONObject;
import com.travelye.base.ActionSupport;
import com.travelye.payCenter.services.PaymentService;
import com.travelye.security.session.UserSessionManager;
import com.travelye.vo.PyPaymentType;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by HS on 2017/12/20.
 */
@Controller
@Slf4j
public class PaymentfigAction extends ActionSupport {

    private static final long serialVersionUID = -3138854873829943269L;
    @Autowired
    private PaymentService paymentService;

    @RequestMapping("/china/paymentConfig/paymentQuery")
    public String paymentQuery() {
        List<PyPaymentType> paymentTypes = paymentService.getPaymentTypes();
        request.setAttribute("paymentTypes", paymentTypes);
        return "/china/payment/paymentfigList";
    }


    @RequestMapping("/china/paymentConfig/savePaymentType")
    @ResponseBody
    public Map<String, Object> savePaymentType(@ModelAttribute PyPaymentType paymentType) {
        Map<String, Object> map = new HashMap<>();
        try {
            long uid = UserSessionManager.getSecurityLoginUser(request).getUid();
            paymentType.setUid((int) uid);
            paymentService.savePaymentType(paymentType);
            map.put("result", 1);
        } catch (Exception e) {
            map.put("result", 0);
            map.put("message", "保存失败：" + e.getMessage());
        }
        request.setAttribute("message", map.toString());
        return map;
    }

    @RequestMapping("/china/paymentConfig/delPaymentType")
    @ResponseBody
    public Map<String, Object> delPaymentType() {
        Map<String, Object> map = new HashMap<>();
        try {
            int id = Integer.parseInt(request.getParameter("id"));
            paymentService.deletePaymentType(id);
            map.put("result", 1);
        } catch (Exception e) {
            map.put("result", 0);
            map.put("message", "保存失败：" + e.getMessage());
        }
        request.setAttribute("message", map.toString());
        return map;
    }


}

package com.travelye.china;

import com.travelye.sjis.ajax.json.JSONArray;
import com.travelye.base.MessageException;
import com.travelye.partner.ClientBaseAction;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.system.Action;
import com.travelye.vo.Department;
import com.travelye.vo.Partner;
import com.travelye.vo.S3bCode;
import com.travelye.vo.Users;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 总公司的 分公司管理
 *
 * <AUTHOR>
 */
@Controller
@Slf4j
public class ChinaClientAction extends ClientBaseAction {
    /**
     * 首页
     *
     * @return
     */
    @RequestMapping("/china/client/index")
    public String index() {
        List<Action> actionList = clientService.listAction();
        request.setAttribute("actionlist", actionList);
        return "/china/client/index";
    }

    /**
     * 修改
     *
     * @return
     */
    @RequestMapping("/china/client/edit")
    @ResponseBody
    public Map<String, Object> edit() {
        Map<String, Object> data = new HashMap<>();
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        String idstr = request.getParameter("clientid");
        if (idstr == null || idstr.trim().length() == 0) return null;
        try {
            long branchID = Long.parseLong(idstr);
            Partner agency = partnerService.getPartner(branchID);
            if (agency == null || agency.getBranch() != userSession.getAgencyId()) {
                throw new MessageException("无效的分公司");
            }
            //读取用户列表
            List<Users> userList = clientService.listUsers(branchID);
            if (userList.size() > 0) {
                agency.setExistsData("1");
            } else {
                agency.setExistsData("0");
            }
            data.put("agency", agency.toEdit());

            List<Map<String, Object>> jsonUserList = new ArrayList<>();
            for (Users user : userList) {
                user.setActionIDList(departmentService.listUserAction(user.getUid()));
                user.setExistsData("1");
                Map<String, Object> userMap = user.toEdit();

                //读取用戶S3bCode
                List<S3bCode> s3bList = partnerService.listS3bCodeById(user.getUid(), S3bCode.CODE_TYPE_USER);
                List<Map<String, Object>> jsonS3bList = new ArrayList<>();
                for (S3bCode s3bCode : s3bList) {
                    jsonS3bList.add(s3bCode.toEdit());
                }

                userMap.put("s3blist", jsonS3bList);
                jsonUserList.add(userMap);
            }
            //读取分公司的功能权限
            Department department = departmentService.getDepartmentByAgency(branchID);
            List<Action> actionList = departmentService.listDepartmentAction(department.getDepartmentID());
            List<Map<String, Object>> jsonActionList = new ArrayList<>();
            for (Action action : actionList) {
                jsonActionList.add(action.toEdit());
            }

            data.put("userlist", jsonUserList);
            data.put("actionlist", jsonActionList);
            request.setAttribute("message", data.toString());
            log.debug("修改分公司:" + data);
        } catch (MessageException e) {
            request.setAttribute("message", e.getMessage());
        } catch (Exception e) {
            log.error("修改分公司异常,分公司ID:" + idstr, e);
        }
        return data;
    }

    /**
     * 保存分公司信息
     *
     * @return
     */
    @RequestMapping("/china/client/save")
    @ResponseBody
    public Map<String, Object> save() {
        Map<String, Object> data = new HashMap<>();
        String branchJson = request.getParameter("client");
        log.info("保存分公司信息:" + branchJson);
        try {
            data = clientService.saveBranch(branchJson, UserSessionManager.getSecurityLoginUser(request).getAgencyId());
            request.setAttribute("message", data);
        } catch (Exception e) {
            log.error("保存分公司异常:", e);
            data.put("result", 0);
            data.put("message", e.getMessage());
            request.setAttribute("message", data.toString());
        }
        return data;
    }

    @RequestMapping("/china/client/getPwAjax")
    @ResponseBody
    public Map<String, Object> getPwAjax() {
        Map<String, Object> map = new HashMap<>();
        map.put("pw", "password");
        request.setAttribute("message", map.toString());
        return map;
    }

}

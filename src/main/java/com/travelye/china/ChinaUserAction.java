package com.travelye.china;

import com.travelye.base.ActionSupport;
import com.travelye.base.Pagination;
import com.travelye.base.QueryResult;
import com.travelye.partner.PartnerService;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.system.Group;
import com.travelye.system.ISystemManagerService;
import com.travelye.system.UserGroup;
import com.travelye.user.Condition;
import com.travelye.user.UserService;
import com.travelye.vo.Partner;
import com.travelye.vo.Users;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.time.LocalDateTime;
import java.util.List;

@Controller
public class ChinaUserAction extends ActionSupport {

    @Autowired
    private PartnerService partnerService;
    @Autowired
    private UserService userService;
    @Autowired
    private ISystemManagerService systemManagerService;

    @RequestMapping("/china/systemManager/listUser")
    public String listUser(Condition condition) {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        condition.setBranchID(userSession.getAgencyId());
        condition.setAgencyID(userSession.getAgencyId());
        int pageStart;
        Pagination page = new Pagination();
        int paginalcount = 20;
        String paginal = request.getParameter("paginalcount");
        if (checkIsEmpty(paginal)) {
            paginalcount = Integer.parseInt(paginal);
        }
        page.setPaginalCount(paginalcount);
        String start = request.getParameter("pagestart");
        if (checkIsEmpty(start)) {
            pageStart = Integer.parseInt(start);
        } else {
            pageStart = 1;
        }
        page.setPageStart(pageStart);
        QueryResult res = userService.getUserList(condition, pageStart, page
                .getPaginalCount());

        page.setRowCount(res.getCount());
        request.setAttribute("page", page);
        request.setAttribute("list", res.getList());
        request.setAttribute("condition", condition);
        request.setAttribute("partnerlist", partnerService.getAgencyListForUserManager(userSession.getAgencyId()));
        return "/china/systemManager/userManager";
    }

    @RequestMapping("/china/systemManager/user")
    public String user() {
        Users user = new Users();
        List<Group> grouplist = systemManagerService.getGroupList();
        List<UserGroup> userGroupList = null;
        String agencyID = request.getParameter("agencyID");
        String userID = request.getParameter("userID");

        user.setAgencyID(Long.parseLong(agencyID));

        if (userID != null && !userID.equals("0") && !userID.equals("")) {
            user = userService.getUser(Long.parseLong(userID));
            userGroupList = systemManagerService.getUserGroupList(Long.parseLong(userID));
            // 循环权限
            for (Group group : grouplist) {
                // 循环组权限，如果权限ID相当，则在权限表标注
                for (UserGroup userGroup : userGroupList) {

                    if (group.getGroupID() == userGroup.getGroupID()) {
                        group.setSelected(1);
                        break;
                    }
                }
            }
        }
        request.getSession().setAttribute("saveflag", "1");
        request.setAttribute("user", user);
        request.setAttribute("grouplist", grouplist);
        // 填写代理商列表
        List list = new ArrayList();
        Partner partner = partnerService.getPartner(1);
        list.add(0, partner);
        request.setAttribute("partnerlist", list);
        return "/china/systemManager/user";

    }


    @RequestMapping("/china/systemManager/saveUser")
    public String saveUser() {

        List<Group> grouplist = systemManagerService.getGroupList();
        List<UserGroup> userGroupList = new ArrayList<UserGroup>();
        String agencyID = request.getParameter("agencyID");
        String userID = request.getParameter("userid");
        String userName = request.getParameter("username");
        String loginName = request.getParameter("loginname");
        String status = request.getParameter("status");
        String macAddr = request.getParameter("macaddr");
        String bindMac = request.getParameter("bindmac");
        String admin = request.getParameter("adminflag");
        String password = request.getParameter("password");
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        long masterID = userSession.getUid();

        if ((userID != null || userID.length() != 0) && (password == null || password.length() == 0)) {
            Users olderUser = userService.getUser(Long.parseLong(userID));
            password = olderUser.getPassword();
        }

        Users user = new Users();
        user.setAgencyID(Long.parseLong(agencyID));
        user.setUid(Long.parseLong(userID));
        user.setUsername(userName);
        user.setLoginname(loginName);

        if (status != null && !"".equals(status)) {
            user.setStatus(Integer.parseInt(status));
        } else {
            user.setStatus(0);
        }


        if (bindMac != null && !"".equals(bindMac)) {
            user.setBindMac(Integer.parseInt(bindMac));
        } else {
            user.setBindMac(0);
        }


        user.setMacAddr(macAddr);

        if (admin != null && !"".equals(admin)) {
            user.setAdmin(Integer.parseInt(admin));
        } else {
            user.setAdmin(0);
        }

        user.setPassword(password);
        String[] groups = request.getParameterValues("groups");

        if (groups != null) {

            for (int i = 0; i < groups.length; i++) {
                UserGroup userGroup = new UserGroup();
                userGroup.setUserID(user.getUid());
                String groupID = groups[i];
                userGroup.setGroupID(Long.valueOf(groupID));
                userGroup.setMasterID(masterID);
                userGroup.setCreateDate(LocalDateTime.now());
                userGroupList.add(userGroup);

                for (Group group : grouplist) {

                    if (group.getGroupID() == userGroup.getGroupID()) {
                        group.setSelected(1);
                        break;
                    }
                }
            }
        }

        String saveflag = (String) request.getSession().getAttribute("saveflag");


        Users existUser = systemManagerService.getUser(loginName);

        if ("1".equals(saveflag)) {

            if (existUser == null || (existUser != null && existUser.getUid() == user.getUid())) {
                systemManagerService.saveUser(user, userGroupList);
                request.getSession().removeAttribute("saveflag");//避免重复提交
                request.setAttribute("message", "保存成功!");
            } else {
                request.setAttribute("message", "保存失败：" + user.getLoginname() + "登录名已存在，请更该用户名再保存！");
            }
        }
        // 填写代理商列表
        request.setAttribute("user", user);
        List list = new ArrayList();
        Partner partner = partnerService.getPartner(1);
        list.add(0, partner);
        request.setAttribute("partnerlist", list);
        request.setAttribute("grouplist", grouplist);

        return "/china/systemManager/user";
    }

    private boolean checkIsEmpty(String str) {
        if ("".equals(str) || str == null) {
            return false;
        } else {
            return true;
        }
    }


}

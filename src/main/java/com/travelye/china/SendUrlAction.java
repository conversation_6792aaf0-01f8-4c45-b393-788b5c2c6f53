package com.travelye.china;

import com.travelye.base.ActionSupport;
import com.travelye.partner.ClientService;
import com.travelye.vo.SendUrl;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;


import java.util.List;

@Controller
@Slf4j
public class SendUrlAction extends ActionSupport{
	private static final long serialVersionUID = 1L;

	@Autowired
	protected ClientService clientService;

	private SendUrl sendUrl =new SendUrl();
	
	@RequestMapping("/china/sendurl/list")
	public String list(){
		List<SendUrl> list =clientService.listSendUrl();
		request.setAttribute("list", list);
		return "/china/sendurl/list";
	}

	@RequestMapping("/china/sendurl/add")
	public String add(){
		return "/china/sendurl/edit";
	}

	@RequestMapping("/china/sendurl/edit")
	public String edit(){
		try{
			SendUrl send=clientService.getSendUrl(sendUrl.getSendUrlID());
			request.setAttribute("sendUrl", send);
			return "/china/sendurl/edit";
		}catch(Exception e){
			log.error("",e);
			request.setAttribute("message", e.getMessage());
			return "/china/message";
		}
	}

	@RequestMapping("/china/sendurl/save")
	public String save(){
		try{
			clientService.saveSendUrl(sendUrl);
			request.setAttribute("sendUrl", sendUrl);
			request.setAttribute("message", "保存成功.");
			return "/china/sendurl/edit";
		}catch(Exception e){
			log.error("",e);
			request.setAttribute("message", e.getMessage());
			return "/china/message";
		}
	}
}

/**
 * 包名：com.wenhq.china<br>
 * 文件：ChinaAction.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-10-30 上午08:56:42<br>
 *
 * <br>
 */

package com.travelye.china;

import com.travelye.base.ActionSupport;
import com.travelye.base.tools.DateUtil;
import com.travelye.external.ctrip.DailyJob;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Controller
@Slf4j
public class ChinaAction extends ActionSupport {

    private final DailyJob dailyJob;

    public ChinaAction(DailyJob dailyJob) {
        this.dailyJob = dailyJob;
    }


    @RequestMapping("/china/index")
    public String index() {
        return "/china/index";
    }

    /**
     * @return 需要定时执行，手工仅补充
     */
    @RequestMapping("/china/ctripDailyBill")
    @ResponseBody
    public String ctripDailyBill(String startStr, String endStr) {
        try {
            LocalDateTime start = DateUtil.getDateWithOut(startStr);
            LocalDateTime end = DateUtil.getDateWithOut(endStr);
            dailyJob.dailyReconcile(start, end);
            return SUCCESS;
        } catch (Exception e) {
            log.error("", e);
            return e.toString();
        }
    }

    /**
     * @return 需要定时执行，手工仅补充
     */
    @RequestMapping("/china/ctripFailedData")
    @ResponseBody
    public String ctripFailedDataResend() {
        dailyJob.dailyCtripFailedDataReconcile();
        return SUCCESS;
    }

    @RequestMapping("/china/iseeFix")
    @ResponseBody
    public String iseeFix(int max, int min) {
        dailyJob.iseeFix(min, max);
        return SUCCESS;
    }

}

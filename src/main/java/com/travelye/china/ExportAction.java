package com.travelye.china;


import com.travelye.base.ActionSupport;
import com.travelye.base.QueryResult;
import com.travelye.base.tools.ExportExcel;
import com.travelye.policy.Condition;
import com.travelye.policy.PolicyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;


import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;



/**
 * Created by linchengdong on 2018/1/31.增加保监数据导出。
 */
@Controller
@Slf4j
public class ExportAction extends ActionSupport {

    @Autowired
    protected PolicyService policyService;

    @RequestMapping("/china/systemManager/exportInformation")
    public String exportInformation(Condition condition){
        String includeLiberty = String.valueOf(request.getAttribute("includeLiberty"));
        if ( includeLiberty.equals("1") ) {
            condition.setIncludeLiberty("1");
        }
        String star_time =  String.valueOf(request.getAttribute("issueBeginDate")+  " 00:00:01");
        String end_time =  String.valueOf(request.getAttribute("issueEndDate")+  " 23:59:59");
        String mounth = star_time.substring(5,7);
        condition.setIssueBeginDate(star_time);
        condition.setIssueEndDate(end_time);
        QueryResult res = policyService.getExportInformation(condition,1, ExportExcel.maxRows);
        ExportExcel.setContentDisposition(res.getCount(),request,response,"travelye-"+mounth+".xls");
        String sheetName = "统计报表";
        List list = res.getList();
        ExportExcel export = new ExportExcel(list);
        String[] eTitleNames = {};
        try {
            String[] cTitleNames = { "COUNTRY CODE", "SOURCE CODE", "AGENCY NAME", "IATA AGENCY CODE", "PCC CODE", "S3 PRODUCER CODE","AGENT ID",
                    "AGENT NAME", "TRAVELSKY TRANSACTION REFERENCE NO.", "POLICY NUMBER", "POLICYHOLDER", "ISSUING DATE / ENDORSEMENT DATE", "EFFECTIVE DATE", "EXPIRY DATE",
                    "POLICY TRANSACTION TYPE","NUMBER OF DAYS","PRODUCT","PLAN","POLICY ENDORSEMENT REASON","VOUCHER NUMBER","PREMIUM CURRENCY","TRANSACTION PREMIUM","TOTAL PREMIUM",
                    "PREMIUM DISCOUNT","TOTAL NUMBER OF INSURED","MAJOR INSURED","TRANSACTION COMMISSION AMOUNT","INVOICE ISSUANCE REQUEST","PAYER'S NAME","CITY","ADDRESS","ZIP CODE",
                    "TRANSACTION NO","CELL PHONE","PRODUCER1NAME","QUALIFICATION1NO","PRODUCER2NAME","QUALIFICATION2NO"};
            String[] fieldNames = { "biataCntryCd", "sourceId", "agencyName", "ciataCntryCd", "agencyPcc", "s3BCode", "huserName","kong", "kong", "policyNo", "policyHolder","transactionDate",
                    "effectiveDate", "expiryDate", "type", "days", "productName", "planName", "kong", "kong", "rmb", "transactionAmount","totalPremium", "discount", "personNum", "insuredname",
                    "insuredID", "kong", "policyHolder", "kong", "kong", "kong", "status", "kong","guserName","kong" ,"kong","kong"};
//            export.getExceLZip(eTitleNames,cTitleNames, fieldNames, sheetName, response.getOutputStream(),"tongji.xls");
            export.getExceL10000FromStart(eTitleNames,cTitleNames, fieldNames, sheetName, response.getOutputStream());
            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (IOException e) {
           log.error("",e);
        }catch (Exception ex){
            log.error("",ex);
        }
        return null;
    }

    @RequestMapping("/china/systemManager/exportvm")
    public String exportvm(){
        //获取前一个月第一天
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar1 = Calendar.getInstance();
        calendar1.add(Calendar.MONTH, -1);
        calendar1.set(Calendar.DAY_OF_MONTH,1);
        String issueBeginDate = sdf.format(calendar1.getTime());
        //获取前一个月最后一天
        Calendar calendar2 = Calendar.getInstance();
        calendar2.set(Calendar.DAY_OF_MONTH, 0);
        String issueEndDate = sdf.format(calendar2.getTime());
        request.setAttribute("issueBeginDate", issueBeginDate);
        request.setAttribute("issueEndDate", issueEndDate);
        return "/china/systemManager/export";
    }



}

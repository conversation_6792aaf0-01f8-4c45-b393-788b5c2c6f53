package com.travelye.china;

import com.travelye.base.ActionSupport;
import com.travelye.base.Pagination;
import com.travelye.partner.PartnerService;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.vo.Partner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

@Controller
public class BranchAction  extends ActionSupport {

	@Autowired
	private PartnerService partnerService;

	@RequestMapping("/china/systemManager/listBranch")
	public String listBranch() {
		SecurityUser userSession=UserSessionManager.getSecurityLoginUser(request);		
		int pageStart;
		Pagination page = new Pagination();
		page.setPaginalCount(50);
		String start = request.getParameter("pagestart");
		if (checkIsEmpty(start)) {
			pageStart = Integer.parseInt(start);
		} else {
			pageStart = 1;
		}
		page.setPageStart(pageStart);
		List<Partner> branchList = partnerService.getBranchList(userSession.getAgencyId());
		page.setRowCount(branchList.size());
		request.setAttribute("page", page);
		request.setAttribute("list", branchList);
		
		return "/china/systemManager/branchManager";
	}	


	@RequestMapping("/china/systemManager/save")
	public String save(Partner partner){
		SecurityUser userSession=UserSessionManager.getSecurityLoginUser(request);
		Partner oldBranch=partnerService.getPartner(partner.getAgencyID());
		if (oldBranch ==null || partner.getBranch() !=userSession.getAgencyId()){
			request.setAttribute("message","无效的分公司信息！");
			return "/china/systemManager/branch";
		}
		oldBranch.setAgencyName(partner.getAgencyName());
		oldBranch.setIATACntryCd(partner.getIATACntryCd());
		oldBranch.setAddress(partner.getAddress());
		oldBranch.setTelphone(partner.getTelphone());
		oldBranch.setPerson(partner.getPerson());
		oldBranch.setAbbrName(partner.getAbbrName());
		oldBranch.setIsRed(partner.getIsRed());
		oldBranch.setIsSendEmail(partner.getIsSendEmail());
		oldBranch.setIssueOffice(partner.getIssueOffice());
		oldBranch.setIssueOfficeEn(partner.getIssueOfficeEn());
		
		partnerService.save(oldBranch);
		request.setAttribute("partner", oldBranch);
		request.setAttribute("message","保存成功！");
		return "/china/systemManager/branch";
	}

	@RequestMapping("/china/systemManager/add")
	public String add(){
		return "/china/systemManager/branch";
	}

	@RequestMapping("/china/systemManager/edit")
	public String edit(){
		SecurityUser userSession=UserSessionManager.getSecurityLoginUser(request);
		long partnerid=Long.parseLong(request.getParameter("partnerid"));
		Partner partner = partnerService.getPartner(partnerid);
		if (partner.getBranch()!=userSession.getAgencyId()){
			request.setAttribute("message","无效的客户信息！");
			return "/china/systemManager/branch";
		}
		request.setAttribute("partner", partner);
		return "/china/systemManager/branch";
	}
	
		
	private boolean checkIsEmpty(String str) {
		if ("".equals(str) || str == null) {
			return false;
		} else {
			return true;
		}
	}

}

package com.travelye.api.down.liberty.service;

import java.util.concurrent.Semaphore;

import com.travelye.api.down.liberty.DTO.LibertyResponse;
import com.travelye.base.MessageException;
import com.travelye.external.LibertyErrorCode;
import com.travelye.sjis.engine.ChartisBJParameters;
import com.travelye.sjis.engine.HttpUtil;
import com.travelye.vo.Policyuser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.dom4j.Node;
import org.springframework.stereotype.Service;

import java.net.http.HttpResponse;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class RequestLibertyServiceImpl implements RequestLibertyService {

    private final Semaphore semaphore = new Semaphore(10);

    /**
     * 出单
     */
    @Override
    public LibertyResponse applyNewPolicy(String messageText, String url) throws Exception {
        if (messageText.contains("MessageText=")) {
            messageText = messageText.substring(messageText.indexOf("MessageText=") + 12);
        }

        LibertyResponse libertyResponseDto = new LibertyResponse();

        try {
            semaphore.acquire();  // Block until permit is available
            try {
                HttpResponse<String> responseBody = HttpUtil.postForm(url, Map.of("MessageText", messageText, "MessageType", ChartisBJParameters.AIGmessageType));
                String responseXml = getResponseXml(responseBody);
                // 记录返回内容
                libertyResponseDto.setResponseXml(responseBody.body());

                Document document = DocumentHelper.parseText(responseXml);
                Element root = document.getRootElement();
                Node segment = root.selectSingleNode("Segment");

                Node header = root.selectSingleNode("Header");
                libertyResponseDto.setTransactionId(header.valueOf("MessageId"));

                int errorCode = segment.numberValueOf("ErrorCode").intValue();
                libertyResponseDto.setErrorCode(errorCode);
                if (errorCode != 0) {

                    LibertyErrorCode libertyErrorCodeEnum = LibertyErrorCode.valueOfCode(errorCode);
                    if (libertyErrorCodeEnum != null) {
                        libertyResponseDto.setErrorMessage(libertyErrorCodeEnum.getDescription());
                        return libertyResponseDto;
                    }

                    libertyResponseDto.setErrorMessage(segment.valueOf("ErrorMessage"));
                    return libertyResponseDto;
                }
                String policyNumber = segment.valueOf("PolicyOut/PolicyNumber");
                String premium = segment.valueOf("PolicyOut/TotalPremium");
                if (StringUtils.isBlank(policyNumber)) {
                    throw new MessageException("网络异常，请稍后重试。核心系统返回保单号为空。");
                }

                libertyResponseDto.setPolicyNumber(policyNumber);
                libertyResponseDto.setPremium(premium);
                return libertyResponseDto;
            } finally {
                semaphore.release();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Thread interrupted while waiting.");
        }
    }

    /**
     * 询价
     */
    @Override
    public LibertyResponse quoteLiberty(String messageText, String url) throws Exception {
        if (messageText.contains("MessageText=")) {
            messageText = messageText.substring(messageText.indexOf("MessageText=") + 12);
        }

        LibertyResponse libertyResponseDto = new LibertyResponse();

        Map<String, String> formData = new HashMap<>();
        formData.put("MessageText", messageText);
        formData.put("MessageType", ChartisBJParameters.AIGmessageType);

        HttpResponse<String> responseBody = HttpUtil.postForm(url, formData);
        String responseXml = getResponseXml(responseBody);
        // 记录返回内容
        libertyResponseDto.setResponseXml(responseBody.body());

        Document document = DocumentHelper.parseText(responseXml);
        Element root = document.getRootElement();
        Node segment = root.selectSingleNode("Segment");
        int errorCode = segment.numberValueOf("ErrorCode").intValue();
        libertyResponseDto.setErrorCode(errorCode);
        if (errorCode != 0) {

            LibertyErrorCode libertyErrorCodeEnum = LibertyErrorCode.valueOfCode(errorCode);
            if (libertyErrorCodeEnum != null) {
                libertyResponseDto.setErrorMessage(libertyErrorCodeEnum.getDescription());
                return libertyResponseDto;
            }

            libertyResponseDto.setErrorMessage(segment.valueOf("ErrorMessage"));
            return libertyResponseDto;
        }
        return libertyResponseDto;
    }

    /**
     * 退保
     */
    @Override
    public LibertyResponse cancelPolicy(String messageText, String url) throws Exception {
        if (messageText.contains("MessageText=")) {
            messageText = messageText.substring(messageText.indexOf("MessageText=") + 12);
        }

        LibertyResponse libertyResponseDto = new LibertyResponse();

        HttpResponse<String> responseBody = HttpUtil.postForm(url, Map.of("MessageText", messageText, "MessageType", ChartisBJParameters.AIGmessageType));
        String responseXml = getResponseXml(responseBody);
        // 记录返回内容
        libertyResponseDto.setResponseXml(responseBody.body());

        Document document = DocumentHelper.parseText(responseXml);
        Element root = document.getRootElement();
        Node segment = root.selectSingleNode("Segment");
        Node header = root.selectSingleNode("Header");
        libertyResponseDto.setTransactionId(header.valueOf("MessageId"));

        int errorCode = segment.numberValueOf("ErrorCode").intValue();
        libertyResponseDto.setErrorCode(errorCode);
        if (errorCode != 0 && errorCode != 137) {

            LibertyErrorCode libertyErrorCodeEnum = LibertyErrorCode.valueOfCode(errorCode);
            if (libertyErrorCodeEnum != null) {
                libertyResponseDto.setErrorMessage(libertyErrorCodeEnum.getDescription());
                return libertyResponseDto;
            }

            libertyResponseDto.setErrorMessage(segment.valueOf("ErrorMessage"));
            return libertyResponseDto;
        }
        return libertyResponseDto;
    }


    /**
     * 查询保单
     */
    @Override
    public LibertyResponse queryPolicy(String messageText, String url) throws Exception {

        if (messageText.contains("MessageText=")) {
            messageText = messageText.substring(messageText.indexOf("MessageText=") + 12);
        }

        LibertyResponse libertyResponseDto = new LibertyResponse();

        HttpResponse<String> responseBody = HttpUtil.postForm(url, Map.of("MessageText", messageText, "MessageType", ChartisBJParameters.AIGmessageType));
        String responseXml = getResponseXml(responseBody);
        // 记录返回内容
        libertyResponseDto.setResponseXml(responseBody.body());

        Document document = DocumentHelper.parseText(responseXml);
        Element root = document.getRootElement();
        Node segment = root.selectSingleNode("Segment");


        libertyResponseDto.setTransactionId(segment.valueOf("TransactionId"));

        int errorCode = segment.numberValueOf("ErrorCode").intValue();
        libertyResponseDto.setErrorCode(errorCode);
        if (errorCode != 0) {
            libertyResponseDto.setErrorMessage(segment.valueOf("ErrorMessage"));
            return libertyResponseDto;
        }
        Node policyIn = segment.selectSingleNode("PolicyIn");
        Node policyOut = segment.selectSingleNode("PolicyOut");

        // 保单信息
        libertyResponseDto.setEffectiveDate(LocalDateTime.parse(policyIn.valueOf("InceptionDate"), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH)));
        libertyResponseDto.setExpiryDate(LocalDateTime.parse(policyIn.valueOf("ExpirationDate"), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH)));
        libertyResponseDto.setSellDateTime(LocalDateTime.parse(policyIn.valueOf("TransactionApplDate"), DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mm:ss a", Locale.ENGLISH)));
        libertyResponseDto.setGdsCode(policyIn.valueOf("GDSCode"));
        libertyResponseDto.setAgencyPcc(policyIn.valueOf("AgencyPCC"));
        libertyResponseDto.setSubCode(policyIn.valueOf("AgencyCode"));
        libertyResponseDto.setIataCntryCd(policyIn.valueOf("IATACntryCd"));
        libertyResponseDto.setProductCode(policyIn.valueOf("GDSProductCode"));
        libertyResponseDto.setPolicyStatus(policyOut.valueOf("PolicyStage"));
        libertyResponseDto.setPremium(policyOut.valueOf("TotalPremium"));

        // 投被保险人信息
        List<Policyuser> policyuserList = new ArrayList<>();
        List<Node> insuredNodeList = segment.selectNodes("Insured");
        for (Node insuredNode : insuredNodeList) {
            policyuserList.add(Policyuser.builder()
                    .insuredname(insuredNode.valueOf("LastNm"))
                    .insuredIdNo(insuredNode.valueOf("InsuredIdNo"))
                    .birthday(LocalDate.parse(insuredNode.valueOf("BirthDt"), DateTimeFormatter.ofPattern("MM/dd/yyyy")))
                    .isInsuredFlag(Integer.parseInt(insuredNode.valueOf("IsInsuredFlag")))
                    .build());

            libertyResponseDto.setPlanCode(insuredNode.valueOf("PlanCode"));

        }
        libertyResponseDto.setPolicyuserList(policyuserList);

        return libertyResponseDto;
    }

    private String getResponseXml(HttpResponse<String> responseBody) {
        // 网络异常或返回body为空
        if (responseBody.statusCode() != 200 || StringUtils.isBlank(responseBody.body())) {
            throw new MessageException("网络异常，请稍后再试。");
        }

        try {
            String[] lines = responseBody.body().split("\n");
            int stateCode = Integer.parseInt(lines[1].trim());
            log.info("status code: {} ", stateCode);
            if (0 == stateCode) {

                String xml = responseBody.body().substring(responseBody.body().indexOf("<TINS_XML_DATA>"),
                        responseBody.body().indexOf("</TINS_XML_DATA>") + 16
                ).trim();

                if (StringUtils.isBlank(xml)) {
                    throw new MessageException("网络异常，请稍后重试。");
                }
                return xml;
            } else {
                throw new MessageException("系统异常，请稍后重试。");
            }
        } catch (Exception e) {
            log.error("解析Liberty返回内容失败", e);
            throw e;
        }
    }


}

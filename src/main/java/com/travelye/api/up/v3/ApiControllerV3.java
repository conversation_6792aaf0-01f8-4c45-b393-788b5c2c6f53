package com.travelye.api.up.v3;

import com.travelye.api.up.v3.DTO.Transaction;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.api.up.v3.DTO.ApiClient;
import com.travelye.api.up.v3.exception.ApiException;
import com.travelye.api.up.v3.exception.ApiExceptionEnum;
import com.travelye.api.up.v3.service.ApiServiceV3;
import com.travelye.api.up.v3.utils.SignatureUtils;
import com.travelye.email.AsyncEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;
import java.util.Objects;

@Controller
@RequestMapping("/api/v3")
@Slf4j
public class ApiControllerV3 {

    private final ApiServiceV3 apiServiceV3;
    private final AsyncEmailService asyncEmailService;

    public ApiControllerV3(ApiServiceV3 apiServiceV3, AsyncEmailService asyncEmailService) {
        this.apiServiceV3 = apiServiceV3;
        this.asyncEmailService = asyncEmailService;
    }

    @RequestMapping("/downloadQuery")
    @ResponseBody
    public String downloadQuery(String timestamp,
                                String nonce,
                                String signature,
                                String clientId,
                                @RequestBody String requestBody) throws Exception {
        log.info("api(v3)请求，timestamp:{},nonce:{},signature:{}", timestamp, nonce, signature);
        // 校验clientId
        ApiClient client = apiServiceV3.getClient(clientId);
        if (client == null || client.getState() != 1) {
            throw new ApiException(ApiExceptionEnum.CLIENT_ERROR);
        }
        // 校验签名
        if (!signature.equals(SignatureUtils.sha256Sign(timestamp + nonce + client.getClientSecret() + SignatureUtils.sha256Sign(requestBody)))) {
            throw new ApiException(ApiExceptionEnum.SIGN_ERROR);
        }
        // 根据10位timestamp校验是否过期
        try {
            long timestampLong = Long.parseLong(timestamp);
            LocalDateTime requestTime = Instant.ofEpochSecond(timestampLong).atZone(ZoneId.systemDefault()).toLocalDateTime();
            if (requestTime.isBefore(LocalDateTime.now().minusMinutes(5))) {
                throw new ApiException(ApiExceptionEnum.TIMESTAMP_ERROR);
            }
        } catch (NumberFormatException | DateTimeParseException e) {
            throw new ApiException(ApiExceptionEnum.TIMESTAMP_ERROR);
        }
        // nonce是否在24小时内重复
        if (!apiServiceV3.checkNonce(clientId, nonce, 60 * 60 * 24)) {
            throw new ApiException(ApiExceptionEnum.NONCE_ERROR);
        }

        // 解析json报文
        Transaction transaction = JsonMapperUtils.jsonToObject(requestBody, Transaction.class);
        if (transaction == null) {
            throw new ApiException(ApiExceptionEnum.JSON_ERROR);
        }
        if (ApiClient.CLIENT_ID_ETRAVEL.equals(clientId)) {
            transaction.setApiClient(client);
            transaction = apiServiceV3.etravelPolicyUrl(transaction);
            if (!Objects.equals(transaction.getErrorCode(), ApiExceptionEnum.SUCCESS.getCode()) || transaction.getSegment().stream().anyMatch(s -> !Objects.equals(s.getErrorCode(), ApiExceptionEnum.SUCCESS.getCode()))) {
                try {
                    asyncEmailService.sendAlertEmail("【eTravel】保单下载失败", "请求:" + requestBody + "\n响应:" + JsonMapperUtils.objectToJson(transaction));
                } catch (Exception e) {
                    log.error("发送邮件异常", e);
                    asyncEmailService.sendAlertEmail("【eTravel】保单下载失败", "详情参考日志，发生时间" + LocalDateTime.now());
                }
            }
        } else if (ApiClient.CLIENT_ID_SINO.equals(clientId)) {
            transaction.setApiClient(client);
            transaction = apiServiceV3.sinoPolicyUrl(transaction);
        } else {
            transaction = apiServiceV3.downloadUrl(transaction, clientId);
        }

        apiServiceV3.insertNonceRecord(clientId, nonce, requestBody);

        // 处理下载请求
        return JsonMapperUtils.objectToJson(transaction);
    }
}

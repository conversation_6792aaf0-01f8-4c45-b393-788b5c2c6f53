package com.travelye.api.up.v3.advice;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.travelye.api.up.v3.ApiControllerV3;
import com.travelye.api.up.v3.DTO.Transaction;
import com.travelye.api.up.v3.exception.ApiException;
import com.travelye.api.up.v3.exception.ApiExceptionEnum;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.email.AsyncEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;

@RestControllerAdvice(basePackageClasses = {ApiControllerV3.class})
@Slf4j
public class ApiExceptionHandler {

    private final AsyncEmailService asyncEmailService;

    public ApiExceptionHandler(AsyncEmailService asyncEmailService) {
        this.asyncEmailService = asyncEmailService;
    }

    /**
     * 处理自定义的业务异常
     */
    @ExceptionHandler(value = ApiException.class)
    @ResponseBody
    public String apiExceptionHandler(HttpServletRequest request,
                                      ApiException e) throws Exception {
        log.error("api(v3)业务异常！原因：{}", e.getMessage());
        asyncEmailService.sendAlertEmail("【eTravel】保单下载失败", "详情参考日志，发生时间" + LocalDateTime.now());
        return JsonMapperUtils.objectToJson(new Transaction(e));
    }

    @ExceptionHandler(value = JsonProcessingException.class)
    @ResponseBody
    public String JsonProcessExceptionHandler(HttpServletRequest request,
                                              JsonProcessingException e) throws Exception {
        log.error("api(v3)报文解析异常", e);
        asyncEmailService.sendAlertEmail("【eTravel】保单下载失败", "详情参考日志，发生时间" + LocalDateTime.now());
        return JsonMapperUtils.objectToJson(new Transaction(ApiExceptionEnum.JSON_ERROR));
    }

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public String commonExceptionHandler(HttpServletRequest request,
                                         Exception e) throws Exception {
        log.error("api(v3)系统异常！", e);
        asyncEmailService.sendAlertEmail("【eTravel】保单下载失败", "详情参考日志，发生时间" + LocalDateTime.now());
        return JsonMapperUtils.objectToJson(new Transaction(ApiExceptionEnum.SYSTEM_ERROR));
    }
}

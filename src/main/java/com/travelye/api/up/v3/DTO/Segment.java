package com.travelye.api.up.v3.DTO;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.travelye.api.up.v2.DTO.Applicant;
import com.travelye.api.up.v2.DTO.Insured;
import com.travelye.api.up.v3.exception.ApiExceptionEnum;
import com.travelye.vo.Plan;
import com.travelye.vo.Product;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class Segment {
    private String policyNo;
    private String endorsementNo;
    private String policyId;
    private String insuredId;
    private String verification;
    private String email;
    private String url;

    private Integer errorCode;
    private String errorMessage;

    // etravel打印电子保单时需要的信息
    private LocalDateTime effectiveDate;
    private LocalDateTime expiryDate;
    private LocalDateTime issueDate;
    private LocalDateTime paymentDate;
    private String productCode;
    private String planCode;
    private String benefitCd;
    private String agencyName;
    private String agencyCode;
    private String issueOffice;
    private String premium;
    private Applicant applicant;
    private List<Insured> insuredList;
    // etravel 打印电子保单时需要的信息结束

    @JsonIgnore
    private Product product;
    @JsonIgnore
    private Plan plan;

    public void fail(ApiExceptionEnum e) {
        this.errorCode = e.getCode();
        this.errorMessage = e.getMsg();
    }

    public void ok() {
        this.fail(ApiExceptionEnum.SUCCESS);
    }

}

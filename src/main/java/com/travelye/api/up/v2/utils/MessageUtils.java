package com.travelye.api.up.v2.utils;

import com.travelye.api.up.v2.DTO.Transaction;
import com.travelye.api.up.v2.exception.ApiException;
import com.travelye.base.tools.JSONUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;


public class MessageUtils {
    private final static Logger log = LoggerFactory.getLogger(MessageUtils.class);

//    public static Transaction xmlToTransaction(String xml) {
//        Transaction transaction = null;
//        try {
//
//            transaction = (Transaction) toTransaction(Transaction.class, xml);
//        } catch (Exception e) {
//            log.error("", e);
//            throw new ApiException(-1, "系统错误");
//        }
//        return transaction;
//    }

    public static Transaction jsonToTransaction(String xml) {
        try {
            return JsonMapperUtils.jsonToObject(xml, Transaction.class);
        } catch (Exception e) {
            log.error("deserialize json failed", e);
            throw new ApiException(-1, "报文解析失败");
        }
    }

    public static String transactionToJson(Transaction transaction) {
        try {
            return JsonMapperUtils.objectToJson(transaction);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ApiException(-1, "系统错误");
        }
    }

    public static String toErrorXml(int errorCode, String errorMessage) {
        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("errorCode", errorCode);
        resultMap.put("errorMessage", errorMessage);
        return JSONUtils.toJson(resultMap);
    }

}

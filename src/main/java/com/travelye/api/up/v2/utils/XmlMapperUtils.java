package com.travelye.api.up.v2.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;

import static com.fasterxml.jackson.databind.PropertyNamingStrategies.UPPER_CAMEL_CASE;

public class XmlMapperUtils {
    public static final XmlMapper xmlMapper;

    static {
        xmlMapper = new XmlMapper();

        SimpleModule module = new SimpleModule();
        xmlMapper.registerModule(module);
        xmlMapper.setDefaultUseWrapper(false);

        xmlMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        xmlMapper.setPropertyNamingStrategy(UPPER_CAMEL_CASE);

        xmlMapper.enable(MapperFeature.USE_STD_BEAN_NAMING);
    }

    public static String beanToXml(Object object) throws JsonProcessingException {
            return xmlMapper.writeValueAsString(object);
    }

    public static <T> T xmlToBean(String xml,Class<T> clazz) throws JsonProcessingException {
        return xmlMapper.readValue(xml, clazz);
    }

}

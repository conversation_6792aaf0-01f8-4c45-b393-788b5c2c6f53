package com.travelye.api.up.v2.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
public class ApiException extends RuntimeException {
    private int errorCode;
    private String errorMessage;

    public ApiException(int errorCode) {
        super("");
        this.errorCode = errorCode;
    }

    public ApiException(int errorCode, String errorMessage) {
        super(errorMessage);
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
    }

    public ApiException(@NotNull ApiExceptionEnum errorCodeEnum) {
        super(errorCodeEnum.getMsg());
        this.errorCode = errorCodeEnum.getCode();
        this.errorMessage = errorCodeEnum.getMsg();
    }

    public ApiException(String errorMessage) {
        super(errorMessage);
        this.errorCode = ApiExceptionEnum.OTHER_ERROR.getCode();
        this.errorMessage = errorMessage;
    }

    public static void isTrue(boolean expression, ApiException apiException) {
        if (!expression) {
            throw apiException;
        }
    }

    public static void isTrue(boolean expression, ApiExceptionEnum apiExceptionEnum) {
        if (!expression) {
            throw new ApiException(apiExceptionEnum);
        }
    }

}

package com.travelye.api.up.v2.XStreamConverter;

import com.thoughtworks.xstream.converters.ConversionException;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

public class ApiDateTimeConverter implements Converter {

    private final DateTimeFormatter DATETIMEFORMAT = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

    @Override
    public boolean canConvert(Class clazz) {
        return clazz.equals(LocalDateTime.class);
    }

    @Override
    public void marshal(Object obj, HierarchicalStreamWriter arg1, MarshallingContext arg2) {
    }

    @Override
    public Object unmarshal(HierarchicalStreamReader reader, UnmarshallingContext context) {
        String value = reader.getValue();
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return LocalDateTime.parse(value, DATETIMEFORMAT);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ConversionException("Cannot parse date:" + reader.getNodeName());
        }
    }

}

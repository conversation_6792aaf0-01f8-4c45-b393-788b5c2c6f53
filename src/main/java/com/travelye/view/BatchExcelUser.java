package com.travelye.view;


import lombok.Data;

@Data
public class BatchExcelUser implements java.io.Serializable {

    private int groupId;            // 标识这组投被保人额组别
    private String insuredName;        // 被保险人姓名
    private String insuredBirthday;    // 被保险人生日
    private String insuredIdNoType;    // 被保险证件类型
    private String insuredIdNo;        // 被保险证件号码
    private String insuredGender;   // 被保险人性别
    private String insuredEmail;    // 被保险邮箱
    private String insuredPhone;    // 被保人手机
    private String insuredRemark;    // 被保险备注
    private String deathBeneficiary; //身故受益人字符串
    private String insuredNationality;
    private String insuredIdentificationId;

    private String holderName;        // 投保人姓名
    private String holderBirthday;    // 投保人生日
    private String holderIdNoType;    // 投保证件类型
    private String holderIdNo;        // 投保证件号码
    private String holderGender;    // 投保人性别
    private String holderEmail;     // 投保人邮箱
    private String holderPhone;     // 投保人手机
    private String holderNationality; // 投保人国籍
    private String holderIdentificationId; // 投保人身份证号

    private String relation;       //投被保人关系
    private int modifyFlag;    //	标注该用户是否修改 ,0-没有修改,1-有修改
    private int recordStatus;    // 记录是否修改 1--修改
    private int policyStatus;        // 状态 1、新增；2、变更；3、取消

    @Override
    public String toString() {
        String s = "";
        if (this.getInsuredName() != null && !"".equals(this.getInsuredName())) {
            s = s + "姓名:" + this.getInsuredName();
        }

        if (this.getInsuredIdNoType() != null && !"".equals(this.getInsuredIdNoType())) {
            s = s + ",证件类型:" + this.getInsuredIdNoType();
        }
        if (this.getInsuredIdNo() != null && !"".equals(this.getInsuredIdNo())) {
            s = s + ",证件号码:" + this.getInsuredIdNo();
        }
        if (this.getInsuredBirthday() != null) {
            s = s + ",出生日期:" + this.getInsuredBirthday();
        }
        return s;
    }


}

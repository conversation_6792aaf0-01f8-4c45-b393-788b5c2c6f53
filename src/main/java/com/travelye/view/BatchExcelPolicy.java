package com.travelye.view;

import java.util.List;



public class BatchExcelPolicy  implements java.io.Serializable{
	private String planCode;		// 计划代码
	private String lineno;			// 线路编号
	private String effectiveDate;	// 生效时间
	private String expiryDate;		// 满期时间
	private String attn;			// 经办人 
	private String telephone;  	   	//联系电话
	private String remark;			// 备注
	private List<BatchExcelUser> userList;  //投被保人列表

	public List<BatchExcelUser> getUserList() {
		return userList;
	}

	public void setUserList(List<BatchExcelUser> userList) {
		this.userList = userList;
	}

	public String toString(){
		String s="";
		if (this.getPlanCode() !=null && !"".equals(this.getPlanCode())){
			s =s + "计划代码:" + this.getPlanCode();
		} 
		if (this.getLineno() !=null && !"".equals(this.getLineno()) ){
			s =s +  ",线路编号:" + this.getLineno();
		}
		if (this.getEffectiveDate() !=null && !"".equals(this.getEffectiveDate())){
			s =s +  ",生效时间:" + this.getEffectiveDate();
		}
		if (this.getExpiryDate() !=null && !"".equals(this.getExpiryDate())){
			s =s + ",满期时间:" + this.getExpiryDate();
		}
		if (this.getRemark() !=null ){
			s =s +  ",备注:" + this.getRemark();
		}
		return s;
	}

	public String getPlanCode() {
		return planCode;
	}

	public void setPlanCode(String planCode) {
		this.planCode = planCode;
	}

	public String getLineno() {
		return lineno;
	}

	public void setLineno(String lineno) {
		this.lineno = lineno;
	}

	public String getEffectiveDate() {
		return effectiveDate;
	}

	public void setEffectiveDate(String effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	public String getExpiryDate() {
		return expiryDate;
	}

	public void setExpiryDate(String expiryDate) {
		this.expiryDate = expiryDate;
	}

	public String getAttn() {
		return attn;
	}

	public void setAttn(String attn) {
		this.attn = attn;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	
}
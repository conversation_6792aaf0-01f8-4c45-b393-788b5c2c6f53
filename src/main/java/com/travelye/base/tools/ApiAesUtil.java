package com.travelye.base.tools;


import jakarta.xml.bind.DatatypeConverter;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;

@Slf4j
public class ApiAesUtil {
    public static final String AES_ALGORITHM = "AES";
    public static final String CRYPT_MODEL = "AES/ECB/PKCS5Padding";

    /**
     * 加密
     *
     * @param data         明文
     * @param secretKeyStr 密钥
     * @return
     * @throws Exception
     */
    public static String encrypt(String data, String secretKeyStr) throws Exception {
        if (data == null || secretKeyStr == null) {
            throw new IllegalArgumentException();
        }
        // KEY转换
        Key key = new SecretKeySpec(DatatypeConverter.parseHexBinary(secretKeyStr), AES_ALGORITHM);
        // 加密
        Cipher cipher = Cipher.getInstance(CRYPT_MODEL);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] result = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        String encryptedStr = DatatypeConverter.printHexBinary(result);
        log.debug("加密密钥:{}\n,密文：{}", secretKeyStr, encryptedStr);
        return encryptedStr;
    }

    /**
     * 解密
     *
     * @param decryptedStr 密文
     * @param secretKeyStr 密钥
     * @return
     * @throws Exception
     */
    public static String decrypt(String decryptedStr, String secretKeyStr) throws Exception {
        if (decryptedStr == null || secretKeyStr == null) {
            throw new IllegalArgumentException();
        }
        // KEY转换
        Key key = new SecretKeySpec(DatatypeConverter.parseHexBinary(secretKeyStr), AES_ALGORITHM);
        // 解密
        Cipher cipher = Cipher.getInstance(CRYPT_MODEL);
        cipher.init(Cipher.DECRYPT_MODE, key);
        byte[] result = cipher.doFinal(DatatypeConverter.parseHexBinary(decryptedStr));
        String sourceData = new String(result, StandardCharsets.UTF_8);
        log.debug("解密明文：{}", sourceData);
        return sourceData;
    }

}

package com.travelye.base.tools;

import java.awt.*;
import java.awt.image.BufferedImage;

public class ImageUtils {

    /**
     * 旋转图片
     */
    public static BufferedImage Rotate(Image src, int angle) {
        int srcWidth = src.getWidth(null);
        int srcHeight = src.getHeight(null);
        //旋转后的图片尺寸
        Rectangle rectDes = calRotatedSize(new Rectangle(new Dimension(srcWidth, srcHeight)), angle);
        BufferedImage res = null;
        res = new BufferedImage(rectDes.width, rectDes.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();

        //进行转换
        g2.translate((rectDes.width - srcWidth) / 2, (rectDes.height - srcHeight) / 2);
        g2.rotate(Math.toRadians(angle), srcWidth / 2, srcHeight / 2);
        g2.drawImage(src, null, null);
        return res;
    }

    public static Rectangle calRotatedSize(Rectangle src, int angle) {
        // 如果旋转的角度大于90度做相应的转换
        if (angle >= 90) {
            if (angle / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angle = angle % 90;
        }

        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angle) / 2) * r;
        double angelAlpha = (Math.PI - Math.toRadians(angle)) / 2;
        double angleDeltaWidth = Math.atan((double) src.height / src.width);
        double angelDeltaHeight = Math.atan((double) src.width / src.height);

        int lenDeltaWidth = (int) (len * Math.cos(Math.PI - angelAlpha
                - angleDeltaWidth));
        int lenDeltaHeight = (int) (len * Math.cos(Math.PI - angelAlpha
                - angelDeltaHeight));
        int desWidth = src.width + lenDeltaWidth * 2;
        int desHeight = src.height + lenDeltaHeight * 2;
        return new Rectangle(new Dimension(desWidth, desHeight));
    }
}

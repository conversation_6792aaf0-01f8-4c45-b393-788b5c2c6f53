package com.travelye.base.tools;

import com.aliyun.cloudauth20190307.Client;
import com.aliyun.cloudauth20190307.models.*;
import com.aliyun.tea.*;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.policy.PolicyDao;
import com.travelye.policy.ruleEngine.RuleResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AliIdVerifyUtil {

    private static final Client CLIENT = createIdVerifyClient();
    private static final RuntimeOptions runtime = new RuntimeOptions();

    private final PolicyDao policyDao;

    public AliIdVerifyUtil(PolicyDao policyDao) {
        this.policyDao = policyDao;
    }

    private static Client createIdVerifyClient() {
        try {
            return new Client(new Config()
                    .setAccessKeyId("LTAI5tLsjLFkiXcBWTK22mHn")
                    .setAccessKeySecret("******************************")
                    .setEndpoint("cloudauth.aliyuncs.com"));
        } catch (Exception e) {
            log.error("创建认证client失败", e);
            return null;
        }
    }

    /**
     * 身份证和姓名二要素核验
     */
    public RuleResult id2Verify(String idNo, String name) {
        RuleResult result = new RuleResult();

        RuleResult historyResult = getAuthRecord(name, idNo, null, Id2MetaVerifyResponseBody.class);
        if (historyResult != null) {
            return historyResult;
        }

        if (CLIENT == null) {
            return result.fail("认证客户端未初始化");
        }

        try {
            log.info("身份二要素核验，{},{}", name, idNo);
            Id2MetaVerifyRequest request = new Id2MetaVerifyRequest();
            request.setParamType("normal");
            request.setIdentifyNum(idNo);
            request.setUserName(name);

            Id2MetaVerifyResponse response = CLIENT.id2MetaVerifyWithOptions(request, runtime);
            RuleResult processedResult = processVerifyResponse(response);
            long recordId = saveAuthRecord(processedResult.isOk(), JSONUtils.toJson(response.getBody()), name, idNo, null);
            processedResult.setId(recordId);
            return processedResult;
        } catch (Exception e) {
            log.error("身份证二要素核验异常", e);
            return result.fail(e instanceof TeaException ? e.getMessage() : "系统异常，认证失败");
        }
    }

    /**
     * 手机号三要素核验 - 简版
     * 仅返回是否一致与运营商信息
     */
    public RuleResult phoneVerifySimple(String phone, String idNo, String name) {

        RuleResult historyResult = getAuthRecord(name, idNo, phone, Mobile3MetaSimpleVerifyResponseBody.class);
        if (historyResult != null) {
            return historyResult;
        }

        RuleResult result = new RuleResult();
        if (CLIENT == null) {
            return result.fail("认证客户端未初始化");
        }

        try {
            log.info("身份三要素核验（简版），{},{},{}", name, idNo, phone);
            Mobile3MetaSimpleVerifyRequest request = new Mobile3MetaSimpleVerifyRequest();
            request.setParamType("normal");
            request.setIdentifyNum(idNo);
            request.setUserName(name);
            request.setMobile(phone);

            Mobile3MetaSimpleVerifyResponse response =
                    CLIENT.mobile3MetaSimpleVerifyWithOptions(request, runtime);
            RuleResult processedResult = processVerifyResponse(response);
            long recordId = saveAuthRecord(processedResult.isOk(), JSONUtils.toJson(response.getBody()), name, idNo, phone);
            processedResult.setId(recordId);
            return processedResult;
        } catch (Exception e) {
            log.error("手机号三要素核验异常", e);
            return result.fail(e instanceof TeaException ? e.getMessage() : "系统异常，认证失败");
        }
    }

    /**
     * 手机号三要素核验 - 详细版
     * 返回具体的核验结果，如：通过；手机号和姓名不一致，但与身份证一致；手机号和姓名、身份证都不一致...
     */
    public RuleResult phoneVerifyDetail(String phone, String idNo, String name) {
        RuleResult result = new RuleResult();
        if (CLIENT == null) {
            return result.fail("认证客户端未初始化");
        }

        try {
            log.info("身份三要素核验（详版），{},{},{}", name, idNo, phone);
            Mobile3MetaDetailVerifyRequest request = new Mobile3MetaDetailVerifyRequest();
            request.setParamType("normal");
            request.setIdentifyNum(idNo);
            request.setUserName(name);
            request.setMobile(phone);

            Mobile3MetaDetailVerifyResponse response =
                    CLIENT.mobile3MetaDetailVerifyWithOptions(request, runtime);
            return processVerifyResponse(response);
        } catch (Exception e) {
            log.error("手机号三要素核验异常", e);
            return result.fail(e instanceof TeaException ? e.getMessage() : "系统异常，认证失败");
        }
    }

    /**
     * 处理核验响应
     */
    private RuleResult processVerifyResponse(Object response) {
        return switch (response) {
            case Id2MetaVerifyResponse id2Response -> processId2Response(id2Response);
            case Mobile3MetaSimpleVerifyResponse mobile3Response -> processMobile3Response(mobile3Response);
            case null -> new RuleResult().fail("响应为空");
            default -> new RuleResult().fail("未知的响应类型");
        };
    }

    private RuleResult processId2Response(Id2MetaVerifyResponse response) {
        try {
            log.info("身份证核验返回信息{}", JsonMapperUtils.objectToJson(response));
        } catch (Exception ignored) {
        }
        return processCommonResponse(
                response.getStatusCode(),
                response.getBody().getCode(),
                response.getBody().getMessage(),
                response.getBody().getResultObject() == null ? null : response.getBody().getResultObject().getBizCode()
        );
    }

    private RuleResult processMobile3Response(Mobile3MetaSimpleVerifyResponse response) {
        try {
            log.info("手机号核验返回信息{}", JsonMapperUtils.objectToJson(response));
        } catch (Exception ignored) {
        }

        return processCommonResponse(
                response.getStatusCode(),
                response.getBody().getCode(),
                response.getBody().getMessage(),
                response.getBody().getResultObject() == null ? null : response.getBody().getResultObject().getBizCode()
        );
    }

    private RuleResult processCommonResponse(Integer statusCode, String code, String message, String bizCode) {
        if (statusCode != 200) {
            return new RuleResult().fail("网络异常，认证失败");
        }

        if (!"200".equals(code)) {
            return new RuleResult().fail(message);
        }

        if (StringUtils.isBlank(bizCode)) {
            return new RuleResult().fail("认证服务异常");
        }

        return "1".equals(bizCode)
                ? new RuleResult().ok()
                : new RuleResult().fail(bizCodeToMessage(bizCode));
    }

    private String bizCodeToMessage(String bizCode) {
        return switch (bizCode) {
            case "1" -> "核验一致";
            case "2" -> "核验不一致";
            case "3" -> "无此记录";
            default -> "未知错误";
        };
    }

    private <T> RuleResult getAuthRecord(String name, String idNo, String phone, Class<T> clazz) {
        RuleResult result = null;
        AuthRecord historyRecord = policyDao.getAuthRecord(name, idNo, phone);
        if (historyRecord != null && StringUtils.isNotBlank(historyRecord.getResult())) {
            T response = JSONUtils.readJson(historyRecord.getResult(), clazz);
            if (response instanceof Mobile3MetaSimpleVerifyResponseBody meta3ResponseBody) {
                Mobile3MetaSimpleVerifyResponse meta3Response = new Mobile3MetaSimpleVerifyResponse();
                meta3Response.setStatusCode(200);
                meta3Response.setBody(meta3ResponseBody);
                result = processMobile3Response(meta3Response);
            } else if (response instanceof Id2MetaVerifyResponseBody meta2ResponseBody) {
                Id2MetaVerifyResponse meta2Response = new Id2MetaVerifyResponse();
                meta2Response.setStatusCode(200);
                meta2Response.setBody(meta2ResponseBody);
                result = processId2Response(meta2Response);
            }
            if (result != null) {
                result.setId(historyRecord.getId());
            }
        }
        return result;
    }

    private long saveAuthRecord(boolean success, String result, String name, String idNo, String phone) {
        return policyDao.saveAuthRecord(success, result, name, idNo, phone);
    }

    public void updateAuthRecordPolicyId(long policyId, RuleResult ruleResult) {
        policyDao.updateAuthRecordPolicyId(policyId, ruleResult);
    }
}

package com.travelye.base.tools;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

public class QrCodeUtil {

    /**
     * 生成二维码，默认长宽 300px * 300px
     */
    public static byte[] QREncode(String content) throws Exception {
        return QREncode(content, 300, 300);
    }


    /**
     * 生成二维码
     */
    public static byte[] QREncode(String content, int width, int height) throws Exception {


        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            String format = "png";

            Map<EncodeHintType, Object> hints = new HashMap<>();

            // 内容编码格式
            hints.put(EncodeHintType.CHARACTER_SET, "utf-8");
            // 指定纠错等级
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);
            // 设置二维码边的宽度
            hints.put(EncodeHintType.MARGIN, 2);
            BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);

            MatrixToImageWriter.writeToStream(bitMatrix, format, bos);
            return bos.toByteArray();
        }

    }
}

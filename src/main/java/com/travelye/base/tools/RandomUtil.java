package com.travelye.base.tools;

import java.util.Random;
import java.util.stream.Collectors;

public class RandomUtil {

	public static Random random = new Random();

	public static String getRandom(int length) {
		StringBuilder ret = new StringBuilder();
		for (int i = 0; i < length; i++) {
			boolean isChar = (random.nextInt(2) % 2 == 0);// 输出字母还是数字
			if (isChar) { // 字符串
				int choice = random.nextInt(2) % 2 == 0 ? 65 : 97; // 取得大写字母还是小写字母
				ret.append((char) (choice + random.nextInt(26)));
			} else { // 数字
				ret.append(Integer.toString(random.nextInt(10)));
			}
		}
		return ret.toString();
	}

	public static String randomNumberString(int length) {
		String seed = "0123456789";
		return new Random().ints(length, 0, seed.length())
				.mapToObj(i -> String.valueOf(seed.charAt(i)))
				.collect(Collectors.joining());
	}

	public static String randomAlphabetString(int length) {
		String seed = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
		return new Random().ints(length, 0, seed.length())
				.mapToObj(i -> String.valueOf(seed.charAt(i)))
				.collect(Collectors.joining());
	}
}

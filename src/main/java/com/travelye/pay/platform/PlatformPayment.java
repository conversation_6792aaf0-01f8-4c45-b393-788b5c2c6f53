package com.travelye.pay.platform;

import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.sjis.engine.HttpUtil;
import com.travelye.base.tools.JSONUtils;
import com.travelye.base.tools.RandomUtil;
import com.travelye.base.tools.RsaUtil;
import com.travelye.pay.Payment;
import com.travelye.pay.ResponseResult;
import com.travelye.vo.Fee;
import com.travelye.vo.Policy;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.time.LocalDateTime;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * Created by zzl on 2018/1/8.
 */
public class PlatformPayment implements Payment {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    private String partnerId;
    private String priKey;
    private String pubKey;
    private String notifyUrl;
    private String platformUrl;
    private String refundUrl;
    private String refundNotifyUrl;
    private JSONObject config;

    public PlatformPayment(String jsonPaymenttype) {
        config = new JSONObject(jsonPaymenttype);
        try {
            partnerId = config.getString("partnerId");
            priKey = config.getString("priKey");
            pubKey = config.getString("pubKey");
            refundUrl = config.getString("refundUrl");
            refundNotifyUrl = config.getString("refundNotifyUrl");
            notifyUrl = config.getString("notifyUrl");
            platformUrl = config.getString("platformUrl");

        } catch (Exception e) {
            log.error("PlatformPayment配置错误", e);
        }
    }

    @Override
    public void sendReqUrl(HttpServletRequest request, HttpServletResponse response) {

        //支付方式Id，从页面传过来滴~
        String DefaultOption = (String) request.getAttribute("DefaultOption");     //默认支付方式  0, 未指定默认支付方式   1, 微信支付,微信扫码支付   2,支付宝支付   3, 微信支付,微信公众号支付
//        if (StringUtils.isBlank(DefaultOption)) {
//            DefaultOption = "0";
//        }

//        String description = String.valueOf(request.getAttribute("Description"));

        String transactionId = String.valueOf(request.getAttribute("order_no"));

        String amount = String.valueOf(request.getAttribute("amount"));

        String goodsName = String.valueOf(request.getAttribute("goods_name"));

        String order_time = String.valueOf(request.getAttribute("order_time"));

        String client = String.valueOf(request.getAttribute("Client"));

        String returnUrl = String.valueOf(request.getAttribute("RedirectUrl"));


        //需要排序，故使用TreeMap
        TreeMap<String, String> queryParamMap = new TreeMap<>(Comparator.naturalOrder());

        queryParamMap.put("PartnerID", partnerId);
        queryParamMap.put("TransactionID", transactionId);
        queryParamMap.put("Body", goodsName);
        queryParamMap.put("Amount", amount);
        queryParamMap.put("TimeExpire", "119");
        queryParamMap.put("NotifyURL", notifyUrl);
        queryParamMap.put("ReturnURL", returnUrl);
        queryParamMap.put("RequestTime", order_time);
        queryParamMap.put("Client", client);
        queryParamMap.put("StrRandom", RandomUtil.getRandom(20));   //20位随机字符串
        queryParamMap.put("DefaultOption", DefaultOption);                  //具体支付方式
//        if (StringUtils.isNotBlank(description)) {
//            queryParamMap.put("Description", description);
//        }

        try {

            String input = generateToSign(queryParamMap);
            log.info("需要签名字符串为：" + input);
            String sign = RsaUtil.sign(input, priKey);  //私钥签名;
            log.info("生成签名为：" + sign);

            log.info("跳转到核心支付平台，TransactionID=" + transactionId + ",amount=" + request.getAttribute("amount") + ",DefaultOption=" + DefaultOption);

            response.setContentType("text/html;charset=UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.getWriter().write(generateAutoRedirectHtml(queryParamMap, sign));
            response.setStatus(HttpServletResponse.SC_OK);
        } catch (Exception e) {
            log.error("跳转到核心支付平台出错", e);
        }
    }


    private String generateToSign(TreeMap<String, String> queryParamMap) {
        StringBuilder input = new StringBuilder();
        for (String key : queryParamMap.keySet()) {
            if (StringUtils.isBlank(queryParamMap.get(key))) {
                continue;
            }
            if (input.length() > 0) {
                input.append("&");
            }
            input.append(key).append("=").append(queryParamMap.get(key));
        }

        return input.toString();
    }


    private String generateAutoRedirectHtml(TreeMap<String, String> queryParamMap, String sign) {
        StringBuilder html = new StringBuilder();
        html.append("<html><body>\n");
        html.append("<form id=\"payForm\"  action=\"").append(platformUrl).append("\" method=\"post\">");
        html.append("<input type=\"hidden\" name=\"PartnerID\" value=\"").append(queryParamMap.get("PartnerID")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"TransactionID\" value=\"").append(queryParamMap.get("TransactionID")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"Body\" value=\"").append(queryParamMap.get("Body")).append("\" />\n");
//        html.append("<input type=\"hidden\" name=\"Description\" value=\"").append(queryParamMap.get("Description")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"Amount\" value=\"").append(queryParamMap.get("Amount")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"TimeExpire\" value=\"").append(queryParamMap.get("TimeExpire")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"Sign\" value=\"").append(sign).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"NotifyURL\" value=\"").append(queryParamMap.get("NotifyURL")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"ReturnURL\" value=\"").append(queryParamMap.get("ReturnURL")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"RequestTime\" value=\"").append(queryParamMap.get("RequestTime")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"Client\" value=\"").append(queryParamMap.get("Client")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"StrRandom\" value=\"").append(queryParamMap.get("StrRandom")).append("\" />\n");
        html.append("<input type=\"hidden\" name=\"DefaultOption\" value=\"").append(queryParamMap.get("DefaultOption")).append("\" />\n");
        html.append("<script language=\"javascript\">window.onload=function(){document.getElementById(\"payForm\").submit();}</script>\n");
        html.append("</form></body></html>");
        log.info(html.toString());
        return html.toString();
    }


    @Override
    public boolean verify(HttpServletRequest request, boolean isRedirect) {

        try {
            String returnJson = String.valueOf(request.getAttribute("returnJson"));
            JSONObject jsonReturn = new JSONObject(returnJson);
            String PartnerID = jsonReturn.optString("PartnerID");
            String RequestTime = jsonReturn.optString("RequestTime");
            String TransactionID = jsonReturn.optString("TransactionID");
            String ParnterTransactionID = jsonReturn.optString("ParnterTransactionID");
            String Body = jsonReturn.optString("Body");
            String Amount = String.format("%.2f", jsonReturn.optDouble("Amount"));
            String Status = jsonReturn.optString("Status");
            String Message = jsonReturn.optString("Message");
            String PaymentOption = jsonReturn.optString("PaymentOption");
            String PaymentTime = jsonReturn.optString("PaymentTime");
            String Sign = jsonReturn.optString("sign");
            String StrRandom = jsonReturn.optString("StrRandom");


            //需要排序，故使用TreeMap
            TreeMap<String, String> queryParamMap = new TreeMap<String, String>(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return o1.compareTo(o2);
                }
            });

            queryParamMap.put("PartnerID", PartnerID);
            queryParamMap.put("RequestTime", RequestTime);
            queryParamMap.put("TransactionID", TransactionID);
            queryParamMap.put("ParnterTransactionID", ParnterTransactionID);
            queryParamMap.put("Body", Body);
            queryParamMap.put("Amount", Amount);
            queryParamMap.put("Status", Status);
            queryParamMap.put("Message", Message);
            queryParamMap.put("PaymentOption", PaymentOption);
            queryParamMap.put("PaymentTime", PaymentTime);
            queryParamMap.put("StrRandom", StrRandom);
            log.info("pubkey:" + pubKey);
            return RsaUtil.doCheck(generateToSign(queryParamMap), Sign, pubKey, "UTF-8");
        } catch (Exception e) {
            log.error("接口返回JSON数据异常", e);
        }
        return false;
    }

    @Override
    public ResponseResult getResponseData(HttpServletRequest request) {
        ResponseResult responseResult = new ResponseResult();
        responseResult.setSuccess(false);
        String returnJson = "";
        try {
            BufferedReader br = null;
            br = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            String temp;
            while ((temp = br.readLine()) != null) {
                sb.append(temp);
            }
            br.close();
            returnJson = sb.toString();
            log.info("核心支付平台支付/退款返回数据,returnJson={}", returnJson);
            request.setAttribute("returnJson", returnJson);
            if (StringUtils.isNotBlank(returnJson)) {
                JSONObject jsonReturn = new JSONObject(returnJson);
                if (StringUtils.isNotBlank(jsonReturn.optString("RefundID"))) {//有这个代表是退款的
                    if ("SUCCESS".equals(jsonReturn.optString("Status"))) {  //退款成功
                        responseResult.setSuccess(true);
                    }
                    responseResult.setStatus(jsonReturn.optString("Status"));
                    responseResult.setFee(Double.parseDouble(jsonReturn.optString("RefundAmount")));
                    responseResult.setOrderno(jsonReturn.optString("RefundID"));
                    responseResult.setTradeno(jsonReturn.optString("TransactionID"));
                    responseResult.setErrMsg(jsonReturn.optString("Message"));
                } else {
                    if ("2".equals(jsonReturn.optString("Status"))) {  //不确定哪个代表返回成功~无语
                        responseResult.setSuccess(true);
                    }
                    responseResult.setFee(Double.parseDouble(jsonReturn.optString("Amount")));
                    responseResult.setOrderno(jsonReturn.optString("ParnterTransactionID"));
                    responseResult.setTradeno(jsonReturn.optString("TransactionID"));
                    responseResult.setErrMsg(jsonReturn.optString("Message"));
                    responseResult.setOption(jsonReturn.optString("PaymentOption"));
                }
            }
        } catch (IOException e) {
            log.error("接口返回JSON数据异常", e);
            responseResult.setErrMsg("接口返回JSON数据异常");
        }
        return responseResult;
    }

    /**
     * created by zoupeng on 2018/08/14 退款方法，暂时只支持核心平台退款
     *
     * @param:
     */
    @Override
    public void refundPolicy(Fee refundFee, Policy policy) {
        log.info("进入退款函数");
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        LocalDateTime now = LocalDateTime.now();
        //需要排序，故使用TreeMap
        TreeMap<String, String> queryParamMap = new TreeMap<>(String::compareTo);

        queryParamMap.put("PartnerID", partnerId);
        queryParamMap.put("TransactionID", refundFee.getFeeNo());
        queryParamMap.put("RefundID", refundFee.getOrderNo());
        queryParamMap.put("TotalAmount", String.format("%.2f", refundFee.getOrderAmount()));
        queryParamMap.put("RefundAmount", String.format("%.2f", -refundFee.getFee()));
        queryParamMap.put("PaymentOption", "alipay".equals(refundFee.getRemark()) ? "2" : "1");
        queryParamMap.put("StrRandom", RandomUtil.getRandom(20));
        queryParamMap.put("PolicyNo", StringUtils.isBlank(policy.getPolicyNo()) ? "NoPolicy" : policy.getPolicyNo());                  //具体支付方式
        queryParamMap.put("Timestamp", now.format(df));
        try {
            refundFee.setRequestStatus("2");
            String input = generateToSign(queryParamMap);
            log.info("需要签名字符串为：" + input);
            String sign = RsaUtil.sign(input, priKey);  //私钥签名;
            log.info("生成签名为：" + sign);
            queryParamMap.put("Sign", sign);
            if (StringUtils.isBlank(policy.getPolicyNo())) {
                queryParamMap.put("Reason", "AutoRefund");
            }
            queryParamMap.put("NotifyURL", refundNotifyUrl);

            String policyServerUrl = refundUrl;
            HttpResponse<String> returnMsg = HttpUtil.postData(policyServerUrl, "text", queryParamMap, null);
            log.info("Payment Gateway退款申请，返回状态为：" + returnMsg.statusCode() + ",返回信息为：" + returnMsg.body());
            if (returnMsg.statusCode() == 200) {
                refundFee.setRequestMsg(returnMsg.body());
                Map<String, Object> msg = JSONUtils.readJson2Map(returnMsg.body());
                if ("I8001".equals(msg.get("Code"))) {
                    refundFee.setRequestStatus("6");
                } else {
                    refundFee.setRequestStatus("3");
                }
            }
        } catch (Exception e) {
            refundFee.setRequestMsg("退款错误，本地异常：" + e);
            log.error("跳转到核心支付平台出错", e);
        }
    }

    @Override
    public boolean refundVerify(HttpServletRequest request) {
        try {
            String returnJson = String.valueOf(request.getAttribute("returnJson"));
            JSONObject jsonReturn = new JSONObject(returnJson);

            //需要排序，故使用TreeMap
            TreeMap<String, String> queryParamMap = new TreeMap<String, String>(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return o1.compareTo(o2);
                }
            });

            Iterator<String> iterator = jsonReturn.keys();
            String sign = "";
            while (iterator.hasNext()) {
                String keyTemp = iterator.next();
                if (!"Sign".equals(keyTemp)) {
                    queryParamMap.put(keyTemp, jsonReturn.getString(keyTemp));
                } else {
                    sign = jsonReturn.getString(keyTemp);
                }
            }

            log.info("pubkey:" + pubKey);
            log.info("待验签的字符串信息：" + generateToSign(queryParamMap));
            return RsaUtil.doCheck(generateToSign(queryParamMap), sign, pubKey, "UTF-8");
        } catch (Exception e) {
            log.error("接口返回JSON数据异常", e);
        }
        return false;
    }

}

package com.travelye.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * websocket 配置
 *
 * <AUTHOR> C.R.
 * @date 2024/2/18 16:37
 */
@Slf4j
public class WebsocketConfig implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry){
        // 客户端订阅消息的前缀：topic用来广播，user用来实现点对点
        registry.enableSimpleBroker("/topic", "/user");
        // 点对点发送前缀
        registry.setUserDestinationPrefix("/user/");
        // 客户端发送消息前缀
        registry.setApplicationDestinationPrefixes("/app");
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry){
        // 注册STOMP的endpoint，前端建立socket连接时，url(http://127.0.0.1:8080/ws)
        registry.addEndpoint("/ws").setAllowedOriginPatterns("*");
//        registry.addEndpoint("/ws").setAllowedOriginPatterns(PropUtil.getString("host"));
    }
}

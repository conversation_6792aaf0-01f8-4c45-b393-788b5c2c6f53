/**
 * 包名：com.wenhq.vo<br>
 * 文件：Planpricerule.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-27 上午10:24:31<br>
 * 
 * Current revision $Revision: 1.2 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: Planpricerule.java,v $
 * Revision 1.2  2011/02/24 23:20:54  wenc
 * *** empty log message ***
 *
 * Revision 1.1  2010/08/28 01:56:45  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.vo;

/**
 * <AUTHOR>
 *
 */
public class Planpricerule {
	private long planpriceruleid;
	private int days;
	private String adultPrice;
	private String minorPrice;
	private long planid;
	private int maxday;
	private int minday;
	private String planname;
	private int minage;
	private int maxage;
	private int planMinday;   //计划的允许投保最小天数
	private int planMaxday;		//计划的允许投保最多天数
	
	public int getPlanMinday() {
		return planMinday;
	}
	public void setPlanMinday(int planMinday) {
		this.planMinday = planMinday;
	}
	public int getPlanMaxday() {
		return planMaxday;
	}
	public void setPlanMaxday(int planMaxday) {
		this.planMaxday = planMaxday;
	}
	/**
	 * @return the planname
	 */
	public String getPlanname() {
		return planname;
	}
	/**
	 * @param planname the planname to set
	 */
	public void setPlanname(String planname) {
		this.planname = planname;
	}
	/**
	 * @return the planpriceruleid
	 */
	public long getPlanpriceruleid() {
		return planpriceruleid;
	}
	/**
	 * @param planpriceruleid the planpriceruleid to set
	 */
	public void setPlanpriceruleid(long planpriceruleid) {
		this.planpriceruleid = planpriceruleid;
	}
	/**
	 * @return the days
	 */
	public int getDays() {
		return days;
	}
	/**
	 * @param days the days to set
	 */
	public void setDays(int days) {
		this.days = days;
	}
	/**
	 * @return the price
	 */
	public String getAdultPrice() {
		return adultPrice;
	}
	/**
	 * @param price the price to set
	 */
	public void setAdultPrice(String adultPrice) {
		this.adultPrice = adultPrice;
	}
	
	public String getMinorPrice() {
		return minorPrice;
	}
	
	public void setMinorPrice(String minorPrice) {
		this.minorPrice = minorPrice;
	}
	/**
	 * @return the planid
	 */
	public long getPlanid() {
		return planid;
	}
	/**
	 * @param planid the planid to set
	 */
	public void setPlanid(long planid) {
		this.planid = planid;
	}
	/**
	 * @return the maxday
	 */
	public int getMaxday() {
		return maxday;
	}
	/**
	 * @param maxday the maxday to set
	 */
	public void setMaxday(int maxday) {
		this.maxday = maxday;
	}
	/**
	 * @return the minday
	 */
	public int getMinday() {
		return minday;
	}
	/**
	 * @param minday the minday to set
	 */
	public void setMinday(int minday) {
		this.minday = minday;
	}
	/**
	 * @return the minage
	 */
	public int getMinage() {
		return minage;
	}
	/**
	 * @param minage the minage to set
	 */
	public void setMinage(int minage) {
		this.minage = minage;
	}
	/**
	 * @return the maxage
	 */
	public int getMaxage() {
		return maxage;
	}
	/**
	 * @param maxage the maxage to set
	 */
	public void setMaxage(int maxage) {
		this.maxage = maxage;
	}

}

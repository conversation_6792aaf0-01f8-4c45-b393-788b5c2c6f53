/***********************************************************************
 * Module:  policymodify.java
 * Author:  wen
 * Purpose: Defines the Class policymodify
 ***********************************************************************/
package com.travelye.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @pdOid *************-4e53-bab9-e1b9e3f6d2a2
 */
@Data
public class Policymodify implements java.io.Serializable {
    private long policymodifyid;// 修改保单的ID
    private String operator;    // 操作者
    private LocalDateTime modifydate;    // 修改时间
    private double oldfee;        // 原有保费
    private double fee;            // 修改后的保费
    private long uid;            // 用户ID
    private long policyid;        // 保单ID

}

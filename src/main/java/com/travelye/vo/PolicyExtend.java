package com.travelye.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 航班信息
 */
@Data
public class PolicyExtend implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 航班所对应的保单Id
     */
    private long policyId;
    /**
     * 承运人编码
     */
    private String carrier;
    /**
     * 乘客电子客票号
     */
    private String eTicketNo;
    /**
     * 航班号
     */
    private String flightNo;
    /**
     * 起飞机场编号
     */
    private String originAirport;
    /**
     * 目的机场编号
     */
    private String destAirport;
    /**
     * 起飞时间
     */
    private LocalDateTime departureTime;
    /**
     * 索引值
     */
    private String externalIndex;

}

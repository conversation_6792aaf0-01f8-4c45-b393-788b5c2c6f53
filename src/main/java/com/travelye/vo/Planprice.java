/***********************************************************************
 * Module:  planprice.java
 * Author:  wen
 * Purpose: Defines the Class planprice
 ***********************************************************************/
package com.travelye.vo;

/** @pdOid 77296701-fcf5-44b0-b1b4-9068f138a32e */
public class Planprice  implements java.io.Serializable{
	private long planpriceid;
	private int days;
	private double adultPrice;
	private double minorPrice;
	private long planid;
	
	private int tag;

	/**
	 * @return the tag
	 */
	public int getTag() {
		return tag;
	}

	/**
	 * @param tag the tag to set
	 */
	public void setTag(int tag) {
		this.tag = tag;
	}

	/**
	 * @return the planpriceid
	 */
	public long getPlanpriceid() {
		return planpriceid;
	}

	/**
	 * @param planpriceid
	 *            the planpriceid to set
	 */
	public void setPlanpriceid(long planpriceid) {
		this.planpriceid = planpriceid;
	}

	/**
	 * @return the days
	 */
	public int getDays() {
		return days;
	}

	/**
	 * @param days
	 *            the days to set
	 */
	public void setDays(int days) {
		this.days = days;
	}

	/**
	 * @return the adultPrice
	 */
	public double getAdultPrice() {
		return adultPrice;
	}

	/**
	 * @param adultPrice
	 *            the adultPrice to set
	 */
	public void setAdultPrice(double adultPrice) {
		this.adultPrice = adultPrice;
	}
	
	public double getMinorPrice() {
		return minorPrice;
	}
	
	public void setMinorPrice(double minorPrice) {
		this.minorPrice = minorPrice;
	}
	/**
	 * @return the planid
	 */
	public long getPlanid() {
		return planid;
	}

	/**
	 * @param planid
	 *            the planid to set
	 */
	public void setPlanid(long planid) {
		this.planid = planid;
	}

}
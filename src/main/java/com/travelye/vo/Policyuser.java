/***********************************************************************
 * Module:  policyuser.java
 * Author:  wen
 * Purpose: Defines the Class policyuser
 ***********************************************************************/
package com.travelye.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.travelye.base.tools.PolicyUtil;
import com.travelye.policy.ruleEngine.RuleEngineAnnotation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @pdOid 623c7e22-be0f-4ffe-8f3e-ee5976f4692a
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Policyuser implements java.io.Serializable, Cloneable {
    public static final String RELATIONSHIP_SELF = "本人 Policyholder";
    public static final String RELATIONSHIP_SPOUSE = "配偶 Spouse";
    public static final String RELATIONSHIP_PARENTS = "父母 Parents";
    public static final String RELATIONSHIP_CHILD = "子女 Child";
    public static final String RELATIONSHIP_OTHER = "其他 Other";

    public static final String PDF_FILE_NAME_REGEX = "[^a-zA-Z\\u4e00-\\u9fa5\\\\s+]";

    public static final int SIGN_CONFIRMED = 1; //投保单已确认
    public static final int SIGN_NO_CONFIRMED = 0; //投保单未确认


    private long insuredID;        // 用户ID
    private long policyid;        // 保单编号
    private String productCode;    // 产品代码，WATTS指定
    private String planCode;    // 计划代码，WATTS指定
    private String typeCD;        // 被保险人类型，成人（18-80岁）=IND 儿童（1-17岁）=CHD
    private int insuredType;   // 投保人类型 0-个人 1-企业
    @RuleEngineAnnotation(name = "姓名")
    private String insuredname;    // 被保险人姓名
    private String insuredEnName; //被保险人英文名
    @RuleEngineAnnotation(name = "性别")
    private String gend;        // 被保险人性别
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @RuleEngineAnnotation(name = "出生日期")
    private LocalDate birthday;        // 被保险人生日
    @RuleEngineAnnotation(name = "证件类型")
    private String insuredIdNoType;    // 证件类型
    @RuleEngineAnnotation(name = "证件号码")
    private String insuredIdNo;    // 证件号码
    private String addressInfo;        // 被保险人地址
    @RuleEngineAnnotation(name = "邮箱")
    private String emailAddr;    // 邮件
    @RuleEngineAnnotation(name = "电话号码")
    private String homePhoneNo;    // 被保险人电话
    @RuleEngineAnnotation(name = "与投保人关系")
    private String relationship;    // 与投保险关系
    private String deathBeneficiary;// 身故受益人
    private List<Beneficiary> beneficiaryList; // 受益人列表
    private int policyStatus;        // 状态 1、新增；2、变更；3、取消
    private double premium;            // 保费
    private double originalPremium;   //原保费  非waats接口拼报文时用....
    /*
     * 如果是保单持有人，非被保险人，IsInsuredFlag=0
     * 如果是保单持有人，且是被保险人，IsInsuredFlag=1
     * 如果不是保单持有人，只是被保险人，InInsuredFlag=2
     * 一个保单必须有一个且只有一个保单持有人，可以有多个被保险人。
     * 一般情况下，第一被保险人如果是成年人，就是保单持有人。
     */
    @RuleEngineAnnotation(name = "身份", desc = "0-投保人，1-即时投保人也是被保险人，2-被保险人")
    private int isInsuredFlag;    //	是否投保人
    private String remark;// 备注

    private String birthdaystr; //	页面中传递的字符串
    private int modifyFlag;    //	标注该用户是否修改 ,0-没有修改,1-有修改
    private int recordStatus;    // 记录是否修改
    private int seq;  //在xml节点的顺序号； 携程对接时使用
    private String sequence;

    private String thirdPartyInsuredId; //第三方渠道对接是的insured标识号

    private int signConfirmStatus; //当前投保人/被保人 投保单签名确认情况 0 - 未确认 1 - 已确认
    private int age;

    //反洗钱项目新增字段
    private String nationalityCd;    //国家代码
    private String occupation;        //职业描述
    private String occupationCd;    //职业代码
    private String annualIncome;    //年收入
    private String companyName;        //工作单位
    private String identificationID;//身份证号码
    private LocalDate identificationIDExpiry;//身份证有效期
    private String countryName;
    private String province;
    private String city;
    private String district;
    private String genderCd;//性别 0-男 1-女

    private Map<String, String> ratingFactorMap;


    public Policyuser clone() {
        // 直接调用父类的clone()方法,返回克隆副本
        try {
            return (Policyuser) super.clone();
        } catch (CloneNotSupportedException e) {

        }
        return null;
    }

    public static String formatRelationShip(String relation) {
        String r = relation.toLowerCase();
        if (r.contains("本人") || r.contains("policyholder")) {
            return RELATIONSHIP_SELF;
        } else if (r.contains("配偶") || r.contains("spouse")) {
            return RELATIONSHIP_SPOUSE;
        } else if (r.contains("父母") || r.contains("parents")) {
            return RELATIONSHIP_PARENTS;
        } else if (r.contains("子女") || r.contains("child")) {
            return RELATIONSHIP_CHILD;
        } else {
            return RELATIONSHIP_OTHER;
        }
    }


    public String getInsuredFullName() {
        return insuredname + (StringUtils.isNotBlank(insuredEnName) ? (" " + insuredEnName) : "");
    }

    public String getInsuredNativeName() {
        return insuredname;
    }

    /*
    根据生效日和被保人生日判断被保人类型
     */
    public void getInsuredType(LocalDateTime e) {
        if (this.birthday.until(e, ChronoUnit.YEARS) < 18) {
            this.typeCD = "CHD";
        } else {
            this.typeCD = "IND";
        }
    }

    public List<Beneficiary> getBeneficiaryList() {
        List<Beneficiary> beneficiaryList = new ArrayList<>();
        if (StringUtils.isNotBlank(this.deathBeneficiary)) {
            String[] beneficiaryStrList = this.deathBeneficiary.split(",");
            for (int j = 0; j < beneficiaryStrList.length; j++) {
                String[] beneficiary = beneficiaryStrList[j].split(":");
                if (beneficiary.length > 4) {
                    Beneficiary b = new Beneficiary();
                    b.setInsuredname(beneficiary[0]);
                    b.setInsuredIdNoType(PolicyUtil.reversWAATSIDNoType(beneficiary[1]));
                    b.setInsuredIdNo(beneficiary[2]);
                    b.setRelationship(beneficiary[3]);
                    b.setProportion(Double.valueOf(beneficiary[4]));
                    beneficiaryList.add(b);
                }
            }
        }
        return CollectionUtils.isNotEmpty(beneficiaryList) ? beneficiaryList : null;
    }
}


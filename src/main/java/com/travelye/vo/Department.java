package com.travelye.vo;

import com.travelye.sjis.ajax.json.JSONArray;
import com.travelye.sjis.ajax.json.JSONException;
import com.travelye.sjis.ajax.json.JSONObject;

import java.util.*;


public class Department {
    private long departmentID;
    private String departmentName;
    private String departmentAbbName; //部门简称
    private long agencyID;
    private String departType;
    private String parent;
    private int status;
    private int visitStatus;  //在给用户授权时，设置是否已经可访问的标记
    private String departmentNo; //部门编号
    private String saleLocation;         //销售网点

    //json与前端交互时信息
    private String pageID;  //前台产生的id
    private String pagePID;//前台产生上级ID
    private long departmentParentID;  //前台传递的部门的上级ID
    private String existsData;       //是否存在数据

    private List<Long> actionIDList;   //功能权限列表
    private List<Users> userList;     //用户列表

    /**
     * 返回该部门的 子部门需要设置的parent的值
     *
     * @return
     */
    public String getChildParent() {
        return this.getParent() + this.getDepartmentID() + ",";
    }

    public List<Long> getActionIDList() {
        return actionIDList;
    }

    public void setActionIDList(List<Long> actionIDList) {
        this.actionIDList = actionIDList;
    }

    public List<Users> getUserList() {
        return userList;
    }

    public void setUserList(List<Users> userList) {
        this.userList = userList;
    }

    public String getPageID() {
        return pageID;
    }

    public void setPageID(String pageID) {
        this.pageID = pageID;
    }

    public String getPagePID() {
        return pagePID;
    }

    public void setPagePID(String pagePID) {
        this.pagePID = pagePID;
    }

    public long getDepartmentParentID() {
        return departmentParentID;
    }

    public void setDepartmentParentID(long departmentParentID) {
        this.departmentParentID = departmentParentID;
    }

    public String getDepartmentNo() {
        return departmentNo;
    }

    public void setDepartmentNo(String departmentNo) {
        this.departmentNo = departmentNo;
    }

    public String getSaleLocation() {
        return saleLocation;
    }

    public void setSaleLocation(String saleLocation) {
        this.saleLocation = saleLocation;
    }

    public long getDepartmentID() {
        return departmentID;
    }

    public void setDepartmentID(long departmentID) {
        this.departmentID = departmentID;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public long getAgencyID() {
        return agencyID;
    }

    public void setAgencyID(long agencyID) {
        this.agencyID = agencyID;
    }

    public String getDepartType() {
        return departType;
    }

    public void setDepartType(String departType) {
        this.departType = departType;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getVisitStatus() {
        return visitStatus;
    }

    public void setVisitStatus(int visitStatus) {
        this.visitStatus = visitStatus;
    }

    public String getDepartmentAbbName() {
        return departmentAbbName;
    }

    public void setDepartmentAbbName(String departmentAbbName) {
        this.departmentAbbName = departmentAbbName;
    }

    /**
     * 返回树形菜单的json格式，放到map中
     * 在部门管理中，显示树形菜单
     *
     * @return {"id":"0001","parentid":"","name":"旅游险"}
     * 参数department为与代理商是同一个的部门
     */
    public Map<String, Object> toTreeMenu(Department department) {
        Map<String, Object> map = new HashMap();
        map.put("id", String.valueOf(this.getDepartmentID()));
        map.put("name", this.getDepartmentName());
        map.put("abbrName", this.getDepartmentAbbName());
        //当部门ID与 公司的部门+部门ID  相等时，表示顶级部门，父id设置为空
        if (!(this.getParent().equals(department.getParent() + department.getDepartmentID() + ","))) {
            map.put("parentid", getParentID(this.getParent()));
        } else {
            map.put("parentid", "");
        }
        return map;
    }

    /**
     * 根据父ID字符串（0,1,2,44,）返回仅父ID（44）
     *
     * @param parent
     * @return
     */
    private String getParentID(String parent) {
        String[] ids = parent.split(",");
        return ids[ids.length - 1];
    }

    /**
     * 修改部门信息时供显示使用
     *
     * @return {"number":"部门编号","name":"部门全称","simple_name":"部门简称","sell":"销售网点"}
     */
    public Map toEdit() {
        Map map = new HashMap();
        map.put("number", this.getDepartmentNo());
        map.put("name", this.getDepartmentName());
        map.put("simple_name", this.getDepartmentAbbName());
        map.put("sell", this.getSaleLocation());
        map.put("ExistsData", this.getExistsData());
        return map;
    }

    /**
     * 修改时 从json中获取部门信息
     *
     * @param json
     * @throws JSONException
     */
    private Department loadByEdit(JSONObject departJson) throws JSONException {
        this.setDepartmentNo(departJson.getString("d_number"));
        setDepartmentName(departJson.getString("d_name"));
        setDepartmentAbbName(departJson.getString("d_simple_name"));
        setSaleLocation(departJson.getString("d_sell"));
        return this;
    }

    /**
     * 添加部门时 从json中获取部门信息
     *
     * @param json
     * @throws JSONException
     */
    private Department load(JSONObject departJson) throws JSONException {
        this.setDepartmentNo(departJson.getString("d_number"));
        setDepartmentName(departJson.getString("d_name"));
        setDepartmentAbbName(departJson.getString("d_simple_name"));
        setSaleLocation(departJson.getString("d_sell"));

        this.setPageID(departJson.getString("id"));
        this.setPagePID(departJson.getString("parentid"));
        this.setDepartmentParentID(departJson.getLong("oldid"));
        return this;
    }

    /**
     * 从json字符串转为部门的map集合
     *
     * @param jsonStr
     * @return
     * @throws JSONException
     */
    public Map<String, Department> load(String jsonStr) throws JSONException {
        Map<String, Department> map = new TreeMap<String, Department>();
        JSONObject jsonObject = new JSONObject(jsonStr);
        JSONArray departmentArr = jsonObject.getJSONArray("add");
        for (int i = 0; i < departmentArr.length(); i++) {
            JSONObject json = departmentArr.getJSONObject(i);
            //部门信息
            JSONObject departJson = json.getJSONObject("d_info");
            Department department = new Department().load(departJson);

            //用户的信息
            department.userList = new ArrayList<Users>();
            JSONArray userArr = json.getJSONArray("d_user");
            for (int j = 0; j < userArr.length(); j++) {
                JSONObject userJson = userArr.getJSONObject(j);
                Users user = new Users().load(userJson, true);
                department.userList.add(user);
            }
            //部门的权限ID
            department.actionIDList = new ArrayList<Long>();
            JSONArray actionIDs = json.getJSONArray("d_power");
            for (int j = 0; j < actionIDs.length(); j++) {
                department.actionIDList.add(actionIDs.getLong(j));
            }
            map.put(department.getPageID(), department);
        }
        return map;
    }

    /**
     * 从json字符串转为部门的map集合
     *
     * @param jsonStr
     * @return
     * @throws JSONException
     */
    public Map<String, Department> loadByEdit(String jsonStr) throws JSONException {
        Map<String, Department> map = new TreeMap<String, Department>();
        JSONObject jsonObject = new JSONObject(jsonStr).getJSONObject("edit");
        for (Iterator it = jsonObject.keys(); it.hasNext(); ) {
            String key = it.next().toString();
            long departmentID = Long.valueOf(key);
            JSONObject json = jsonObject.getJSONObject(key);
            //部门信息
            JSONObject departJson = json.getJSONObject("d_info");
            Department department = new Department().loadByEdit(departJson);
            department.setDepartmentID(departmentID);

            //用户的信息
            department.userList = new ArrayList<Users>();
            JSONArray userArr = json.getJSONArray("d_user");
            for (int j = 0; j < userArr.length(); j++) {
                JSONObject userJson = userArr.getJSONObject(j);
                Users user = new Users().load(userJson, true);
                department.userList.add(user);
            }
            //部门的权限ID
            department.actionIDList = new ArrayList<Long>();
            JSONArray actionIDs = json.getJSONArray("d_power");
            for (int j = 0; j < actionIDs.length(); j++) {
                department.actionIDList.add(actionIDs.getLong(j));
            }
            map.put(key, department);
        }
        return map;
    }

    /**
     * 从json字符串转为部门的list集合
     *
     * @param jsonStr
     * @return
     * @throws JSONException
     */
    public List<Long> loadByDelete(String jsonStr) throws JSONException {
        List<Long> list = new ArrayList<Long>();
        JSONArray jsonObject = new JSONObject(jsonStr).getJSONArray("del");
        for (int i = 0; i < jsonObject.length(); i++) {
            list.add(jsonObject.getLong(i));
        }
        Collections.sort(list);
        Collections.reverse(list);
        return list;
    }

    public String getExistsData() {
        return existsData;
    }

    public void setExistsData(String existsData) {
        this.existsData = existsData;
    }
}

package com.travelye.vo;

import java.time.LocalDateTime;

/**
 * Created by linchengdong on 2018/2/1.
 */
public class Export {
    private String biataCntryCd;
    private String sourceId;
    private String agencyName;
    private String ciataCntryCd;
    private String agencyPcc;
    private String s3BCode;
    private String huserName;
    private String policyNo;
    private String policyHolder;
    private LocalDateTime transactionDate;
    private LocalDateTime effectiveDate;
    private LocalDateTime expiryDate;
    private String type;
    private String days;
    private String productName;
    private String planName;
    private String rmb;
    private String transactionAmount;
    private String totalPremium;
    private String discount;
    private String  personNum;
    private String insuredname;
    private String insuredID;
    private String  status;
    private String  guserName;
    private String  kong;

    public String getBiataCntryCd() {
        return biataCntryCd;
    }

    public void setBiataCntryCd(String biataCntryCd) {
        this.biataCntryCd = biataCntryCd;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getAgencyName() {
        return agencyName;
    }

    public void setAgencyName(String agencyName) {
        this.agencyName = agencyName;
    }

    public String getCiataCntryCd() {
        return ciataCntryCd;
    }

    public void setCiataCntryCd(String ciataCntryCd) {
        this.ciataCntryCd = ciataCntryCd;
    }

    public String getAgencyPcc() {
        return agencyPcc;
    }

    public void setAgencyPcc(String agencyPcc) {
        this.agencyPcc = agencyPcc;
    }

    public String getS3BCode() {
        return s3BCode;
    }

    public void setS3BCode(String s3BCode) {
        this.s3BCode = s3BCode;
    }

    public String getHuserName() {
        return huserName;
    }

    public void setHuserName(String huserName) {
        this.huserName = huserName;
    }

    public String getPolicyNo() {
        return policyNo;
    }

    public void setPolicyNo(String policyNo) {
        this.policyNo = policyNo;
    }

    public String getPolicyHolder() {
        return policyHolder;
    }

    public void setPolicyHolder(String policyHolder) {
        this.policyHolder = policyHolder;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public LocalDateTime getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(LocalDateTime effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public LocalDateTime getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(LocalDateTime expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDays() {
        return days;
    }

    public void setDays(String days) {
        this.days = days;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getRmb() {
        return rmb;
    }

    public void setRmb(String rmb) {
        this.rmb = rmb;
    }

    public String getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(String transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    public String getTotalPremium() {
        return totalPremium;
    }

    public void setTotalPremium(String totalPremium) {
        this.totalPremium = totalPremium;
    }

    public String getDiscount() {
        return discount;
    }

    public void setDiscount(String discount) {
        this.discount = discount;
    }

    public String getPersonNum() {
        return personNum;
    }

    public void setPersonNum(String personNum) {
        this.personNum = personNum;
    }

    public String getInsuredname() {
        return insuredname;
    }

    public void setInsuredname(String insuredname) {
        this.insuredname = insuredname;
    }

    public String getInsuredID() {
        return insuredID;
    }

    public void setInsuredID(String insuredID) {
        this.insuredID = insuredID;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGuserName() {
        return guserName;
    }

    public void setGuserName(String guserName) {
        this.guserName = guserName;
    }

    public String getKong() {
        return kong;
    }

    public void setKong(String kong) {
        this.kong = kong;
    }
}

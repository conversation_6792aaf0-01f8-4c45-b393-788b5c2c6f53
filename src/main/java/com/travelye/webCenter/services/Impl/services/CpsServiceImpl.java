package com.travelye.webCenter.services.Impl.services;

import com.travelye.base.QueryResult;
import com.travelye.vo.IcCmsPartner;
import com.travelye.webCenter.daos.Impl.CpsDaoImpl;
import com.travelye.webCenter.services.Impl.CpsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by zzl on 2017/8/28.
 */
@Service
public class CpsServiceImpl implements CpsService {

    @Autowired
    private CpsDaoImpl cpsDao;


    @Override
    public void save(IcCmsPartner partner) {
        if (partner.getPartnerId() > 0) {
            cpsDao.update(partner);
        } else {
            cpsDao.insert(partner);
        }
    }

    @Override
    public QueryResult getCpsByCondition(String name, int pageNumber, int linesPerPage) {
        return cpsDao.getCpsByCondition(name, pageNumber, linesPerPage);
    }

    @Override
    public IcCmsPartner getPartnerById(String partnerIdStr, boolean isValid) {
        return cpsDao.getPartnerById(partnerIdStr, isValid);
    }

}

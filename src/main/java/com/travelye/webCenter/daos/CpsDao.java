package com.travelye.webCenter.daos;

import com.travelye.base.QueryResult;
import com.travelye.vo.IcCmsPartner;

/**
 * Created by zzl on 2017/8/28.
 */
public interface CpsDao {

    /*
    插入Cps
     */
    void insert(IcCmsPartner partner);


    /*
     更新Cps
    */
    void update(IcCmsPartner partner);

    /*
    根据cps名称或渠道名称查询cps
     */
    QueryResult getCpsByCondition(String name, int pageNumber, int linesPerPage);

    /**
     * 根据Id提取partner
     * @param partnerId
     * @return
     */
    IcCmsPartner getPartnerById(String partnerId,boolean isValid);
}

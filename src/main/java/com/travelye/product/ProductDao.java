/**
 * 包名：com.wenhq.product<br>
 * 文件：ProductDao.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-22 上午09:43:38<br>
 *
 * Current revision $Revision: 1.3 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: ProductDao.java,v $
 * Revision 1.3  2010/11/03 00:27:15  wenc
 * *** empty log message ***
 *
 * Revision 1.2  2010/08/28 01:56:46  wenc
 * *** empty log message ***
 *
 * Revision 1.1  2010/08/22 07:30:03  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.product;

import com.travelye.api.up.v2.module.ApiPlan;
import com.travelye.api.up.v2.module.ApiProduct;
import com.travelye.base.BaseDao;
import com.travelye.base.QueryResult;
import com.travelye.cps.vo.CpsProductIntro;
import com.travelye.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface ProductDao  extends BaseDao{
	/**
	 * 根据产品code获取产品
	 * @param productCode
	 * @return
	 */
	public Product getProductByCode(String productCode);

	/**
	 * 根据产品code获取产品
	 * @created by hs on 2018-03-14
	 * @param productNum
	 * @return
	 */
	public Product getProductByNum(String productNum);

	public Product getProduct(long productid);
	public Plan getPlan(long planid);

	public List<Plan> listPlan(long productid);
	public List<Planprice> listPlanprice(long planid);
	public List<Planpricerule> listPlanpricerule(long planid);

	public QueryResult listProduct(int start, int count, String branchid);

	List<ApiProduct> getApiProduct(long agencyId);

	List<ApiPlan> getApiPlanList(long productId);

	public void saveProduct(Product product);
	public void savePlan(Plan plan);
	public void savePlanPrice(Planprice model);
	public void savePlanPriceRule(Planpricerule model);

	public void deletePlanPrice(long planpriceid);
	public void deletePlanPriceRule(long planpriceruleid);

	public void savePlan1(Plan model) ;

	public  List<Plan> listPlan1();

	/*
	根据计划ID和渠道对接编码，获取计划，用于计划渠道对接编码验重
	 */
	public Plan getPlanNumsBySubCodeAndId(long planId,String subPlanCode,long productId);

	/*
     该计划，是否已经出单。用于删除计划之前的验证
    */
	public boolean isPlanExistInPolicy(long planId);


	/*
	删除计划
	 */
    public void deletePlanByPlanId(long planId);

	/*
	删除计划费率表（计算保费）
	 */
	public void deletePlanPriceRuleByPlanId(long planId);

	/*
        删除计划费率表（产品显示）
         */
	public void deletePlanPriceByPlanId(long planId);


	/**
	 * 根据产品ID，和是否有效取所有计划
	 * @param productid
	 * @param valid
	 * @return
	 */
	public List<Plan> listPlanByValid(long productid,String valid);


	/**
	 * 保存cps授权的计划
	 * @param list
	 */
	public void savePartnerPlanList( List<PartnerPlan> list);


	/**
	 * 删除cps的授权计划
	 * @param partnerId
	 */
	public void deletePartnerPlanByPartnerId( long partnerId);


	/**
	 * 取出cps所有的授权计划
	 *
	 */
	public List<PartnerPlan> listPartnerPlans(int partnerId,int agencyId);

	/**
	 * 取出cps授权产品
	 *
	 */
	public List<Product> listCpsValidProduct(long partnerId) ;


	/**
	 * 根据计划ID，取出cps授权产品
	 *
	 */
	public List<Plan> getCpsValidPlansByPid(long productId,int partnerId);


	List<CpsProductIntro> getCpsProductIntro(long productId);

	/**
	 * 取出cps分配的产品
	 * @param parntnerId
	 * @return
	 */
	List<Product> getCpsValidProduct(int parntnerId);


	/**
	 * 根据计划ID 提取有效产品
	 * @param planId
	 * @return
	 */
	public Product getValidProductByPlanId(long planId);

//	void saveOperator(int productId,int uid,String time);

	public void savePlanRatingfactor(List<PlanRatingfactor> ratingfactors);

	public void savePlanAge(List<PlanAge> planAges);

	public List<PlanRatingfactor> getPlanRatingfactorList(long planid);

	public void deletePlanRatingfactor(long planid);

	public void deletePlanAge(long planid);

	public PlanRatingfactor getPlanRatingfactorByRf(String ratingFactorStr);

	public List<PlanAge> getPlanAgeList(long planid);

	public List<String> getPlanRatingfactorStrs(long planId);

	public List<Plan> getCpsPlans(long cpspartnerId);

}

/**
 * 包名：com.wenhq.product<br>
 * 文件：ProductService.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-22 上午09:44:05<br>
 *
 * Current revision $Revision: 1.4 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: ProductService.java,v $
 * Revision 1.4  2010/11/03 00:27:15  wenc
 * *** empty log message ***
 *
 * Revision 1.3  2010/08/30 04:28:35  wenc
 * *** empty log message ***
 *
 * Revision 1.2  2010/08/28 01:56:46  wenc
 * *** empty log message ***
 *
 * Revision 1.1  2010/08/22 07:30:03  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.product;

import com.travelye.api.up.v2.module.ApiProduct;
import com.travelye.base.QueryResult;
import com.travelye.cps.vo.CpsProductIntro;
import com.travelye.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 *
 */
public interface ProductService {

	/**
	 * 根据产品code获取产品
	 * @param productCode
	 * @return
	 */
	public Product getProductByCode(String productCode);

	/**
	 * 根据产品code获取产品
	 * @created by hs on 2018-03-14
	 * @param productNum
	 * @return
	 */
	public Product getProductByNum(String productNum);

	public Product getProduct(long productid);
	public Plan getPlan(long planid);

	public List<Plan> listPlan(long productid);

	public QueryResult listProduct(int start, int count, String branchid);

	List<ApiProduct> getApiProductInfo(long agencyId);

		public void saveProduct(Product product);
	public void savePlan(Plan plan);
	/**根据计划id，先查询计费规则，计算计划的费用，然后保存
	 * @param planid
	 * <AUTHOR>
	 */
	public void savePrice(long planid);

	/** 保存一个新的产品 的所有信息
	 * plan 中还包含 有 计划的费用 规则
	 * 最后根据费用规则计算计划的保费，保存到费率表中
	 * @param product
	 * @param planList
	 * <AUTHOR>
	 */
	public void saveAll(Product product,List<Plan> planList);


     /*
      该计划，是否已经出单。用于删除计划之前的验证
     */
	public boolean isPlanExistInPolicy(long planId);


	/*
         删除计划的相关信息，价格等等
    */
	public void deletePlanInfosById(long planId);


	/**
	 * 根据产品ID，和是否有效取所有计划
	 * @param productid
	 * @param valid
	 * @return
	 */
	public List<Plan> listPlanByValid(long productid,String valid);


	/**
	 * 保存cps授权的计划
	 * @param list
	 */
	public void savePartnerPlanList( List<PartnerPlan> list);


	/**
	 * 取出cps所有的授权计划
	 *
	 */
	public List<PartnerPlan> listPartnerPlans(int partnerId,int agencyId);



	/**
	 * 取出cps授权产品
	 *
	 */
	public List<Product> listCpsValidProduct(long partnerId) ;


	/**
	 * 根据计划ID，取出cps授权产品
	 *
	 */
	public List<Plan> getCpsValidPlansByPid(long productId,int partnerId);

	/**
	 * 根据productId 和 cmsId/CPS Id 取得产品的介绍信息
	 */
	List<CpsProductIntro> getCpsProductIntro(long productId);

	/**
	 * 取出cps分配的产品
	 * @param parntnerId
	 * @return
	 */
	List<Product> getCpsValidProduct(int parntnerId);

	/**
	 * 根据计划ID 提取有效产品
	 * @param planId
	 * @return
	 */
	public Product getValidProductByPlanId(long planId);

//	void saveOperator(int productId,int uid);

	public void savePlanRatingfactor(List<PlanRatingfactor> ratingfactors);

	public void savePlanAge(List<PlanAge> planAges);

	public String getPlanRatingfactorList(long planid);

	public void savePlanRatingfactorDele(long planid);

	public void savePlanAgeDele(long planid);

	public PlanRatingfactor getPlanRatingfactorByRf(String ratingFactorStr);

	public List<PlanAge> getPlanAgeList(long planid);

	public List<String> getPlanRatingfactorStrs(long planId);

	public List<Plan> getCpsPlans(long cpspartnerId);
}

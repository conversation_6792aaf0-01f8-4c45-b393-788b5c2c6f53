package com.travelye.email;

import com.travelye.base.MessageException;
import com.travelye.email.sendcloud.SendCloudUtil;
import com.travelye.email.smtp.SmtpEmailUtil;
import com.travelye.generate.policy.vo.EmailMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 邮件发送适配器
 * 支持在SendCloud和SMTP之间切换
 */
@Slf4j
@Component
public class EmailSenderAdapter {

    /**
     * 发送邮件
     * 根据配置选择使用SendCloud或SMTP发送
     *
     * @param message 邮件消息
     * @throws MessageException 发送失败时抛出异常
     */
    public void sendEmail(EmailMessage message) throws MessageException {
        try {
            SmtpEmailUtil.sendEmail(message);
        } catch (Exception e) {
            log.error("邮件发送失败", e);
        }
    }

    /**
     * 强制使用SMTP发送邮件
     */
    public void sendEmailViaSmtp(EmailMessage message) throws MessageException {
        SmtpEmailUtil.sendEmail(message);
    }

    /**
     * 强制使用SendCloud发送邮件
     */
    public void sendEmailViaSendCloud(EmailMessage message) throws MessageException {
        SendCloudUtil.sendEmail(message);
    }

}

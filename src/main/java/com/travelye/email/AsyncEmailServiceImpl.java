package com.travelye.email;

import com.travelye.email.sendcloud.SendCloudUtil;
import com.travelye.email.EmailSenderAdapter;
import com.travelye.generate.policy.vo.EmailMessage;
import com.travelye.policy.PolicyDao;
import com.travelye.rabbitmaq.RabbitMessagePublisher;
import com.travelye.vo.EditableConfig;
import com.travelye.vo.Policy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Collections;

@Service
@Slf4j
public class AsyncEmailServiceImpl implements AsyncEmailService {

    final EmailService emailService;
    final PolicyDao policyDao;
    private final RabbitMessagePublisher rabbitMessagePublisher;
    private final EmailSenderAdapter emailSenderAdapter;

    public AsyncEmailServiceImpl(EmailService emailService, PolicyDao policyDao, RabbitMessagePublisher rabbitMessagePublisher, EmailSenderAdapter emailSenderAdapter) {
        this.emailService = emailService;
        this.policyDao = policyDao;
        this.rabbitMessagePublisher = rabbitMessagePublisher;
        this.emailSenderAdapter = emailSenderAdapter;
    }


    @Override
    @Async("emailSmsExecutor")
    public void sendConfirmEmailAsync(Long policyId, Long insuredId, String email, String baseUrl) {
        emailService.sendConfirmEmail(policyId, insuredId, email, baseUrl);
    }

    @Override
    public void sendPolicyEmailAsync(Policy policy, String email, int type) {
        rabbitMessagePublisher.sendPolicyPdfEmail(policy.getPolicyID());
//        emailService.sendPolicyEmail(policy, email, type);
    }

    @Override
    @Async("emailSmsExecutor")
    public void sendAlertEmail(String title, String content) {
        try {
            EditableConfig editableConfig = policyDao.getConfigByName("AlertRecipientEmail");
            if (editableConfig != null) {
                String alertRecipientEmail = editableConfig.getConfigValue();
                //警告邮件给TY Support Team
                EmailMessage e = new EmailMessage();
                e.setTitle(title);
                e.setContent(content);
                e.setEmailList(Collections.singletonList(alertRecipientEmail));
                emailSenderAdapter.sendEmail(e);
            }
        } catch (Exception e) {
            log.error("给 TY IT SUPPORT 发送邮件失败", e);
        }
    }

}

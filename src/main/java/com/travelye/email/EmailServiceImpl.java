package com.travelye.email;

import com.travelye.base.MessageException;
import com.travelye.base.SjisConstant;
import com.travelye.base.tools.JSONUtils;
import com.travelye.base.tools.PropUtil;
import com.travelye.email.sendcloud.SendCloudUtil;
import com.travelye.email.EmailSenderAdapter;
import com.travelye.generate.policy.vo.EmailMessage;
import com.travelye.policy.PolicyDao;
import com.travelye.policy.PolicyService;
import com.travelye.product.ProductService;
import com.travelye.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.travelye.email.EmailRecord.*;

@Service
@Slf4j
public class EmailServiceImpl implements EmailService {
    public final static int RECIPIENT_TYPE_APPLICANT = 1;
    public final static int RECIPIENT_TYPE_INSURED = 2;
    public final static int RECIPIENT_TYPE_ALL = 3;

    private final static DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final PolicyService policyService;
    private final ProductService productService;
    private final PolicyDao policyDao;
    private final EmailSenderAdapter emailSenderAdapter;

    private final String confirmURI = PropUtil.getString("application.confirmURI");

    public EmailServiceImpl(PolicyService policyService, ProductService productService, PolicyDao policyDao, EmailSenderAdapter emailSenderAdapter) {
        this.policyService = policyService;
        this.productService = productService;
        this.policyDao = policyDao;
        this.emailSenderAdapter = emailSenderAdapter;
    }

    @Override
    @EmailRecordAnnotation(description = "投保单确认", type = EMAIL_TYPE_APPLICATION, moduleId = "#insuredId")
    public Map<String, Object> sendConfirmEmail(Long policyId, Long insuredId, String email, String baseUrl) {
        Map<String, Object> result = new HashMap<>();
        try {
            EmailMessage e = setEmailInfo(policyId, insuredId, email, "confirmation", baseUrl);
            e.setEmailTemplateName("ty_application_confirmation");
            emailSenderAdapter.sendEmail(e);
            result.put("code", 1);
            result.put("emailList", e.getEmailList());
        } catch (IllegalArgumentException i) {
            log.error("发送投保单确认邮件失败", i);
            result.put("code", 0);
            result.put("msg", i.getMessage());
        } catch (Exception e) {
            log.error("发送投保单确认邮件失败", e);
            result.put("code", 0);
            result.put("msg", "系统错误");
        }
        return result;
    }

    @Override
    public Map<String, Object> sendPolicyEmailSilent(Policy policy, String email, int type) {
        return sendPolicyEmail(policy, email, type);
    }

    @Override
    @EmailRecordAnnotation(description = "电子保单", type = EMAIL_TYPE_POLICY, moduleId = "#policy.policyID")
    public Map<String, Object> sendRecordedPolicyEmail(Policy policy, String email, int type) {
        return sendPolicyEmail(policy, email, type);
    }

    private Map<String, Object> sendPolicyEmail(Policy policy, String email, int type) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> customParams = new HashMap<>();
        try {
            EmailMessage emailMessage = new EmailMessage();
            Product product = productService.getProduct(policy.getProductID());
            Plan plan = productService.getPlan(policy.getPlanID());
            Assert.isTrue(policy.validate(), "无效保单，不发送电子保单通知邮件");
            Assert.notNull(product, "产品信息错误");
            Assert.notNull(plan, "产品信息错误");
            List<String> emailList = new ArrayList<>();
            if (StringUtils.isNotBlank(email)) {
                emailList.add(email);
            } else {
                if (type == RECIPIENT_TYPE_APPLICANT) {
                    Policyuser applicant = policyService.getPolicyHolder(policy.getPolicyID());
                    if (StringUtils.isNotBlank(applicant.getEmailAddr())) {
                        emailList.add(applicant.getEmailAddr());
                    }
                } else if (type == RECIPIENT_TYPE_INSURED) {
                    List<Policyuser> insuredList = policyService.getPolicyUserList(policy.getPolicyID()).stream().filter(i -> i.getIsInsuredFlag() != 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(insuredList)) {
                        for (Policyuser insured : insuredList) {
                            if (StringUtils.isNotBlank(insured.getEmailAddr())) {
                                emailList.add(insured.getEmailAddr());
                            }
                        }
                    }
                } else if (type == RECIPIENT_TYPE_ALL) {
                    List<Policyuser> policyuserList = policyService.getPolicyUserList(policy.getPolicyID());
                    if (CollectionUtils.isNotEmpty(policyuserList)) {
                        for (Policyuser policyuser : policyuserList) {
                            if (StringUtils.isNotBlank(policyuser.getEmailAddr())) {
                                emailList.add(policyuser.getEmailAddr());
                            }
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(emailList)) {
                log.warn("邮箱地址为空，不发送");
                result.put("code", -1);
                result.put("msg", "邮箱为空");
                return result;
            } else {
                emailList = emailList.stream().distinct().collect(Collectors.toList());
            }

            emailMessage.setEmailList(emailList);
            result.put("emailList", emailMessage.getEmailList());
            customParams.put("to", emailList);
            if (policy.getApplicationSignStatus() == Policy.SIGN_NO_NEED_CONFIRMED) {
                emailMessage.setEmailTemplateName("ty_internet_policy_notify");
            } else {
                emailMessage.setEmailTemplateName("ty_offline_policy_notify");
                Map<String, Object> sub = Map.of("%applicationPdfUrl%", Collections.singletonList(PropUtil.getString("host") + "/policy/applicationPdf?key=" + policy.getPolicyOkey()));

                customParams.put("sub", sub);
            }

            emailMessage.setCustomParams(JSONUtils.toJson(customParams));
            Map<String, byte[]> attachments = new HashMap<>();
            attachments.put(policy.getPolicyNo() + ".pdf", policyService.getOssPolicyPdf(policy));
            File termFile = new File(SjisConstant.root_path + product.getTerms());
            if (termFile.exists()) {
                attachments.put(product.getProductname() + ".pdf", FileUtils.readFileToByteArray(new File(SjisConstant.root_path + product.getTerms())));
            }
            emailMessage.setAttachments(attachments);
            emailSenderAdapter.sendEmail(emailMessage);
            result.put("code", 1);
            result.put("msg", "发送成功");
        } catch (IllegalArgumentException i) {
            log.error("发送保单邮件失败", i);
            result.put("code", 0);
            result.put("msg", i.getMessage());
        } catch (Exception e) {
            log.error("发送保单邮件失败", e);
            result.put("code", 0);
            result.put("msg", "系统错误");
        }
        return result;
    }


    @Override
    @EmailRecordAnnotation(description = "支付通知", type = EMAIL_TYPE_PAY_NOTIFY, moduleId = "#policyId")
    public Map<String, Object> sendPayNotifyEmail(Long policyId, Long insuredId, String email, String baseUrl) {
        Map<String, Object> result = new HashMap<>();
        try {
            EmailMessage e = setEmailInfo(policyId, insuredId, email, "pay", baseUrl);
            e.setEmailTemplateName("ty_pay_notify");
            emailSenderAdapter.sendEmail(e);
            result.put("code", 1);
            result.put("msg", "发送成功");
            result.put("emailList", e.getEmailList());
        } catch (IllegalArgumentException i) {
            log.error("发送支付通知邮件失败", i);
            result.put("code", 0);
            result.put("msg", i.getMessage());
        } catch (Exception e) {
            log.error("发送支付通知邮件失败", e);
            result.put("code", 0);
            result.put("msg", "系统错误");
        }
        return result;
    }

    private EmailMessage setEmailInfo(Long policyId, Long insuredId, String email, String type, String baseUrl) {

        EmailMessage e = new EmailMessage();

        Policy policy = policyService.getPolicy(policyId);
        Policyuser u = policyService.getPolicyUser(insuredId);
        Assert.notNull(policy, "无此投保记录");
        Product product = productService.getProduct(policy.getProductID());
        Plan plan = productService.getPlan(policy.getPlanID());
        Assert.notNull(product, "产品信息错误");
        Assert.notNull(plan, "产品信息错误");
        Map<String, Object> sub = new HashMap<>();
        sub.put("%applicant%", Collections.singletonList((policy.isToC() || u.getIsInsuredFlag() == 2 ? policy.getPolicyholder() : "业务员")));
        sub.put("%productName%",
                Collections.singletonList(
                        product.getProductname() + "-" + plan.getPlanname()
                                + "（"
                                + policy.getEffectiveDate().format(dateTimeFormatter)
                                + " 至 "
                                + policy.getExpiryDate().format(dateTimeFormatter) + "）"

                )
        );
        sub.put("%effectiveDate%", Collections.singletonList(policy.getEffectiveDate().format(dateTimeFormatter)));

        String module = policy.isToC() && u.getIsInsuredFlag() == 0 ? "order" : "policy";
        long moduleId = policy.isToC() && u.getIsInsuredFlag() == 0 ? policy.getOrderId() : policy.getPolicyID();

        if ("confirmation".equals(type)) {
            String confirmUrl = baseUrl + confirmURI
                    + "?module=" + module
                    + "&moduleId=" + moduleId
                    + "&insuredId=" + insuredId;
            sub.put("%signUrl%", Collections.singletonList(confirmUrl));
            e.setQrCodeMessage(confirmUrl);
        }
        if ("pay".equals(type)) {
            String payUrl = baseUrl + "/payCenter/orderIndex?payModule=" + module + "&payModuleId=" + moduleId;
            sub.put("%payUrl%", Collections.singletonList(payUrl));
            e.setQrCodeMessage(payUrl);
        }

        Map<String, Object> customParams = new HashMap<>();
        customParams.put("sub", sub);

        if (StringUtils.isNotBlank(email)) {
            customParams.put("to", Collections.singletonList(email));
            e.setEmailList(Collections.singletonList(email));
        } else if (insuredId != 0) {
            Policyuser insured = policyService.getPolicyUser(insuredId);
            Assert.hasLength(insured.getEmailAddr(), "无目标邮箱，停止发送");
            customParams.put("to", Collections.singletonList(insured.getEmailAddr()));
            e.setEmailList(Collections.singletonList(insured.getEmailAddr()));
        }
        e.setCustomParams(JSONUtils.toJson(customParams));
        return e;
    }


    @Override
    public void saveEmailSendRecord(EmailRecord emailRecord) {
        policyDao.saveEmailSendHistory(emailRecord);
    }

    @Override
    public Map<String, Object> sendEmail(String email, String content, String title) {
        Map<String, Object> result = new HashMap<>();
        result.put("error", 1);
        result.put("msg", "发送成功");
        try {
            EmailMessage e = new EmailMessage();
            e.setTitle(title);
            e.setContent(content);
            e.setEmailList(Collections.singletonList(email));
            emailSenderAdapter.sendEmail(e);
        } catch (MessageException e) {
            result.put("error", 0);
            result.put("msg", e.getMessage());
        } catch (Exception e) {
            result.put("error", 0);
            result.put("msg", "发送失败");
        }
        return result;
    }

}

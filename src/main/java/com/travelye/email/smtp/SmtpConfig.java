package com.travelye.email.smtp;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SMTP邮件配置类
 */
@Data
public class SmtpConfig {

    /**
     * SMTP服务器地址
     */
    private String host = "smtp.partner.outlook.cn";

    /**
     * SMTP服务器端口
     */
    private String port = "587";

    /**
     * SMTP用户名
     */
    private String username = "<EMAIL>";

    /**
     * SMTP密码
     */
    private String password = "Retail@Test@0725";

    /**
     * 发件人邮箱
     */
    private String from = "<EMAIL>";

    /**
     * 发件人名称
     */
    private String fromName = "苏黎世保险_旅行险";

    /**
     * 是否启用认证
     */
    private boolean auth = true;

    /**
     * 是否启用STARTTLS
     */
    private boolean starttlsEnable = true;

    /**
     * 是否启用SSL
     */
    private boolean sslEnable = false;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 30000;

    /**
     * 读取超时时间（毫秒）
     */
    private int timeout = 30000;

    /**
     * 写入超时时间（毫秒）
     */
    private int writeTimeout = 30000;
}

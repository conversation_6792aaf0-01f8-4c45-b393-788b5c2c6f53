package com.travelye.email.smtp;

import com.travelye.base.MessageException;
import com.travelye.base.tools.QrCodeUtil;
import com.travelye.generate.policy.vo.EmailMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.UnsupportedEncodingException;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * SMTP邮件发送工具类
 * 替代SendCloudUtil，使用标准SMTP协议发送邮件
 */
@Slf4j
public class SmtpEmailUtil {

    private static final SmtpConfig smtpConfig = new SmtpConfig();

    /**
     * 发送邮件
     *
     * @param message 邮件消息对象
     * @throws MessageException 发送失败时抛出异常
     */
    public static void sendEmail(EmailMessage message) throws MessageException {
        try {
            // 创建邮件会话
            Session session = createMailSession();

            // 创建邮件消息
            MimeMessage mimeMessage = createMimeMessage(session, message);

            // 发送邮件
            Transport.send(mimeMessage);

            log.info("SMTP邮件发送成功，收件人: {}, 主题: {}",
                    String.join(";", message.getEmailList()),
                    message.getTitle());

        } catch (Exception e) {
            log.error("SMTP邮件发送失败", e);
            throw new MessageException("邮件发送失败: " + e.getMessage());
        }
    }

    /**
     * 创建邮件会话
     */
    private static Session createMailSession() {
        Properties props = new Properties();

        // 基本SMTP配置
        props.put("mail.smtp.host", smtpConfig.getHost());
        props.put("mail.smtp.port", smtpConfig.getPort());
        props.put("mail.smtp.auth", String.valueOf(smtpConfig.isAuth()));

        // 超时配置
        props.put("mail.smtp.connectiontimeout", String.valueOf(smtpConfig.getConnectionTimeout()));
        props.put("mail.smtp.timeout", String.valueOf(smtpConfig.getTimeout()));
        props.put("mail.smtp.writetimeout", String.valueOf(smtpConfig.getWriteTimeout()));

        // TLS/SSL配置
        if (smtpConfig.isStarttlsEnable()) {
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
        }

        if (smtpConfig.isSslEnable()) {
            props.put("mail.smtp.ssl.enable", "true");
            props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.put("mail.smtp.socketFactory.port", smtpConfig.getPort());
        }

        // 创建认证器
        Authenticator authenticator = null;
        if (smtpConfig.isAuth()) {
            authenticator = new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(smtpConfig.getUsername(), smtpConfig.getPassword());
                }
            };
        }

        return Session.getInstance(props, authenticator);
    }

    /**
     * 创建MIME邮件消息
     */
    private static MimeMessage createMimeMessage(Session session, EmailMessage message)
            throws MessagingException, UnsupportedEncodingException {

        MimeMessage mimeMessage = new MimeMessage(session);

        // 设置发件人
        mimeMessage.setFrom(new InternetAddress(smtpConfig.getFrom(), smtpConfig.getFromName(), "UTF-8"));

        // 设置收件人
        InternetAddress[] toAddresses = message.getEmailList().stream()
                .map(email -> {
                    try {
                        return new InternetAddress(email);
                    } catch (AddressException e) {
                        log.warn("无效的邮箱地址: {}", email);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toArray(InternetAddress[]::new);

        if (toAddresses.length == 0) {
            throw new MessagingException("没有有效的收件人地址");
        }

        mimeMessage.setRecipients(Message.RecipientType.TO, toAddresses);

        // 设置主题
        if (StringUtils.isNotBlank(message.getTitle())) {
            mimeMessage.setSubject(message.getTitle(), "UTF-8");
        }

        // 创建邮件内容
        createMessageContent(mimeMessage, message);

        return mimeMessage;
    }

    /**
     * 创建邮件内容（支持HTML、附件、嵌入图片）
     */
    private static void createMessageContent(MimeMessage mimeMessage, EmailMessage message)
            throws MessagingException {

        boolean hasAttachments = message.getAttachments() != null && !message.getAttachments().isEmpty();
        boolean hasQrCode = StringUtils.isNotBlank(message.getQrCodeMessage());
        boolean hasHtmlContent = StringUtils.isNotBlank(message.getContent());

        if (!hasAttachments && !hasQrCode) {
            // 简单邮件，只有文本/HTML内容
            if (hasHtmlContent) {
                mimeMessage.setContent(message.getContent(), "text/html; charset=UTF-8");
            } else {
                mimeMessage.setText("", "UTF-8");
            }
        } else {
            // 复杂邮件，包含附件或嵌入图片
            MimeMultipart multipart = new MimeMultipart("mixed");

            // 添加邮件正文
            if (hasHtmlContent || hasQrCode) {
                MimeBodyPart contentPart = createContentPart(message);
                multipart.addBodyPart(contentPart);
            }

            // 添加附件
            if (hasAttachments) {
                addAttachments(multipart, message.getAttachments());
            }

            mimeMessage.setContent(multipart);
        }
    }

    /**
     * 创建邮件正文部分（支持嵌入二维码）
     */
    private static MimeBodyPart createContentPart(EmailMessage message) throws MessagingException {
        MimeBodyPart contentPart = new MimeBodyPart();

        boolean hasQrCode = StringUtils.isNotBlank(message.getQrCodeMessage());
        boolean hasHtmlContent = StringUtils.isNotBlank(message.getContent());

        if (hasQrCode && hasHtmlContent) {
            // HTML内容 + 嵌入二维码
            MimeMultipart related = new MimeMultipart("related");

            // HTML内容
            MimeBodyPart htmlPart = new MimeBodyPart();
            htmlPart.setContent(message.getContent(), "text/html; charset=UTF-8");
            related.addBodyPart(htmlPart);

            // 嵌入二维码
            MimeBodyPart qrCodePart = new MimeBodyPart();
            try {
                byte[] qrCodeBytes = QrCodeUtil.QREncode(message.getQrCodeMessage());
                DataSource qrCodeDataSource = new ByteArrayDataSource(qrCodeBytes, "image/png");
                qrCodePart.setDataHandler(new DataHandler(qrCodeDataSource));
                qrCodePart.setHeader("Content-ID", "<qrCode>");
                qrCodePart.setDisposition(Part.INLINE);
                related.addBodyPart(qrCodePart);
            } catch (Exception e) {
                log.warn("生成二维码失败: {}", e.getMessage());
            }

            contentPart.setContent(related);
        } else if (hasHtmlContent) {
            // 只有HTML内容
            contentPart.setContent(message.getContent(), "text/html; charset=UTF-8");
        } else if (hasQrCode) {
            // 只有二维码
            try {
                byte[] qrCodeBytes = QrCodeUtil.QREncode(message.getQrCodeMessage());
                DataSource qrCodeDataSource = new ByteArrayDataSource(qrCodeBytes, "image/png");
                contentPart.setDataHandler(new DataHandler(qrCodeDataSource));
                contentPart.setDisposition(Part.INLINE);
            } catch (Exception e) {
                log.warn("生成二维码失败: {}", e.getMessage());
                contentPart.setText("", "UTF-8");
            }
        } else {
            contentPart.setText("", "UTF-8");
        }

        return contentPart;
    }

    /**
     * 添加附件
     */
    private static void addAttachments(MimeMultipart multipart, Map<String, byte[]> attachments)
            throws MessagingException {

        for (Map.Entry<String, byte[]> entry : attachments.entrySet()) {
            String fileName = entry.getKey();
            byte[] fileData = entry.getValue();

            MimeBodyPart attachmentPart = new MimeBodyPart();
            DataSource dataSource = new ByteArrayDataSource(fileData, "application/octet-stream");
            attachmentPart.setDataHandler(new DataHandler(dataSource));

            try {
                // 设置附件文件名，支持中文
                attachmentPart.setFileName(MimeUtility.encodeText(fileName, "UTF-8", "B"));
            } catch (UnsupportedEncodingException e) {
                log.warn("编码附件文件名失败: {}", fileName);
                attachmentPart.setFileName(fileName);
            }

            attachmentPart.setDisposition(Part.ATTACHMENT);
            multipart.addBodyPart(attachmentPart);
        }
    }
}

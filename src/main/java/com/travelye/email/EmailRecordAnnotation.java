package com.travelye.email;

import java.lang.annotation.*;

/**
 * 邮件发送记录入库注解
 *
 * <AUTHOR> C.R.
 * @date 2023/07/22 00:00
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface EmailRecordAnnotation {
    // 内容描述
    String description() default "";

    // 邮件类型
    int type() default 0;

    // 业务数据ID
    String moduleId() default "";

    // 执行结果
    int success() default 0;

}

package com.travelye.block;

import com.travelye.vo.Block;

import java.util.List;

/**
 * Created by zzl on 2017/8/25.
 */
public interface BlockDao {

    /**
     * 查询所有产品板块
     * @return
     */
    List<Block> queryBlocklist();


    /**
     * 根据BlockId获取产品块儿
     * @param blockId
     * @return
     */
    Block getBlockById(int blockId);


    void update(Block block);

    void insert(Block block);

    /**
     * 获取代理商的授权产品板块
     * @return
     */
    List<Block> getBlockListByPartnerId(int partnerId,boolean isMobile);
}

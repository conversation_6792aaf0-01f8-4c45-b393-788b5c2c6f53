package com.travelye.bill;

public class SPCommission {
	private String newSingular;		// 新增数量(保单/被保险人)
	private String newPremium; 		// 新增保费(保单/被保险人)
	private String modifySingular;	// 批改数量(保单/被保险人)
	private String modifyPremium;	// 批改保费(保单/被保险人)
	private String cancelSingular;	// 取消数量(保单/被保险人)
	private String cancelPremium;	// 取消保费 (保单/被保险人)
	private String totalSingular;	// 总数量(保单/被保险人)
	private String totalPremium;	// 总保费(保单/被保险人)
	/**
	 * @param newSingular the newSingular to set
	 */
	public void setNewSingular(String newSingular) {
		this.newSingular = newSingular;
	}
	/**
	 * @return the newSingular
	 */
	public String getNewSingular() {
		return newSingular;
	}
	/**
	 * @param newPremium the newPremium to set
	 */
	public void setNewPremium(String newPremium) {
		this.newPremium = newPremium;
	}
	/**
	 * @return the newPremium
	 */
	public String getNewPremium() {
		return newPremium;
	}
	/**
	 * @param modifySingular the modifySingular to set
	 */
	public void setModifySingular(String modifySingular) {
		this.modifySingular = modifySingular;
	}
	/**
	 * @return the modifySingular
	 */
	public String getModifySingular() {
		return modifySingular;
	}
	/**
	 * @param modifyPremium the modifyPremium to set
	 */
	public void setModifyPremium(String modifyPremium) {
		this.modifyPremium = modifyPremium;
	}
	/**
	 * @return the modifyPremium
	 */
	public String getModifyPremium() {
		return modifyPremium;
	}
	/**
	 * @param cancelSingular the cancelSingular to set
	 */
	public void setCancelSingular(String cancelSingular) {
		this.cancelSingular = cancelSingular;
	}
	/**
	 * @return the cancelSingular
	 */
	public String getCancelSingular() {
		return cancelSingular;
	}
	/**
	 * @param cancelPremium the cancelPremium to set
	 */
	public void setCancelPremium(String cancelPremium) {
		this.cancelPremium = cancelPremium;
	}
	/**
	 * @return the cancelPremium
	 */
	public String getCancelPremium() {
		return cancelPremium;
	}
	/**
	 * @param totalSingular the totalSingular to set
	 */
	public void setTotalSingular(String totalSingular) {
		this.totalSingular = totalSingular;
	}
	/**
	 * @return the totalSingular
	 */
	public String getTotalSingular() {
		return totalSingular;
	}
	/**
	 * @param totalPremium the totalPremium to set
	 */
	public void setTotalPremium(String totalPremium) {
		this.totalPremium = totalPremium;
	}
	/**
	 * @return the totalPremium
	 */
	public String getTotalPremium() {
		return totalPremium;
	}

}

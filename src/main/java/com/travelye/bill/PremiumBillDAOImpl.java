package com.travelye.bill;

import com.travelye.base.BaseJdbcDaoImpl;
import com.travelye.base.QueryResult;
import com.travelye.base.tools.JdbcUtil;
import com.travelye.security.session.SecurityUser;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository
public class PremiumBillDAOImpl extends BaseJdbcDaoImpl implements IPremiumBillDAO {
	/**
	 *
	 */
	public void savePremiumBill(PremiumBill premiumBill) {

		String sql = "INSERT INTO tblTransaction (agencyID, policyID, Status," +
				"transactionAmount, commissionRate, changesNumberInsured, "
				+ "transactionDate, summary) VALUES ("
				+ JdbcUtil.getNumberValue(premiumBill.getAgencyID()) + ","
				+ JdbcUtil.getNumberValue(premiumBill.getPolicyID()) + ","
				+ JdbcUtil.getNumberValue(premiumBill.getStatus()) + ","
				+ JdbcUtil.getNumberValue(premiumBill.getTransactionAmount()) + ","
				+ JdbcUtil.getNumberValue(premiumBill.getCommissionRate()) + ","
				+ JdbcUtil.getNumberValue(premiumBill.getChangesNumberInsured()) + ","
				+ JdbcUtil.getDateTime(premiumBill.getTransactionDate()) + ","
				+ JdbcUtil.getStringValue(premiumBill.getSummary()) + ")";
		this.execute(sql);
	}

	public void savePremiumBillDetail(PremiumBillDetail premiumBillDetail) {
		String sql = "INSERT INTO tblTransactionDetail (agencyID, policyID,"
		+ "insuredID, Status, transactionAmount, transactionDate) VALUES ("
		+ JdbcUtil.getNumberValue(premiumBillDetail.getAgencyID()) + ","
		+ JdbcUtil.getNumberValue(premiumBillDetail.getPolicyID()) + ","
		+ JdbcUtil.getNumberValue(premiumBillDetail.getInsuredID()) + ","
		+ JdbcUtil.getNumberValue(premiumBillDetail.getStatus()) + ","
		+ JdbcUtil.getNumberValue(premiumBillDetail.getTransactionAmount()) + ","
		+ JdbcUtil.getDateTime(premiumBillDetail.getTransactionDate()) + ")";
		this.execute(sql);
	}
	/**
	 *
	 */
	public QueryResult queryPremiumBill(Condition condition,SecurityUser userSession,int start,int count) {
		StringBuffer conStr=new StringBuffer();
		conStr.append(" From (tbltransaction t");
		conStr.append(condition.getTableStr(userSession));
		conStr.append(")");
		conStr.append(" JOIN tblPolicy p ON p.policyid = t.policyid");
		conStr.append(" JOIN tblAgency a ON a.agencyid = t.agencyid");
		conStr.append(" JOIN tblProduct d ON d.productid = p.productid");
		conStr.append(" JOIN tblUser u ON u.uid = p.uid");
		conStr.append(" JOIN tblPlan l on l.planid = p.planid");
//		conStr.append(" LEFT JOIN tblFee f on (f.payModule = 'policy' and f.payModuleId = p.policyid) or (f.payModule = 'order' and f.payModuleId = p.orderId) AND f.status = 1 AND ((t.status=1 AND f.fee>=0) OR (t.status=3 AND f.fee<0))");
		conStr.append(" Where 1=1");

		conStr.append(condition.getConStr(condition,userSession));
		if (condition.getBranch() != 0){
			conStr.append(" and p.BRANCH=").append(condition.getBranch());
		}

		if (condition.getUserID() != 0){
			conStr.append(" and p.uid=").append(condition.getUserID());
		}

		if (condition.getAgencyID() != 0){
			conStr.append(" and t.agencyID=").append(condition.getAgencyID());
		}
		if (condition.has(condition.getPolicyID())){
			conStr.append(" and t.policyid=").append(condition.getPolicyID());
		}
		if (condition.has(condition.getStartDate())){
			conStr.append(" and t.transactionDate>=").append(JdbcUtil.getStringValue(condition.getStartDate()));
		}
		if (condition.has(condition.getEndDate())){
			conStr.append(" and t.transactionDate<'").append(condition.getEndDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getPolicyNo())){
			conStr.append(" and p.policyNo=").append(JdbcUtil.getStringValue(condition.getPolicyNo()));
		}

		if (condition.has(condition.getEffectiveBeginDate())) {
			conStr.append(" AND p.effectiveDate>=").append(JdbcUtil.getStringValue(condition.getEffectiveBeginDate()));
		}

		if (condition.has(condition.getEffectiveEndDate())) {
			conStr.append(" AND p.effectiveDate<'").append(condition.getEffectiveEndDate()).append(" 23:59:59'");
		}

		if (condition.has(condition.getBExpiryDate())) {
			conStr.append(" AND p.expiryDate>=").append(JdbcUtil.getStringValue(condition.getBExpiryDate()));
		}

		if (condition.has(condition.getEExpiryDate())) {
			conStr.append(" AND p.expiryDate<'").append(condition.getEExpiryDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getRemark())){
			conStr.append(" and p.remark=").append(JdbcUtil.getStringValue(condition.getRemark()));
		}
		if (condition.getProductid() != 0){
			conStr.append(" and p.productID=").append(condition.getProductid());
		}
		if (condition.has(condition.getFeeNo())) {
			conStr.append(" and p.feeNo=").append(JdbcUtil.getStringValue(condition.getFeeNo()));
		}

		String countString="Select Count(1) from (select distinct t.id " + conStr.toString() + ") A";
		String queryString="SELECT distinct t.id ,t.agencyID, t.policyID,t.status, t.transactionAmount, " +
				"t.commissionRate, t.changesNumberInsured, t.transactionDate," +
				"(t.transactionAmount * t.commissionRate) AS commission,p.policyholder,p.plancode,p.productcode," +
				"p.lineNo, p.policyNo, p.inssueDate, p.effectiveDate, p.expiryDate, p.attn,p.ISPAYMENT,p.remark,p.furthestCity,p.feeNo," +
				"a.agencyName, u.username, d.productName,l.Planname,u.loginname " +
				",(SELECT departmentname FROM tbldepartment WHERE departmentID=p.agencysalesdepartmentid) AS agencyDepartment" +
				",(SELECT loginname FROM tblUser WHERE uid=if(p.AGENCYSALESMANID=0,p.SALESMANID,p.AGENCYSALESMANID)) AS salesLoginName" +
				conStr.toString() +
				" order by p.policyID desc";
		return this.find(queryString,countString, start, count,PremiumBill.class);
	}

	@Override
	public SPCommission queryPolicyCommisson(Condition condition,SecurityUser userSession) {
		StringBuffer conStr = new StringBuffer();
		conStr.append(condition.getConStr(condition,userSession));
		if (condition.getBranch() != 0){
			conStr.append(" and p.BRANCH=").append(condition.getBranch());
		}

		if (condition.getAgencyID() != 0){
			conStr.append(" and t.agencyID=").append(condition.getAgencyID());
		}
		if (condition.getUserID() !=0){
			conStr.append(" and p.uid=").append(condition.getUserID());
		}

		if (condition.has(condition.getPolicyID())){
			conStr.append(" and p.policyid=").append(condition.getPolicyID());
		}
		if (condition.has(condition.getStartDate())){
			conStr.append(" and t.transactionDate>=").append(JdbcUtil.getStringValue(condition.getStartDate()));
		}
		if (condition.has(condition.getEndDate())){
			conStr.append(" and t.transactionDate<'").append(condition.getEndDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getPolicyNo())){
			conStr.append(" and p.policyNo=").append(JdbcUtil.getStringValue(condition.getPolicyNo()));
		}
		if (condition.has(condition.getEffectiveBeginDate())) {
			conStr.append(" AND p.effectiveDate>=").append(JdbcUtil.getStringValue(condition.getEffectiveBeginDate()));
		}

		if (condition.has(condition.getEffectiveEndDate())) {
			conStr.append(" AND p.effectiveDate<'").append(condition.getEffectiveEndDate()).append(" 23:59:59'");
		}

		if (condition.has(condition.getBExpiryDate())) {
			conStr.append(" AND p.expiryDate>=").append(JdbcUtil.getStringValue(condition.getBExpiryDate()));
		}

		if (condition.has(condition.getEExpiryDate())) {
			conStr.append(" AND p.expiryDate<'").append(condition.getEExpiryDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getRemark())){
			conStr.append(" and p.remark=").append(JdbcUtil.getStringValue(condition.getRemark()));
		}

		if (condition.getProductid() != 0){
			conStr.append(" and p.productID=").append(condition.getProductid());
		}

		if (condition.has(condition.getFeeNo())) {
			conStr.append(" and p.feeNo=").append(JdbcUtil.getStringValue(condition.getFeeNo()));
		}

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT SUM(IF(t.STATUS = 1,1,0)) AS newSingular, SUM(IF(t.STATUS = 1,t.transactionAmount,0)) AS newPremium ");

		sql.append(",SUM(IF(t.STATUS = 2,1,0)) AS modifySingular,  SUM(IF(t.STATUS = 2,t.transactionAmount,0)) AS modifyPremium ");

		sql.append(",SUM(IF(t.STATUS = 3,1,0)) AS cancelSingular,  SUM(IF(t.STATUS = 3,t.transactionAmount,0)) AS cancelPremium ");

		sql.append(",SUM(1) AS totalSingular, SUM(t.transactionAmount) AS totalPremium ");
		sql.append("  From (select distinct t.* FROM (tblTransaction t");
		sql.append(condition.getTableStr(userSession));
		sql.append(")");
		sql.append(" JOIN tblPolicy p ON p.policyid = t.policyid");
		sql.append(" WHERE t.STATUS IN (1,2,3)").append(conStr).append(") t");

		List list=this.findObject(sql.toString(), SPCommission.class);
		SPCommission commission = null;
		if(list != null)
		{
		  if(list.size()>0){
			  commission = (SPCommission)list.get(0);
		  }
		}
		return commission;
	}

	public QueryResult queryPremiumBillDetail(Condition condition,SecurityUser userSession,int start,int count) {
		StringBuffer conStr=new StringBuffer();
		conStr.append(" FROM tbltransactiondetail a, tblPolicy p, tblAgency c, tblProduct d, ");
		conStr.append(" tblInsured e, tblUser f, tblPlan g" );
		conStr.append(condition.getTableStr(userSession));
		conStr.append(" WHERE a.policyID = p.policyID ");
		conStr.append(" AND c.agencyID = a.agencyID AND p.productID = d.productID ");
		conStr.append(" AND a.insuredID=e.insuredID AND p.uid=f.uid AND p.planid = g.planid ");

		conStr.append(condition.getConStr(condition,userSession));
		if (condition.getBranch() != 0){
			conStr.append(" and p.BRANCH=").append(condition.getBranch());
		}

		if (condition.getAgencyID() != 0){
			conStr.append(" and a.agencyID=").append(condition.getAgencyID());
		}
		if (condition.getUserID() !=0){
			conStr.append(" and p.uid=").append(condition.getUserID());
		}

		if (condition.has(condition.getPolicyID())){
			conStr.append(" and a.policyid=").append(condition.getPolicyID());
		}
		if (condition.has(condition.getStartDate())){
			conStr.append(" and a.transactionDate>=").append(JdbcUtil.getStringValue(condition.getStartDate()));
		}
		if (condition.has(condition.getEndDate())){
			conStr.append(" and a.transactionDate<'").append(condition.getEndDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getPolicyNo())){
			conStr.append(" and p.policyNo=").append(JdbcUtil.getStringValue(condition.getPolicyNo()));
		}
		if (condition.has(condition.getEffectiveBeginDate())) {
			conStr.append(" AND p.effectiveDate>=").append(JdbcUtil.getStringValue(condition.getEffectiveBeginDate()));
		}

		if (condition.has(condition.getEffectiveEndDate())) {
			conStr.append(" AND p.effectiveDate<'").append(condition.getEffectiveEndDate()).append(" 23:59:59'");
		}

		if (condition.has(condition.getBExpiryDate())) {
			conStr.append(" AND p.expiryDate>=").append(JdbcUtil.getStringValue(condition.getBExpiryDate()));
		}

		if (condition.has(condition.getEExpiryDate())) {
			conStr.append(" AND p.expiryDate<'").append(condition.getEExpiryDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getIsWaats())){
			conStr.append(" and p.iswaats=").append(JdbcUtil.getStringValue(condition.getIsWaats()));
		}
		if (condition.has(condition.getRemark())){
			conStr.append(" and e.remark=").append(JdbcUtil.getStringValue(condition.getRemark()));
		}
		String countString="Select Count(1) from (select distinct a.id " + conStr.toString() + ") A";
		String queryString="Select p.*,ti.insuredIdNoType AS IDtype3,ti.insuredIdNo AS IDNo3,td.departmentname as agencyDepartment,tu.loginname AS salesLoginName " +
			" From (SELECT distinct a.id,a.agencyID, a.policyID, a.transactionAmount, a.transactionDate," +
			"a.Status, p.lineNo, p.policyNo, p.inssueDate, p.effectiveDate," +
			"p.policyHolder, p.expiryDate, p.lasttime, p.attn,p.ISPAYMENT,p.productCode,p.plancode," +
			"d.productName,e.insuredID, e.insuredname, e.insuredIdNoType,e.remark," +
			"e.insuredIdNo, e.birthday, e.deathBeneficiary, e.relationship, f.username,f.loginname, p.isGroup," +
			"g.Planname, g.insureProject, p.POLICYNO AS POLICYNO2,c.agencyName,c.code,c.agencyPCC, p.agency as pdfAgencyName,p.agencyPCC as pdfAgencyPcc" +
			",p.agencysalesdepartmentid,p.AGENCYSALESMANID,p.SALESMANID" +
			conStr.toString() +
			" order by  p.policyID asc limit " + (start - 1) * count + "," + count +
			" ) as p LEFT JOIN tblInsured ti ON ti.policyID = p.policyID AND isInsuredFlag<>2 and policystatus<>3" +
			" left join tbldepartment td On td.departmentID=p.agencysalesdepartmentid" +
			" left Join tblUser tu On tu.uid=if(p.AGENCYSALESMANID=0,p.SALESMANID,p.AGENCYSALESMANID)";
		QueryResult qr = new QueryResult();
		qr.setCount(this.findObjectSize(countString));
		qr.setList(this.findObject(queryString, PremiumBillDetail.class));
		return qr;
	}

	/**
	 * 被保险人交易汇总
	 * @param condition
	 * @return
	 */
	public SPCommission queryInsuredCommisson(Condition condition,SecurityUser userSession) {
		StringBuffer conStr = new StringBuffer();

		conStr.append(condition.getConStr(condition,userSession));
		if (condition.getBranch() != 0){
			conStr.append(" and p.branch=").append(condition.getBranch());
		}

		if (condition.getAgencyID() != 0){
			conStr.append(" and t.agencyID=").append(condition.getAgencyID());
		}
		if (condition.getUserID() !=0){
			conStr.append(" and p.uid=").append(condition.getUserID());
		}

		if (condition.has(condition.getPolicyID())){
			conStr.append(" and p.policyid=").append(condition.getPolicyID());
		}
		if (condition.has(condition.getStartDate())){
			conStr.append(" and t.transactionDate>=").append(JdbcUtil.getStringValue(condition.getStartDate()));
		}
		if (condition.has(condition.getEndDate())){
			conStr.append(" and t.transactionDate<'").append(condition.getEndDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getPolicyNo())){
			conStr.append(" and p.policyNo=").append(JdbcUtil.getStringValue(condition.getPolicyNo()));
		}
		if (condition.has(condition.getEffectiveBeginDate())) {
			conStr.append(" AND p.effectiveDate>=").append(JdbcUtil.getStringValue(condition.getEffectiveBeginDate()));
		}

		if (condition.has(condition.getEffectiveEndDate())) {
			conStr.append(" AND p.effectiveDate<'").append(condition.getEffectiveEndDate()).append(" 23:59:59'");
		}

		if (condition.has(condition.getBExpiryDate())) {
			conStr.append(" AND p.expiryDate>=").append(JdbcUtil.getStringValue(condition.getBExpiryDate()));
		}

		if (condition.has(condition.getEExpiryDate())) {
			conStr.append(" AND p.expiryDate<'").append(condition.getEExpiryDate()).append(" 23:59:59'");
		}
		if (condition.has(condition.getRemark())){
			conStr.append(" and i.remark=").append(JdbcUtil.getStringValue(condition.getRemark()));
		}

		StringBuffer sql = new StringBuffer();
		sql.append("SELECT SUM(IF(t.STATUS = 1,1,0)) AS newSingular, SUM(IF(t.STATUS = 1,t.transactionAmount,0)) AS newPremium ");

		sql.append(",SUM(IF(t.STATUS = 2,1,0)) AS modifySingular, SUM(IF(t.STATUS = 2,t.transactionAmount,0)) AS modifyPremium ");

		sql.append(",SUM(IF(t.STATUS = 3,1,0)) AS cancelSingular, SUM(IF(t.STATUS = 3,t.transactionAmount,0)) AS cancelPremium ");

		sql.append(", COUNT(t.policyid)AS totalSingular, SUM(t.transactionAmount)AS totalPremium ");
		sql.append(" From (select distinct t.* FROM tblTransactionDetail t, tblinsured i, tblPolicy p");
		sql.append(condition.getTableStr(userSession));
		sql.append(" WHERE t.insuredid=i.insuredid and t.policyID=p.POLICYID" );
		sql.append(" and t.STATUS IN (1,2,3)").append(conStr).append(") t");
		List list=this.findObject(sql.toString(), SPCommission.class);
		SPCommission commission = null;
		if(list != null)
		{
		  if(list.size()>0){
			  commission = (SPCommission)list.get(0);
		  }
		}
		return commission;

	}

}

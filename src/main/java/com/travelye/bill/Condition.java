/**
 * 包名：com.wenhq.policy<br>
 * 文件：Condition.java<br>
 * 作者：<EMAIL><br>
 * 时间：2010-8-25 上午10:20:06<br>
 * 
 * Current revision $Revision: 1.3 $<br>
 * Latest change by $Author: wenc $<br>
 * $Log: Condition.java,v $
 * Revision 1.3  2010/11/03 00:27:16  wenc
 * *** empty log message ***
 *
 * Revision 1.2  2010/09/15 04:02:03  wenc
 * *** empty log message ***
 *
 * Revision 1.1  2010/08/26 04:11:54  wenc
 * *** empty log message ***
 * <br>
 */

package com.travelye.bill;

import com.travelye.policy.ConditionFace;
import com.travelye.security.session.SecurityUser;
import lombok.Data;

/**
 * <AUTHOR>
 *保单的搜索条件
 */
@Data
public class Condition implements ConditionFace{

	private long branch;		// 分公司ID
	private long agencyID;		// 代理商ID
	private long userID;		// 操作者ID
	private String policyID;	// 保单ID
	private String policyNo;	// 保单号
	private String startDate;	// 起始时间
	private String endDate;		// 终止时间
	private String effectiveBeginDate; // 起保时间
	private String effectiveEndDate; // 起保时间
	private String bExpiryDate;	// 失效时间
	private String eExpiryDate;	// 时效时间
	private String isWaats;		// 是否是waats产品
	private String remark;	// 备注
	private String departmentId;	// 部门id，多个已逗号分割
	private String branchDepartmentID;	// 分公司部门id，多个已逗号分割
	private String webSubmit;      //网页提交查询
	private long productid;        //产品id
	private String feeNo;			//支付流水号
	public boolean has(String o){
		if (o ==null || "".equals(o.trim())){
			return false;
		}else
			return true;
	}
	
	/** 根据用户 返回查询字符串
	 * @param userSession
	 * @return
	 */
	public String getConStr(Condition condition,SecurityUser userSession){
		StringBuffer conStr = new StringBuffer();
		conStr.append((new com.travelye.policy.Condition()).getConStr(condition,userSession));
		return conStr.toString();
	}
	
	/** 返回关联的表，目前仅指 分公司的部门
	 * @param userSession
	 * @return
	 */
	public String getTableStr(SecurityUser userSession){
		return (new com.travelye.policy.Condition()).getTableStr(userSession);
	}
}

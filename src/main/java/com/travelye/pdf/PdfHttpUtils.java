package com.travelye.pdf;

import com.travelye.base.tools.PropUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.config.RequestConfig;
import org.apache.hc.client5.http.entity.mime.MultipartEntityBuilder;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.core5.http.ClassicHttpResponse;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.core5.http.io.SocketConfig;
import org.apache.hc.core5.http.io.entity.EntityUtils;
import org.apache.hc.core5.util.Timeout;

import java.io.IOException;
import java.util.List;

@Slf4j
public class PdfHttpUtils {

    public static byte[] getMergedPdf(List<byte[]> files) throws IOException {


        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        SocketConfig socketConfig = SocketConfig.custom().setSoTimeout(Timeout.ofSeconds(60)).build();
        connManager.setDefaultSocketConfig(socketConfig);
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(Timeout.ofSeconds(30)).build();
        ClassicHttpResponse response;
        try (CloseableHttpClient client = HttpClients.custom().setConnectionManager(connManager).build()) {
            String policyServerUrl = PropUtil.getString("policySendServer") + "/mergePdf";

            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            for (byte[] file : files) {
                multipartEntityBuilder.addBinaryBody("files", file, ContentType.MULTIPART_FORM_DATA, "");
            }

            HttpEntity entity = multipartEntityBuilder.build();
            HttpPost post = new HttpPost(policyServerUrl);
            post.setConfig(requestConfig);
            log.info("policyServerUrl发送到:{}", policyServerUrl);

            post.setEntity(entity);
            response = client.execute(post);
            int statusCode = response.getCode();
            if (statusCode != 200) {
                throw new RuntimeException("请求policySendServer失败，http code : " + statusCode);
            } else {
                HttpEntity responseEntity = response.getEntity();
                return responseEntity != null ? EntityUtils.toByteArray(responseEntity) : null;
            }
        }
    }
}

//    public static HttpResponse<byte[]> getMergedPdf(List<byte[]> files) throws IOException, InterruptedException {
////        HttpMultipartBody.Builder builder = new HttpMultipartBody.Builder();
////        for (byte[] file : files) {
////            builder.addPart("files", file);
////        }
////
////        HttpMultipartBody body = builder.build();
//        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
//        builder.setCharset(StandardCharsets.UTF_8);
//        for (byte[] file : files) {
//            builder.addBinaryBody("files", file);
//        }
//
//
//        try (HttpEntity entity = builder.build()) {
//
//            Pipe pipe = Pipe.open();
//
//            new Thread(() -> {
//                try (OutputStream outputStream = Channels.newOutputStream(pipe.sink())) {
//                    // Write the encoded data to the pipeline.
//                    entity.writeTo(outputStream);
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//
//            }).start();
//
//            HttpRequest request = HttpRequest.newBuilder()
//                    .header("Content-Type", entity.getContentType())
//                    .uri(URI.create(PropUtil.getString("policySendServer") + "/mergePdf"))
//                    .timeout(Duration.ofMinutes(30))
//                    .POST(HttpRequest.BodyPublishers.ofInputStream(() -> Channels.newInputStream(pipe.source())))
//                    .build();
//
//            return HttpUtil.post(request, byte[].class);
//        }
//    }

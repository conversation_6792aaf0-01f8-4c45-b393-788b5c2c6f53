package com.travelye.pdf;

import com.travelye.api.up.v3.DTO.Segment;
import com.travelye.vo.Policy;

import java.util.List;

public interface PdfJobService {
    void sendPdfJobMessage(Policy policy, int type);

    byte[] generatePdf(Policy policy, int type);

    byte[] generateApplication(Policy policy);

    /**
     * 生成电子保单PDF
     */
    byte[] generatePolicyPdf(Policy policy) throws Exception;

    byte[] generateEtravelPdf(Segment segment);

    byte[] mergePdf(List<byte[]> pdfs);

    byte[] getPolicyListPdf(List<Policy> policyList);

    /**
     * 打印领馆、医疗批注/说明函
     */
    byte[] generateExplanationPdf(Policy policy, String type);

    /**
     * 查询保单PDF生成失败次数
     */
    Integer getPolicyPdfFailedCount(String policyNo);

    /**
     * 增加保单PDF失败记录
     */
    void addPolicyPdfFailedCount(String policyNo);

    /**
     * 删除保单PDF失败记录
     */
    void removePolicyPdfFailedRecord(String policyNo);
}

package com.travelye.pdf;

import com.travelye.api.up.v2.DTO.Insured;
import com.travelye.api.up.v2.utils.JsonMapperUtils;
import com.travelye.api.up.v3.DTO.Segment;
import com.travelye.base.MessageException;
import com.travelye.base.SjisConstant;
import com.travelye.base.tools.AliOSSUtil;
import com.travelye.base.tools.DateUtil;
import com.travelye.base.tools.JSONUtils;
import com.travelye.base.tools.PropUtil;
import com.travelye.partner.PartnerService;
import com.travelye.policy.PolicyCommonService;
import com.travelye.policy.PolicyDao;
import com.travelye.product.ProductService;
import com.travelye.rabbitmaq.RabbitMessagePublisher;
import com.travelye.sjis.engine.HttpUtil;
import com.travelye.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.io.File;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PdfJobServiceImpl implements PdfJobService {

    public final static Map<String, Integer> FAILED_POLICY_PDF_RECORD = new HashMap<>();

    /**
     * PDF类型，电子保单PDF
     */
    public final static int PDF_TYPE_POLICY = 1;
    /**
     * PDF类型，投保单PDF
     */
    public final static int PDF_TYPE_APPLICATION = 2;

    /**
     * 保障区域批注
     */
    public final static String EXPLANATION_PDF_AREA = "area";
    /**
     * 医疗批注
     */
    public final static String EXPLANATION_PDF_MEDICAL = "medical";
    private final RabbitMessagePublisher publisher;
    private final ProductService productService;
    private final PartnerService partnerService;
    private final PolicyCommonService policyCommonService;
    private final PolicyDao policyDao;

    public PdfJobServiceImpl(RabbitMessagePublisher publisher, ProductService productService, PartnerService partnerService, PolicyCommonService policyCommonService, PolicyDao policyDao) {
        this.publisher = publisher;
        this.productService = productService;
        this.partnerService = partnerService;
        this.policyCommonService = policyCommonService;
        this.policyDao = policyDao;
    }

    /**
     * 向消息队列发送PDF生成消息
     *
     * @param policy 保单/投保单
     * @param type   类型
     *               1 - policy
     *               2 - application
     */
    @Override
    public void sendPdfJobMessage(Policy policy, int type) {
        // 是否是打印模板 1-不是 0-无签名，打印模板
        policy.setIsRed("1");
        publisher.sendPdfMsg(pdfJobRequest(policy, type));
    }

    @Override
    public byte[] generatePdf(Policy policy, int type) {
        PdfJobRequest pdfJobRequest = pdfJobRequest(policy, type);
        Map<String, String> postParams = new HashMap<>();
        postParams.put("pdfJobRequest", JSONUtils.toJson(pdfJobRequest));

        try {
            HttpResponse<byte[]> httpResponse = HttpUtil.post(
                    PropUtil.getString("policySendServer") + "/generatePdf",
                    postParams,
                    HttpUtil.BODY_TYPE_FORM,
                    null,
                    byte[].class
            );
            Assert.isTrue(httpResponse.statusCode() == 200, "网络异常");
            Assert.notNull(httpResponse.body(), "PDF为空");
            return httpResponse.body();
        } catch (InterruptedException | IOException e) {
            throw new RuntimeException("网络异常", e);
        } catch (Exception e) {
            throw new RuntimeException("生成PDF失败", e);
        }
    }

    @Override
    public byte[] generateApplication(Policy policy) {
        return generatePdf(policy, PDF_TYPE_APPLICATION);
    }

    @Override
    public byte[] generatePolicyPdf(Policy policy) throws Exception {
        // 生成电子保单内容
        byte[] pdf = generatePdf(policy, PDF_TYPE_POLICY);

        if (pdf == null) {
            throw new MessageException("空数据流");
        }
        // 以下，决定是否拼接条款目录和条款
        Partnerproduct pp = partnerService.getPartnerProduct(policy.getAgencyID(), policy.getProductID());
        // 默认仅保单
        int pdfType = Policy.PDF_TYPE_POLICY_ONLY;
        if (pp != null) {
            pdfType = pp.getPolicyPdfType();
        }
        log.info("电子保单类型:{}", pdfType);
        List<byte[]> attachFileByteList = new ArrayList<>();
        attachFileByteList.addFirst(pdf);
        // 条款文件
        File term = new File(SjisConstant.root_path + policy.getProduct().getTerms());
        // 条款目录
        File termCatalog = new File(SjisConstant.root_path + "/sale/product/term_catalog/" + policy.getProduct().getProductNum() + ".pdf");
        if (Policy.PDF_TYPE_POLICY_TERM_LIST_TERM == pdfType) {
            if (termCatalog.exists()) {
                attachFileByteList.add(FileUtils.readFileToByteArray(termCatalog));
            }
            if (term.exists()) {
                attachFileByteList.add(FileUtils.readFileToByteArray(term));
            }
        } else if (Policy.PDF_TYPE_POLICY_TERM_LIST == pdfType) {
            if (termCatalog.exists()) {
                attachFileByteList.add(FileUtils.readFileToByteArray(termCatalog));
            }
        }

        if (attachFileByteList.size() > 1) {
            pdf = mergePdf(attachFileByteList);
        }
        // 异步上传PDF至阿里云OSS
        AliOSSUtil.uploadPolicyAsync(pdf, policy.getPolicyNo() + ".pdf");
        // 电子保单生成记录表
        policyDao.insertPolicyPdfLog(policy.getPolicyID());

        // 删除失败记录
        removePolicyPdfFailedRecord(policy.getPolicyNo());

        return pdf;
    }

    @Override
    public byte[] generateEtravelPdf(Segment segment) {
        // 拼接生成保单需要的参数
        PdfJobRequest pdfJobRequest = new PdfJobRequest();
        pdfJobRequest.setModule("policy");
        pdfJobRequest.setModuleId(String.valueOf(segment.getPolicyNo()));
        if ("9106".equals(segment.getProductCode())) {
            pdfJobRequest.setFtlPath("etravel-DTA.ftl");
        } else {
            pdfJobRequest.setFtlPath("etravel.ftl");
        }
        pdfJobRequest.setOssBucket(PropUtil.getString("ali-cloud.oss.policy-bucket"));
        pdfJobRequest.setFileName(segment.getPolicyNo() + ".pdf");
        Map<String, Object> pdfParams = new HashMap<>();
        Map<String, Object> policyMap = new HashMap<>();
        Product product = segment.getProduct();
        Plan plan = segment.getPlan();
        policyMap.put("issueOffice", segment.getIssueOffice());
        policyMap.put("agencyCode", segment.getAgencyCode());
        policyMap.put("agencyName", segment.getAgencyName());
        policyMap.put("productName", product.getProductname());
        policyMap.put("productNameEn", product.getEnglishname());
        policyMap.put("planName", plan.getPlanname());
        policyMap.put("planNameEn", plan.getEnglishName());
        policyMap.put("productId", product.getProductid());
        policyMap.put("termUrl", getProductTermsUrl(product));
        policyMap.put("effYear", segment.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("effMonth", segment.getEffectiveDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("effDay", segment.getEffectiveDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("effTime", segment.getEffectiveDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        policyMap.put("expYear", segment.getExpiryDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("expMonth", segment.getExpiryDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("expDay", segment.getExpiryDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("expTime", segment.getExpiryDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        policyMap.put("premium", segment.getPremium());
        policyMap.put("policyNo", segment.getPolicyNo());
        policyMap.put("issueDate", segment.getIssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
        policyMap.put("issueDateTime", segment.getIssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        policyMap.put("pdfPrintDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        policyMap.put("transYear", segment.getIssueDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("transMonth", segment.getIssueDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("transDay", segment.getIssueDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("transTime", segment.getIssueDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        if (segment.getPaymentDate() != null) {
            policyMap.put("paymentDate", segment.getPaymentDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        } else {
            policyMap.put("paymentDate", segment.getIssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        }
        List<Insured> insuredList = segment.getInsuredList();
        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        for (Insured insured : insuredList) {
            Map<String, Object> insuredMap = new HashMap<>();
            insuredMap.put("name", insured.getNativeName());
            insuredMap.put("idNo", insured.getIdNo());
            insuredMap.put("birthday", DateUtil.formatBirthday(insured.getBirthday()));
            insuredMap.put("relation", insured.getRelation());
            insuredMap.put("beneficiary", insured.getBeneficiaryStr());
            insuredMapList.add(insuredMap);
        }
        pdfParams.put("applicant", Map.of("name", segment.getApplicant().getNativeName()));
        pdfParams.put("insuredList", insuredMapList);
        pdfParams.put("benefitList", benefitList(plan, false));
        pdfParams.put("benefitDtaList", benefitList(plan, true));
        pdfParams.put("notes", notes(plan));
        pdfParams.put("policy", policyMap);

        pdfJobRequest.setParams(pdfParams);
        Map<String, String> postParams = new HashMap<>();
        postParams.put("pdfJobRequest", JSONUtils.toJson(pdfJobRequest));
        try {
            HttpResponse<byte[]> httpResponse = HttpUtil.post(
                    PropUtil.getString("policySendServer") + "/generatePdf",
                    postParams,
                    HttpUtil.BODY_TYPE_FORM,
                    null,
                    byte[].class
            );
            Assert.isTrue(httpResponse.statusCode() == 200, "网络异常");
            Assert.notNull(httpResponse.body(), "PDF为空");
            return httpResponse.body();
        } catch (Exception e) {
            log.error("生成etravel电子保单失败", e);
            throw new MessageException("生成PDF失败");
        }
    }

    @Override
    public byte[] mergePdf(List<byte[]> pdfs) {
        try {
            byte[] pdf = PdfHttpUtils.getMergedPdf(pdfs);
            Assert.notNull(pdf, "PDF为空");
            return pdf;
        } catch (Exception e) {
            log.error("合并pdf失败", e);
        }
        return null;
    }

    @Override
    public byte[] getPolicyListPdf(List<Policy> policyList) {
        PdfJobRequest pdfJobRequest = policyListPdfParams(policyList);
        Map<String, String> postParams = new HashMap<>();
        postParams.put("pdfJobRequest", JSONUtils.toJson(pdfJobRequest));
        try {
            HttpResponse<byte[]> httpResponse = HttpUtil.post(
                    PropUtil.getString("policySendServer") + "/generatePdf",
                    postParams,
                    HttpUtil.BODY_TYPE_FORM,
                    null,
                    byte[].class
            );
            Assert.isTrue(httpResponse.statusCode() == 200, "网络异常");
            Assert.notNull(httpResponse.body(), "PDF为空");
            return httpResponse.body();
        } catch (Exception e) {
            log.error("获取保单列表pdf失败", e);
            throw new RuntimeException("获取保单列表pdf失败", e);
        }
    }

    /**
     * @param type area、medical
     */
    @Override
    public byte[] generateExplanationPdf(Policy policy, String type) {
        PdfJobRequest pdfJobRequest = PdfJobRequest.builder()
                .module("explanation")
                .moduleId(policy.getPolicyID() + "")
                .fileName(policy.getPolicyNo() + "-" + policy.getPolicyID() + ".pdf")
                .ftlPath(PropUtil.getString("explanation.ftlTemplate"))
                .params(explanationLetterParams(policy, type))
                .build();

        try {
            Map<String, String> postParams = new HashMap<>();
            postParams.put("pdfJobRequest", JsonMapperUtils.objectToJson(pdfJobRequest));
            HttpResponse<byte[]> httpResponse = HttpUtil.post(
                    PropUtil.getString("policySendServer") + "/generatePdf",
                    postParams,
                    HttpUtil.BODY_TYPE_FORM,
                    null,
                    byte[].class
            );
            Assert.isTrue(httpResponse.statusCode() == 200, "网络异常");
            Assert.notNull(httpResponse.body(), "PDF为空");
            return httpResponse.body();
        } catch (InterruptedException | IOException e) {
            throw new RuntimeException("网络异常", e);
        } catch (Exception e) {
            throw new RuntimeException("生成PDF失败", e);
        }
    }

    @Override
    public Integer getPolicyPdfFailedCount(String policyNo) {
        return FAILED_POLICY_PDF_RECORD.getOrDefault(policyNo, 0);
    }

    @Override
    public void addPolicyPdfFailedCount(String policyNo) {
        FAILED_POLICY_PDF_RECORD.compute(policyNo, (key, value) -> value == null ? 1 : value + 1);
    }

    @Override
    public void removePolicyPdfFailedRecord(String policyNo) {
        FAILED_POLICY_PDF_RECORD.remove(policyNo);
    }

    /**
     * 组装PDF生成请求/消息队列消息
     *
     * @param policy 待生成的保单
     * @param type   消息类型
     *               1 - policy
     *               2 - application
     * @return PdfJobRequest对象
     */
    private PdfJobRequest pdfJobRequest(Policy policy, int type) {

        if (policy.getProduct() == null) {
            Product product = productService.getProduct(policy.getProductID());
            policy.setProduct(product);
        }

        if (policy.getPlan() == null) {
            Plan plan = productService.getPlan(policy.getPlanID());
            policy.setPlan(plan);
        }

        if (policy.getPartner() == null) {
            Partner agency = partnerService.getPartner(policy.getAgencyID());
            policy.setPartner(agency);
        }

        policy.setPartner(getPdfAgency(policy));

        if (CollectionUtils.isEmpty(policy.getPolicyuserList())) {
            List<Policyuser> policyuserList = policyCommonService.getPolicyUserList(policy.getPolicyID());
            policy.setPolicyuserList(policyuserList);
        }

//        // 微保的被保险人证件需要特别处理
//        long agencyUserId = Long.parseLong(externalService.getEditableConfig("wesureAgencyUserId"));
//        if (agencyUserId == policy.getUid()) {
//            policy.getPolicyuserList().forEach(i -> {
//                if ("身份证".equals(i.getInsuredIdNoType())) {
//                    i.setIdentificationID(i.getInsuredIdNo());
//                    i.setInsuredIdNo("");
//                }
//            });
//        }

        if (policy.getPolicyExtend() == null) {
            policy.setPolicyExtend(policyDao.getPolicyExtend(policy.getPolicyID()));
        }

        PdfJobRequest pdfJobRequest = new PdfJobRequest();
        if (PDF_TYPE_POLICY == type) {
            // 电子保单PDF
            pdfJobRequest.setModule("policy");
            pdfJobRequest.setModuleId(String.valueOf(policy.getPolicyID()));
            pdfJobRequest.setFtlPath(policy.getProduct().getTemplate());
            pdfJobRequest.setOssBucket(PropUtil.getString("ali-cloud.oss.policy-bucket"));
            pdfJobRequest.setFileName(policy.getPolicyNo() + ".pdf");

            pdfJobRequest.setParams(policyParams(policy));
        }
        if (PDF_TYPE_APPLICATION == type) {
            String insuredSignPath = "";
            String applicantSignPath = "";
            if ("1".equals(policy.getIsRed())) {
                Policyuser applicant = policy.getPolicyuserList().
                        stream().filter(u -> u.getIsInsuredFlag() != 2).findFirst().get();
                List<Policyuser> insuredList = policy.getPolicyuserList()
                        .stream().filter(u -> u.getIsInsuredFlag() != 0)
                        .toList();
                applicantSignPath = SjisConstant.root_path + PropUtil.getString("application.applicationFold") + "/sign/" + applicant.getInsuredID() + ".png";
                if (!insuredList.isEmpty()) {
                    insuredSignPath = SjisConstant.root_path + PropUtil.getString("application.applicationFold") + "/sign/" + insuredList.get(0).getInsuredID() + ".png";
                }
            }
            pdfJobRequest.setModule("application");
            pdfJobRequest.setModuleId(String.valueOf(policy.getPolicyID()));
            pdfJobRequest.setFtlPath(PropUtil.getString("application.ftlTemplate"));
            pdfJobRequest.setOssBucket(PropUtil.getString("ali-cloud.oss.application-bucket"));
            pdfJobRequest.setFileName(policy.getPolicyID() + ".pdf");
            pdfJobRequest.setParams(applicationParams(policy, applicantSignPath.replace("//", "/"), insuredSignPath.replace("//", "/")));
        }
        return pdfJobRequest;
    }


    private Map<String, Object> policyParams(Policy policy) {
        Map<String, Object> pdfParams = new HashMap<>();
        Partner agency = policy.getPartner();
        Product product = policy.getProduct();
        Plan plan = policy.getPlan();
        Map<String, Object> policyMap = new HashMap<>();
        basicPolicyInfo(policyMap, policy, product, plan, agency);
        pdfParams.put("benefitList", benefitList(plan, false));
        pdfParams.put("benefitDtaList", benefitList(plan, true));
        pdfParams.put("notes", notes(plan));
        pdfParams.put("agency", agencyInfo(policy, agency));
        pdfParams.put("policy", policyMap);
        applicantAndInsured(pdfParams, policy.getPolicyuserList());
        return pdfParams;
    }

    private Map<String, Object> applicationParams(Policy policy,
                                                  String applicantSignPath,
                                                  String insuredSignPath) {
        Map<String, Object> pdfParams = new HashMap<>();
        Partner agency = policy.getPartner();
        Product product = policy.getProduct();
        Plan plan = policy.getPlan();
        pdfParams.put("isEn", "1".equals(policy.getIsRed()) ? "1" : "0");
        pdfParams.put("isFilled", policy.getIsRed());

        Map<String, Object> policyMap = new HashMap<>();
        policyMap.put("policyId", policy.getPolicyID());

        if (policy.getSignDate() != null) {
            policyMap.put("signDate", policy.getSignDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        }
        policyMap.put("isSinglePlan", plan.getMinday() == 365 && plan.getMaxday() == 366 ? "0" : "1");
        policyMap.put("duration", policy.getPolicyDays());
        policyMap.put("applicantSign", applicantSignPath);
        policyMap.put("insuredSign", insuredSignPath);
        basicPolicyInfo(policyMap, policy, product, plan, agency);
        if (StringUtils.isNotBlank(plan.getDurationReminder())
                && plan.getDurationReminder().split("\\[:]").length == 2) {
            String[] durationReminderList = plan.getDurationReminder().split("\\[:]");
            policyMap.put("durationReminderCn", durationReminderList[0]);
            policyMap.put("durationReminderEn", durationReminderList[1]);
        }
        pdfParams.put("benefitList", benefitList(plan, false));
        pdfParams.put("applicationReminder", applicationReminder(plan));
        pdfParams.put("policy", policyMap);
        applicantAndInsured(pdfParams, policy.getPolicyuserList());
        return pdfParams;
    }

    private Map<String, Object> explanationLetterParams(Policy policy, String type) {
        List<Policyuser> insuredList = policyCommonService.getPolicyUserList(policy.getPolicyID());
        Map<String, Object> params = new HashMap<>();
        params.put("policyNo", policy.getPolicyNo());
        params.put("issueDate", policy.getInssueDate());
        params.put("effectiveDate", policy.getEffectiveDate());
        params.put("expiryDate", policy.getExpiryDate());
        params.put("branch", policy.getBranch());
        params.put("branchName", getBranchName(policy.getBranch(), "cn"));
        params.put("branchNameEn", getBranchName(policy.getBranch(), "en"));
        params.put("branchNameAbbr", getBranchName(policy.getBranch(), "abbr"));
        params.put("insuredNames", insuredList.stream().filter(i -> i.getIsInsuredFlag() > 0 && i.getPolicyStatus() != 3).map(Policyuser::getInsuredFullName).collect(Collectors.joining(", ")));
        params.put("type", type);
        return params;
    }


    private Map<String, Object> agencyInfo(Policy policy, Partner agency) {
        Map<String, Object> agencyMap = new HashMap<>();
        if (policy.getBranch() == SjisConstant.branch_jiangsu || policy.getBranch() == SjisConstant.branch_suzhou) {
            List<String> saleMsgMap = new ArrayList<>();

            if (SjisConstant.saleChannel_directsale.equals(policy.getSalesChannel())) {
                StringBuffer salesMsgName = new StringBuffer("公司直销");
                if (SjisConstant.saleType_walkin.equals(policy.getSalesType())) {
                    salesMsgName.append("-客户上门");
                } else if (SjisConstant.saleType_employeesale.equals(policy.getSalesType())) {
                    salesMsgName.append("-员工销售");
                } else if (SjisConstant.saleType_telephonesale.equals(policy.getSalesType())) {
                    salesMsgName.append("-电销");
                } else if (SjisConstant.saleType_ecommercesale.equals(policy.getSalesType())) {
                    salesMsgName.append("-网销");
                } else if (SjisConstant.saleType_officesale.equals(policy.getSalesType())) {
                    salesMsgName.append("-门店销售");
                }
                saleMsgMap.add("<b>销售渠道: </b>" + nullToBlank(salesMsgName));
                saleMsgMap.add("<b>销售人员: </b>" + nullToBlank(policy.getSalesMan()));
                saleMsgMap.add("<b>销售人员工号: </b>" + nullToBlank(policy.getEmployeeID()));
                saleMsgMap.add("<b>销售网点: </b>" + nullToBlank(policy.getSalesLocation()));
                String department = policy.getDepartment();
                if (department != null) {
                    department = department.replaceAll("&", "&amp;");
                    String finalDepartment = department;
                    saleMsgMap.add("<b>业务归属部门: </b>" + nullToBlank(finalDepartment));
                }

                saleMsgMap.add("<b>联系方式: </b>" + nullToBlank(policy.getSalesContact()));
            } else if (SjisConstant.saleChannel_personalsale.equals(policy.getSalesChannel())) {
                saleMsgMap.add("<b>销售渠道: </b>" + "个人营销业务");
                saleMsgMap.add("<b>经办人员: </b>" + nullToBlank(policy.getAttn()));
                saleMsgMap.add("<b>联系方式: </b>" + nullToBlank(policy.getAttnContact()));
                saleMsgMap.add("<b>展业证书号码: </b>" + nullToBlank(policy.getQualificationNO()));
            } else {
                StringBuffer salesMsgName = new StringBuffer("保险中介机构-");
                if (SjisConstant.saleChannel_corporateagnecy.equals(policy.getSalesChannel())) {
                    salesMsgName.append("专业代理");
                } else if (SjisConstant.saleChannel_sideline.equals(policy.getSalesChannel())) {
                    salesMsgName.append("兼业代理");
                } else if (SjisConstant.saleChannel_broker.equals(policy.getSalesChannel())) {
                    salesMsgName.append("经纪公司");
                }
                saleMsgMap.add("<b>销售渠道: </b>" + nullToBlank(salesMsgName));
                saleMsgMap.add("<b>经办人员: </b>" + nullToBlank(policy.getAttn()));
                saleMsgMap.add("<b>联系方式: </b>" + nullToBlank(policy.getAttnContact()));
                saleMsgMap.add("<b>中介机构名称: </b>" + nullToBlank(policy.getAgency()));
                saleMsgMap.add("<b>电话: </b>" + nullToBlank(policy.getAgencyContact()));
                saleMsgMap.add("<b>地址: </b>" + nullToBlank(policy.getAgencyAddress()));
            }
            agencyMap.put("saleMsgMap", saleMsgMap);
        } else {
            agencyMap.put("agencyCode", policy.getAgencyPCC());
            if (SjisConstant.saleChannel_directsale.equals(policy.getSalesChannel())) {
                agencyMap.put("agencyName", "直销 Direct");
            } else {
                agencyMap.put("agencyName", policy.getAgency());
            }
        }
        //北分的保单模板，加“经营保险业务许可证号”
        if (policy.getBranch() == SjisConstant.branch_beijing) {
            List<AgencyPCC> list = partnerService.listEffectiveAgencyPcc(agency.getAgencyID());
            if (CollectionUtils.isNotEmpty(list)) {
                agencyMap.put("branchAgencyPcc", list.get(0).getAgencyPCC());
            }
        }
        //江苏分公司加代理人编号Agency Code
        if (policy.getBranch() == SjisConstant.branch_jiangsu) {
            agencyMap.put("agencyPccCode", policy.getAgencyPCC());
        }


        agencyMap.put("producerName", policy.getSalesMan());
        agencyMap.put("qualificationNo", policy.getEmployeeID());
        DateTimeFormatter ymdhms = DateTimeFormatter.ofPattern("yyyy/MM/dd hh:mm:ss a", Locale.ENGLISH);
        agencyMap.put("feeDate", policy.getInssueDate().format(ymdhms));
        agencyMap.put("policyDate", policy.getInssueDate().plusMinutes(1).format(ymdhms));
        agencyMap.put("printDate", LocalDateTime.now().format(ymdhms));
        return agencyMap;
    }

    private void basicPolicyInfo(Map<String, Object> policyMap, Policy policy, Product product, Plan plan, Partner agency) {
        policyMap.put("issueOffice", agency.getIssueOffice());
        policyMap.put("issueOfficeEn", agency.getIssueOfficeEn());
        policyMap.put("agencyCode", policy.getAgencyPCC());
        policyMap.put("agencyName", policy.getAgency());
        policyMap.put("productName", product.getProductname());
        policyMap.put("productNameEn", product.getEnglishname());
        policyMap.put("planName", plan.getPlanname());
        policyMap.put("planNameEn", plan.getEnglishName());
        policyMap.put("productId", product.getProductid());
        policyMap.put("termUrl", getProductTermsUrl(product));
        policyMap.put("effYear", policy.getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("effMonth", policy.getEffectiveDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("effDay", policy.getEffectiveDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("effTime", policy.getEffectiveDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        policyMap.put("expYear", policy.getExpiryDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("expMonth", policy.getExpiryDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("expDay", policy.getExpiryDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("expTime", policy.getExpiryDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        policyMap.put("premium", String.format("%.2f", policy.getTotalPremium()));
        policyMap.put("issueDateTime", policy.getInssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        policyMap.put("pdfPrintDate", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        if (policy.getPayDate() != null) {
            policyMap.put("paymentDate", policy.getPayDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        } else {
            policyMap.put("paymentDate", policy.getInssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss")));
        }
        policyMap.put("policyNo", policy.getPolicyNo());
        policyMap.put("issueDate", policy.getInssueDate().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
        policyMap.put("transYear", policy.getInssueDate().format(DateTimeFormatter.ofPattern("yyyy")));
        policyMap.put("transMonth", policy.getInssueDate().format(DateTimeFormatter.ofPattern("MM")));
        policyMap.put("transDay", policy.getInssueDate().format(DateTimeFormatter.ofPattern("dd")));
        policyMap.put("transTime", policy.getInssueDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        PolicyExtend policyExtend = policy.getPolicyExtend();
        if (policyExtend != null) {
            policyMap.put("ticketNo", policyExtend.getETicketNo());
            policyMap.put("flightNo", policyExtend.getFlightNo());
            policyMap.put("originAirport", policyExtend.getOriginAirport());
            policyMap.put("destAirport", policyExtend.getDestAirport());
            policyMap.put("departureTime", policyExtend.getDepartureTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        policyMap.put("branch", getBranchName(policy.getBranch(), "abbr"));
        policyMap.put("applicant", policy.getPolicyholder());
    }

    private void applicantAndInsured(Map<String, Object> policyMap, List<Policyuser> userList) {
        List<Policyuser> insuredList = userList
                .stream().filter(u -> u.getIsInsuredFlag() != 0)
                .collect(Collectors.toList());
        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        for (Policyuser insured : insuredList) {
            Map<String, Object> insuredMap = new HashMap<>();
            insuredMap.put("name", insured.getInsuredFullName());
            insuredMap.put("idNo", insured.getInsuredIdNo());
            insuredMap.put("identificationNo", insured.getIdentificationID());
            insuredMap.put("idType", insured.getInsuredIdNoType());
            insuredMap.put("birthday", DateUtil.formatBirthday(insured.getBirthday()));
            insuredMap.put("relation", insured.getRelationship());
            insuredMap.put("premium", insured.getPremium());
            insuredMap.put("nationality", insured.getNationalityCd());
            insuredMap.put("phone", insured.getHomePhoneNo());
            insuredMap.put("email", insured.getEmailAddr());
            insuredMap.put("beneficiary", insuredBeneficiaryStr(insured));
            insuredMapList.add(insuredMap);
        }
        policyMap.put("insuredList", insuredMapList);
        policyMap.put("beneficiaryList", beneficiaryList(insuredList));
        userList.stream().filter(u -> u.getIsInsuredFlag() != 2)
                .findFirst().map(a -> {
                    Map<String, Object> applicantMap = new HashMap<>();
                    applicantMap.put("name", a.getInsuredFullName());
                    applicantMap.put("idNo", a.getInsuredIdNo());
                    applicantMap.put("idType", a.getInsuredIdNoType());
                    applicantMap.put("nationality", a.getNationalityCd());
                    applicantMap.put("phone", a.getHomePhoneNo());
                    applicantMap.put("email", a.getEmailAddr());
                    if (a.getBirthday() != null) {
                        applicantMap.put("birthday", DateUtil.formatBirthday(a.getBirthday()));
                    }
                    policyMap.put("applicant", applicantMap);
                    return null;
                });
    }

    private String insuredBeneficiaryStr(Policyuser insured) {
        StringBuilder result = new StringBuilder();
        String[] beneficiarys = insured.getDeathBeneficiary().split(",");
        for (int i = 0; i < beneficiarys.length; i++) {
            String[] beneficiary = beneficiarys[i].split(":");
            result.append(beneficiary[0]);
            if (beneficiary.length > 4) {
                int proportion = (int) (Double.parseDouble(beneficiary[4]) * 100);
                result.append(":").append(String.format("%d%%", proportion));
            }
            if (i < beneficiarys.length - 1) {
                result.append(",");
            }
        }
        return result.toString();
    }

    private List<Map<String, Object>> beneficiaryList(List<Policyuser> insuredList) {
        List<Map<String, Object>> beneficiaryList = new ArrayList<>();
        for (Policyuser insured : insuredList) {
            String[] beneficiariesStr = insured.getDeathBeneficiary().split(",");
            for (String s : beneficiariesStr) {
                Map<String, Object> beneficiaryMap = new HashMap<>();
                String[] b = s.split(":");
                beneficiaryMap.put("insuredName", insured.getInsuredFullName());
                beneficiaryMap.put("beneficiaryName", b[0]);
                if (b.length >= 4) {
                    beneficiaryMap.put("idType", b[1]);
                    beneficiaryMap.put("idNo", b[2]);
                    beneficiaryMap.put("relation", b[3]);
                    beneficiaryMap.put("proportion", Double.parseDouble(b[4]) * 100);
                }
                beneficiaryList.add(beneficiaryMap);
            }
        }
        return beneficiaryList;
    }

    private List<Map<String, Object>> benefitList(Plan plan, boolean isDta) {
        List<Map<String, Object>> benefitList = new ArrayList<>();
        int sequence = 1, order = 1;
        for (Insureproject benefit : plan.getInsureprojectList()) {
            Map<String, Object> benefitMap = new HashMap<>();
            if (isDta && !"InternationalC".equals(benefit.getProjectCode())) {
                // 四海游
                continue;
            }

            if (!isDta && "InternationalC".equals(benefit.getProjectCode())) {
                // 不是四海游
                continue;
            }

            if (StringUtils.isNotBlank(benefit.getProjectCode()) && 0 != "AOC".compareToIgnoreCase(benefit.getProjectCode())) {
                benefitMap.put("sequence", sequence++);
            }

            benefitMap.put("description", benefit.getProjectname().replace("&", "&amp;"));
            benefitMap.put("adult", benefit.getAdult().replace("&", "&amp;"));
            benefitList.add(benefitMap);
        }
        return benefitList;
    }

    private List<Map<String, Object>> notes(Plan plan) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<Planmemo> notesList = plan.getPlanmemoList();
        for (Planmemo note : notesList) {
            result.add(Map.of("cn", note.getChinese().trim().replaceFirst("^\\*+", ""), "en", note.getEnglish()));
        }
        return result;
    }

    private Map<String, Object> applicationReminder(Plan plan) {
        if (StringUtils.isBlank(plan.getApplicationReminder())) {
            return null;
        }
        return JSONUtils.readJson2Map(plan.getApplicationReminder());
    }

    private String getProductTermsUrl(Product product) {
        String termsUrl = "";
        if (StringUtils.isNotBlank(product.getTerms())) {
            termsUrl = PropUtil.getString("host")
                    + "/policyCenter/b2c/termShow?productId="
                    + product.getProductid();
        }
        return termsUrl;
    }

    private PdfJobRequest policyListPdfParams(List<Policy> policyList) {
        PdfJobRequest pdfJobRequest = new PdfJobRequest();
        // 电子保单PDF
        pdfJobRequest.setModule("policyList");
        pdfJobRequest.setModuleId(String.valueOf(policyList.get(0).getPolicyID()));
        pdfJobRequest.setFtlPath("policyList.ftl");
        pdfJobRequest.setParams(policyListParams(policyList));
        return pdfJobRequest;
    }

    private Map<String, Object> policyListParams(List<Policy> policyList) {
        Map<String, Object> pdfParams = new HashMap<>();
        Product product = productService.getProduct(policyList.get(0).getProductID());
        Plan plan = productService.getPlan(policyList.get(0).getPlanID());
        Partner agency = partnerService.getPartner(policyList.get(0).getAgencyID());
        Map<String, Object> travelInfo = new HashMap<>();
        travelInfo.put("issueOffice", agency.getIssueOffice());
        travelInfo.put("issueOfficeEn", agency.getIssueOfficeEn());
        travelInfo.put("agencyCode", policyList.get(0).getAgencyPCC());
        travelInfo.put("agencyName", policyList.get(0).getAgency());
        travelInfo.put("productName", product.getProductname());
        travelInfo.put("productNameEn", product.getEnglishname());
        travelInfo.put("planName", plan.getPlanname());
        travelInfo.put("planNameEn", plan.getEnglishName());
        travelInfo.put("productId", product.getProductid());
        travelInfo.put("termUrl", getProductTermsUrl(product));
        travelInfo.put("effYear", policyList.get(0).getEffectiveDate().format(DateTimeFormatter.ofPattern("yyyy")));
        travelInfo.put("effMonth", policyList.get(0).getEffectiveDate().format(DateTimeFormatter.ofPattern("MM")));
        travelInfo.put("effDay", policyList.get(0).getEffectiveDate().format(DateTimeFormatter.ofPattern("dd")));
        travelInfo.put("effTime", policyList.get(0).getEffectiveDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        travelInfo.put("expYear", policyList.get(0).getExpiryDate().format(DateTimeFormatter.ofPattern("yyyy")));
        travelInfo.put("expMonth", policyList.get(0).getExpiryDate().format(DateTimeFormatter.ofPattern("MM")));
        travelInfo.put("expDay", policyList.get(0).getExpiryDate().format(DateTimeFormatter.ofPattern("dd")));
        travelInfo.put("expTime", policyList.get(0).getExpiryDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
        travelInfo.put("premium", String.format("%.2f", policyList.get(0).getTotalPremium()));
        travelInfo.put("lineNo", policyList.stream().map(Policy::getLineno).filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(",")));
        travelInfo.put("branch", getBranchName(policyList.get(0).getBranch(), "abbr"));
        travelInfo.put("branchName", getBranchName(policyList.get(0).getBranch(), "cn"));
        travelInfo.put("branchNameEn", getBranchName(policyList.get(0).getBranch(), "en"));

        pdfParams.put("benefitList", benefitList(plan, false));
        pdfParams.put("notes", notes(plan));
        pdfParams.put("agency", agencyInfo(policyList.get(0), agency));
        pdfParams.put("policy", travelInfo);

        List<Map<String, Object>> insuredMapList = new ArrayList<>();
        int sequence = 1;
        for (Policy policy : policyList) {
            List<Policyuser> insuredList = policyCommonService.getPolicyUserList(policy.getPolicyID()).stream().filter(i -> i.getIsInsuredFlag() != 0).collect(Collectors.toList());
            for (Policyuser insured : insuredList) {
                Map<String, Object> insuredMap = new HashMap<>();
                insuredMap.put("seq", sequence);
                insuredMap.put("policyNo", policy.getPolicyNo());
                insuredMap.put("applicantName", policy.getPolicyholder());
                insuredMap.put("name", insured.getInsuredFullName());
                insuredMap.put("idNo", insured.getInsuredIdNo());
                insuredMap.put("identificationNo", insured.getIdentificationID());
                insuredMap.put("idType", insured.getInsuredIdNoType());
                insuredMap.put("birthday", DateUtil.formatBirthday(insured.getBirthday()));
                insuredMap.put("relation", insured.getRelationship());
                insuredMap.put("premium", insured.getPremium());
                insuredMap.put("beneficiary", insuredBeneficiaryStr(insured));
                insuredMapList.add(insuredMap);
            }
            sequence++;
        }

        pdfParams.put("insuredList", insuredMapList);
        return pdfParams;
    }

    private Partner getPdfAgency(Policy policy) {
        Partner agency = policy.getPartner();
        if (policy.getBranch() != agency.getAgencyID() && agency.getBranch() != 0) {
            Partner branch = partnerService.getPartner(agency.getBranch());
            agency.setIssueOffice(branch.getIssueOffice());//设置签发地
            agency.setIssueOfficeEn(branch.getIssueOfficeEn());//设置签发地英文
            if (StringUtils.isBlank(agency.getIsRed())) {
                agency.setIsRed(branch.getIsRed());
            }
        }
        if (StringUtils.isBlank(agency.getAbbrName())) {
            agency.setAbbrName(agency.getAgencyName());
        }
        return agency;
    }

    /**
     * @param type cn-中文名称 en-英文名称 abbr-缩写
     */
    private String getBranchName(long branchId, String type) {
        if ("cn".equals(type)) {
            switch ((int) branchId) {
                case 2:
                    return "北京";
                case 66:
                    return "上海";
                case 87:
                case 526:
                    return "江苏";
                case 454:
                    return "浙江";
                case 458:
                    return "广东";
                case 714:
                    return "深圳";
            }
        }
        if ("en".equals(type)) {
            switch ((int) branchId) {
                case 2:
                    return "Beijing";
                case 66:
                    return "Shanghai";
                case 87:
                case 526:
                    return "Jiangsu";
                case 454:
                    return "Zhejiang";
                case 458:
                    return "Guangdong";
                case 714:
                    return "Shenzhen";
            }
        }
        if ("abbr".equals(type)) {
            switch ((int) branchId) {
                case 2:
                    return "BJ";
                case 66:
                    return "SH";
                case 87:
                case 526:
                    return "JS";
                case 454:
                    return "ZJ";
                case 458:
                    return "GD";
                case 714:
                    return "SZ";
            }
        }
        return "";
    }

    private String nullToBlank(Object o) {
        return o == null ? "" : o.toString();
    }
}

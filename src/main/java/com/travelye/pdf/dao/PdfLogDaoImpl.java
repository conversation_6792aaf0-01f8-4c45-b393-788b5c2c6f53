package com.travelye.pdf.dao;

import com.travelye.pdf.entity.PdfLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public class PdfLogDaoImpl implements PdfLogDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<PdfLog> getPdfRecord(String module, String moduleId) {
        String sql = "select * from pdf_log where module = ? and moduleId = ?";
        return jdbcTemplate.query(sql,
                new BeanPropertyRowMapper<>(PdfLog.class),
                module,
                moduleId);
    }

    @Override
    public int insert(String module, String moduleId) {
        String sql = "insert into pdf_log(module,moduleId,generateTime) values (?,?,?)";
        return jdbcTemplate.update(sql,
                module,
                moduleId,
                LocalDateTime.now());
    }

}

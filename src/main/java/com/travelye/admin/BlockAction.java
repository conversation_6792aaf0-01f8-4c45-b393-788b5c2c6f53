package com.travelye.admin;

import com.travelye.base.ActionSupport;
import com.travelye.base.MessageException;
import com.travelye.base.QueryResult;
import com.travelye.base.SjisConstant;
import com.travelye.block.BlockService;
import com.travelye.product.ProductService;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.vo.Block;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;


import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zzl on 2017/8/25.
 */
@Controller
public class BlockAction extends ActionSupport {

    private final static Logger log = LoggerFactory.getLogger(BlockAction.class);

    @Autowired
    private BlockService blockService;

    private int blockId;

    public int getBlockId() {
        return blockId;
    }

    public void setBlockId(int blockId) {
        this.blockId = blockId;
    }

    private Block block;

    public Block getBlock() {
        return block;
    }

    public void setBlock(Block block) {
        this.block = block;
    }

    @Autowired
    private ProductService productService;


    /**
     * 产品块列表
     *
     * @return
     */
    @RequestMapping("/admin/block/blockList")
    public String blockList() {
        if (!(super.hasAction("blockManager"))) {
            request.setAttribute("message", "没有权限操作！");
            return "/admin/block/list";
        }
        List<Block> list = blockService.queryBlocklist();
        request.setAttribute("blocklist", list);
        log.debug("产品板块列表：/blocklist/list got: " + list);
        return "/admin/block/list";
    }


    /**
     * 编辑产品块
     *
     * @return
     */
    @RequestMapping("/admin/block/edit")
    public String edit() {
        if (!(super.hasAction("blockManager"))) {
            request.setAttribute("message", "没有权限操作！");
            return "/admin/block/edit";
        }
        if (blockId > 0) {
            Block block = blockService.getBlockById(blockId);
            request.setAttribute("block", block);
        }

        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        String branchid = userSession.getAgencyId() + "," + userSession.getBranch();
        QueryResult result = productService.listProduct(1, 1000, branchid);
        request.setAttribute("productlist", result.getList());

        log.debug("编辑产品板块：/blocklist/editBlick edit: " + blockId);
        return "/admin/block/edit";
    }


    /**
     * 保存产品块
     *
     * @return
     */
    @RequestMapping("/admin/block/saveBlock")
    @ResponseBody
    public Map<String, Object> saveBlock() {
        Map<String, Object> data = new HashMap<>();
        try {
            if (!(super.hasAction("blockManager"))) {
                throw new MessageException("没有权限");
            }
            long blickid = blockService.saveBlock(block);
            log.debug("保存产品板块：/blocklist/save got: " + block);
            data.put("message", blickid);
            data.put("result", 1);
        } catch (Exception e) {
            data.put("result", 0);
            data.put("message", "保存失败：" + e.getMessage());
        }
        request.setAttribute("message", data.toString());
        return data;
    }


    //ckeditorUpload插件上传文件都存在upload中
    @RequestMapping("/admin/block/ckeditorUpload")
    @ResponseBody
    public String ckeditorUpload() {

        // 文件扩展名
        String callback = request.getParameter("CKEditorFuncNum");
        String savePath = request.getSession().getServletContext().getRealPath("/") + SjisConstant.upload_folder + "/imgs/";
        try {
            PrintWriter out = response.getWriter();
            if (uploadContentType.equals("image/pjpeg")
                    || uploadContentType.equals("image/jpeg")) {
                // IE6上传jpg图片的headimageContentType是image/pjpeg，而IE9以及火狐上传的jpg图片是image/jpeg
            } else if (uploadContentType.equals("image/png")
                    || uploadContentType.equals("image/x-png")) {
                // IE6上传的png图片的headimageContentType是"image/x-png"
            } else if (uploadContentType.equals("image/gif")) {
            } else if (uploadContentType.equals("image/bmp")) {
            } else if (uploadContentType.equals("image/jpeg")) {
            } else if (uploadContentType.equals("image/ico")) {
            } else {
                out.println("<script type=\"text/javascript\">");
                out.println("window.parent.CKEDITOR.tools.callFunction(" + callback
                        + ",''," + "'文件格式不正确（必须为.jpg/.gif/.bmp/.png/.jpeg/.ico文件）');");
                out.println("</script>");
                return null;
            }
            if (upload.length() > 10240 * 1024) {
                out.println("<script type=\"text/javascript\">");
                out.println("window.parent.CKEDITOR.tools.callFunction(" + callback
                        + ",''," + "'图片大小不得大于10M');");
                out.println("</script>");
                return null;
            }
            if (new File(savePath + uploadFileName).exists()) {
                out.println("<script type=\"text/javascript\">");
                out.println("window.parent.CKEDITOR.tools.callFunction(" + callback
                        + ",''," + "'该图片名称已被其他图片占用，请换一个名字：）');");
                out.println("</script>");
                return null;
            }
            InputStream in = new FileInputStream(upload);
            File uploadFile = new File(savePath, uploadFileName);
            OutputStream os = new FileOutputStream(uploadFile);
            byte[] buffer = new byte[10240 * 1024];
            int length;
            while ((length = in.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
            in.close();
            os.close();
            out.println("<script type=\"text/javascript\">");
            out.println("window.parent.CKEDITOR.tools.callFunction(" + callback
                    + ",'" + "/upload/imgs/" + uploadFileName + "','')");
            out.println("</script>");
        } catch (Exception e) {
            log.error("", e);
            request.setAttribute("message", "保存失败");
        }
        return null;

    }

    private File upload;  //文件
    private String uploadContentType;  //文件类型
    private String uploadFileName;   //文件名

    public File getUpload() {
        return upload;
    }

    public void setUpload(File upload) {
        this.upload = upload;
    }

    public String getUploadContentType() {
        return uploadContentType;
    }

    public void setUploadContentType(String uploadContentType) {
        this.uploadContentType = uploadContentType;
    }

    public String getUploadFileName() {
        return uploadFileName;
    }

    public void setUploadFileName(String uploadFileName) {
        this.uploadFileName = uploadFileName;
    }


}

package com.travelye.admin;

import com.travelye.base.ActionSupport;
import com.travelye.base.MessageException;
import com.travelye.base.tools.JSONUtils;
import com.travelye.searchZoneCenter.services.CodeItemService;
import com.travelye.searchZoneCenter.services.CodeSetService;
import com.travelye.security.session.SecurityUser;
import com.travelye.security.session.UserSessionManager;
import com.travelye.vo.CodeItem;
import com.travelye.vo.CodeSet;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Created by shao on 2018/5/30.
 */
public class SearchZoneAction extends ActionSupport {
    protected transient final Logger log = LoggerFactory.getLogger(getClass());
    private CodeSetService codeSetService;
    private CodeItemService service;

    public String searchQuery() {
        return SUCCESS;
    }

    public String getCodeSets() {
        List<CodeSet> codeSets = codeSetService.getCodeSets();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeSets", codeSets);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    public String getFirstLevelCodeItems() {
        String id = request.getParameter("id");
        List<CodeItem> codeItems = service.getFirstLevelCodeItemsOnCodeSetId(Integer.parseInt(id));
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeItems", codeItems);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //获取下一级的分类信息
    public String getNextLevelCodeItems() {
        int codeItemId = Integer.parseInt(request.getParameter("codeItemId"));
        List<CodeItem> codeItems = service.getNextLevelCodeItemsOnCodeItemId(codeItemId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeItems", codeItems);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //点击大陆，就会获得大陆的所有省份以及直辖市
    public String getProvinces() {
        try {
            List<CodeItem> list = service.getValidProvinces();
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("provinces", list);
            JSONObject json = new JSONObject(map);
            saveMessage(json.toString());
        } catch (Exception e) {
            log.error("", e);
        }
        return "ajax";
    }

    //获取所有的CodeItem
    public String getCodeItems() {
        try {
            List<CodeItem> codeItems = service.getProvinces();
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("codeItems", codeItems);
            JSONObject json = new JSONObject(map);
            saveMessage(json.toString());
        } catch (Exception e) {
            log.error("", e);
        }
        return "ajax";
    }

    //获取所有国家以及地区的信息
    public String getCountriesAndAreas() {
        List<CodeItem> codeItems = service.getCountriesAndAreas();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeItems", codeItems);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //点击省或者直辖市，会出现下一季单位
    public String getCities() {
        //获取拼出来的levelCode，例如：province_0011
        String long_levelCode = request.getParameter("LevelCode");
        //将拼出来的levelCode截断，取后四位
        String levelCode = long_levelCode.substring(9);
        List<CodeItem> list = service.getCitiesOnLevelCode(levelCode);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("firstCities", list);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //出现区县单位 cities_00130001
    public String getTowns() {
        //获取拼出来的levelCode，例如：province_0011
        String long_levelCode = request.getParameter("LevelCode");
        //将拼出来的levelCode截断，取后四位
        String levelCode = long_levelCode.substring(7);
        List<CodeItem> list = service.getTownsOnLevelCode(levelCode);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("secondTowns", list);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //根据县区找到其上级单位
    public String getSuperAreas() {
        String levelCode = null;
        CodeItem town = null;
        //获取拼出来的levelCode，例如：province_0011
        String long_levelCode = request.getParameter("LevelCode");
        if (StringUtils.isBlank(long_levelCode)) { //若不用levelCode请求，则用行政区划代码code请求
            String code = request.getParameter("code");
            town = service.getCodeItemEnabledByCodeAndCodeSetCode(CodeSet.CODE_DIVISION, code);
            town = town == null ? (new CodeItem()) : town;
            levelCode = town.getLevelCode();
        } else {
            levelCode = long_levelCode.substring(6);
        }
        String superAreas = null;
        if (StringUtils.isNotBlank(levelCode)) {
            //根据4位 8位的LevelCode获得其所属的省，直辖市 以及 第一级城市（市辖区，县辖区）
            String first_4_LevelCode = levelCode.substring(0, 4);
            String first_8_LevelCode = levelCode.substring(0, 8);
            CodeItem province = service.getDivisionOnLevelCode(first_4_LevelCode);
            CodeItem city = service.getDivisionOnLevelCode(first_8_LevelCode);
            //获得其自身的信息
            if (town == null) {
                town = service.getDivisionOnLevelCode(levelCode);
            }
            //将信息拼接在superArea中
            if (city.getCustomize1().equals("0")) {
                superAreas = province.getNAME() + "-" + town.getNAME() + "/" + town.getCODE();
            } else {
                superAreas = province.getNAME() + "-" + city.getNAME() + "-" + town.getNAME() + "/" + town.getCODE();
            }

        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("superAreas", superAreas.split("/")[0]);
        map.put("code", superAreas.split("/")[1]);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //根据市找到其上级单位
    public String getCityAreas() {
        String levelCode = null;
        CodeItem town = null;
        //获取拼出来的levelCode，例如：province_0011
        String long_levelCode = request.getParameter("LevelCode");
        if (StringUtils.isBlank(long_levelCode)) { //若不用levelCode请求，则用行政区划代码code请求
            String code = request.getParameter("code");
            town = service.getCodeItemEnabledByCodeAndCodeSetCode(CodeSet.CODE_DIVISION, code);
            town = town == null ? (new CodeItem()) : town;
            levelCode = town.getLevelCode();
        } else {
            levelCode = long_levelCode.substring(7);
        }
        String superAreas = null;
        if (StringUtils.isNotBlank(levelCode)) {
            //根据4位 8位的LevelCode获得其所属的省，直辖市 以及 第一级城市（市辖区，县辖区）
            String first_4_LevelCode = levelCode.substring(0, 4);
            String first_8_LevelCode = levelCode.substring(0, 8);
            CodeItem province = service.getDivisionOnLevelCode(first_4_LevelCode);
            CodeItem city = service.getDivisionOnLevelCode(first_8_LevelCode);
            //获得其自身的信息
            if (town == null) {
                town = service.getDivisionOnLevelCode(levelCode);
            }
            //将信息拼接在superArea中
            if (city.getCustomize1().equals("0")) {
                superAreas = province.getNAME() + "-" + town.getNAME() + "/" + town.getCODE();
            } else {
                superAreas = province.getNAME() + "-" + city.getNAME() + "/" + town.getCODE();
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("superAreas", superAreas.split("/")[0]);
        map.put("code", superAreas.split("/")[1]);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //找到具体一个国家
    public String getSuperAreas_country() {
        String long_CodeItemId = request.getParameter("CodeItemId");
        String CodeItemId = long_CodeItemId.substring(8);
        CodeItem country = service.getCountryOnCodeItemId(CodeItemId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("country", country.getNAME());
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //找到具体一个地区
    public String getSuperAreas_area() {
        String long_CodeItemId = request.getParameter("CodeItemId");
        String CodeItemId = long_CodeItemId.substring(5);
        CodeItem area = service.getCountryOnCodeItemId(CodeItemId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("area", area.getNAME());
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //找到一个市的所有同级别单位
    public String clickOnCity() {
        String long_back_city_id = request.getParameter("back_city_id");
        String levelCode = long_back_city_id.substring(7, 11);
        List<CodeItem> cities = service.getCitiesOnLevelCode(levelCode);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("cities", cities);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //找到一个区县的所有同级别单位
    public String clickOnTown() {
        String long_back_city_id = request.getParameter("back_town_id");
        String long_leveCode = long_back_city_id.substring(6);
        String levelCode = long_leveCode.substring(0, 8);
        List<CodeItem> towns = service.getTownsOnLevelCode(levelCode);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("towns", towns);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }


    //找到一个CodeItem的信息并且把它返还回去
    public String getCodeItem_update() {
        int codeItemId = Integer.parseInt(request.getParameter("codeItemId"));
        CodeItem codeitem = service.getCodeItem(codeItemId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeitem", codeitem);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //找到一个CodeSet的信息并且将它返回回去
    public String getCodeSet_update() {
        int codeSetId = Integer.parseInt(request.getParameter("codeSetID"));
        CodeSet codeSet = codeSetService.getCodeSetByCodeSetId(codeSetId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("codeSet", codeSet);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    private CodeItem codeItem;

    public CodeItem getCodeItem() {
        return codeItem;
    }

    public void setCodeItem(CodeItem codeItem) {
        this.codeItem = codeItem;
    }

    private CodeSet codeSet;

    public CodeSet getCodeSet() {
        return codeSet;
    }

    public void setCodeSet(CodeSet codeSet) {
        this.codeSet = codeSet;
    }


    /*
     * 进行更新操作
     *
     * 首先也要进行明细编号的校验
     * */
    public String updateZones() {
        Map<String, Object> map = new HashMap<String, Object>();
        CodeItem existCodeItem = service.getCodeItemByCodeAndCodeSetId(codeItem.getCodeSetId(), codeItem.getCODE());
        if (existCodeItem != null && existCodeItem.getCodeItemId() != codeItem.getCodeItemId()) {
            //如果该编号已经存在，并且不是自己的编号
            map.put("success", 2);
        } else {
            try {

                codeItem.setChangeUser("");

                //将当前的日期设置为更改日期
                codeItem.setChangeTime(LocalDateTime.now());

                service.updateCodeItem(codeItem);
                map.put("success", "1");
                map.put("codeItem", codeItem);
            } catch (Exception e) {
                log.error("", e.getMessage());
                map.put("success", "0");
            }
        }

        String json = JSONUtils.toJson(map, new SimpleDateFormat("yyyy-MM-dd"));
        saveMessage(json);

        return "ajax";
    }

    /*
     * 更新CodeSet
     * */
    public String updateCodeSet() {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        Map<String, Object> map = new HashMap<String, Object>();
        CodeSet existCodeSet = codeSetService.getCodeSetOnCodeSetCode(codeSet.getCodeSetCode());
        if (existCodeSet != null && existCodeSet.getCodeSetId() != codeSet.getCodeSetId()) {
            //如果该编号已经存在，并且不是自己的编号
            map.put("success", 2);
        } else {
            try {
                codeSet.setChangeUser(userSession.getLoginName());
                //将当前的日期设置为更改日期
                codeSet.setChangeTimedatetime(LocalDateTime.now());
                codeSetService.updateCodeSet(codeSet);
                map.put("success", "1");
                map.put("codeSet", codeSet);
            } catch (Exception e) {
                log.error("", e.getMessage());
                map.put("success", "0");
            }
        }
        String json = JSONUtils.toJson(map, new SimpleDateFormat("yyyy-MM-dd"));
        saveMessage(json);
        return "ajax";
    }

    public String job_choice1() {
        String customize1 = request.getParameter("customize1");
        List<CodeItem> jobs1 = service.oneGradeJobsOnCustomize1(customize1);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("jobs1", jobs1);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }


    //点击第二级的下拉条读取的所有工作信息
    public String clickOnJob_choice2() {
        String jobs1_id = request.getParameter("jobs1_id");
        String LevelCode = jobs1_id.substring(6);
        String customize1 = request.getParameter("customize1");
        List<CodeItem> jobs2 = service.twoGradeJobsOnLevelCodeAndCustomize1(LevelCode, customize1);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("jobs2", jobs2);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }


    //点击第三级的下拉条读取的所有工作信息
    public String clickOnJob_choice3() {
        String jobs2_id = request.getParameter("jobs2_id");
        String LevelCode = jobs2_id.substring(6);
        String custonsize1 = request.getParameter("customsize1");
        List<CodeItem> jobs3 = service.threeGradeJobsOnLevelCodeAndCustomize1(LevelCode, custonsize1);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("jobs3", jobs3);
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }


    /*
     * 增加同级目录信息
     *
     *  首先对CODE进行校验，同一级别的CODE不能重复
     *  需要的操作有：首先获得同一级的最大分级码，将最大分级码加1当做当前将要添加的分级码
     *  同时获得当前表中的最大主键数将其加1作为将要添加信息的主键
     *
     * */
    public String addSameLevelInfo() {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        Map<String, Object> map = new HashMap<String, Object>();
        try {


            CodeItem existCodeItem = service.getCodeItemByCodeAndCodeSetId(codeItem.getCodeSetId(), codeItem.getCODE());
            if (existCodeItem != null) {
                //如果同一CodeSetId中已经有该类了，那么就返回提示说明细编号重复
                map.put("success", 2);
            } else {
                //获得同一级的最大分级码，将分级码增加1，作为当前添加信息的分级码
                String maxLevelCode = service.getMaxLevelCodeInSameLevel(codeItem);
//                long addLevelCode = Long.parseLong(maxLevelCode);
//                addLevelCode += 1;
//                String strFormart = "%0" + Integer.toString(maxLevelCode.length()) + "d";
//
//                String max_LevelCode = String.format(strFormart, addLevelCode);
                String hstr = maxLevelCode.substring(maxLevelCode.length() - 4, maxLevelCode.length());
                String qstr = maxLevelCode.substring(0, maxLevelCode.length() - 4);
                int hcode = Integer.parseInt(hstr);
                String Thcode = "";
                if (hcode == 9999) {
                    throw new MessageException("此级已满");
                } else {
                    hcode += 1;
                    if (hcode >= 1000) {
                        Thcode = hcode + "";
                    } else if (hcode >= 100 && hcode < 1000) {
                        Thcode = "0" + hcode;
                    } else if (hcode >= 10 && hcode < 100) {
                        Thcode = "00" + hcode;
                    } else {
                        Thcode = "000" + hcode;
                    }
                }
                String hhstr = Thcode + "";
                String max_LevelCode = qstr + hhstr;

                //获得当前表格中的最大的主键数，加1作为当前增加信息的主键
                int pri_key = (int) service.getMaxPrimaryKey();
                pri_key += 1;

                codeItem.setCodeItemId(pri_key);

                codeItem.setIsLowest(1);
                codeItem.setLevelCode(max_LevelCode);

                codeItem.setCreateUser(userSession.getLoginName());

                codeItem.setCreateTime(LocalDateTime.now());


                service.saveCodeItem(codeItem);

                map.put("codeItem", codeItem);
                map.put("success", 1);
            }

        } catch (Exception e) {

            map.put("message", e.getMessage());
            map.put("success", "3");
        }

        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    //增加下级目录信息
    public String addNextLevelInfo() {
        SecurityUser userSession = UserSessionManager.getSecurityLoginUser(request);
        Map<String, Object> map = new HashMap<String, Object>();
        try {

            //获得起点对象
            CodeItem add_from = service.getCodeItem(codeItem.getCodeItemId());
            //首先要判断当前等级是否为末级，如果是末级的话，首先将起点设置为非末级
            if (add_from.getIsLowest() == 1) {
                add_from.setIsLowest(0);
                codeItem.setIsLowest(1);
                //其次就是拼接一个下一级的分级码
                codeItem.setLevelCode(add_from.getLevelCode() + "0001");
                codeItem.setLevel(add_from.getLevel() + 1);
                service.updateCodeItem(add_from);
            } else {
                //获得下一级的最大的分级码
                String maxLevelCode = service.getMaxLevelCodeInNextLevel(add_from);
                //根据下级的分级码获得CodeItem对象
                CodeItem codeItem_maxLevelCode = service.getCodeItemOnLevelCodeAndCodeSetId(maxLevelCode, add_from.getCodeSetId());

                String hstr = maxLevelCode.substring(maxLevelCode.length() - 4, maxLevelCode.length());
                String qstr = maxLevelCode.substring(0, maxLevelCode.length() - 4);
                int hcode = Integer.parseInt(hstr);
                String Thcode = "";
                if (hcode == 9999) {
                    throw new MessageException("此级已满");
                } else {
                    hcode += 1;
                    if (hcode >= 1000) {
                        Thcode = hcode + "";
                    } else if (hcode >= 100 && hcode < 1000) {
                        Thcode = "0" + hcode;
                    } else if (hcode >= 10 && hcode < 100) {
                        Thcode = "00" + hcode;
                    } else {
                        Thcode = "000" + hcode;
                    }
                }
                String hhstr = Thcode + "";
                String max_LevelCode = qstr + hhstr;
        /*    long addLevelCode = Long.parseLong(maxLevelCode);
            addLevelCode += 1;*/
/*            String strFormart = "%0" + Integer.toString(maxLevelCode.length()) + "d";

            String max_LevelCode = String.format(strFormart, addLevelCode);*/
                codeItem.setLevelCode(max_LevelCode);

                codeItem.setLevel(codeItem_maxLevelCode.getLevel());
                codeItem.setIsLowest(1);

            }
            //获得当前表格中的最大的主键数，加1作为当前增加信息的主键
            int pri_key = (int) service.getMaxPrimaryKey();
            pri_key += 1;

            codeItem.setCodeItemId(pri_key);

            codeItem.setCreateUser(userSession.getLoginName());
            codeItem.setCreateTime(LocalDateTime.now());

            service.saveCodeItem(codeItem);

            map.put("codeItem", codeItem);
            map.put("success", "1");

        } catch (Exception e) {
            log.error("", e);
            map.put("message", e.getMessage());
            map.put("success", "3");
        }

        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    public String getLastCodeItem() {
        String codeItemIds = request.getParameter("codeItemIds");
        String[] codeItemIdlist = codeItemIds.split(",");
        List<List<CodeItem>> codeItemNextList = new ArrayList<List<CodeItem>>();
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            for (int i = 0; i < codeItemIdlist.length; i++) {
                List<CodeItem> codeItems = service.getNextLevelCodeItemsOnCodeItemId(Integer.valueOf(codeItemIdlist[i]));
                codeItemNextList.add(codeItems);
            }
            map.put("list", codeItemNextList);
            map.put("success", "1");
        } catch (Exception e) {
            log.error("", e);
            map.put("success", "0");
        }
        JSONObject json = new JSONObject(map);
        saveMessage(json.toString());
        return "ajax";
    }

    public void setCodeSetService(CodeSetService codeSetService) {
        this.codeSetService = codeSetService;
    }

    public CodeSetService getCodeSetService() {
        return codeSetService;
    }

    public void setService(CodeItemService service) {
        this.service = service;
    }

    public CodeItemService getService() {
        return service;
    }
}

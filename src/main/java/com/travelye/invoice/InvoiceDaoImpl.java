package com.travelye.invoice;

import com.travelye.base.BaseJdbcDaoImpl;
import com.travelye.base.QueryResult;
import com.travelye.base.tools.JdbcUtil;
import com.travelye.policy.Condition;
import com.travelye.policy.PolicyView;
import com.travelye.security.session.SecurityUser;
import org.springframework.stereotype.Repository;

@Repository
public class InvoiceDaoImpl  extends BaseJdbcDaoImpl implements InvoiceDao {
	public QueryResult query(Condition condition,SecurityUser userSession, int start, int count){
		StringBuffer conStr=new StringBuffer();
		conStr.append(" from tblpolicy p,tblAgency e");
		conStr.append(condition.getTableStr(userSession));
		conStr.append(" Where p.agencyID=e.agencyID and p.invoicetag=1");
		
		conStr.append(condition.getConStr(condition,userSession));
	
		if (condition.getBranch() != 0){
			conStr.append(" and p.branch=").append(condition.getBranch());
		}
		
		if (condition.getPartnerid() !=0){
			conStr.append(" and p.agencyid=").append(condition.getPartnerid());
		}
			
		if (condition.has(condition.getIssueBeginDate())){
			conStr.append(" and p.invoiceTime>=").append(JdbcUtil.getStringValue(condition.getIssueBeginDate()));
		}
		
		if (condition.has(condition.getIssueEndDate())){
			conStr.append(" and p.invoiceTime<'").append(condition.getIssueEndDate()).append(" 23:59:59'");
		}
		
		String countString="Select Count(1) from (select distinct p.policyid " + conStr.toString() + ") A";
		String queryString="Select distinct p.*,e.agencyName" +
			conStr.toString() +
			" order by p.invoiceTime desc";		
		return this.find(queryString,countString, start, count,PolicyView.class);
	}
}
